import { type ServiceFilter } from '@moego/api-web/moego/api/appointment/v1/print_card_api';
import { useDispatch, type Dispatch } from 'amos';
import dayjs, { type Dayjs } from 'dayjs';
import { useEffect, useState } from 'react';
import {
  getPlaygroupPrintCardSettings,
  listDailyPlaygroupCard,
  type PlaygroupsPrintCard,
} from '../../../store/playgroups/playgroups.actions';
import type { ActivityPrintCardInfo } from '../../../store/printCard/activityCard/activityCard.actions';
import {
  getActivityPrintCardContent,
  getActivityPrintCardSettings,
} from '../../../store/printCard/activityCard/activityCard.actions';
import type { PrintCardInfo } from '../../../store/printCard/appointmentCard/appointmentCard.actions';
import {
  getApptPrintCardContent,
  getApptPrintCardSettings,
} from '../../../store/printCard/appointmentCard/appointmentCard.actions';
import type { ApptListPrintCardInfo } from '../../../store/printCard/appointmentList/appointmentList.actions';
import {
  getApptListPrintCardContent,
  getApptListPrintCardSettings,
} from '../../../store/printCard/appointmentList/appointmentList.actions';
import {
  getBoardingArrivalPrintCardContent,
  getBoardingArrivalPrintCardSettings,
} from '../../../store/printCard/boardingArrival/boardingArrival.actions';
import {
  getBoardingDeparturePrintCardContent,
  getBoardingDeparturePrintCardSettings,
} from '../../../store/printCard/boardingDeparture/boardingDeparture.actions';
import type { StayPrintCardInfo } from '../../../store/printCard/stayCard/stayCard.actions';
import { getStayPrintCardContent, getStayPrintCardSettings } from '../../../store/printCard/stayCard/stayCard.actions';
import { DATE_FORMAT_EXCHANGE } from '../../../utils/DateTimeUtil';
import { useBool } from '../../../utils/hooks/useBool';
import { useDebounceCallback } from '../../../utils/hooks/useDebounceCallback';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { stringToDateMessage } from '../../../utils/utils';
import {
  PrintCardType,
  getServiceItemTypesByCareType,
  type MultiTypePrintModalState,
} from '../MultiTypePrintModal.options';
import { type BoardingPrintCardContent } from '../components/common/BoardingCard/BoardingCard.types';

const DATA_ACTION = {
  [PrintCardType.ApptList]: (
    dispatch: Dispatch,
    input: {
      date?: string;
      serviceFilters?: ServiceFilter[];
      isPetsCheckedInOnly?: boolean;
    },
  ) => {
    const { date, serviceFilters = [], isPetsCheckedInOnly } = input;
    return dispatch(
      getApptListPrintCardContent({
        date: stringToDateMessage(date ?? dayjs()),
        isPetsCheckedInOnly,
        types: [],
        serviceFilters,
      }),
    );
  },
  [PrintCardType.BoardingDeparture]: (
    dispatch: Dispatch,
    input: {
      date?: string;
      isPetsCheckedInOnly?: boolean;
    },
  ) => {
    const { date, isPetsCheckedInOnly } = input;
    return dispatch(
      getBoardingDeparturePrintCardContent({
        date: stringToDateMessage(date ?? dayjs()),
        isPetsCheckedInOnly,
      }),
    );
  },
  [PrintCardType.BoardingArrival]: (
    dispatch: Dispatch,
    input: {
      date?: string;
      isPetsCheckedInOnly?: boolean;
    },
  ) => {
    const { date, isPetsCheckedInOnly } = input;
    return dispatch(
      getBoardingArrivalPrintCardContent({
        date: stringToDateMessage(date ?? dayjs()),
        isPetsCheckedInOnly,
      }),
    );
  },
  [PrintCardType.Stay]: (
    dispatch: Dispatch,
    input: {
      groomingId?: string;
      date?: string;
      serviceItemTypes?: string;
      isPetsCheckedInOnly?: boolean;
    },
  ) => {
    return dispatch(
      getStayPrintCardContent({
        groomingId: input.groomingId,
        date: input.date,
        serviceItemTypes: input.serviceItemTypes,
        isPetsCheckedInOnly: input.isPetsCheckedInOnly,
      }),
    );
  },
  [PrintCardType.Activity]: (
    dispatch: Dispatch,
    input: {
      date?: string;
      serviceItemTypes?: string;
      isPetsCheckedInOnly?: boolean;
    },
  ) => {
    return dispatch(
      getActivityPrintCardContent({
        date: input.date,
        serviceItemTypes: input.serviceItemTypes,
        isPetsCheckedInOnly: input.isPetsCheckedInOnly,
      }),
    );
  },
  [PrintCardType.Appt]: (
    dispatch: Dispatch,
    input: {
      groomingId?: string;
      date?: string;
    },
  ) => {
    return dispatch(getApptPrintCardContent({ groomingId: input.groomingId, date: input.date }));
  },
  [PrintCardType.Playgroup]: (
    dispatch: Dispatch,
    input: {
      date?: string;
      isPetsCheckedInOnly?: boolean;
    },
  ) => {
    return dispatch(
      listDailyPlaygroupCard({
        date: stringToDateMessage(input.date ?? dayjs()),
        isPetsCheckedInOnly: input.isPetsCheckedInOnly,
      }),
    );
  },
};

const SETTINGS_ACTION = {
  [PrintCardType.ApptList]: (dispatch: Dispatch) => {
    return dispatch(getApptListPrintCardSettings());
  },
  [PrintCardType.BoardingDeparture]: (dispatch: Dispatch) => {
    return dispatch(getBoardingDeparturePrintCardSettings());
  },
  [PrintCardType.BoardingArrival]: (dispatch: Dispatch) => {
    return dispatch(getBoardingArrivalPrintCardSettings());
  },
  [PrintCardType.Stay]: (dispatch: Dispatch) => {
    return dispatch(getStayPrintCardSettings());
  },
  [PrintCardType.Activity]: (dispatch: Dispatch) => {
    return dispatch(getActivityPrintCardSettings());
  },
  [PrintCardType.Appt]: (dispatch: Dispatch) => {
    return dispatch(getApptPrintCardSettings());
  },
  [PrintCardType.Playgroup]: (dispatch: Dispatch) => {
    return dispatch(getPlaygroupPrintCardSettings());
  },
};

type Data = {
  [PrintCardType.ApptList]: ApptListPrintCardInfo[];
  [PrintCardType.BoardingDeparture]: BoardingPrintCardContent[];
  [PrintCardType.BoardingArrival]: BoardingPrintCardContent[];
  [PrintCardType.Stay]: StayPrintCardInfo[];
  [PrintCardType.Activity]: ActivityPrintCardInfo[];
  [PrintCardType.Appt]: PrintCardInfo[];
  [PrintCardType.Playgroup]: PlaygroupsPrintCard[];
};

export const useGetData = (props: {
  isOpen?: boolean;
  groomingId?: number;
  date?: Dayjs;
  modalState: MultiTypePrintModalState;
}) => {
  const dispatch = useDispatch();
  const { isOpen, groomingId, date, modalState } = props;
  const { selectedCardType, careType, isPetsCheckedInOnly, serviceFilters } = modalState;
  const loadingContent = useBool(true);
  const loadingSettings = useBool(true);
  const [cachedData, setCachedData] = useState<Data>({
    [PrintCardType.ApptList]: [],
    [PrintCardType.BoardingDeparture]: [],
    [PrintCardType.BoardingArrival]: [],
    [PrintCardType.Stay]: [],
    [PrintCardType.Activity]: [],
    [PrintCardType.Appt]: [],
    [PrintCardType.Playgroup]: [],
  });

  const getData = useDebounceCallback(async () => {
    loadingContent.open();
    try {
      const rest = await DATA_ACTION[selectedCardType](dispatch, {
        groomingId: groomingId ? `${groomingId}` : undefined,
        date: date?.format(DATE_FORMAT_EXCHANGE),
        serviceItemTypes: getServiceItemTypesByCareType(careType),
        serviceFilters,
        isPetsCheckedInOnly,
      });
      setCachedData((data) => ({ ...data, [selectedCardType]: rest }));
    } finally {
      loadingContent.close();
    }
  }, 300);

  const getSettings = useSerialCallback(async () => {
    loadingSettings.open();
    try {
      await SETTINGS_ACTION[selectedCardType](dispatch);
    } finally {
      loadingSettings.close();
    }
  });

  // Get content data...
  useEffect(() => {
    if (!isOpen) {
      // clear data when modal is closed, otherwise it will show the previous data when open the modal again.
      setCachedData({
        [PrintCardType.ApptList]: [],
        [PrintCardType.BoardingDeparture]: [],
        [PrintCardType.BoardingArrival]: [],
        [PrintCardType.Stay]: [],
        [PrintCardType.Activity]: [],
        [PrintCardType.Appt]: [],
        [PrintCardType.Playgroup]: [],
      });
      return;
    }
    getData();
  }, [isOpen, groomingId, date, selectedCardType, careType, isPetsCheckedInOnly, serviceFilters]);

  // Get settings data...
  useEffect(() => {
    if (!isOpen) return;
    getSettings();
  }, [isOpen, selectedCardType]);

  return {
    loadingContent: loadingContent.value,
    loadingSettings: loadingSettings.value,
    loading: loadingContent.value || loadingSettings.value,
    data: cachedData[selectedCardType] || [],
  };
};
