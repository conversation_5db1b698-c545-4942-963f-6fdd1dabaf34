import { useDispatch, useSelector, useStore } from 'amos';
import { useContext } from 'react';
import { setBlockTimeAddDrawer } from '../../../../../../store/calendarLatest/actions/private/calendar.actions';
import {
  calendarMonthlyTicketDetailMapBox,
  calendarOnlineBookingModal,
} from '../../../../../../store/calendarLatest/calendar.boxes';
import { selectBusinessCalendarConfig } from '../../../../../../store/calendarLatest/calendar.selectors';
import { type BlockDrawerEditPreset, BookingType } from '../../../../../../store/calendarLatest/calendar.types';
import { apptEventCardMapBox } from '../../../../../../store/calendarLatest/card.boxes';
import { CalendarCardComponentType } from '../../../../../../store/calendarLatest/card.types';
import { useCloseAllDrawer } from '../../../../../../utils/hooks/useCloseAllDrawer';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useOpenApptDetailDrawer } from '../../../../../../utils/hooks/useOpenApptDetailDrawer';
import { selectTicketRepeatPreviewState } from '../../../../../Appt/components/RepeatSeries/store/repeatSeries.selectors';
import { useEventCardState } from '../../hooks/useEventCardState';
import { ApptEventCtx } from './ApptEventCtx';
import { useSSState } from './hooks/useSSState';
import { datadogRum } from '@datadog/browser-rum';
import { DataDogActionName } from '../../../../../../utils/logger';

export interface ApptEventClickProps {
  ticketId?: number;
  monthlyEventId?: string;
  children?: (_: { openTicketDetail: () => void }) => JSX.Element;
}

export const ApptEventClick = function CalendarEventClick(props: ApptEventClickProps) {
  const { children, ticketId, monthlyEventId } = props;
  const store = useStore();
  const dispatch = useDispatch();
  const { cardId } = useContext(ApptEventCtx);
  const [monthlyTicket] = useSelector(calendarMonthlyTicketDetailMapBox.mustGetItem(monthlyEventId ?? ''));
  const { appointmentId, isOBPending, cardType } = useEventCardState();
  const isMonthlyEvent = !!monthlyEventId;
  const isMonthlyOBPending = isMonthlyEvent && monthlyTicket.bookingType === BookingType.OnlineBooking;
  const isMonthlyBlock = isMonthlyEvent && monthlyTicket.isBlock;
  const isBlock = cardType === CalendarCardComponentType.Block;
  const { closeAllDrawer } = useCloseAllDrawer();
  const targetTicketId = ticketId ?? (isMonthlyEvent ? monthlyTicket.appointmentId : appointmentId);
  const isInSSState = useSSState();
  const { openApptDetailDrawer } = useOpenApptDetailDrawer();

  const openTicketDetail = useLatestCallback(() => {
    datadogRum.startDurationVital(DataDogActionName.OPEN_APPT_DRAWER);
    const { magicWaitListMode } = store.select(selectBusinessCalendarConfig);
    const { drawerVisible: repeatDrawerVisible } = store.select(selectTicketRepeatPreviewState);
    if (magicWaitListMode || repeatDrawerVisible) {
      return;
    }
    // 先清除各种抽屉弹窗
    closeAllDrawer();
    if (isInSSState) {
      return;
    }
    // ob 单独打开弹窗
    if (isOBPending || isMonthlyOBPending) {
      dispatch(
        calendarOnlineBookingModal.setState({
          visible: true,
          ticketId: targetTicketId,
        }),
      );
      return;
    }
    // block time类型的抽屉
    if (isBlock || isMonthlyBlock) {
      const preset: BlockDrawerEditPreset = isMonthlyBlock
        ? monthlyTicket.transformToBlockPreset()
        : store.select(apptEventCardMapBox.mustGetItem(cardId)).toJSON();
      dispatch(
        setBlockTimeAddDrawer({
          visibleAddBlockDrawer: true,
          preset,
        }),
      );
      return;
    }
    // 普通的预约打开详情抽屉
    // 注意这里之后要是有 evaluation 的话，要先判断一下再处理
    openApptDetailDrawer({ ticketId: targetTicketId, cardId });
  });

  return children?.({ openTicketDetail }) ?? null;
};
