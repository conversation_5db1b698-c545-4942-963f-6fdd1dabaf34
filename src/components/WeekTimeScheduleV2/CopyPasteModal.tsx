import { Checkbox, Modal } from '@moego/ui';
import React, { memo, useEffect, useState } from 'react';
import { dayOfWeeks } from '../../store/business/business.boxes';
import { type FullWeekDay, FullWeekDayList } from '../../store/onlineBooking/models/OnlineBookingPreference';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';

export interface CopyPasteModalProps {
  onApply?: (days: FullWeekDay[]) => void;
  day?: FullWeekDay;
  visible?: boolean;
  onClose: () => void;
}

export const CopyPasteModal = memo(function CopyPastModal(props: CopyPasteModalProps) {
  const { onClose, visible, day, onApply } = props;
  const [days, setDays] = useState(FullWeekDayList);
  useEffect(() => {
    if (visible) {
      setDays(FullWeekDayList);
    }
  }, [visible]);
  const isAllSelected = days.length === FullWeekDayList.length ? true : undefined;
  const isIndeterminate = days.length > 0 && days.length < FullWeekDayList.length;
  const handleConfirm = useLatestCallback(async () => {
    await onApply?.(days);
  });
  return (
    <Modal
      title={`Copy ${day}’s setting to`}
      onClose={onClose}
      confirmText="Apply"
      isOpen={visible}
      size="s"
      onConfirm={handleConfirm}
    >
      <div className="moe-flex moe-flex-col moe-gap-y-[16px]">
        <Checkbox
          isSelected={isAllSelected}
          isIndeterminate={isIndeterminate}
          onChange={(e) => {
            if (e || isIndeterminate) {
              setDays(FullWeekDayList);
            } else {
              setDays([day!]);
            }
          }}
        >
          All days
        </Checkbox>
        {dayOfWeeks.sundayFirst.map((d) => {
          const weekday = FullWeekDayList[d];
          const disabledCheckbox = day === weekday;
          return (
            <Checkbox
              key={weekday}
              isDisabled={disabledCheckbox}
              isSelected={days.includes(weekday)}
              onChange={() => {
                setDays(() => {
                  const index = days.indexOf(weekday);
                  const nextDays = days.slice();
                  if (index === -1) {
                    nextDays.push(weekday);
                    return nextDays;
                  }
                  nextDays.splice(index, 1);
                  return nextDays;
                });
              }}
            >
              {weekday}
            </Checkbox>
          );
        })}
      </div>
    </Modal>
  );
});
