import { type ActivityPrintCardInfo } from '../../../../store/printCard/activityCard/activityCard.actions';

export interface ActivityTableDataItem
  extends Pick<
    ActivityPrintCardInfo['medicationInstructions'][number],
    | 'avatarPath'
    | 'petTypeId'
    | 'petName'
    | 'clientFirstName'
    | 'clientLastName'
    | 'breed'
    | 'gender'
    | 'weight'
    | 'serviceName'
    // 排序会有依赖到
    | 'lodgingUnitId'
    | 'lodgingTypeId'
    | 'petCodeBindings'
  > {
  lodgingInfos?: ActivityPrintCardInfo['addOns'][number]['lodgingInfos'];
  lodgingInfo?: ActivityPrintCardInfo['medicationInstructions'][number]['lodgingInfo'];
  staffName?: ActivityPrintCardInfo['addOns'][number]['staffName'];
  time?: ActivityPrintCardInfo['medicationInstructions'][number]['time'];
  /** getFeedingIntro / getMedicationIntro 从 feedingInstructions / medicationInstructions 转换出来的字符串 */
  instruction: string[];
  ticketComments?: ActivityPrintCardInfo['addOns'][number]['ticketComments'];
}
