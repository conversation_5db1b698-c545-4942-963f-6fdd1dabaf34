import { useDispatch, useSelector } from 'amos';
import React, { useRef, useState } from 'react';
import { pick } from 'lodash';
import {
  EvaluationApptDetailStep,
  businessEvaluationMapBox,
  evaluationApptDetailDrawerBox,
} from '../../../../../store/evaluation/evaluation.boxes';
import { EvaluationAddNewPet } from '../../../components/Evaluation/EvaluationAddNewPet';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { type PetEvaluationInfoFormRef } from '../../QuickAddEvaluationApptDrawer/components/PetEvaluationInfoForm';
import { ID_ANONYMOUS } from '../../../../../store/utils/identifier';
import { setEvaluationApptDetailDrawerEdit } from '../../../../../store/evaluation/evaluation.actions';
import { updatePetService } from '../../../store/appt.api';
import { apptInfoMapBox } from '../../../store/appt.boxes';
import { useEvaluationTicketActions } from '../../../hooks/useEvaluationTicketActions';
import { toastApi } from '../../../../../components/Toast/Toast';
import { ApptValidOneDayMinutes } from '../../../store/appt.types';

export const EvaluationApptDetailAddNewPet: React.FC = () => {
  const [evaluationMap, { customerId, petIds: defaultPetIds, startDate, startTime, ticketId }] = useSelector(
    businessEvaluationMapBox,
    evaluationApptDetailDrawerBox,
  );
  const [ticket] = useSelector(apptInfoMapBox.mustGetItem(String(ticketId)));
  const { serviceDetail, appointmentStartTime } = ticket;
  const [petId, setPetId] = useState(ID_ANONYMOUS);
  const dispatch = useDispatch();
  const formRef = useRef<PetEvaluationInfoFormRef>(null);
  const targetEvaluationInfo = evaluationMap.mustGetItem(formRef.current?.getValues().serviceId);
  const { refreshTicket } = useEvaluationTicketActions();
  const jumpToStep = () => {
    dispatch(setEvaluationApptDetailDrawerEdit({ stepInfo: { type: EvaluationApptDetailStep.Overview } }));
  };

  const handleSubmit = useSerialCallback(async () => {
    const values = formRef?.current?.getValues();

    if (!values || !targetEvaluationInfo) return;

    const appointmentDetails = serviceDetail[0].evaluations[0];
    const currentServiceEndTime = appointmentStartTime + (targetEvaluationInfo.duration || 0);
    dispatch(
      updatePetService({
        appointmentId: String(ticketId),
        petDetails: serviceDetail
          .map((item) => {
            return {
              petId: item.pet.id.toString(),
              services: [],
              addOns: [],
              ...pick(item, ['evaluations']),
            };
          })
          .concat({
            petId: petId?.toString(),
            services: [],
            addOns: [],
            evaluations: [
              {
                ...appointmentDetails,
                appointmentId: String(ticketId),
                id: targetEvaluationInfo.id!,
                servicePrice: +(targetEvaluationInfo.price || 0),
                serviceTime: +(targetEvaluationInfo.duration || 0),
                serviceName: targetEvaluationInfo.name!,
                petId: petId.toString(),
                staffId: values.staffId,
                lodgingId: values.lodgingId,
                serviceId: values.serviceId,
                endTime:
                  currentServiceEndTime >= ApptValidOneDayMinutes ? ApptValidOneDayMinutes : currentServiceEndTime,
              },
            ],
          }),
      }),
    );
    await refreshTicket();
    toastApi.success('Updated successfully!');
    jumpToStep();
  });

  const handleCancel = useSerialCallback(() => {
    jumpToStep();
  });

  return (
    <EvaluationAddNewPet
      startDate={startDate}
      startTime={startTime}
      petIds={defaultPetIds}
      customerId={customerId.toString()}
      onSubmit={handleSubmit}
      onCancel={handleCancel}
      ref={formRef}
      petId={petId}
      setPetId={setPetId}
    />
  );
};
