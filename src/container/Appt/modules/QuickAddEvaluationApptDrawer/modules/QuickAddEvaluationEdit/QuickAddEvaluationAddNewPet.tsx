import { useSelector } from 'amos';
import React, { useRef, useState } from 'react';
import { businessEvaluationMapBox } from '../../../../../../store/evaluation/evaluation.boxes';
import { EvaluationQuickAddStep } from '../../../../../../store/evaluation/evaluation.types';
import { ID_ANONYMOUS } from '../../../../../../store/utils/identifier';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { useEvaluationQuickAdd } from '../../hooks/useEvaluationQuickAdd';
import { type PetEvaluationInfoFormRef } from '../../components/PetEvaluationInfoForm';
import { EvaluationAddNewPet } from '../../../../components/Evaluation/EvaluationAddNewPet';
import { isNumber } from 'lodash';

export const QuickAddEvaluationAddNewPet: React.FC = () => {
  const { customerId, evaluationInfo, petIds: defaultPetIds, setQuickAddFields, jumpToStep } = useEvaluationQuickAdd();
  const [evaluationMap] = useSelector(businessEvaluationMapBox);
  const [petId, setPetId] = useState(ID_ANONYMOUS);
  const formRef = useRef<PetEvaluationInfoFormRef>(null);
  const targetEvaluationInfo = evaluationMap.mustGetItem(formRef.current?.getValues().serviceId);

  const handleSubmit = useSerialCallback(async () => {
    const values = formRef?.current?.getValues();

    if (!values) return;

    setQuickAddFields({
      petIds: [...defaultPetIds, petId],
      evaluationInfo: {
        ...evaluationInfo,
        petServiceList: [
          ...evaluationInfo.petServiceList,
          {
            petId: petId.toString(),
            lodgingId: values.lodgingId,
            serviceId: values.serviceId,
            staffId: values.staffId,
            serviceTime: targetEvaluationInfo.duration || 0,
            servicePrice: targetEvaluationInfo.price || 0,
          },
        ],
      },
    });
    jumpToStep(EvaluationQuickAddStep.AfterSelectPetAndEvaluation);
  });

  const handleCancel = useSerialCallback(() => {
    jumpToStep(EvaluationQuickAddStep.AfterSelectPetAndEvaluation);
  });

  if (!evaluationInfo.startDate || !isNumber(evaluationInfo.startTime)) {
    return null;
  }

  return (
    <EvaluationAddNewPet
      startDate={evaluationInfo.startDate}
      startTime={evaluationInfo.startTime}
      petIds={defaultPetIds}
      customerId={customerId.toString()}
      onSubmit={handleSubmit}
      onCancel={handleCancel}
      ref={formRef}
      petId={petId}
      setPetId={setPetId}
    />
  );
};
