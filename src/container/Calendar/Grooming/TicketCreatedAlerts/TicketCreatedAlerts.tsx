import { AppointmentPaymentStatus } from '@moego/api-web/moego/models/appointment/v1/appointment_enums';
import { OrderStatus } from '@moego/api-web/moego/models/order/v1/order_enums';
import { useDispatch, useStore } from 'amos';
import { noop } from 'monofile-utilities/lib/consts';
import React, { memo, useEffect, useRef, useState } from 'react';
import {
  getAppointmentAutoMessageList,
  getPaymentAutoMessageList,
} from '../../../../store/autoMessage/autoMessage.actions';
import { type AutoMessageTypeValue, AutoMessageUseCase2TypeMap } from '../../../../store/autoMessage/autoMessage.boxes';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import { useBusinessIsWorkingLocation } from '../../../../utils/BusinessUtil';
import { useBool } from '../../../../utils/hooks/useBool';
import { useOpenApptDetailDrawer } from '../../../../utils/hooks/useOpenApptDetailDrawer';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { useAgreement } from '../../../Appt/modules/ApptDetailDrawer/hooks/useAgreement';
import { getAppointment } from '../../../Appt/store/appt.api';
import { apptInfoMapBox } from '../../../Appt/store/appt.boxes';
import { selectDepositInfo } from '../../../Appt/store/selectors/appt';
import { usePreviewDepositOrderAmountByRules } from '../../../PaymentFlow/CartMixinOrderDrawer/SubpageLeft/DepositSubpage';
import { useInvoiceReinvent } from '../../../PaymentFlow/hooks/useInvoiceReinvent';
import { useTakePayment } from '../../../PaymentFlow/TakePaymentDrawer/useTakePayment';
import { AppointmentStatus } from '../../../TicketDetail/AppointmentStatus';
import { MessageAlert } from './MessageAlert';
import { OrderModelOrderType } from '@moego/api-web/moego/models/order/v1/order_models';

export interface TicketCreatedAlertsProps {
  ticketId: number;
  customerId: number;
  mode: AutoMessageTypeValue;
  isRepeat?: boolean;
  onClose?: () => void;
}

export interface TicketCreatedAlertsState {
  resolve: () => void;
  reject: () => void;
  alertType: 'message' | 'agreement' | null;
}

export interface AlertProps extends Omit<TicketCreatedAlertsProps, 'onClose'> {
  onCancel: () => void;
  onSubmit: () => void;
  visible: boolean;
}

const _ALERT_SERIAL_LIST = ['message', 'agreement', 'preAuthFailed', ''] as const;
type AlertType = (typeof _ALERT_SERIAL_LIST)[number];

/**
 * autoMessage alert -> agreement alert -> sign agreement list -> preAuthFailed
 */
export const TicketCreatedAlerts = memo<TicketCreatedAlertsProps>((props) => {
  const { ticketId, customerId, mode, onClose: onCloseProp = noop } = props;
  const dispatch = useDispatch();
  const store = useStore();

  const showModal = useRef<boolean>(false);
  const [alertType, setAlertType] = useState<AlertType>('');
  const { showAgreementAlertIfNeed } = useAgreement({ mode });
  const isCancelLoading = useBool();
  const { requestTakeDeposit } = useTakePayment();
  const { isNewOrderV4Flow } = useInvoiceReinvent();

  const previewDeposit = usePreviewDepositOrderAmountByRules();
  const { openApptDetailDrawer } = useOpenApptDetailDrawer();
  const isWorkingLocation = useBusinessIsWorkingLocation();

  const previewDepositAndTakeDeposit = useSerialCallback(async () => {
    if (!isNewOrderV4Flow) return;

    await dispatch(getAppointment({ appointmentId: ticketId.toString() }));
    const permissions = store.select(selectCurrentPermissions);
    const apptInfo = store.select(apptInfoMapBox.mustGetItem(String(ticketId)));
    const depositInfo = store.select(selectDepositInfo(String(ticketId)));
    const isUnpaid = apptInfo.appointment.isPaid === AppointmentPaymentStatus.UNPAID;
    const hasDepositRequested = !!depositInfo;
    const depositPaid = hasDepositRequested && depositInfo.status === 2;
    const disabledStatus = !permissions.has('canProcessPayment') || !isWorkingLocation;
    const hasOrders = !!apptInfo.orders?.filter(
      (o) => o.status !== OrderStatus.REMOVED && o.orderType !== OrderModelOrderType.DEPOSIT,
    ).length;
    const isAfterConfirmed = apptInfo.appointment.status > AppointmentStatus.CONFIRMED;
    const disabledDeposit = !isUnpaid || depositPaid || disabledStatus || isAfterConfirmed || hasOrders;

    if (disabledDeposit) {
      return;
    }

    const rulesAmount = (await previewDeposit(ticketId.toString())) ?? 0;
    if (rulesAmount > 0) {
      requestTakeDeposit({
        invoiceId: undefined as unknown as number,
        appointmentId: ticketId,
        module: 'grooming',
        onClose: () => {
          openApptDetailDrawer({ ticketId });
        },
      });
    }
  });

  const onClose = () => {
    setAlertType('');
    onCloseProp();
    isCancelLoading.close();
  };
  const onBeforeOpenAgreement = () => {
    setAlertType('');
    isCancelLoading.close();
  };

  const showMessageAlertIfNeed = useSerialCallback(async () => {
    showModal.current = false;

    try {
      let autoMessageStatus = false;

      const [paymentData, apptData] = await Promise.all([
        dispatch(getPaymentAutoMessageList()),
        dispatch(getAppointmentAutoMessageList()),
      ]);
      const autoMessage = [...paymentData, ...apptData].find(
        (message) => AutoMessageUseCase2TypeMap[message.useCase] === mode,
      );
      autoMessageStatus = !!autoMessage?.isEnabled;

      if (autoMessageStatus) {
        showModal.current = true;
        setAlertType('message');
      }
    } finally {
      if (!showModal.current) {
        await showAgreementAlertIfNeed({ customerId, ticketId, onClose, onBeforeOpenAgreement });
        await previewDepositAndTakeDeposit();
      }
    }
  });

  useEffect(() => {
    showMessageAlertIfNeed();
  }, []);

  return (
    <>
      {alertType === 'message' && (
        <MessageAlert
          {...props}
          isCancelLoading={isCancelLoading.value}
          onCancel={async () => {
            isCancelLoading.open();
            await showAgreementAlertIfNeed({ customerId, ticketId, onClose, onBeforeOpenAgreement });
            await previewDepositAndTakeDeposit();
          }}
          onSubmit={async () => {
            await showAgreementAlertIfNeed({ customerId, ticketId, onClose, onBeforeOpenAgreement });
            await previewDepositAndTakeDeposit();
          }}
          visible
        />
      )}
    </>
  );
});
