import { Divider } from 'antd';
import React, { memo, useState } from 'react';
import { Avatar } from '../../../../components/Avatar/Avatar';
import { getPetAvatarType } from '../../../../utils/BusinessUtil';
import { OnlineBookingRequestImgPreviewModal } from '../../modules/OnlineBookingRequests/components/OnlineBookingRequestModal/OnlineBookingRequestModal.style';
import { type InfoItem } from './BookingInfo';
import { useIsShowItem } from './BookingRequestModal.hooks';

export interface PetInfoProps {
  petInfoList: InfoItem[][];
}

export const PetInfo = memo<PetInfoProps>(({ petInfoList }) => {
  const [avatarModal, setAvatarModal] = useState({
    visible: false,
    avatarPath: '',
    petTypeId: 0,
  });

  const isShowItem = useIsShowItem();

  const renderPetItemInfoList = (petItemInfoList: InfoItem[]) => {
    return petItemInfoList.map((petItem) => {
      const { className = '', label, value, valueClassName = '', isCustom } = petItem;
      return isShowItem(petItem) ? (
        <div
          key={label}
          className={`${className} !moe-flex !moe-flex-wrap !moe-items-start !moe-pb-[12px] !moe-text-[14px] !moe-leading-[19px] !moe-text-[#2a2d34]/60`}
        >
          <span className="!moe-flex-shrink-0 !moe-min-w-[124px]">{label}</span>
          <span
            className={`!moe-flex-1 !moe-text-[#333] !moe-whitespace-pre-line ${
              isCustom ? '!moe-min-w-full' : '!moe-min-w-[calc(100%-124px)]'
            } ${valueClassName}`}
          >
            {typeof value === 'function' ? value(setAvatarModal) : value}
          </span>
        </div>
      ) : null;
    });
  };

  return (
    <div>
      <OnlineBookingRequestImgPreviewModal
        visible={avatarModal.visible}
        onClose={() => setAvatarModal({ ...avatarModal, ['visible']: false })}
        width="500px"
        className="modal"
      >
        <Avatar
          src={avatarModal.avatarPath}
          type={getPetAvatarType(avatarModal.petTypeId)}
          size="400px"
          className="modal-avatar modal-preview-img"
        />
      </OnlineBookingRequestImgPreviewModal>

      {petInfoList.map((petItemInfoList, index) => (
        <div key={index}>
          <Divider dashed className="!moe-my-[16px]" />
          {renderPetItemInfoList(petItemInfoList)}
        </div>
      ))}
    </div>
  );
});
