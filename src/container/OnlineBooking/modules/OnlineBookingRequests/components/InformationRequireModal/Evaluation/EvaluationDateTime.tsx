import { Markup, Text } from '@moego/ui';
import React, { FC, memo } from 'react';
import dayjs from 'dayjs';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import { useSelector } from 'amos';
import { T_MINUTE } from 'monofile-utilities/lib/consts';

interface EvaluationDateTimeProps {
  evaluationName: string;
  startDate: string;
  startTime: number;
  endTime: number;
}

export const EvaluationDateTime: FC<EvaluationDateTimeProps> = memo((props) => {
  const { evaluationName, startDate, startTime, endTime } = props;
  const [business] = useSelector(selectCurrentBusiness);
  return (
    <div className="moe-text-primary">
      <Markup as="span" variant="small">
        {evaluationName}:&nbsp;
      </Markup>
      <Text as="span" variant="small">
        {dayjs(startDate).format(business.dateFormatMD)},&nbsp;
        {business.formatFixedTime(startTime * T_MINUTE)}
        &nbsp;-&nbsp;{business.formatFixedTime(endTime * T_MINUTE)}
      </Text>
    </div>
  );
});
