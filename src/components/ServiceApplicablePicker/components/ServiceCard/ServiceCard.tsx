import { cn } from '@moego/ui';
import React, { memo } from 'react';
import { ServiceColorBar } from './ServiceColorBar';

export interface ServiceCardProps {
  className?: string;
  childClassName?: string;
  disabled?: boolean;
  children: React.ReactNode | (() => React.ReactNode);
}

export const ServiceCard = memo((props: ServiceCardProps) => {
  const { className, children, childClassName, disabled } = props;
  const color = disabled ? '#E6E6E6' : '#A9EBBC';

  return (
    <div className={cn('moe-flex moe-gap-x-[12px]', className)}>
      <ServiceColorBar color={color} />
      <div className={cn('moe-flex-1 moe-min-w-0 moe-items-stretch', childClassName)}>
        {typeof children === 'function' ? children() : children}
      </div>
    </div>
  );
});
