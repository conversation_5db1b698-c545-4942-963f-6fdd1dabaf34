import { cn } from '@moego/ui';
import React, { memo } from 'react';
import { type DiscountRecord } from '../../../../../store/discount/discount.boxes';
import { useDiscountFactorDescription } from '../../hooks/useDiscountFactorDescription';

export interface DiscountDescProps {
  discount: DiscountRecord;
  className?: string;
}

export const DiscountDesc = memo<DiscountDescProps>(({ discount, className }) => {
  const getDiscountFactorDescription = useDiscountFactorDescription();
  const desc = getDiscountFactorDescription(discount);

  return <span className={cn('moe-text-[#999]', className)}>{desc}</span>;
});
