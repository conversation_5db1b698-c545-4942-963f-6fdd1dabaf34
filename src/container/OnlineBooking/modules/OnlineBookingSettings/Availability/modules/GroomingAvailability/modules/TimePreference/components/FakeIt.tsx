import { LegacySelect as Select } from '@moego/ui';
import { useDispatch } from 'amos';
import React, { memo } from 'react';
import { Condition } from '../../../../../../../../../../components/Condition';
import { OBTestIds } from '../../../../../../../../../../config/testIds/onlineBooking';
import { setAvailabilityDraftSettings } from '../../../../../../../../../../store/onlineBooking/actions/private/availabilityGrooming.actions';
import { FakeItFilterKind } from '../../../../../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { DraftAvailableSlotRecord } from '../../../../../../../../../../store/onlineBooking/settings/availabilityGrooming.boxes';
import { TimePreferenceSettingItem } from './TimePreferenceSettingItem';

export const FakeIt = memo<{ draftSettings: DraftAvailableSlotRecord }>((props) => {
  const { draftSettings } = props;
  const dispatch = useDispatch();
  const { isByWorkingHour } = draftSettings;
  const handleFakeItChange = (value: number) => {
    dispatch(setAvailabilityDraftSettings({ ['fakeIt']: value }));
  };

  return (
    <Condition if={isByWorkingHour}>
      <TimePreferenceSettingItem label="Fake it filter">
        <Select
          className="moe-w-[352px]"
          value={draftSettings.fakeIt}
          options={FakeItFilterKind.values.map((k) => ({
            description: FakeItFilterKind.mapLabels[k].label,
            label: FakeItFilterKind.mapLabels[k].name,
            value: k,
          }))}
          isSearchable={false}
          onChange={(e) => handleFakeItChange(e as number)}
          data-testid={OBTestIds.SettingFakeItSelect}
        />
      </TimePreferenceSettingItem>
    </Condition>
  );
});
