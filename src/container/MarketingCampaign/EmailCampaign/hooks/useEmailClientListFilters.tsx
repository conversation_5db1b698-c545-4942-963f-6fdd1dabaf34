import { useSelector } from 'amos';
import { useEffect, useState } from 'react';
import { ComMoegoCommonParamsFilterParamsProperty as FilterParamsProperty } from '../../../../openApi/customer-schema';
import { PATH_MARKETING_CAMPAIGN_PANEL } from '../../../../router/paths';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { selectChosenCustomers } from '../../../../store/customer/chosenCustomers.selectors';
import { type AddFilterModel, ClientFilterListSourceMap } from '../../../../store/customer/clientFilters.boxes';
import { selectBusinessCustomers } from '../../../../store/customer/customer.selectors';
import { emailCampaignTemplateFilters } from '../../../../store/marketingCampaign/emailCampaign.boxes';
import { isNormal } from '../../../../store/utils/identifier';
import { useRouteQuery } from '../../../../utils/RoutePath';
import {
  ClientStatusOptions,
  OperatorMap,
} from '../../../Client/ClientList/componentsLegacy/ClientFilters/ClientFilters.config';

const { MassEmail } = ClientFilterListSourceMap;

const defaultFilters: AddFilterModel[] = [
  {
    source: MassEmail,
    property: FilterParamsProperty.client_status,
    operator: OperatorMap.EqualTo,
    value: ClientStatusOptions[0].value,
  },
  {
    source: MassEmail,
    property: FilterParamsProperty.unsubscribed_marketing_emails,
    operator: OperatorMap.EqualTo,
    value: 0,
  },
  {
    source: MassEmail,
    property: FilterParamsProperty.email_cnt,
    operator: OperatorMap.MoreThan,
    value: 0,
  },
];

// 这里可以理解为取默认 filter 的逻辑，后续 filter 更新不应该影响这里
export const useEmailClientListFilters = (templateId?: string, customerType?: 'lead' | 'client') => {
  const query = useRouteQuery(PATH_MARKETING_CAMPAIGN_PANEL);
  const hasFilter = !!query.hasFilter;
  const [{ chosenList, unchosenList, isAll }] = useSelector(selectChosenCustomers(MassEmail));
  // 如果是来自 lead 移除 client_status 为 active 的 filter
  const defaultEmailFilters =
    customerType === 'lead'
      ? defaultFilters.filter((filter) => filter.property !== FilterParamsProperty.client_status)
      : defaultFilters;

  const [filters, setFilters] = useState(defaultEmailFilters);
  const templateFilters = useSpecificEmailClientListFilters(templateId);

  // 如果是非 lead 白名单，且没有 customerType，默认加上 customer_type = CUSTOMER 的 filter
  // 这里容易产生 bug，因为 enableLeadManagement 是后生效的，但影响也还好，最差就是加了个默认的 customer_type = CUSTOMER 的 filter
  // useEffect(() => {
  //   if (!enableLeadManagement && !customerType) {
  //     setFilters([
  //       ...filters,
  //       {
  //         ...customerInternalFilter,
  //         source: MassEmail,
  //       },
  //     ]);
  //   }
  // }, [enableLeadManagement]);

  useEffect(() => {
    if (hasFilter) {
      // care about unchosenList
      if (isAll && unchosenList.length > 0) {
        const extraFilter: AddFilterModel = {
          source: MassEmail,
          property: FilterParamsProperty.client_id,
          operator: OperatorMap.NotIn,
          values: unchosenList,
        };
        setFilters([...defaultEmailFilters, extraFilter]);
      } else if (!isAll && chosenList.length > 0) {
        // care about chosenList
        const extraFilter: AddFilterModel = {
          source: MassEmail,
          property: FilterParamsProperty.client_id,
          operator: OperatorMap.In,
          values: chosenList,
        };
        setFilters([...defaultEmailFilters, extraFilter]);
      }
    } else {
      // if not came from smart client list page, use default & template filters
      setFilters([...defaultEmailFilters, ...templateFilters]);
    }
  }, [hasFilter, chosenList, unchosenList, isAll, templateFilters, templateId]);
  return {
    filters,
    hasExtraFilter: filters.some(
      (filter) => filter.property === FilterParamsProperty.client_id && filter.values?.length,
    ),
  };
};

export const useSpecificEmailClientListFilters = (templateId?: string) => {
  const [filters, setFilters] = useState<AddFilterModel[]>([]);
  const [business, templateFilters] = useSelector(selectCurrentBusiness, emailCampaignTemplateFilters);

  useEffect(() => {
    if (isNormal(business.id)) {
      setFilters(templateFilters.filters);
    }
  }, [templateId, business, templateFilters]);
  return filters;
};

export const useEmailClientListKeyword = () => {
  const query = useRouteQuery(PATH_MARKETING_CAMPAIGN_PANEL);
  const hasFilter = !!query.hasFilter;
  const [customerList] = useSelector(selectBusinessCustomers(ClientFilterListSourceMap.MassEmail));

  return hasFilter ? (customerList.filter.queries?.keyword ?? '') : '';
};
