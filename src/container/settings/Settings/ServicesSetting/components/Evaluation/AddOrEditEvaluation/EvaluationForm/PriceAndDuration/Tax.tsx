import { Form, LegacySelect as Select } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { taxMapBox } from '../../../../../../../../../store/business/tax.boxes';
import { selectBusinessTaxes } from '../../../../../../../../../store/business/tax.selectors';
import { useServiceStaffPermissions } from '../../../../AddOrEditComponents/hooks/useServiceStaffPermissions';
import { useEvaluationFormContext } from '../EvaluationFormContext';

export interface TaxProps {}

export const Tax = memo<TaxProps>(() => {
  const { isEdit } = useEvaluationFormContext();
  const { isDisabledForPriceAndTax } = useServiceStaffPermissions(isEdit);
  const [taxIdList, taxMap] = useSelector(selectBusinessTaxes, taxMapBox);

  const taxOptionList = useMemo(() => {
    return taxIdList.toArray().map((id) => {
      const tax = taxMap.mustGetItem(id);
      return {
        label: `${tax.taxName} (${tax.taxRate}%)`,
        value: String(tax.id),
      };
    });
  }, [taxIdList, taxMap]);

  return (
    <Form.Item name="taxId" label="Tax">
      <Select
        isDisabled={isDisabledForPriceAndTax}
        options={taxOptionList}
        formatOptionLabel={(opt) => opt?.label || ''}
        placeholder="Select tax rate"
      />
    </Form.Item>
  );
});
