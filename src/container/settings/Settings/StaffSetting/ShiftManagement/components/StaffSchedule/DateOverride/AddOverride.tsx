import { MinorPlusOutlined } from '@moego/icons-react';
import { Button } from '@moego/ui';
import { useSelector } from 'amos';
import React from 'react';
import { editingWorkingHourStaffIdBox } from '../../../../../../../../store/staffSchedule/staffSchedule.boxes';
import { useStaffScheduleDrawerContext } from '../StaffScheduleDrawerContext';
import type { EnumValues } from '../../../../../../../../store/utils/createEnum';
import { AvailabilityType } from '../../../../../../../../store/staffSchedule/staffSchedule.types';

export interface AddOverrideProps {
  className?: string;
  businessId: number;
  availabilityType: EnumValues<typeof AvailabilityType>;
}

export function AddOverride(props: AddOverrideProps) {
  const { className, businessId, availabilityType } = props;
  const [editingStaffId] = useSelector(editingWorkingHourStaffIdBox);
  const { openAddOverrideDrawer, openAddOverrideDrawerBySlot } = useStaffScheduleDrawerContext();

  const isByTime = availabilityType === AvailabilityType.BY_TIME;

  return (
    <>
      <Button
        className={className}
        variant="tertiary-legacy"
        icon={<MinorPlusOutlined />}
        onPress={() => {
          if (isByTime) {
            openAddOverrideDrawer({
              staffId: editingStaffId,
              businessId,
            });
          } else {
            openAddOverrideDrawerBySlot({
              staffId: editingStaffId,
              businessId,
            });
          }
        }}
      >
        Add date override
      </Button>
    </>
  );
}
