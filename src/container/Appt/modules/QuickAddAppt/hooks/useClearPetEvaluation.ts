import { useDispatch, useStore } from 'amos';
import { apptPetMapBox } from '../../../store/appt.boxes';
import { CreateApptId } from '../../../store/appt.types';
import { selectApptPetInfo } from '../../../store/appt.selectors';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';

export const useClearPetEvaluation = (appointmentId = CreateApptId) => {
  const dispatch = useDispatch();
  const store = useStore();

  return useLatestCallback((petId: string) => {
    const petInfo = store.select(selectApptPetInfo(appointmentId)).toJSON();

    dispatch(
      apptPetMapBox.mergeItem(CreateApptId, {
        pets: petInfo.pets.map((pet) => ({
          ...pet,
          evaluations: pet.petId !== petId ? pet.evaluations : [],
        })),
      }),
    );
  });
};
