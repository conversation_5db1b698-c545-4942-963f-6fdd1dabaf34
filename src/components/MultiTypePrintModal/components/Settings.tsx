import { Heading, LegacySelect as Select, Spin } from '@moego/ui';
import React from 'react';
import { type EnumValues } from '../../../store/utils/createEnum';
import { ReportActionName } from '../../../utils/reportType';
import { reportData } from '../../../utils/tracker';
import { Condition } from '../../Condition';
import { Switch } from '../../SwitchCase';
import { PrintCardType } from '../MultiTypePrintModal.options';
import { ActivityPrintCardSettings } from './ActivityCard/ActivityPrintCardSetting';
import { ApptPrintCardSettings } from './ApptCard/ApptPrintCardSettings';
import { AppointmentListSettings } from './ApptListCard/AppointmentListSettings';
import { BoardingArrivalSettings } from './BoardingArrivalCard/BoardingArrivalSettings';
import { BoardingDepartureSettings } from './BoardingDepartureCard/BoardingDepartureSettings';
import { PlaygroupPrintCardSettings } from './PlaygroupCard/PlaygroupPrintCardSettings';
import { StayPrintCardSettings } from './StayCard/StayPrintCardSettings';

export const Settings = (props: {
  cardTypeOptions?: EnumValues<typeof PrintCardType>[];
  onChange: (value: EnumValues<typeof PrintCardType>) => void;
  cardType: EnumValues<typeof PrintCardType>;
  isLoading?: boolean;
}) => {
  const { cardTypeOptions = PrintCardType.values, isLoading, onChange, cardType } = props;

  const renderSpecialSettings = () => {
    switch (cardType) {
      case PrintCardType.ApptList:
        return <AppointmentListSettings />;
      case PrintCardType.BoardingArrival:
        return <BoardingArrivalSettings />;
      case PrintCardType.BoardingDeparture:
        return <BoardingDepartureSettings />;
      case PrintCardType.Stay:
        return <StayPrintCardSettings />;
      case PrintCardType.Activity:
        return <ActivityPrintCardSettings />;
      case PrintCardType.Appt:
        return <ApptPrintCardSettings />;
      case PrintCardType.Playgroup:
        return <PlaygroupPrintCardSettings />;
      default:
        return null;
    }
  };
  return (
    <div className="moe-w-[430px]">
      <Condition if={cardTypeOptions.length !== 1}>
        <div className="moe-border-b-2 moe-border-b-divider moe-border-solid">
          <Select
            isDisabled={isLoading}
            label={<Heading size="4">Card type</Heading>}
            value={cardType}
            options={cardTypeOptions.map((value) => ({ label: PrintCardType.mapLabels[value], value }))}
            onChange={(value) => {
              if (value) onChange(value);
              reportData(ReportActionName.printCardTypeChange, { cardType: PrintCardType.mapLabels[value] });
            }}
            classNames={{
              formItemWrapper: 'moe-m-m',
              formItemLabel: 'moe-mb-s',
            }}
          />
        </div>
      </Condition>
      <div>
        <Switch shortCircuit>
          <Switch.Case if={isLoading}>
            <div className="moe-flex moe-justify-center moe-mt-xl">
              <Spin isLoading />
            </div>
          </Switch.Case>
          <Switch.Case else>{renderSpecialSettings()}</Switch.Case>
        </Switch>
      </div>
    </div>
  );
};
