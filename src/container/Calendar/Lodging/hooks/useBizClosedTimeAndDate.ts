import { type SlotLaneContentArg } from '@fullcalendar/common';
import { isNormal } from '@moego/finance-utils';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import { useCallback, useEffect } from 'react';
import { currentBusinessIdBox } from '../../../../store/business/business.boxes';
import { getClosedDateList } from '../../../../store/business/closedDate.actions';
import { getHolidays } from '../../../../store/business/holiday.actions';
import { type WeekTimeValueLegacy } from '../../../../store/staffSchedule/staffSchedule.types';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { type ClosedRecord, useClosedDate } from '../../../settings/Settings/BusinessSetting/hooks/useClosedDate';
import { transformTimeDateToDate } from './useBizSchedule';

export function useBizClosedTimeAndDate(params: { timeData: WeekTimeValueLegacy }) {
  const { timeData } = params;
  const dispatch = useDispatch();

  const [businessId] = useSelector(currentBusinessIdBox);
  const dateList: ClosedRecord[] = useClosedDate(businessId, false);

  const handleGetData = useSerialCallback(() => {
    dispatch([getClosedDateList(businessId), getHolidays(businessId)]);
  });

  const getClosedData = useCallback(
    (event: SlotLaneContentArg) => {
      const { dow, isToday, date } = event;

      if (!dow && !date) {
        return {
          showCloseBg: false,
          showCloseDate: false,
        };
      }

      // Business hours 里不营业的时间
      const bizClosedHoursDate = transformTimeDateToDate(timeData)[dow || date!.getDay()];
      // 商家的节假日关门日期
      const isBizHolidayDate = dateList.some((item) =>
        item.dateRange ? dayjs(item.dateRange).isSame(dayjs(date), 'date') : false,
      );

      // close 背景阴影展示条件
      // 1. 不在营业时间
      // 2. 商家设置的节假日
      const showCloseBg = !bizClosedHoursDate.length || isBizHolidayDate;
      // close 日期灰色展示条件
      // 1. 不在营业时间
      // 2. 商家设置的节假日
      // 3. 不是今天
      const showCloseDate = showCloseBg && !isToday;

      return {
        showCloseBg,
        showCloseDate,
      };
    },
    [dateList, timeData],
  );

  useEffect(() => {
    if (isNormal(businessId)) {
      handleGetData();
    }
  }, [businessId]);

  return {
    getClosedData,
  };
}
