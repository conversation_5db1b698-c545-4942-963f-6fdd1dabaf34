import React, { memo } from 'react';
import { FormSection } from '../../../../AddOrEditComponents/FormSection/FormSection';
import { ServiceFormSections } from '../../../../AddOrEditComponents/FormSection/formSectionConfig';
import { ColorCode } from './ColorCode';
import { Description } from './Description';
import { EvaluationPeriod } from './EvaluationPeriod';
import { OnlineBookingServiceName } from './OnlineBookingServiceName';
import { ServiceName } from './ServiceName';
import { Status } from './Status';

export const BasicInfo = memo(() => {
  return (
    <FormSection section={ServiceFormSections.BasicInfo}>
      <ServiceName />
      <OnlineBookingServiceName />
      <Description />
      <EvaluationPeriod />
      <Status />
      <ColorCode />
    </FormSection>
  );
});
