import { Button } from '@moego/ui';
import React from 'react';
import { CommentNoteType } from '../../../../../../store/calendarLatest/calendar.utils';
import {
  CreateEvaluationNavigationTabs,
  EvaluationQuickAddStep,
  getDefaultEvaluationInfo,
} from '../../../../../../store/evaluation/evaluation.types';
import { toNumber } from '../../../../../../store/utils/identifier';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useAutoSelectSinglePet } from '../../hooks/useAutoSelectSinglePet';
import { useEvaluationQuickAdd } from '../../hooks/useEvaluationQuickAdd';
import { EvaluationPetInfoCard } from './EvaluationPetInfoCard';
import { MajorPlusOutlined } from '@moego/icons-react';
import { useAddNewPet } from '../../../../hooks/useAddNewPet';

export const EvaluationPetInfoCardList = () => {
  const { evaluationInfo, setQuickAddFields, jumpToStep, customerId, petIds = [] } = useEvaluationQuickAdd();
  const addNewPet = useAddNewPet(customerId, petIds);

  const handleEditEvaluationInfo = useLatestCallback((petId: number) => {
    setQuickAddFields({
      payload: {
        petId,
      },
    });
    jumpToStep(EvaluationQuickAddStep.EditPetEvaluation);
  });

  const addNewPetServices = async () => {
    await addNewPet({
      onAddPet: () => {
        jumpToStep(EvaluationQuickAddStep.AddNewPet);
      },
    });
  };

  const handleDeleteEvaluationInfo = useLatestCallback((petId: number) => {
    const petServiceList = evaluationInfo.petServiceList;

    if (petServiceList.length <= 1) {
      setQuickAddFields({
        ...evaluationInfo,
        petIds: [],
        evaluationInfo: {
          ...getDefaultEvaluationInfo(),
        },
        stepInfo: {
          type: EvaluationQuickAddStep.SelectPetMultipleMode,
        },
      });
    }

    setQuickAddFields({
      evaluationInfo: {
        ...evaluationInfo,
        petServiceList: petServiceList.filter((item) => item.petId !== petId.toString()),
      },
      petIds: petIds.filter((id) => id !== petId),
    });
  });

  useAutoSelectSinglePet();

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-[12px]">
      {evaluationInfo.petServiceList.map((item) => (
        <EvaluationPetInfoCard
          key={item.petId}
          petId={toNumber(item.petId)}
          evaluationInfo={item}
          startTime={evaluationInfo.startTime}
          startDate={evaluationInfo.startDate}
          endDate={evaluationInfo.endDate}
          endTime={evaluationInfo.endTime}
          onEdit={handleEditEvaluationInfo}
          onDelete={handleDeleteEvaluationInfo}
          onGo2PetNotes={() => {
            setQuickAddFields({
              activeTab: CreateEvaluationNavigationTabs.TICKET_COMMENT,
              ticketCommentsActiveTab: CommentNoteType.PetNotes,
              petNotesDefaultPetId: toNumber(item.petId),
            });
          }}
        />
      ))}
      <div className="moe-flex moe-justify-start">
        <Button variant="tertiary" align="start" icon={<MajorPlusOutlined />} onPress={addNewPetServices}>
          Add pet
        </Button>
      </div>
    </div>
  );
};
