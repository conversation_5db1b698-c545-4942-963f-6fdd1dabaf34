# ⚠️ Notes

为了兼顾开发体验与跨项目（npm 包）做 [API 层数据共享](https://tkdodo.eu/blog/react-query-data-transformations)，因此期望做以下简单约定：

- 使用 `select` 替代在 `queryFn` 里做 Response 数据转换
- 不随意改动 `queryKey`，除非你明确该 key 没被其他项目使用

# Amos => Query

## Concept Replacement

- boxKey => queryKey
- action => queryOption
- dispatch => query
- select(apptBox) => select(data => new ApptRecord(data))
- CURD: refresh => invalidateQueries

## Example

### Amos

```ts
// box.ts
export const stripePaymentMethodMapBox = createRecordMapBox(
  'payment/stripe/payment_methods',
  StripePaymentMethodRecord,
  'id',
);
export const stripeCustomerPaymentMethodListBox = createOwnListBox(
  'payment/stripe/payment_methods/customer',
  OwnList.ns(),
);

// action.ts
export const getStripeCustomerPaymentMethodList = action(async (dispatch, select, customerId: number) => {
  const paymentMethodList = (await http.open('GET/payment/stripe/getPaymentMethodList', { customerId })).data;
  dispatch([
    stripePaymentMethodMapBox.mergeItems(paymentMethodList),
    stripeCustomerPaymentMethodListBox.setList(customerId, paymentMethodList.map(get('id'))),
  ]);
});
export const removeStripeCustomerCard = action(async (dispatch, select, id: string, customerId: number) => {
  await http.open('POST/payment/stripe/deleteCardForCustomer', {
    customerId,
    cardId: id,
  });
  await dispatch(stripeCustomerPaymentMethodListBox.deleteItem(customerId, id));
  await dispatch(stripePaymentMethodMapBox.deleteItem(id));
  await dispatch(getStripeCustomerPaymentMethodList(customerId));
});

// selector.ts
export const selectStripePaymentMethodList = selector((select, customerId: number) => {
  const methodMap = select(stripePaymentMethodMapBox);
  const methodList = select(stripeCustomerPaymentMethodListBox.getList(customerId));
  return methodList.map((id) => methodMap.mustGetItem(id)).sort((a, b) => b.created - a.created);
});

// App.tsx
const dispatch = useDispatch();
const [paymentMethodList] = useSelector(selectStripePaymentMethodList);
useEffect(() => {
  dispatch(getStripeCustomerPaymentMethodList('customerId'));
}, [])
const handleDeleteCard = async (cardId: string) => {
  await dispatch(removeStripeCustomerCard(cardId, 'customerId'));
}
```

### Query

```ts
// query.ts
export const queryStripePaymentMethod = {
  list: createQueryOptions({
    queryKey: (customerId: number) => ['payment', 'stripe', 'paymentMethodList', customerId],
    queryFn: async (customerId: number) => http.open('GET/payment/stripe/getPaymentMethodList', { customerId }),
    select: (data) => data.data.map((item) => new StripePaymentMethodRecord(item)),
  }),
  delete: createMutationOptions({
    mutationFn: async (params: { customerId: number; cardId: string }) =>
      http.open('POST/payment/stripe/deleteCardForCustomer', {
        customerId,
        cardId,
      }),
    onSuccess: (_, params) => {
      queryStripePaymentMethod.list.invalidateQueries(params.customerId);
    },
  }),
} as const;

// App.tsx
const { data } = queryStripePaymentMethod.list.useQuery('customerId');
const { mutate: deleteCard } = queryStripePaymentMethod.delete.useMutation();
const handleDeleteCard = async (cardId: string) => {
  await deleteCard({ customerId: 'customerId', cardId });
}
```


### Migration Guide

`useSelector` => `queryStripePaymentMethod.list.useQuery()`

`await dispatch` (e.g. loading) => `await queryStripePaymentMethod.list.prefetch()`

在 JS 函数中的 `store.select()` => `await queryStripePaymentMethod.list.ensureQueryData()`
