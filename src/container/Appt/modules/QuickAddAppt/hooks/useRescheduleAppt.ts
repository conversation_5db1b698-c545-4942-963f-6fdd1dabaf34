import { useDispatch, useStore } from 'amos';
import { type ServiceAddOnDateInfo } from '../../../store/appt.types';
import { useTransformApptDates } from './useTransformApptDates';
import { RepeatAppointmentModifyScope } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { scheduleAppointmentPetDetails } from '../../../store/appt.api';
import { selectMainServiceInAppt, selectPetsInAppt } from '../../../store/appt.selectors';
import { rescheduleEvaluationService } from '../../../../../store/evaluation/evaluation.actions';

export const useRescheduleAppt = () => {
  const store = useStore();
  const dispatch = useDispatch();
  const transformDates = useTransformApptDates();

  return async (
    draftDates: ServiceAddOnDateInfo[],
    appointmentId: string,
    repeatType?: RepeatAppointmentModifyScope,
  ) => {
    const pets = store.select(selectPetsInAppt(appointmentId));
    const evaluations = pets.flatMap((pet) => pet.evaluations) || [];
    const mainService = store.select(selectMainServiceInAppt(appointmentId));
    const newVal = transformDates(draftDates, appointmentId);

    await dispatch(
      scheduleAppointmentPetDetails({
        appointmentId,
        petDetails: newVal,
        repeatAppointmentModifyScope: repeatType || RepeatAppointmentModifyScope.ONLY_THIS,
      }),
    );

    if (evaluations.length) {
      await dispatch(
        rescheduleEvaluationService({
          appointmentId,
          evaluationServiceSchedules: evaluations.map(({ startTime, staffId, id }) => ({
            evaluationId: id,
            startDate: mainService.startDate || '',
            startTime,
            staffId,
          })),
        }),
      );
    }
  };
};
