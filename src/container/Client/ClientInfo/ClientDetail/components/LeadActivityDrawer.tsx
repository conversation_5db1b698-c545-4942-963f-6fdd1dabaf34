// create a drawer component that can be used to display the activity and log of a lead

import { Drawer, type DrawerProps } from '@moego/ui';
import React, { memo } from 'react';
import { LeadsHistory } from '../../../../Leads/Details/components/LeadsActivityAndLog/LeadsHistory';

export interface LeadActivityDrawerProps extends DrawerProps {
  customerId: number;
}

export const LeadActivityDrawer = memo<LeadActivityDrawerProps>((props) => {
  const { customerId, ...drawerProps } = props;
  return (
    <Drawer {...drawerProps} title="Activities history" isOpen showCancelButton={false} showConfirmButton={false}>
      <LeadsHistory customerId={customerId.toString()} header={null} />
    </Drawer>
  );
});
