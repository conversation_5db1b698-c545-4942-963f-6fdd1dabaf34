import { useSelector } from 'amos';
import { Radio } from 'antd';
import classNames from 'classnames';
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { ErrorHint } from '../../../../../../components/ColoredHint/ColoredHint';
import { Condition } from '../../../../../../components/Condition';
import { OBTestIds } from '../../../../../../config/testIds/onlineBooking';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import {
  NoShowProtectType,
  type TNoShowProtectType,
} from '../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { selectPaymentSettingInfo } from '../../../../../../store/payment/payment.selectors';
import { PRE_AUTH_FEATURE_NAME } from '../../../../../../store/stripe/preAuth.boxes';
import { useBool } from '../../../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { usePaymentVersionInfo } from '../../../../../../utils/hooks/usePaymentVersionInfo';
import { omitEmpty } from '../../../../../../utils/misc';
import { HintLearnMoreButton } from '../../../../../Calendar/Grooming/PreAuthForAppt/Onboarding/HintLearnMoreButton';
import { useInvoiceReinvent } from '../../../../../PaymentFlow/hooks/useInvoiceReinvent';
import { CardOnFileForm, type CardOnFileFormRef } from './CardOnFileForm';
import { GuardLevel, GuardLevelType } from './GuardLevel';
import { usePaymentType } from './PaymentOptionsContext';
import { PaymentSelectItem } from './PaymentSelectItem';
import { type PaymentSettingFormRef, type SubmitInput, usePaymentSettingFormState } from './PaymentSettingContext';
import { PreAuthForm, type PreAuthFormRef } from './PreAuthForm';
import { PrepaymentForm, type PrepaymentFormRef } from './PrepaymentForm';

const {
  SettingPaymentDefaultPrepay,
  SettingPaymentDefaultPreAuth,
  SettingPaymentDefaultCOF,
  SettingPaymentDefaultNoPayment,
} = OBTestIds;

interface Props {
  type: 'default' | 'certain_clients';
  className?: string;
}

export const PaymentOptionsForm = forwardRef<PaymentSettingFormRef, Props>((props, ref) => {
  const { type, className } = props;
  const { onDirty } = usePaymentSettingFormState(type);
  const [business, { preAuthBspd }] = useSelector(selectCurrentBusiness(), selectPaymentSettingInfo());
  const isStripeCardProcessor = business.preferStripe();
  const prepaymentFormRef = useRef<PrepaymentFormRef>(null);
  const preAuthFormRef = useRef<PreAuthFormRef>(null);
  const cardOnFileFormRef = useRef<CardOnFileFormRef>(null);
  const hasNoProcessor = !business.primaryPayType;
  const { paymentType, setPaymentType, paymentTypeForDisableView, setPaymentTypeForDisableView } = usePaymentType();
  const invalidNoShowProtectType = useBool();
  const { isLoading: isPaymentVersionLoading, isPaymentV2 } = usePaymentVersionInfo();
  const { isNewOrderV4Flow } = useInvoiceReinvent();

  const validate = useLatestCallback(async () => {
    if (!(NoShowProtectType.values as number[]).includes(paymentType)) {
      invalidNoShowProtectType.as(true);
      throw new Error('Invalid noShowProtectType');
    }
    let input: SubmitInput = {};
    switch (paymentType) {
      case NoShowProtectType.Prepayment:
        const prepaymentFormValues = await prepaymentFormRef.current!.validate();
        input = omitEmpty(prepaymentFormValues);
        if (input.prepayTipEnable !== undefined) {
          input.prepayTipEnable = input.prepayTipEnable ? 1 : 0;
        }
        break;
      case NoShowProtectType.PreAuth:
        const preAuthFormValues = await preAuthFormRef.current!.validate();
        input = omitEmpty(preAuthFormValues);
        if (input.preAuthTipEnable !== undefined) {
          input.preAuthTipEnable = input.preAuthTipEnable ? 1 : 0;
        }
        break;
      case NoShowProtectType.CardOnFile:
        const cardOnFileFormValues = await cardOnFileFormRef.current!.validate();
        input = omitEmpty(cardOnFileFormValues);
        break;
      case NoShowProtectType.Closed:
      default:
        break;
    }
    input.paymentType = paymentType;
    return input;
  });

  useImperativeHandle(ref, () => ({
    validate,
  }));

  const handleSelectProtectType = (type: TNoShowProtectType, disabled = false) => {
    if (!disabled) {
      invalidNoShowProtectType.as(false);
      setPaymentType(type);
      onDirty();
    }
    setPaymentTypeForDisableView(type);
  };

  const renderPrepaymentSection = () => {
    const isPrepaymentDisabled = hasNoProcessor || !isStripeCardProcessor;
    const isPrepaymentDisabledChecked = paymentTypeForDisableView === NoShowProtectType.Prepayment;
    const isPrepaymentChecked = paymentType === NoShowProtectType.Prepayment;
    return (
      <PaymentSelectItem
        className={classNames('radio-section section__prepayment', {
          active: isPrepaymentChecked,
        })}
        onClick={() => handleSelectProtectType(NoShowProtectType.Prepayment, isPrepaymentDisabled)}
        disabled={isPrepaymentDisabled}
        active={isPrepaymentDisabledChecked}
        data-testid={SettingPaymentDefaultPrepay}
      >
        <div className="radio-row">
          <div className="left">
            {isPrepaymentDisabled ? (
              <>
                <span className="radio"></span>
                <span className="radio-text" style={{ marginRight: '12px' }}>
                  Prepayment / Deposit
                </span>
              </>
            ) : (
              <Radio value={NoShowProtectType.Prepayment} checked={isPrepaymentChecked}>
                Prepayment / Deposit
              </Radio>
            )}
          </div>
          <div className="right">
            <GuardLevel level={GuardLevelType.Prepayment} />
          </div>
        </div>
        <div className="radio-desc">
          <p className="radio-desc-content">
            To require clients to pay up to the full amount of your service fee when submitting booking requests.{' '}
            <a href="https://wiki.moego.pet/moego-prepayment/" target="_blank" rel="noreferrer">
              Learn more
            </a>
          </p>
        </div>
        <PrepaymentForm
          className={classNames({
            hidden: !isPrepaymentChecked,
            'form-section': true,
          })}
          onValuesChange={onDirty}
          ref={prepaymentFormRef}
        />
      </PaymentSelectItem>
    );
  };

  const renderPreAuthSection = () => {
    if (isPaymentVersionLoading || isPaymentV2) return null;
    const isPreAuthDisabled = hasNoProcessor || !isStripeCardProcessor;
    const isPreAuthDisabledChecked = paymentTypeForDisableView === NoShowProtectType.PreAuth;
    const isPreAuthChecked = paymentType === NoShowProtectType.PreAuth;
    return (
      <PaymentSelectItem
        className={classNames('radio-section section__prepayment', {
          active: isPreAuthChecked,
        })}
        onClick={() => handleSelectProtectType(NoShowProtectType.PreAuth, isPreAuthDisabled)}
        disabled={isPreAuthDisabled}
        active={isPreAuthDisabledChecked}
        data-testid={SettingPaymentDefaultPreAuth}
      >
        <div className="radio-row">
          <div className="left">
            <Radio value={NoShowProtectType.PreAuth} checked={isPreAuthChecked}>
              {PRE_AUTH_FEATURE_NAME}
            </Radio>
          </div>
          <div className="right">
            <GuardLevel level={GuardLevelType.PreAuth} />
          </div>
        </div>
        <div className="radio-desc">
          <p className="radio-desc-content">
            To require a valid card on file upon booking. The card will be pre-authorized for the client's ticket amount{' '}
            {preAuthBspd} hours prior to the appointment, safeguarding revenue and reducing no-shows.{' '}
            <HintLearnMoreButton theme="orange" type="link" />
          </p>
        </div>
        <PreAuthForm
          className={classNames({
            hidden: !isPreAuthChecked,
            'form-section': true,
          })}
          onValuesChange={onDirty}
          ref={preAuthFormRef}
        />
      </PaymentSelectItem>
    );
  };

  const renderCardOnFileSection = () => {
    const isCardOnFileDisabledChecked = paymentTypeForDisableView === NoShowProtectType.CardOnFile;
    const isCardOnFileChecked = paymentType === NoShowProtectType.CardOnFile;
    return (
      <PaymentSelectItem
        className={classNames('radio-section', { active: isCardOnFileChecked })}
        onClick={() => handleSelectProtectType(NoShowProtectType.CardOnFile, hasNoProcessor)}
        disabled={hasNoProcessor}
        active={isCardOnFileDisabledChecked}
        data-testid={SettingPaymentDefaultCOF}
      >
        <div className="radio-row">
          <div className="left">
            {hasNoProcessor ? (
              <>
                <span className="radio"></span>
                <span className="radio-text">Card on File</span>
              </>
            ) : (
              <Radio value={NoShowProtectType.CardOnFile} checked={isCardOnFileChecked}>
                Card on File
              </Radio>
            )}
          </div>
          <div className="right">
            <GuardLevel level={GuardLevelType.CardOnFile} />
          </div>
        </div>
        <div className="radio-desc">
          <p className="radio-desc-content">
            To require clients to input valid credit <span className="moe-text-[#666] moe-font-bold">card on file</span>{' '}
            when submitting booking requests.
          </p>
        </div>
        <CardOnFileForm
          className={classNames({
            hidden: !isCardOnFileChecked,
            'form-section': true,
          })}
          onValuesChange={onDirty}
          ref={cardOnFileFormRef}
        />
      </PaymentSelectItem>
    );
  };

  const renderDisablePaymentSection = () => {
    return (
      <div
        className="radio-section"
        onClick={() => handleSelectProtectType(NoShowProtectType.Closed)}
        data-testid={SettingPaymentDefaultNoPayment}
      >
        <div className="radio-row">
          <div className="left">
            <Radio value={NoShowProtectType.Prepayment} checked={paymentType === NoShowProtectType.Closed}>
              No Payment
            </Radio>
          </div>
          <div className="right">
            <GuardLevel level={GuardLevelType.Closed} />
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={className}>
      {isNewOrderV4Flow ? null : renderPrepaymentSection()}
      {renderPreAuthSection()}
      {renderCardOnFileSection()}
      {renderDisablePaymentSection()}
      <Condition if={invalidNoShowProtectType.value}>
        <ErrorHint className="moe-mt-[16px]" text="Please select the payment method before moving on." />
      </Condition>
    </div>
  );
});
