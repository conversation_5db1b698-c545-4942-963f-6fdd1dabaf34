import { useDispatch, useSelector } from 'amos';
import type React from 'react';
import { memo, useEffect, useMemo } from 'react';
import { selectCurrentPermissions } from '../../store/business/role.selectors';
import { getCustomerNotesWithPets } from '../../store/customer/customerNote.actions';
import { getPetDetail } from '../../store/pet/pet.actions';
import { type PetRecord, petMapBox } from '../../store/pet/pet.boxes';
import { getPetCodeList } from '../../store/pet/petCode.actions';
import { type PetCodeRecord, petCodeMapBox } from '../../store/pet/petCode.boxes';
import { getPetIncidentReportList } from '../../store/pet/petIncident.actions';
import { type PetNoteRecord, petNoteListBox, petNoteMapBox } from '../../store/pet/petNote.boxes';
import { isNormal } from '../../store/utils/identifier';
import { jsonRecordMap } from '../../store/utils/utils';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { useClientPet } from './hooks/useClientPets';

export interface PetInfoRenderProps {
  petId: number;
  /** 阻止自动加载pet数据 */
  preventAutoReload?: boolean;
  children?: (_: {
    pet: PetRecord;
    codes: PetCodeRecord[];
    notes: PetNoteRecord[];
    go2PetDetail: (petId: string) => void;
  }) => React.ReactElement | null;
}

export const PetInfoRender = memo(function PetInfoRender(props: PetInfoRenderProps) {
  const { petId, children, preventAutoReload = true } = props;
  const { petHasDeleted } = useClientPet(petId);
  const dispatch = useDispatch();
  const [petMap, petCodeMap, noteList, noteMap, permissions] = useSelector(
    petMapBox,
    petCodeMapBox,
    petNoteListBox.getList(petId),
    petNoteMapBox,
    selectCurrentPermissions(),
  );

  const canViewClientProfile = permissions.has('viewIndividualClientProfile');
  const pet = petMap.mustGetItem(petId);
  const { customerId, petId: prePetId } = pet;
  const codes = pet.petCodeIdList.map((codeId) => petCodeMap.mustGetItem(codeId));
  const inValidatePrePet = !isNormal(prePetId);
  const notes = useMemo(() => jsonRecordMap(noteMap, noteList), [noteMap, noteList]);

  useEffect(() => {
    if (preventAutoReload) {
      return;
    }
    if (isNormal(petId)) {
      dispatch([getPetDetail(petId), getPetCodeList(), getPetIncidentReportList({ petId: String(petId) })]);
    }
  }, [petId, preventAutoReload]);

  useEffect(() => {
    if (preventAutoReload) {
      return;
    }
    if (isNormal(customerId) && isNormal(petId)) {
      dispatch(getCustomerNotesWithPets(customerId, [petId]));
    }
  }, [customerId, petId, preventAutoReload]);

  const go2PetDetail = useLatestCallback((petId: string) => {
    if (petHasDeleted || !canViewClientProfile) {
      return;
    }
    if (isNormal(customerId) && isNormal(petId)) {
      window.open(`/client/${customerId}/pets/${petId}`, '_blank', 'noopener noreferrer');
    }
  });

  if (inValidatePrePet) {
    return null;
  }

  return children?.({ pet, codes, notes, go2PetDetail }) ?? null;
});
