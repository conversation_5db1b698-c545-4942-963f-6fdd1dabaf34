import { cn, DatePicker, Heading } from '@moego/ui';
import React, { memo, useMemo } from 'react';
import { SelectStaff } from '../../../../EditServiceItem/components/SelectStaff';
import { ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useSelector, useStore } from 'amos';
import {
  apptAddOnMapBox,
  ApptAddOnRecord,
  apptServiceMapBox,
  ApptServiceRecord,
} from '../../../../../store/appt.boxes';
import { useTimeSlotWrapperNormalContext } from './hooks/useTimeSlotWrapperNormalContext';
import { type ApptInfoPetServiceInfo } from '../../../../../../../store/calendarLatest/calendar.types';
import { useAddSelectedCalendarStaff } from '../../../../EditPetServiceList/hooks/useAddSelectedCalendarStaff';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { SectionInfo } from '../../../../SectionInfo';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import dayjs from 'dayjs';
import { addApptPetService } from '../../../../../store/appt.actions';
import { isNormal } from '../../../../../../../store/utils/identifier';
import { Condition } from '../../../../../../../components/Condition';

export interface TimeSlotItemWrapperProps {
  className?: string;
  children: React.ReactNode;
  serviceType: ServiceType;
  ownId: string;
  appointmentId: string;
  showDatePicker?: boolean;
}

/**
 * 为每个 service 包裹一层，提供 staff 选择器
 */
export const TimeSlotItemWrapper = memo<TimeSlotItemWrapperProps>((props) => {
  const { className, children, serviceType, ownId, appointmentId, showDatePicker = true } = props;

  const store = useStore();
  const dispatch = useDispatch();
  const { petsServiceList, hasGroomingService, isEnableSlotCalender } = useTimeSlotWrapperNormalContext();
  const [business] = useSelector(selectCurrentBusiness);

  const addSelectedStaffs = useAddSelectedCalendarStaff();

  const handleStaffChange = useLatestCallback(async (value: ApptInfoPetServiceInfo) => {
    if (!value?.petId) return;
    const newServices = (petsServiceList.find((petService) => petService.petId === value.petId)?.services || []).map(
      (service) => {
        if (service.id === value.id) {
          return {
            ...service,
            staffId: value.staffId,
          };
        }
        return service;
      },
    );
    addSelectedStaffs(newServices);
    dispatch(addApptPetService(value.petId, newServices, isNormal(appointmentId) ? appointmentId : undefined, true));
  });

  const value = useMemo(() => {
    if (serviceType === ServiceType.SERVICE) {
      const service = store.select(apptServiceMapBox.mustGetItem(ownId));
      const value = petsServiceList
        .find((s) => s.petId === service.petId)
        ?.services.find((s) => ApptServiceRecord.createOwnId(appointmentId, s.id) === ownId);

      return value;
    }

    const addOn = store.select(apptAddOnMapBox.mustGetItem(ownId));
    const value = petsServiceList
      .find((s) => s.petId === addOn.petId)
      ?.services.find((s) => ApptAddOnRecord.createOwnId(appointmentId, s.id) === ownId);

    return value;
  }, [petsServiceList, ownId, serviceType, appointmentId]);

  if (!value) return null;

  return (
    <div className={cn('moe-flex moe-flex-col', className)}>
      <Condition if={isEnableSlotCalender && hasGroomingService}>
        <div className="moe-flex moe-flex-col moe-gap-s">
          <Heading size="4" className="moe-text-primary">
            {value.serviceName}
          </Heading>

          <Condition if={isNormal(value.staffId)}>
            <SelectStaff
              showMultiStaffOption={!isEnableSlotCalender}
              disableConflictCheck
              className="moe-w-full"
              value={value}
              appointmentId={+appointmentId}
              onChange={(newValue: ApptInfoPetServiceInfo) => {
                handleStaffChange(newValue);
              }}
            />
          </Condition>

          <Condition if={showDatePicker}>
            <SectionInfo label="Date" className="moe-flex-1" isRequired>
              <DatePicker isClearable={false} format={business.dateFormat} value={dayjs(value.startDate)} isDisabled />
            </SectionInfo>
          </Condition>
        </div>
      </Condition>
      {children}
    </div>
  );
});

TimeSlotItemWrapper.displayName = 'TimeSlotItemWrapper';
