import { Checkbox, ListBox } from '@moego/ui';
import React from 'react';
import { dayOfWeeks } from '../../../../../../store/business/business.boxes';
import { FullWeekDayList } from '../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { useControllableValue } from '../../../../../../utils/hooks/useControlledValue';
import { StaffScheduleTestIds } from '../../../../../../config/testIds/staffSchedule';

interface WeekListEventMeta {
  key?: number;
  checked?: boolean;
}

export interface WeekListProps {
  className?: string;
  isDisabled?: boolean;
  defaultSelectedKey?: number;
  defaultCheckedKeys?: number[];
  selectedKey?: number;
  checkedKeys?: number[];
  disabled?: boolean;
  onSelectChange?: (v: number) => void;
  onCheckedKeysChange?: (v: number[], meta: WeekListEventMeta) => void;
}

export const WeekList = (props: WeekListProps) => {
  const { className, onCheckedKeysChange, isDisabled } = props;

  const [selectedKey, setSelectedKey] = useControllableValue<number>(
    {
      ...props,
      defaultSelectedKey: props.defaultSelectedKey || dayOfWeeks.sundayFirst[0],
    },
    {
      defaultValuePropName: 'defaultSelectedKey',
      valuePropName: 'selectedKey',
      trigger: 'onSelectChange',
    },
  );

  const [checkedKeys, setCheckedKeys] = useControllableValue<number[]>(
    {
      ...props,
      defaultCheckedKeys: props.defaultCheckedKeys || [],
    },
    {
      defaultValuePropName: 'defaultCheckedKeys',
      valuePropName: 'checkedKeys',
    },
  );

  return (
    <div className={className} data-testid={StaffScheduleTestIds.SlotScheduleWeekList}>
      <ListBox
        classNames={{
          viewport: 'moe-p-0',
          list: 'moe-gap-1',
        }}
        virtualizerProps={{
          isDisabled: true,
        }}
        selectedKeys={[`${selectedKey}`]}
        onSelectionChange={(v) => {
          if (typeof v === 'string' || v.size === 0) {
            return;
          }
          const currentKey = Number(Array.from(v)[0]);
          setSelectedKey(currentKey);
        }}
      >
        {dayOfWeeks.sundayFirst.map((day) => (
          <ListBox.Item key={day} className="moe-p-s" textValue={FullWeekDayList[day]}>
            <div className="moe-flex moe-gap-xs" data-testid={`week-list-item-${day}`}>
              <Checkbox
                value={day}
                isDisabled={isDisabled}
                isSelected={checkedKeys && checkedKeys.includes(day)}
                onChange={(isSelected) => {
                  const nextKeys = new Set(checkedKeys);
                  if (isSelected) {
                    nextKeys.add(day);
                  } else {
                    nextKeys.delete(day);
                  }

                  setCheckedKeys(Array.from(nextKeys));
                  onCheckedKeysChange?.(Array.from(nextKeys), { key: day, checked: isSelected });
                }}
              />
              {FullWeekDayList[day]}
            </div>
          </ListBox.Item>
        ))}
      </ListBox>
    </div>
  );
};
