import { cn } from '@moego/ui';
import React, { type PropsWithChildren } from 'react';

export interface IconImageItemProps {
  isSelected: boolean;
  onClick: () => void;
}

export const IconImageItem: React.FC<PropsWithChildren<IconImageItemProps>> = (props) => {
  const { isSelected, onClick, children } = props;

  return (
    <div
      onClick={onClick}
      className={cn(
        'moe-w-[80px] moe-h-[80px] moe-flex moe-items-center moe-justify-center moe-border moe-border-divider moe-rounded-m moe-cursor-pointer',
        {
          'moe-border-brand moe-border-[2px]': isSelected,
        },
      )}
    >
      {children}
    </div>
  );
};
