/*
 * @since 2021-07-01 14:40:57
 * <AUTHOR> <<EMAIL>>
 */
import { ExclamationCircleOutlined, LeftOutlined, LoadingOutlined } from '@ant-design/icons';
import { UpdateCreditType, UserType } from '@moego/api-web/moego/models/subscription/v1/subscription_models';
import { Moe<PERSON><PERSON> } from '@moego/finance-utils';
import { type ErrorResponse, type ICancelResponse } from '@stripe/terminal-js';
import { useDispatch, useSelector } from 'amos';
import { Checkbox, Col, Form, Input, Row, message } from 'antd';
import classNames from 'classnames';
import React, { type Dispatch, type SetStateAction, memo, useContext, useEffect, useRef, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { useSetState } from 'react-use';
import { alertError } from '../../../../components/Alert/Alert.utils';
import { alertApi } from '../../../../components/Alert/AlertApi';
import { AmountInput } from '../../../../components/AmountInput';
import { Button } from '../../../../components/Button/Button';
import { ImgIcon, SvgIcon } from '../../../../components/Icon/Icon';
import { ConfirmModalWithClose } from '../../../../components/Modal/ConfirmModalWithClose';
import { modalApi } from '../../../../components/Modal/Modal';
import { type MessageSendByType, SendMessageConfirmModal } from '../../../../components/Modal/SendMessageConfirmModal';
import { QuestionTooltip } from '../../../../components/Popup/Tooltip';
import { toastApi } from '../../../../components/Toast/Toast';
import { AUTO_CANCEL_PROCESSING_FEE_BY_DEBIT_CARD_TIPS } from '../../../../config/host/const';
import { ApptTestIds } from '../../../../config/testIds/apptDrawer';
import { PATH_CREDIT_CARD_SETTING } from '../../../../router/paths';
import { type BusinessRecord, PreferPayTypes } from '../../../../store/business/business.boxes';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import { selectPricingPermission } from '../../../../store/company/company.selectors';
import { getCredit, updateCustomer } from '../../../../store/customer/customer.actions';
import { type CustomerRecord, customerMapBox } from '../../../../store/customer/customer.boxes';
import { updateCredit } from '../../../../store/finance/subscription/subscription.actions';
import {
  changeGroomingTicketStatus,
  getGroomingDepositGuid,
  getGroomingTicketGuid,
  getGroomingTicketInvoice,
} from '../../../../store/grooming/grooming.actions';
import {
  GroomingTicketInvoiceRecord,
  GroomingTicketInvoiceType,
  GroomingTicketStatus,
  InvoiceStatus,
} from '../../../../store/grooming/grooming.boxes';
import { getTemplateMessagePreview } from '../../../../store/message/message.actions';
import { MessageDetailType, TARGET_TYPE_PAY_ONLINE } from '../../../../store/message/message.boxes';
import { useEnableFeature } from '../../../../store/metadata/featureEnable.hooks';
import { META_DATA_KEY_LIST } from '../../../../store/metadata/metadata.config';
import {
  calculateProcessingFee,
  checkSmartReaderInProgress,
  createStripeTerminalPaymentIntent,
  deleteStripePaymentMethod,
  getInvoice,
  getStripePaymentMethod,
  sendPayOnlineEmail,
  submitGeneralPayment,
  submitSquareTerminalPayment,
} from '../../../../store/payment/actions/private/payment.actions';
import { assignItemPaidAmount } from '../../../../store/payment/allocate.actions';
import {
  type IsDeposit,
  PayOnlineEmailType,
  type PaymentMethodRecord,
  PrefabPaymentMethod,
  ProcessingFeePayBy,
  StripePaymentMethod,
  paymentMethodMapBox,
} from '../../../../store/payment/payment.boxes';
import { PaymentMethodResourceMap } from '../../../../store/payment/payment.resources';
import {
  type InvoiceModuleType,
  type InvoiceRecord,
  isGroomingTicketInvoice,
  selectBusinessActivePaymentMethodList,
  selectPaymentSettingInfo,
} from '../../../../store/payment/payment.selectors';
import { addAndConfirmACH } from '../../../../store/stripe/ach.actions';
import { preAuthDetailMapBox } from '../../../../store/stripe/preAuth.boxes';
import { stripeDebitMethodMapBox } from '../../../../store/stripe/stripe.boxes';
import { getStripeTerminalLocationList } from '../../../../store/stripe/stripeTerminal.actions';
import {
  currentStripeTerminalLocationIdBox,
  isStripeSmartReaderAvailableCountry,
} from '../../../../store/stripe/stripeTerminal.boxes';
import { formInput } from '../../../../utils/form';
import { useFormRef } from '../../../../utils/hooks/hooks';
import { useBool } from '../../../../utils/hooks/useBool';
import { useChargesEnabled } from '../../../../utils/hooks/useChargesEnabled';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useModalToggle } from '../../../../utils/hooks/useModalToggle';
import { useMoeGoGB } from '../../../../utils/hooks/useMoegoGB';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { ReportActionName } from '../../../../utils/reportType';
import { reportData } from '../../../../utils/tracker';
import { CREDIT_PERCENT } from '../../../Client/ClientInfo/CreditCards/hooks/useAvailableCredit';
import { type MoePay, type TakePaymentInput } from '../../../Payment/components/MoePay';
import { captureMoePayError } from '../../../Payment/components/MoePayError';
import { SetTipsModal } from '../../../Payment/components/SetTipsModal';
import { CANCEL_PAY_CODE, READER_USED_IN_PARALLEL_ERROR } from '../../../Payment/components/useStripeTerminal';
import { useAllocateItemPaymentWhitelist } from '../../../PaymentFlow/hooks/useAllocateItemPaymentWhitelist';
import { AppointmentStatus } from '../../../TicketDetail/AppointmentStatus';
import { TicketCreatedAlerts } from '../TicketCreatedAlerts/TicketCreatedAlerts';
import { PaymentMethodsIds } from '../interfaces';
import { type AllocateResult, PaymentAllocation } from './AllocationItems/PaymentAllocation';
import { CashPay } from './CashPay';
import { CheckPay } from './CheckPay';
import { CreditCardPay, CreditCardPayMode } from './CreditCardPay';
import { DebitCardPay } from './DebitCardPay';
import { type ACHVerificationParams, ALTER_TEXT, DebitCardVerificationModal } from './DebitCardVerificationModal';
import { MarkFinished } from './MarkFinished';
import { ButtonWrap, PaymentInfoBody, PaymentInfoView } from './PaymentInfo.style';
import { PaymentResult } from './PaymentResult';
import { PreAuthPay } from './PreAuthPay';
import { type SaveCardModalProps } from './SaveCardModal';
import { SelectSignatureModal } from './SelectSignatureModal';
import { SignatureModal } from './SignatureModal';
import { type LocalProcessingFee, TakePaymentContext } from './TakePaymentContext';
import { TerminalPaymentInfoModal, type TerminalPaymentInfoModel } from './TerminalPaymentInfoModal';
import { ModalHeader } from './components/ModalHeader';
export interface DefaultPaymentMethod {
  methodId: number;
  options?: any;
}

export interface PaymentInfoProps {
  className?: string;
  invoice: InvoiceRecord;
  business: BusinessRecord;
  onClose: () => void;
  pay: MoePay;
  module: InvoiceModuleType;
  retailModuleProps?: {
    isApplyingProcessingFee: boolean;
    amount: number;
    methodId: number;
  };
  isDeposit?: IsDeposit;
  defaultPaymentMethod?: DefaultPaymentMethod;
  isChargeNoShowFee?: boolean;
  onReadyToShowPreAuthHint?: (delay?: number) => void;
  hideAmountInput?: boolean;
}

const takePaymentInput = formInput<TakePaymentInput>().copy(
  'paymentAmount',
  'paidBy',
  'description',
  'checkNumber',
  'cardOnFileId',
  'saveCardOnFile',
  'tipsAmountValue',
  'withSignature',
  'signature',
  'squareDeviceId',
  'isDeposit',
  'stripeSmartReaderId',
  'addProcessingFee',
  'bankEmail',
  'accountName',
  'accountNumber',
  'mandateConfirm',
  'routingNumber',
  'accountType',
  'keepAsCredit',
  'paidAmount',
);

export type Model = {
  paymentAmount: number;
  paidBy: string;
  description: string;
  bankEmail?: string;
  accountName?: string;
  accountNumber?: string;
  mandateConfirm?: string;
  routingNumber?: string;
  accountType?: number;
};

const useModel = ({
  invoice,
  isDeposit,
  customer,
  form,
}: {
  invoice: PaymentInfoProps['invoice'];
  isDeposit: PaymentInfoProps['isDeposit'];
  customer: CustomerRecord;
  form: ReturnType<typeof useFormRef>;
}): [Model, Dispatch<SetStateAction<Model>>] => {
  const [model, setModel] = useState<Model>({
    paymentAmount: isDeposit ? 0 : invoice.remainAmount,
    paidBy: customer.fullName(),
    description: isDeposit ? `This is a deposit` : '',
  });

  useEffect(() => {
    // 当外部 invoice 数据改变时，需要局部复用之前保存的数据。
    const nextModel: Model = {
      paymentAmount: isDeposit ? model.paymentAmount : invoice.remainAmount,
      paidBy: model.paidBy ? model.paidBy : customer.fullName(),
      description: model.description,
    };
    setModel(nextModel);
    form.current?.setFieldsValue(nextModel);
  }, [invoice, customer]);

  return [model, setModel];
};

/**
 * CreditCard 组件内部涉及 Stripe 的场景，Processing Fee 计算场景依赖
 */
const PayModeToStripeMethodMap = new Map([
  [CreditCardPayMode.CardNumber, StripePaymentMethod.Card],
  [CreditCardPayMode.CardOnFile, StripePaymentMethod.CardOnFile],
  [CreditCardPayMode.StripeTerminal, StripePaymentMethod.SmartReader],
  // note: pay online 场景下，fee 用 card 的模式来计算
  [CreditCardPayMode.PayOnline, StripePaymentMethod.Card],
]);

/**
 * 维护 CreditCard 组件内部的 mode 状态与本组件的 PaymentMethod 的映射
 * TODO: 重构并统一 paymentMethodId 概念，干掉不必要的映射
 */
const PayModeToPrefabMethodMap = new Map([
  [CreditCardPayMode.CardNumber, PrefabPaymentMethod.CreditCard],
  [CreditCardPayMode.CardOnFile, PrefabPaymentMethod.CreditCard],
  [CreditCardPayMode.SquareTerminal, PrefabPaymentMethod.SquareTerminal],
  [CreditCardPayMode.StripeTerminal, PrefabPaymentMethod.StripeTerminal],
  [CreditCardPayMode.PayOnline, PrefabPaymentMethod.PayOnline],
]);

// TODO(Perqin, P1): 这里使用 useModalToggle 重构了部分弹窗，但是重构不完整。主要原因在于几个弹窗弹完之后的支付 submit 流程借用了这些
//  弹窗里最后展示的那个弹窗的 loading 态，这导致弹窗需要感知其他步骤的 loading 状态。期望可以通过调整/优化交互来解决这个问题。
export const PaymentInfo = memo<PaymentInfoProps>(
  ({
    className,
    invoice,
    business,
    onClose,
    pay,
    module,
    isDeposit,
    defaultPaymentMethod,
    hideAmountInput,
    retailModuleProps = { isApplyingProcessingFee: false, amount: 0, methodId: PaymentMethodsIds.CREDIT_CARD },
  }) => {
    const history = useHistory();
    const payOnlineActionsVisible = useBool();
    const addTipsModal = useModalToggle<number>();
    const selectSignatureModal = useModalToggle<boolean>();
    const signatureModal = useModalToggle();
    const isStripeSuccess = useBool();
    const completed = invoice.status === InvoiceStatus.Completed;
    const isGrooming = isGroomingTicketInvoice(invoice);
    const isNoShow = isGroomingTicketInvoice(invoice) && invoice.type === GroomingTicketInvoiceType.NoShow;
    const dispatch = useDispatch();
    const form = useFormRef();
    const { value: skipReaderTips } = useMoeGoGB<boolean>('skip_reader_tips');
    const [saveCardData, setSaveCardData] = useSetState<SaveCardModalProps>({
      visible: false,
    });
    const stripeTerminalPaymentLoading = useBool(false);
    const smartReaderInProgressModalVisible = useBool(false);
    const debitCardVerificationVisible = useBool(false);
    const isACHSubmitBtnDisabled = useBool(false);
    const [achVerificationParams, setACHVerificationParams] = useState<ACHVerificationParams>();
    const [debitCardPayMode, setDebitCardPayMode] = useState(CreditCardPayMode.CardNumber);
    const { enable: achEnabled } = useEnableFeature(META_DATA_KEY_LIST.ACHEnabled);
    const isRetail = module === 'retail';
    const isRetailWithProcessingFee = isRetail && retailModuleProps.isApplyingProcessingFee;
    const isRetailWithCreditCardAndProcessingFee =
      isRetailWithProcessingFee && retailModuleProps.methodId === PaymentMethodsIds.CREDIT_CARD;

    const [
      customer,
      methodMap,
      methodList,
      locationId,
      settingInfo,
      pricingPermission,
      permissions,
      stripeCardMap,
      preAuthMap,
    ] = useSelector(
      customerMapBox.mustGetItem(invoice.customerId),
      paymentMethodMapBox,
      selectBusinessActivePaymentMethodList(invoice.businessId),
      currentStripeTerminalLocationIdBox,
      selectPaymentSettingInfo(),
      selectPricingPermission(),
      selectCurrentPermissions(),
      stripeDebitMethodMapBox,
      preAuthDetailMapBox,
    );
    const isChargesEnabled = useChargesEnabled();
    const preAuthInfo = preAuthMap.mustGetItem(invoice instanceof GroomingTicketInvoiceRecord ? invoice.groomingId : 0);
    const { value: enableAllocate } = useAllocateItemPaymentWhitelist();
    const [allocateInfo, setAllocateInfo] = useSetState<AllocateResult>({
      allocateItems: [],
      allocateDone: false,
    });

    const tipBasedAmount =
      invoice instanceof GroomingTicketInvoiceRecord ? invoice.tipBasedAmount : invoice.subTotalAmount;

    const [model, setModel] = useModel({
      invoice,
      isDeposit,
      customer,
      form,
    });

    // 保存 form 数据到本地。
    const handleChangeForm = (changedValues: any) => {
      const nextModel: Model = {
        ...model,
        ...changedValues,
      };
      setModel(nextModel);
    };

    // note: createEnum 出来的 StripePaymentMethod 没有 enum 类型，只有 number
    const [stripePaymentMethod, setStripePaymentMethod] = useState<number>();
    const [selectedMethodId, setSelectedMethodId] = useState(() => {
      if (isGrooming) return void 0;

      // 如果是 retail 场景，且正在应用 processing fee，则代表是 MGP，则只能使用 credit card 或 stripe terminal，根据前面预设的 methodId 来判断。
      if (isRetailWithProcessingFee) {
        return retailModuleProps.methodId === PaymentMethodsIds.CREDIT_CARD
          ? PrefabPaymentMethod.CreditCard
          : PrefabPaymentMethod.StripeTerminal;
      }
      return PrefabPaymentMethod.CreditCard;
    });
    const isSelectSmartReader = selectedMethodId === PrefabPaymentMethod.StripeTerminal;

    const withTips =
      !isNoShow &&
      !isDeposit &&
      !settingInfo.skipTipping &&
      !isRetail &&
      !(customer.preferredTip.enable && invoice instanceof GroomingTicketInvoiceRecord && invoice.tipsAmount) &&
      // TODO（Sam）：跳过 smart reader 的小费收取，Just for BestFriends, 临时方案，后续需要做到产品侧。
      !(isSelectSmartReader && !!skipReaderTips);

    const [creditCardPayMode, setCreditCardPayMode] = useState(CreditCardPayMode.CardNumber);
    const clearSelectedPaymentMethod = useLatestCallback(() => {
      setSelectedMethodId(void 0);
      setStripePaymentMethod(void 0);
    });

    // 使用 stripe Terminal 支付场景，未连接成功，不给提交
    const disableStripeTerminalSubmit =
      selectedMethodId === PrefabPaymentMethod.StripeTerminal && !form.current?.getFieldValue('stripeSmartReaderId');

    const paymentInfoDivRef = useRef<HTMLDivElement>(null);
    const handlePaymentMethodBackClick = useLatestCallback(() => {
      paymentInfoDivRef.current?.scrollIntoView({ behavior: 'auto' });
      clearSelectedPaymentMethod();
    });

    const closeInvoiceModal = (delay?: number) => {
      if (delay) {
        setTimeout(() => {
          onClose();
        }, delay);
      } else {
        onClose();
      }
    };

    // processing fee 相关
    const isAddProcessingFee = form.current?.getFieldValue('addProcessingFee');
    const { customizedFeeName, processingFeePayBy } = settingInfo;
    const processingFeeAvailable = business.preferStripe() && processingFeePayBy === ProcessingFeePayBy.Client;
    const processingFeeEnabled =
      processingFeeAvailable &&
      selectedMethodId &&
      [
        PrefabPaymentMethod.StripeTerminal,
        PrefabPaymentMethod.CreditCard,
        PrefabPaymentMethod.PayOnline,
        PrefabPaymentMethod.PreAuth,
      ].includes(selectedMethodId);
    const showProcessingFeePrompt = !!processingFeeEnabled && !!settingInfo.autoCancelFeeByClient;
    const amountWording = processingFeeAvailable ? 'Amount' : 'Payment amount';

    // 最终展示的金额，在 processing fee 加载完后刷新
    const { updateLocalProcessingFee, ...localProcessingFee } = useContext(TakePaymentContext);
    const paymentAmount = form.current?.getFieldValue('paymentAmount');
    const [paymentAmountWithProcessingFee, setPayAmountWithProcessingFee] = useState(paymentAmount);
    const totalPayAmount = isAddProcessingFee ? paymentAmountWithProcessingFee : paymentAmount;
    const updateProcessingFee = useLatestCallback((newData: Partial<LocalProcessingFee>) => {
      const newFeeObject = {
        ...localProcessingFee,
        ...newData,
      };
      if (newFeeObject.isProcessingFeeShow && !newFeeObject.isProcessingFeeLoading) {
        setPayAmountWithProcessingFee(newFeeObject.processingFeeAmountVal + paymentAmount);
      }
      updateLocalProcessingFee(newData);
    });

    const handleCalculateProcessingFee = useSerialCallback(async (customAmount = 0) => {
      if (!processingFeeEnabled) {
        return;
      }
      try {
        const newFee = await dispatch(
          calculateProcessingFee({ amount: customAmount || paymentAmount || 0, stripePaymentMethod }),
        );
        updateProcessingFee({
          isProcessingFeeLoading: false,
          processingFeeAmountVal: newFee,
        });
      } catch (e) {
        updateProcessingFee({
          isProcessingFeeLoading: false,
        });
        throw e;
      }
    });

    const isDirty = useRef(false);
    const lastProcessingFeeChecked = useRef(true);
    useEffect(() => {
      let handler: number;
      if (processingFeeEnabled && stripePaymentMethod) {
        updateProcessingFee({
          isProcessingFeeLoading: true,
        });
        // TODO(gq): 优化点，用 useModel 的 initialValue 来初始化 addProcessingFee 表单
        form.current?.setFieldsValue({
          addProcessingFee: lastProcessingFeeChecked.current,
        });
        // TODO: 重试机制、弃旧结果、初始优化（同 mobile）
        if (!isDirty.current) {
          handleCalculateProcessingFee();
          isDirty.current = true;
        } else {
          handler = window.setTimeout(() => {
            isDirty.current && handleCalculateProcessingFee();
          }, 500);
        }
      } else {
        form.current?.setFieldsValue({
          addProcessingFee: false,
        });
        updateProcessingFee({
          isProcessingFeeLoading: false,
          processingFeeAmountVal: 0,
        });
      }

      return () => {
        handler && clearTimeout(handler);
      };
    }, [paymentAmount, processingFeeEnabled, stripePaymentMethod]);

    // sync to store
    useEffect(() => {
      updateProcessingFee({
        isProcessingFeeShow: isAddProcessingFee,
      });
    }, [isAddProcessingFee]);

    useEffect(() => {
      // Note that the Pre-auth payment method doesn't provide the back button. In the other words, we are forcing the
      // user to charge with pre-auth if available.
      if (preAuthInfo.isToBeCapture && !selectedMethodId) {
        setSelectedMethodId(PrefabPaymentMethod.PreAuth);
        setStripePaymentMethod(StripePaymentMethod.Card);
      }
    }, [preAuthInfo, selectedMethodId]);

    const handleCheckBoxChange = useLatestCallback((ev) => {
      lastProcessingFeeChecked.current = ev.target.checked;
    });

    const handleSubmitPreAuthPay = useLatestCallback(async () => {
      await pay.submitPreAuthPay(invoice.id, preAuthInfo.preAuthId).then(allocateMiddleware);
      toastApi.success('Payment successfully.');
    });

    const handlePayOnline = async (input: TakePaymentInput) => {
      if (isDeposit) {
        dispatch(getGroomingTicketInvoice(invoice.id)).then(() => payOnlineActionsVisible.open());
        return;
      }
      if (input.paymentAmount !== invoice.remainAmount) {
        modalApi.confirm({
          title: 'Send invoice to pay online only supports full payment. Update payment amount?',
          icon: <ExclamationCircleOutlined />,
          okText: 'Yes',
          okType: 'primary',
          cancelText: 'Cancel',
          width: '500px',
          onOk: async () => {
            input.paymentAmount = invoice.remainAmount;
            form.current?.setFieldsValue({ paymentAmount: input.paymentAmount });
            input.addProcessingFee && (await handleCalculateProcessingFee(input.paymentAmount));
            handlePayOnline(input);
          },
        });
        return;
      }

      if (
        isGroomingTicketInvoice(invoice) &&
        invoice.appointmentInfo.appointmentStatus !== GroomingTicketStatus.Finished &&
        !isNoShow
      ) {
        await dispatch(
          changeGroomingTicketStatus({
            groomingId: invoice.groomingId,
            status: AppointmentStatus.FINISHED,
          }),
        );
        await dispatch(getGroomingTicketInvoice(invoice.id));
      }
      payOnlineActionsVisible.open();
    };

    const getPayOnlineMessage = useLatestCallback(async () => {
      if (!paymentAmount) {
        // Should never reach
        alertApi.warn('Invalid payment amount.');
        throw new Error('Invalid payment amount.');
      }
      // 目前 retail 场景下只允许 credit card/card on file/reader，不支持 pay online
      if (!isGroomingTicketInvoice(invoice)) {
        // Should never reach
        alertApi.warn('Retail invoice does not support Pay Online.');
        throw new Error('Retail invoice does not support Pay Online.');
      }
      let orderUuid: string;
      if (isDeposit) {
        ({ guid: orderUuid } = await dispatch(getGroomingDepositGuid(invoice.id, paymentAmount, isAddProcessingFee)));
      } else {
        ({ guid: orderUuid } = await dispatch(getGroomingTicketGuid(invoice.id, isAddProcessingFee)));
      }
      return dispatch(
        getTemplateMessagePreview(TARGET_TYPE_PAY_ONLINE, invoice.id, MessageDetailType.Text, {
          groomingId: invoice.groomingId,
          orderUuid,
        }),
      );
    });

    const handleSendPayOnlineActionConfirm = useSerialCallback(async (type: MessageSendByType, email: string = '') => {
      // 非 MoeGo Pay 时，Pay Online 成功后也不要弹出 onboarding
      switch (type) {
        case 'email':
          await dispatch(
            sendPayOnlineEmail({
              invoiceId: invoice.id,
              email,
              type: isDeposit ? PayOnlineEmailType.deposit : PayOnlineEmailType.payInvoice,
              depositAmount: form.current?.getFieldValue('paymentAmount'),
              requiredProcessingFee: isAddProcessingFee,
            }),
          )
            .then(async () => {
              if (!customer.email) {
                await dispatch(
                  updateCustomer(
                    { customerId: invoice.customerId, email },
                    {
                      autoToast: false,
                    },
                  ),
                ).catch(() => void 0);
              }
              message.success('Invoice email sent successfully!', 2);
              // 视觉上延迟 2s 再关闭
              closeInvoiceModal(2000);
            })
            .finally(() => {
              payOnlineActionsVisible.close();
            });
          break;
        case 'message':
          payOnlineActionsVisible.close();
          // 视觉上延迟 2s 再关闭
          closeInvoiceModal(2000);
          break;
      }
    });

    /**
     * Perform credit card payment with the following steps:
     * 1. Initialize payment;
     * 2. Choose with/without signature;
     * 3. (Optional) Choose tips;
     * 4. (Optional) Sign the signature;
     * 5. Submit payment.
     * This is supported by the following payment methods:
     * - Stripe credit card (new card or COF);
     * - Square credit card (new card or COF);
     * - Stripe pre-auth.
     */
    const handleCreditCardPay = async (input: TakePaymentInput) => {
      // 1. Initialize payment
      try {
        if (selectedMethodId === PrefabPaymentMethod.PreAuth) {
          await pay.startPreAuthPay(input);
        } else {
          await pay.startPay(input);
        }
      } catch (e) {
        alertError(e);
        // In case startStripePay or startSquarePay in MoePay throws error
        captureMoePayError(e);
        return;
      }
      // 2. Show select signature modal
      const withSignature = await selectSignatureModal.open();
      pay.set('withSignature', withSignature);
      // 3-1. Show tip modal, and then pay
      if (withTips && !withSignature) {
        // The payment will be done inside the tip modal
        await addTipsModal.open();
        return;
      }
      // 3-2. Show tip modal, then show signature modal, and then pay
      if (withTips) {
        const amount = await addTipsModal.open();
        pay.set('tipsAmountValue', amount);
        await signatureModal.open();
        return;
      }
      // 3-3. Show signature modal, and then pay
      if (withSignature) {
        await signatureModal.open();
        return;
      }
      // 3-4. Directly pay
      await handleSubmitCreditCardPay().catch(alertError);
    };

    const [terminalPaymentInfo, setTerminalPaymentInfo] = useState<TerminalPaymentInfoModel>();

    const handleSquareTerminalPay = async (input: TakePaymentInput) => {
      const r = await dispatch(
        submitSquareTerminalPayment(invoice.id, module, { ...input, skipTipping: withTips ? 0 : 1 }),
      );
      setTerminalPaymentInfo(r);
    };

    const handleCloseSaveCardModal = () => {
      setSaveCardData({ visible: false });
    };

    const handleConfirmSaveCard = async (paymentIntentId: string) => {
      // eslint-disable-next-line no-async-promise-executor
      return new Promise<void>(async (resolve, _reject) => {
        // 只有新卡才需要确认 SaveCard
        const cardInfo = await dispatch(getStripePaymentMethod(paymentIntentId));
        const { cardNumber, cardType, paymentMethodId } = cardInfo;
        if (cardInfo?.newCard && paymentMethodId) {
          setSaveCardData({
            visible: true,
            cardNumber: cardNumber,
            cardType: cardType,
            onCancel: async () => {
              dispatch(deleteStripePaymentMethod(paymentMethodId));
              handleCloseSaveCardModal();
              resolve();
            },
            onConfirm: async () => {
              handleCloseSaveCardModal();
              resolve();
            },
          });
        } else {
          resolve();
        }
      });
    };

    const allocateMiddleware = async (result: unknown) => {
      if (!showAllocate || !allocateInfo.allocateItems.length || !allocateInfo.allocateDone) {
        return result;
      }

      try {
        await dispatch(
          assignItemPaidAmount({
            orderId: invoice.id + '',
            businessId: invoice.businessId + '',
            items: allocateInfo.allocateItems.map((s) => {
              return {
                itemId: s.value + '',
                assignedPaidAmount: MoeMoney.fromAmount(s.allocateAmount, business.currencyCode),
              };
            }),
          }),
        );
        // eslint-disable-next-line sonarjs/no-ignored-exceptions, @typescript-eslint/no-unused-vars
      } catch (e) {
        // ignore error
      } finally {
        setAllocateInfo({ allocateItems: [], allocateDone: false });
      }

      return result;
    };

    const handleStripeSmartReaderPay = async (input: TakePaymentInput) => {
      if (!input.stripeSmartReaderId || !locationId) return;

      isStripeSuccess.close();
      stripeTerminalPaymentLoading.close();
      handleCloseSaveCardModal();

      const paymentIntent = await dispatch(
        createStripeTerminalPaymentIntent(
          {
            invoiceId: invoice.id,
            module: module,
            locationId,
            readerId: input.stripeSmartReaderId,
            stripePaymentMethod: StripePaymentMethod.SmartReader,
            addProcessingFee: isRetail ? retailModuleProps.isApplyingProcessingFee : isAddProcessingFee,
          },
          input,
        ),
      );

      setTerminalPaymentInfo({
        amount: input.paymentAmount,
        processingFeeAmount: isRetail
          ? retailModuleProps.amount
          : isAddProcessingFee
            ? localProcessingFee.processingFeeAmountVal
            : 0,
      });

      await pay
        .submitStripeSmartReaderPay(
          {
            paymentIntentSecret: paymentIntent.paymentIntentSecret,
            paymentIntentId: paymentIntent.paymentIntentId,
          },
          {
            eligibleAmount: withTips ? tipBasedAmount : 0,
          },
        )
        .then(allocateMiddleware)
        .catch((error) => {
          let message = (error as ErrorResponse)?.error?.message || 'something went wrong';
          if (message === READER_USED_IN_PARALLEL_ERROR) {
            message =
              'Reader connection was lost due to links with multiple devices. Please refresh this page to re-establish connection.';
          }
          if (error.error.code !== CANCEL_PAY_CODE) {
            toastApi.error(message);
          }
          setTerminalPaymentInfo(void 0);
          throw error;
        });

      // 获取最终的卡信息是异步的，调用了 MoePay 内的 fetchState，但这里无法保证 100% 获取到最终状态，但除了 UI 多弹出外不会产生其他副作用
      stripeTerminalPaymentLoading.open();
      try {
        await pay.fetchState(paymentIntent.paymentId);
        await handleConfirmSaveCard(paymentIntent.paymentIntentId);
      } catch {
        stripeTerminalPaymentLoading.close();
      }

      isStripeSuccess.open();
      clearSelectedPaymentMethod();
    };

    const cancelStripePay = async () => {
      let res: ErrorResponse | ICancelResponse | undefined;
      try {
        res = await pay.cancelStripePay();
      } catch (error: any) {
        toastApi.error(error?.message || 'Something went wrong');
        return;
      }

      if (res?.error) {
        toastApi.error(res?.error?.message || 'Something went wrong');
      }

      setTerminalPaymentInfo(void 0);
    };

    const handleSubmitCreditCardPay = useSerialCallback(async () => {
      // Pre-auth: Should call special payment API and won't show successful modal
      if (selectedMethodId === PrefabPaymentMethod.PreAuth) {
        await handleSubmitPreAuthPay();
        clearSelectedPaymentMethod();
        return;
      }
      await pay.submitPay().then(allocateMiddleware);
      clearSelectedPaymentMethod();
      // 这里展示的是击掌的弹窗
      signatureModal.open();
    });

    /**
     * This function is called during SetTipsModal's submission.
     * If the signature modal won't show after setting tips, the payment submission should be delegated to SetTipsModal
     * to keep the modal loading during submission. Otherwise, this function should simply close the modal.
     */
    const handleSubmitTips = useSerialCallback(async (amount) => {
      if (!pay.get('withSignature')) {
        pay.set('tipsAmountValue', amount);
        try {
          await handleSubmitCreditCardPay();
        } catch (e) {
          // Dismiss the payment flow on failure
          addTipsModal.cancel();
          alertError(e);
          throw e;
        }
      }
      addTipsModal.submit(amount);
    });

    const handleGeneralPay = async (input: TakePaymentInput, methodId: number) => {
      await dispatch(submitGeneralPayment(invoice.id, module, methodId, input)).then(allocateMiddleware);
      await dispatch(getInvoice(invoice.id, module));
      toastApi.success('Payment successful!');
      clearSelectedPaymentMethod();
      // deposit 则关闭
      if (isDeposit) {
        closeInvoiceModal();
      }
    };

    const handleACHPay = async (input: TakePaymentInput) => {
      const card = stripeCardMap.mustGetItem(input.cardOnFileId);
      const params = {
        input,
        invoice,
        model,
        module,
        card,
        debitCardPayMode,
        isAddProcessingFee,
        isDeposit,
        totalPayAmount,
      };
      const createResult = await dispatch(addAndConfirmACH(params));

      if (createResult?.redirectUrl) {
        debitCardVerificationVisible.open();
        setACHVerificationParams(createResult);
      } else {
        toastApi.success(ALTER_TEXT.success);
        clearSelectedPaymentMethod();
        closeInvoiceModal();
      }
    };

    const handleTakePayment = useSerialCallback(async (options?: { isSmartReaderInProgressConfirmed: boolean }) => {
      const input = await takePaymentInput.validate(form);
      input.isDeposit = isDeposit;
      // retail 模块默认无 processingFee，但默认值为 true，覆盖掉这种场景
      if (module === 'retail') {
        input.addProcessingFee = retailModuleProps.isApplyingProcessingFee;
      }
      switch (selectedMethodId) {
        case void 0:
          break;
        case PrefabPaymentMethod.PayOnline:
          await handlePayOnline(input);
          break;
        case PrefabPaymentMethod.CreditCard:
        case PrefabPaymentMethod.SquareTerminal:
        case PrefabPaymentMethod.StripeTerminal:
        case PrefabPaymentMethod.PreAuth:
          if (input.squareDeviceId) {
            await handleSquareTerminalPay(input);
          } else if (input.stripeSmartReaderId) {
            if (options?.isSmartReaderInProgressConfirmed) {
              await handleStripeSmartReaderPay(input);
            } else {
              await checkStripeSmartReaderInProgress(input);
            }
          } else {
            await handleCreditCardPay(input);
          }
          break;
        case PrefabPaymentMethod.ACH:
          await handleACHPay(input);
          break;
        default:
          await handleGeneralPay(input, selectedMethodId);
          break;
      }
      if (input.keepAsCredit && input.paidAmount) {
        reportData(ReportActionName.checkKeepChangeAsCredit);
        const credit = parseInt(
          (
            MoeMoney.fromAmount(input.paidAmount).minus(MoeMoney.fromAmount(input.paymentAmount)).valueOf() *
            CREDIT_PERCENT
          ).toFixed(2),
        );
        dispatch(
          updateCredit({
            user: {
              id: invoice.customerId.toString(),
              type: UserType.CUSTOMER,
            },
            credit: credit.toString(),
            type: UpdateCreditType.KEEP_CHANGE,
            invoiceId: invoice.id.toString(),
            ...(isGroomingTicketInvoice(invoice) ? { appointmentId: invoice.groomingId.toString() } : {}),
          }),
        );
        // 更新当前的 credit
        dispatch(getCredit(invoice.customerId));
      }
    });

    const paymentMethodClick = useSerialCallback(async (paymentMethod: PaymentMethodRecord, isCreditCard: boolean) => {
      if (paymentMethod.methodId === PrefabPaymentMethod.StripeTerminal && !locationId) {
        modalApi.confirm({
          title: 'Set up smart reader payment',
          content: 'Address information should be completed first.',
          onOk: () => {
            history.push(PATH_CREDIT_CARD_SETTING.queried({ stripeModal: 'LOCATION_MODAL' }));
          },
          okText: 'Go to settings',
        });
        return;
      }

      if (isCreditCard && !isChargesEnabled) {
        modalApi.confirm({
          title: 'Set up credit card payment',
          content: 'Please set up at least 1 credit card processor.',
          onOk: () => history.push(PATH_CREDIT_CARD_SETTING.build()),
          okText: 'Go to settings',
        });
        return;
      }
      setSelectedMethodId(paymentMethod.methodId);

      // init stripe payment method
      if (!business.preferStripe()) return;
      switch (paymentMethod.methodId) {
        case PrefabPaymentMethod.StripeTerminal:
          setStripePaymentMethod(StripePaymentMethod.SmartReader);
          break;
        case PrefabPaymentMethod.PayOnline:
          // Pay online 场景按 creditCard 计算费率
          setStripePaymentMethod(StripePaymentMethod.Card);
          break;
        default:
          setStripePaymentMethod(StripePaymentMethod.Card);
          break;
      }
    });

    const checkStripeSmartReaderInProgress = async (input: TakePaymentInput) => {
      const info = await dispatch(checkSmartReaderInProgress({ readerId: input.stripeSmartReaderId }));
      if (info.inProgress) {
        smartReaderInProgressModalVisible.open();
      } else {
        await handleStripeSmartReaderPay(input);
      }
    };

    const smartReaderInProgressConfirm = () => {
      smartReaderInProgressModalVisible.close();
      handleTakePayment({
        isSmartReaderInProgressConfirmed: true,
      });
    };

    const defaultPaymentMethodApplied = useBool();
    useEffect(() => {
      defaultPaymentMethodApplied.as(false);
    }, [defaultPaymentMethod]);
    useEffect(() => {
      const methodKey =
        defaultPaymentMethod &&
        methodList.find((m) => methodMap.getItem(m)?.methodId === defaultPaymentMethod.methodId);
      const method = methodKey && methodMap.mustGetItem(methodKey);
      if (!defaultPaymentMethodApplied.value && method && method.methodId !== selectedMethodId) {
        defaultPaymentMethodApplied.as(true);
        paymentMethodClick(method, method.methodId === PrefabPaymentMethod.CreditCard);
      }
    }, [defaultPaymentMethod, methodMap, methodList, selectedMethodId, defaultPaymentMethodApplied.value]);

    const handleModeChange = useLatestCallback((newMode: CreditCardPayMode) => {
      setSelectedMethodId(PayModeToPrefabMethodMap.get(newMode));
      setCreditCardPayMode(newMode);
      if (business.preferStripe()) {
        setStripePaymentMethod(PayModeToStripeMethodMap.get(newMode));
      }
    });

    const handleDebitModeChange = useLatestCallback((newMode: CreditCardPayMode) => {
      setDebitCardPayMode(newMode);
    });

    const renderPaymentMethod = () => {
      // Square Pay Online 场景仍走原逻辑
      if (selectedMethodId === PrefabPaymentMethod.PayOnline && business.preferSquare()) {
        return void 0;
      }
      switch (selectedMethodId) {
        case PrefabPaymentMethod.ACH:
          return (
            <DebitCardPay
              invoice={invoice}
              onPayModeUpdate={handleDebitModeChange}
              model={model}
              isACHSubmitBtnDisabled={isACHSubmitBtnDisabled}
              defaultMode={debitCardPayMode}
            />
          );
        case PrefabPaymentMethod.CreditCard:
        case PrefabPaymentMethod.SquareTerminal:
        case PrefabPaymentMethod.StripeTerminal:
        case PrefabPaymentMethod.PayOnline:
          // eslint-disable-next-line no-case-declarations
          const defaultMode = isRetailWithProcessingFee
            ? isRetailWithCreditCardAndProcessingFee
              ? CreditCardPayMode.CardNumber
              : CreditCardPayMode.StripeTerminal
            : defaultPaymentMethod?.options?.mode;

          return (
            <CreditCardPay
              pay={pay}
              invoice={invoice}
              business={business}
              methodId={selectedMethodId}
              defaultMode={defaultMode}
              enablePayOnline={!isRetail}
              onPayModeUpdate={handleModeChange}
              // 如果是 retail + 使用了 processing fee，不允许切换 payment method。
              changeable={!isRetailWithProcessingFee}
            />
          );
        case PrefabPaymentMethod.PreAuth:
          return <PreAuthPay preAuthInfo={preAuthInfo} />;
        case PrefabPaymentMethod.Check:
          return <CheckPay />;
        case PrefabPaymentMethod.Cash:
          return <CashPay business={business} amount={model.paymentAmount} />;
        default:
          return void 0;
      }
    };

    const renderPaymentMethodList = () => {
      return (
        <ul className="payment-method-wrap">
          {methodList.map((id) => {
            const item = methodMap.mustGetItem(id);

            function renderItem(item: PaymentMethodRecord, isCreditCard: boolean) {
              const resource = PaymentMethodResourceMap[item.methodId];
              const icon = resource ? (
                resource.iconType === 'svg' ? (
                  <SvgIcon src={resource.icon} />
                ) : (
                  <ImgIcon src={resource.icon} />
                )
              ) : (
                void 0
              );
              const name =
                isDeposit && item.id === PrefabPaymentMethod.PayOnline ? 'Request deposit with online pay' : item.name;
              return (
                <li
                  key={item.methodId}
                  className={selectedMethodId === item.methodId ? 'payment-method-selected' : ''}
                  // eslint-disable-next-line sonarjs/no-nested-functions
                  onClick={() => {
                    paymentMethodClick(item, isCreditCard);
                  }}
                  data-testid={ApptTestIds.ApptPaymentPopupPaymentMethod}
                >
                  {icon}
                  {name}
                </li>
              );
            }
            if (item.methodId === PrefabPaymentMethod.CreditCard) {
              // fulfillment can't use pay online currently
              const payOnline =
                isGrooming && !invoice.isFulfillment
                  ? renderItem(methodMap.mustGetItem(PrefabPaymentMethod.PayOnline), true)
                  : void 0;
              const achBlock = achEnabled ? renderItem(methodMap.mustGetItem(PrefabPaymentMethod.ACH), true) : void 0;

              switch (business.primaryPayType) {
                case PreferPayTypes.Square:
                  return (
                    <React.Fragment key={item.methodId}>
                      {achBlock}
                      {renderItem(item, true)}
                      {payOnline}
                      {renderItem(methodMap.mustGetItem(PrefabPaymentMethod.SquareTerminal), true)}
                    </React.Fragment>
                  );
                case PreferPayTypes.Stripe:
                  return (
                    <React.Fragment key={item.methodId}>
                      {achBlock}
                      {renderItem(item, true)}
                      {payOnline}
                      {isStripeSmartReaderAvailableCountry(business.country) &&
                        pricingPermission.enable.has('stripeReader') &&
                        renderItem(methodMap.mustGetItem(PrefabPaymentMethod.StripeTerminal), true)}
                    </React.Fragment>
                  );
                default:
                  return (
                    <React.Fragment key={item.methodId}>
                      {achBlock}
                      {renderItem(item, true)}
                      {payOnline}
                    </React.Fragment>
                  );
              }
            }
            return renderItem(item, false);
          })}
        </ul>
      );
    };

    useEffect(() => {
      dispatch(getStripeTerminalLocationList());
    }, []);

    const renderProcessingFeePay = () => {
      const showCheckBox = permissions.has('canControlProcessingFeeInInvoice');
      return (
        <>
          <Row className="take-payment-processing">
            <Row className="take-payment-processing-fee">
              <Col className={classNames('fee-checkbox-col', { 'hide-check': !showCheckBox })} span={17}>
                <Form.Item noStyle name="addProcessingFee" valuePropName="checked">
                  <label>
                    <Checkbox
                      disabled={!showCheckBox}
                      onChange={handleCheckBoxChange}
                      defaultChecked={lastProcessingFeeChecked.current}
                      style={{ display: showCheckBox ? 'block' : 'none' }}
                    />
                    <div className="fee-tt">{customizedFeeName}</div>
                  </label>
                </Form.Item>
                {showCheckBox && (
                  <QuestionTooltip
                    className="fee-tooltip"
                    overlay="Debit card transactions are prohibited from surcharging."
                    size={14}
                    overlayInnerStyle={{
                      padding: '16px 16px 24px 16px',
                      fontSize: '12px',
                      fontWeight: 400,
                    }}
                  />
                )}
              </Col>
              <Col span={7}>
                <div className="processing-fee-amount">
                  {localProcessingFee.isProcessingFeeLoading ? (
                    <LoadingOutlined />
                  ) : (
                    business.formatAmount(localProcessingFee.processingFeeAmountVal)
                  )}
                </div>
              </Col>
            </Row>
            {settingInfo.autoCancelFeeByClient ? (
              <Row className={`${showCheckBox ? '!moe-ml-[28px]' : '!moe-ml-[0]'} !moe-text-[#999] !moe-mt-[8px]`}>
                {AUTO_CANCEL_PROCESSING_FEE_BY_DEBIT_CARD_TIPS}
              </Row>
            ) : null}
          </Row>
          <Form.Item
            className="take-payment-result"
            labelAlign="left"
            labelCol={{ span: 12 }}
            wrapperCol={{ span: 12 }}
            label="Payment amount"
          >
            <div className="total-amount">{business.formatAmount(totalPayAmount)}</div>
          </Form.Item>
        </>
      );
    };

    const showAllocate = enableAllocate && paymentAmount > 0 && paymentAmount < invoice.remainAmount;
    const onAllocateChange = useLatestCallback((info: Partial<AllocateResult>) => {
      setAllocateInfo(info);
    });

    const paymentMethodNode = renderPaymentMethod();
    const finalAmount = pay.get('addProcessingFee')
      ? pay.get('paymentAmount') + localProcessingFee.processingFeeAmountVal
      : pay.get('paymentAmount');

    const renderTitle = () => {
      if (completed) {
        return 'Fully paid';
      }
      if (isNoShow) {
        return 'Charge no-show fee';
      }
      if (isDeposit) {
        return 'Take Deposit';
      }
      return 'Take payment';
    };

    const renderPaymentForm = () => {
      return (
        <>
          <Form className="take-payment-form-wrap" ref={form} onValuesChange={handleChangeForm} initialValues={model}>
            <div className="payment-input" style={{ display: !isGroomingTicketInvoice(invoice) ? 'none' : 'block' }}>
              <section className="take-payment-form-basic-info">
                <Form.Item name="paidBy" label="Paid by">
                  <Input />
                </Form.Item>
                <Form.Item name="description" label="Description">
                  <Input />
                </Form.Item>
              </section>
              <section
                className={classNames('take-payment-form-amount-wrap', {
                  'with-processing-fee': processingFeeEnabled,
                  '!moe-border-b-0': hideAmountInput && !processingFeeEnabled && !showAllocate,
                })}
              >
                <Form.Item
                  className={classNames('take-payment-amount-input', {
                    'moe-invisible !moe-h-0': hideAmountInput,
                  })}
                  name="paymentAmount"
                  rules={[
                    {
                      // REFACTOR_OPTIMIZE_MARKER(yueyue): optimize this
                      validator: async (rule, value: number | undefined) => {
                        if (typeof value !== 'number') {
                          throw new Error(`${amountWording} is required.`);
                        }
                        if (value <= 0 && isDeposit) {
                          throw new Error('Please input deposit amount');
                        }
                        if (value <= 0 && invoice.remainAmount > 0) {
                          throw new Error(processingFeeAvailable ? 'Invalid amount.' : 'Invalid payment amount.');
                        }
                        if (
                          (selectedMethodId === PrefabPaymentMethod.CreditCard ||
                            selectedMethodId === PrefabPaymentMethod.SquareTerminal) &&
                          value < 1
                        ) {
                          throw new Error(
                            `Credit card payment amount should not less than ${business.formatAmount(1)}`,
                          );
                        }
                        if (value > invoice.remainAmount) {
                          throw new Error(
                            `${amountWording} should be less than or equal to ${business.formatAmount(
                              invoice.remainAmount,
                            )}.`,
                          );
                        }
                      },
                    },
                  ]}
                >
                  <AmountInput business={business} className="take-payment-input" prefix={amountWording} />
                </Form.Item>

                {showAllocate && (
                  <PaymentAllocation
                    allocateAmount={paymentAmount}
                    invoice={invoice as GroomingTicketInvoiceRecord}
                    onChange={onAllocateChange}
                  />
                )}
                {processingFeeEnabled && renderProcessingFeePay()}
              </section>
            </div>
            {paymentMethodNode && isGrooming && selectedMethodId !== PrefabPaymentMethod.PreAuth && (
              <div className="back-c">
                <a onClick={handlePaymentMethodBackClick}>
                  <LeftOutlined /> Back
                </a>
              </div>
            )}
            {paymentMethodNode || (
              <div className="payment-method-container">
                <p className="payment-method-title">Payment method</p>
                {renderPaymentMethodList()}
              </div>
            )}
          </Form>
          {/* 兼容性：sell product 需用回原组件样式 */}
          <ButtonWrap>
            {module === 'retail' ? (
              <Button
                btnType="primary"
                disabled={!selectedMethodId || disableStripeTerminalSubmit}
                onClick={() => handleTakePayment()}
                buttonRadius="circle"
                block
                size="lg"
                loading={handleTakePayment.isBusy()}
              >
                {isDeposit ? 'Take Deposit' : 'Take payment'}
              </Button>
            ) : (
              <Button
                btnType="primary"
                disabled={
                  !selectedMethodId ||
                  (isAddProcessingFee && localProcessingFee.isProcessingFeeLoading) ||
                  !totalPayAmount ||
                  disableStripeTerminalSubmit ||
                  isACHSubmitBtnDisabled.value ||
                  (showAllocate && allocateInfo.allocateItems.length && !allocateInfo.allocateDone)
                }
                onClick={() => handleTakePayment()}
                buttonRadius="circle"
                block
                size="lg"
                loading={handleTakePayment.isBusy()}
                data-testid={ApptTestIds.ApptPaymentPopupChargeBtn}
              >
                {'Charge ' + business.formatAmount(totalPayAmount)}
              </Button>
            )}
          </ButtonWrap>
        </>
      );
    };

    const renderPaymentBody = () => {
      if (isGroomingTicketInvoice(invoice) && completed) {
        return (
          <div>
            {isNoShow && (
              // TODO(yueyue): optimize do not use magic value
              <TicketCreatedAlerts ticketId={invoice.groomingId} customerId={invoice.customerId} mode={3} />
            )}
            <PaymentResult invoice={invoice} business={business} />
          </div>
        );
      }
      if (!completed && !invoice.remainAmount && module !== 'retail') {
        return <MarkFinished invoiceId={invoice.id} onClose={onClose} />;
      }
      return renderPaymentForm();
    };

    return (
      <PaymentInfoView
        ref={paymentInfoDivRef}
        className={classNames(className, {
          'info--retail-mode': module === 'retail',
        })}
      >
        {payOnlineActionsVisible.value && isGroomingTicketInvoice(invoice) && (
          <SendMessageConfirmModal
            visible={true}
            builtInMessageSender={true}
            customerId={invoice.customerId}
            message={getPayOnlineMessage}
            email={customer.email}
            title="Send invoice to pay online"
            onClose={payOnlineActionsVisible.close}
            onConfirm={handleSendPayOnlineActionConfirm}
          />
        )}
        {addTipsModal.isOpen && (
          <SetTipsModal
            visible={true}
            amount={tipBasedAmount}
            maxTipsAmount={business.maxTipsAmount}
            business={business}
            onSubmit={handleSubmitTips}
            onClose={addTipsModal.cancel}
            closeOnSubmit={false}
          />
        )}
        {selectSignatureModal.isOpen && (
          <SelectSignatureModal
            amount={finalAmount}
            business={business}
            onSubmit={selectSignatureModal.submit}
            onClose={selectSignatureModal.cancel}
            showProcessingFeePrompt={showProcessingFeePrompt}
          />
        )}
        <DebitCardVerificationModal
          visible={debitCardVerificationVisible.value}
          onClose={debitCardVerificationVisible.close}
          invoice={invoice}
          onFinished={closeInvoiceModal}
          achVerificationParams={achVerificationParams as ACHVerificationParams}
        />
        {signatureModal.isOpen && (
          <SignatureModal
            business={business}
            customer={customer}
            onClose={signatureModal.cancel}
            processingFeeAmount={localProcessingFee.processingFeeAmountVal}
            onSuccess={() => {
              // Payment succeeded and close this signature modal
              signatureModal.submit(undefined);
              if (isDeposit || module === 'retail') {
                // Also close the invoice modal
                closeInvoiceModal();
              }
            }}
            pay={pay}
            isCardOnFile={creditCardPayMode === CreditCardPayMode.CardOnFile}
            onPay={handleSubmitCreditCardPay}
          />
        )}
        {terminalPaymentInfo && (
          /**
           * TODO(xiaoyang): 拆分内部的多个 Modal，会影响到 SquareTerminal，本次没有进行
           */
          // REFACTOR_OPTIMIZE_MARKER(yueyue): optimize this
          <TerminalPaymentInfoModal
            info={terminalPaymentInfo}
            business={business}
            invoiceId={invoice.id}
            module={module}
            payMethodId={
              business.preferStripe() ? PrefabPaymentMethod.StripeTerminal : PrefabPaymentMethod.SquareTerminal
            }
            isStripeSuccess={isStripeSuccess.value}
            onClose={() => setTerminalPaymentInfo(void 0)}
            onSuccess={() => {
              if (isDeposit) {
                closeInvoiceModal();
              } else {
                setTerminalPaymentInfo(void 0);
              }
            }}
            onCancel={cancelStripePay}
            saveCardData={saveCardData}
            paymentStatusLoading={stripeTerminalPaymentLoading.value}
            showProcessingFeePrompt={showProcessingFeePrompt}
          />
        )}

        <ModalHeader title={renderTitle()} onClose={onClose} />
        <PaymentInfoBody>{renderPaymentBody()}</PaymentInfoBody>

        <ConfirmModalWithClose
          width="480px"
          title="Are you sure to proceed the new transaction?"
          // @text-lint ignore
          content="There is a pending transaction. Are you sure to cancel the pending transaction and proceed the new one?"
          visible={smartReaderInProgressModalVisible.value}
          onCancel={smartReaderInProgressModalVisible.close}
          onConfirm={smartReaderInProgressConfirm}
          onClose={smartReaderInProgressModalVisible.close}
          cancelBtnText="Not now"
          confirmBtnText="Proceed"
        />
      </PaymentInfoView>
    );
  },
);
