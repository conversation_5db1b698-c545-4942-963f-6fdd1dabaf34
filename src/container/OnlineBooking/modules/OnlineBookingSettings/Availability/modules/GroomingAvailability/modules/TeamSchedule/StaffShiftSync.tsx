import { Button, Form, Switch } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { memo, useMemo } from 'react';
import { useHistory } from 'react-router-dom';
import { OBTestIds } from '../../../../../../../../../config/testIds/onlineBooking';
import { PATH_SETTING_STAFF } from '../../../../../../../../../router/paths';
import { selectCurrentBusiness } from '../../../../../../../../../store/business/business.selectors';
import { useSerialCallback } from '../../../../../../../../../utils/hooks/useSerialCallback';
import { MajorNewTabOutlined } from '@moego/icons-react';

interface StaffShiftSyncProps {
  isSync?: boolean;
  onSyncChange?: (visible: boolean) => void;
  isLoading?: boolean;
}

export const StaffShiftSync = memo(({ isSync, onSyncChange, isLoading }: StaffShiftSyncProps) => {
  const history = useHistory();
  const today = useMemo(() => dayjs(), []);
  const [business] = useSelector(selectCurrentBusiness);

  // sync为 true 时，要从 sm 获取数据
  // logic:
  // 1. 如果 sync 关闭，显示 Availability type dropdown
  // 2. 如果 sync 开启，隐藏 Availability type dropdown
  //    - 获取 SM 的 availability type
  //    - 同步 availability type 到 OB
  //    - 把 schedule 设为 disabled 状态
  const changeSyncStatus = useSerialCallback(async (visible: boolean) => {
    await onSyncChange?.(visible);
  });

  const currentWeekRange = useMemo(() => {
    const start = today.startOf('week');
    const end = today.endOf('week');
    return isSync ? `(The current week is ${business.formatDate(start)} - ${business.formatDate(end)})` : '';
  }, [isSync, today, business]);

  const handleRoute = () => {
    history.push(PATH_SETTING_STAFF.build({ panel: 'workingHours' }));
  };

  return (
    <div className="moe-flex moe-flex-col moe-items-start moe-mt-[24px]">
      <div className="moe-flex moe-flex-col moe-gap-y-s moe-mb-[8px]">
        <Form.Label label="Staff shift sync"></Form.Label>
        <Switch
          isLoading={isLoading}
          isSelected={isSync}
          onChange={changeSyncStatus}
          isDisabled={changeSyncStatus.isBusy()}
          data-testid={OBTestIds.SettingStaffShiftSyncSwitch}
        >
          {isSync ? 'On' : 'Off'}
        </Switch>
        {!!currentWeekRange && <div className="moe-text-caption moe-text-tertiary">{currentWeekRange}</div>}
      </div>
      <Button onPress={handleRoute} variant="tertiary" size="s" icon={<MajorNewTabOutlined />}>
        View shift management
      </Button>
    </div>
  );
});
