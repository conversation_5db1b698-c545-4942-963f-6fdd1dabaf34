import React from 'react';
import { ScheduleWeekList, type ScheduleWeekListProps } from './ScheduleWeekList';
import { WorkingSlot } from './WorkingSlot';
import { useModal } from '../../../../../../../../components/Modal/useModal';
import { CopyPasteModal } from '../../../../../../../../components/WeekTimeScheduleV2/CopyPasteModal';
import { useLatestCallback } from '../../../../../../../../utils/hooks/useLatestCallback';
import { cloneDeep, isUndefined } from 'lodash';
import {
  getDefaultWeekSlotValue,
  type WeekSlotValue,
} from '../../../../../../../../store/staffSchedule/staffSchedule.types';
import type { WorkingSlotValue } from '../../../../../../../../store/staff/staff.boxes';
import {
  type FullWeekDay,
  FullWeekDayList,
} from '../../../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { Text } from '@moego/ui';
import cn from 'classnames';

interface WeekSlotScheduleProps {
  className?: string;
  value: WeekSlotValue;
  onChange?: (v: WeekSlotValue) => void;
  isDisabled?: boolean;
  /**
   * For OB grooming team schedule by slot setting
   * 仅在前端隐藏显示，后端实际上还是有数据的
   */
  hideDailyWorkingHour?: boolean;
}

export const WeekSlotSchedule = (props: WeekSlotScheduleProps) => {
  const { value, onChange, isDisabled, className, hideDailyWorkingHour } = props;
  const openCopySettingsModal = useModal(CopyPasteModal);

  const onWeekDayCheck: ScheduleWeekListProps['onCheck'] = (checked, dayIndex) => {
    if (isUndefined(dayIndex) || isUndefined(checked)) return;

    const dayKey = FullWeekDayList[dayIndex].toLowerCase() as Lowercase<FullWeekDay>;
    const nextWeekData = {
      ...value,
      [dayKey]: {
        ...getDefaultWeekSlotValue()[dayKey], // 需要 merge default data，因为可能第一次打开时没数据
        ...value[dayKey],
        isAvailable: checked,
      },
    };

    onChange?.(nextWeekData);
  };

  const onSlotSettingCopy = (sourceData: WorkingSlotValue, sourceDay: number) => {
    openCopySettingsModal({
      visible: true,
      day: FullWeekDayList[sourceDay],
      onApply: (days) => {
        const copiedWeekData = days.reduce((data, targetDayKey) => {
          const targetDayKeyLowerCase = targetDayKey.toLowerCase() as Lowercase<FullWeekDay>;
          return {
            ...data,
            [targetDayKeyLowerCase]: cloneDeep(sourceData),
          };
        }, {} as WeekSlotValue);

        const nextWeekData = {
          ...value,
          ...copiedWeekData,
        };

        onChange?.(nextWeekData);
      },
    });
  };

  const handleDayDataChange = useLatestCallback((v: WorkingSlotValue, dayIndex: number) => {
    const dayKey = FullWeekDayList[dayIndex].toLowerCase() as Lowercase<FullWeekDay>;
    const nextWeekData = {
      ...value,
      [dayKey]: v,
    };

    onChange?.(nextWeekData);
  });

  const renderEmptyContent = (dayKey: number) => (
    <div className="moe-flex-1 moe-flex moe-items-center moe-justify-center moe-h-full">
      <Text variant="small" className="moe-text-tertiary moe-text-center">
        Not working on {FullWeekDayList[dayKey]}
      </Text>
    </div>
  );

  return (
    <ScheduleWeekList
      className={cn('moe-relative moe-h-full', className)}
      classNames={{
        weekList: 'moe-min-w-[140px] moe-w-[140px]',
      }}
      value={value}
      onCheck={onWeekDayCheck}
      isDisabled={isDisabled}
    >
      {({ selectedDayKey }) => {
        const dayKey = FullWeekDayList[selectedDayKey].toLowerCase() as Lowercase<FullWeekDay>;
        const dayData = value[dayKey];

        return dayData.isAvailable ? (
          <WorkingSlot
            isDisabled={isDisabled}
            value={dayData}
            day={selectedDayKey}
            onCopy={onSlotSettingCopy}
            onChange={handleDayDataChange}
            hideDailyWorkingHour={hideDailyWorkingHour}
          />
        ) : (
          renderEmptyContent(selectedDayKey)
        );
      }}
    </ScheduleWeekList>
  );
};
