import { Operator } from '@moego/api-web/moego/utils/v2/condition_messages';
import { LocalPricingRule } from '../PricingRuleSettingContext';
import { BasePricingRuleFormHelper } from './BasePricingRuleFormHelper';
import { ConditionType, EffectType, RuleType } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { MIN_RULE_NUMBER } from '../../PricingRules.utils';

export class MultiplePetsPricingRuleFormHelper extends BasePricingRuleFormHelper {
  protected ruleType = RuleType.MULTIPLE_PET;

  public createCondition() {
    return {
      type: ConditionType.PET_COUNT,
      operator: Operator.GE,
      value: { numberValue: MIN_RULE_NUMBER },
    };
  }

  public createEffect() {
    return {
      type: EffectType.FIXED_DISCOUNT,
      value: null,
    };
  }

  protected initAddFormCustomizedValue(): Partial<LocalPricingRule> {
    return {};
  }

  protected initEditFormCustomizedValue(): Partial<LocalPricingRule> {
    return {};
  }
}
