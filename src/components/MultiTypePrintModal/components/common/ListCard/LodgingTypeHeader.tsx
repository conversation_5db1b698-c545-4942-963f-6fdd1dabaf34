import { MinorRoomOutlined } from '@moego/icons-react';
import { Text, cn } from '@moego/ui';
import React from 'react';

export interface LodgingTypeHeaderProps {
  title: string;
  className?: string;
}

export const LodgingTypeHeader = (props: LodgingTypeHeaderProps) => {
  const { title, className = '' } = props;

  return (
    <div
      className={cn(
        'moe-flex moe-items-center moe-px-spacing-xs moe-py-spacing-xxs moe-border moe-border-[#cbcbcb] moe-border-b-0 moe-bg-neutral-sunken-0',
        className,
      )}
    >
      <MinorRoomOutlined />
      <Text variant="caption" className="moe-ml-xxs moe-text-primary !moe-text-[10px]">
        {title}
      </Text>
    </div>
  );
};
