import React, { memo } from 'react';
import { useSelector } from 'amos';
import { AvailableTimeTypeKind } from '../../../../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { selectDraftAvailabilitySettings } from '../../../../../../../../../store/onlineBooking/settings/availabilityGrooming.selectors';
import { staffMapBox } from '../../../../../../../../../store/staff/staff.boxes';
import { Alert, Button } from '@moego/ui';
import { useHistory } from 'react-router';
import { PATH_SETTING_STAFF } from '../../../../../../../../../router/paths';
import { WorkingHour } from './WorkingHour/WorkingHour';
import { WorkingSlot } from './WorkingSlot/WorkingSlot';
import cn from 'classnames';

export interface AvailableTimeFormProps {
  className?: string;
  staffId: number;
  isDisabled?: boolean;
  // 白名单相关
  showAlert?: boolean;
}

export const AvailableTimeForm = memo<AvailableTimeFormProps>(({ className, staffId, showAlert, isDisabled }) => {
  const history = useHistory();
  const [staff, draftSettings] = useSelector(staffMapBox.mustGetItem(staffId), selectDraftAvailabilitySettings());

  const isWorkingHour = draftSettings.availableTimeType === AvailableTimeTypeKind.ByWorkingHour;

  return (
    <div className={cn('moe-relative moe-flex moe-flex-col moe-h-full', className)}>
      {showAlert && (
        <Alert
          className="moe-mb-m"
          isRounded
          color="information"
          description={
            <p>
              With sync enabled, Online Booking uses the staff settings from{' '}
              <Button
                className="moe-inline-block moe-h-[20px] moe-text-[14px]"
                size="s"
                variant="tertiary"
                onPress={() => {
                  history.push(PATH_SETTING_STAFF.build({ panel: 'workingHours' }));
                }}
              >
                {`Staff shift management`}
              </Button>
              .
            </p>
          }
        />
      )}
      {isWorkingHour ? (
        <WorkingHour className="moe-flex-1" staff={staff} isDisabled={isDisabled} />
      ) : (
        <WorkingSlot className="moe-flex-1" staff={staff} isDisabled={isDisabled} />
      )}
    </div>
  );
});
