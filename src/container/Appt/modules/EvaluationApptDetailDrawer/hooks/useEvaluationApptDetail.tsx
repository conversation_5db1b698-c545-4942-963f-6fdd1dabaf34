import { useDispatch } from 'amos';
import { setEvaluationApptDetailDrawerEdit } from '../../../../../store/evaluation/evaluation.actions';
import { type EvaluationApptDetailStep } from '../../../../../store/evaluation/evaluation.boxes';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';

export const useJumpToStep = () => {
  const dispatch = useDispatch();

  const jumpToStep = useLatestCallback((step: EvaluationApptDetailStep) => {
    dispatch(
      setEvaluationApptDetailDrawerEdit({
        stepInfo: {
          type: step,
        },
      }),
    );
  });

  return jumpToStep;
};
