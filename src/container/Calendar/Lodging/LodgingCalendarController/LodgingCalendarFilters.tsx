import { MinorChevronDownOutlined } from '@moego/icons-react';
import { Checkbox, Dropdown, Heading, Markup, cn, type SelectionMode } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { isNil } from 'lodash';
import React, { type ReactNode, memo, useMemo } from 'react';
import { useMount } from 'react-use';
import { Condition } from '../../../../components/Condition';
import { defaultAreaTypeOption } from '../../../../store/calendarLatest/calendar_data.utils';
import {
  type LodgingCalendarConfigModel,
  LodgingCalendarFilterLodgingStatus,
} from '../../../../store/calendarLatest/lodgingCalendar.boxes';
import { selectLodgingCalendarConfig } from '../../../../store/calendarLatest/lodgingCalendar.selectors';
import { getLodgingTypeList } from '../../../../store/lodging/actions/public/lodgingType.actions';
import { lodgingTypeMapBox } from '../../../../store/lodging/lodgingType.boxes';
import { selectLodgingTypeList } from '../../../../store/lodging/lodgingType.selectors';
import { ReportActionName } from '../../../../utils/reportType';
import { reportData } from '../../../../utils/tracker';
import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { GrowthBookFeatureList } from '../../../../utils/growthBook/growthBook.config';

export interface LodgingCalendarFiltersProps {
  onChange?: (filter: Partial<LodgingCalendarConfigModel>) => void;
}

const ALL_LODGING_TYPES = 'all';

export const LodgingCalendarFilters = memo<LodgingCalendarFiltersProps>((props) => {
  const { onChange } = props;

  const [lodgingTypeList, lodgingTypMap, lodgingCalendarConfig] = useSelector(
    selectLodgingTypeList,
    lodgingTypeMapBox,
    selectLodgingCalendarConfig,
  );

  const enableHideDefaultArea = useFeatureIsOn(GrowthBookFeatureList.EnableHideDefaultAreaInLodgingView);

  const dispatch = useDispatch();

  const handleChangeLodgingFilter = (filter: Partial<LodgingCalendarConfigModel>) => {
    if (!isNil(filter.lodgingOccupiedStatus)) {
      reportData(ReportActionName.lodgingViewFilterLodgingStatus, {
        status: LodgingCalendarFilterLodgingStatus.mapLabels[filter.lodgingOccupiedStatus],
      });
    }
    if (filter.lodgingTypeList) {
      reportData(ReportActionName.lodgingViewFilterLodgingTypes);
    }
    onChange?.(filter);
  };

  const LodgingCalendarLodgingTypesFilterOptions = useMemo(() => {
    const lodgingTypeOptions = lodgingTypeList
      .map((id) => ({
        value: id,
        label: lodgingTypMap.mustGetItem(id).name,
      }))
      .toArray();

    if (enableHideDefaultArea) {
      return lodgingTypeOptions;
    }

    return [defaultAreaTypeOption, ...lodgingTypeOptions];
  }, [lodgingTypeList, lodgingTypMap, enableHideDefaultArea]);

  useMount(() => {
    dispatch(getLodgingTypeList());
  });

  return (
    <div className="moe-flex moe-gap-x-xs moe-h-[40px]">
      <LodgingCalendarDropdown
        options={LodgingCalendarFilterLodgingStatus.values.map((value) => ({
          value,
          label: LodgingCalendarFilterLodgingStatus.mapLabels[value],
        }))}
        value={[lodgingCalendarConfig.lodgingOccupiedStatus]}
        onChange={(lodgings) => handleChangeLodgingFilter({ lodgingOccupiedStatus: lodgings?.[0] })}
      />
      <LodgingCalendarDropdown
        options={LodgingCalendarLodgingTypesFilterOptions}
        value={lodgingCalendarConfig.lodgingTypeList}
        selectionMode="multiple"
        renderTriggerLabel={(value) => {
          const firstOption = LodgingCalendarLodgingTypesFilterOptions.find((option) => option.value === value[0]);
          return value.length === LodgingCalendarLodgingTypesFilterOptions.length || !value.length
            ? 'All lodging types'
            : value.length > 1
              ? `Lodging types: ${value.length}`
              : firstOption?.label;
        }}
        renderLabel={({ value, label }) => (
          <Checkbox value={value} isSelected={lodgingCalendarConfig.lodgingTypeList.includes(value)}>
            {label}
          </Checkbox>
        )}
        onChange={(lodgingTypeList) => handleChangeLodgingFilter({ lodgingTypeList })}
      />
    </div>
  );
});

export type LodgingCalendarDropdownValue = string[];
export interface Option {
  label: string;
  value: string;
}

export interface LodgingCalendarDropdownProps {
  options: Option[];
  value: LodgingCalendarDropdownValue;
  selectionMode?: SelectionMode;
  renderTriggerLabel?: (value: LodgingCalendarDropdownValue) => ReactNode;
  renderLabel?: (option: Option) => ReactNode;
  onChange?: (value: LodgingCalendarDropdownValue) => void;
}

export const LodgingCalendarDropdown = memo<LodgingCalendarDropdownProps>((props) => {
  const { options, value, selectionMode, onChange, renderTriggerLabel, renderLabel } = props;

  const isMultiSelect = selectionMode === 'multiple';

  const selectedLabelList = useMemo(() => {
    return options.filter((option) => value?.includes(option.value)).map((option) => option.label);
  }, [options, value, isMultiSelect]);

  return (
    <Dropdown>
      <Dropdown.Trigger>
        <div className="moe-flex moe-items-center moe-gap-xxs moe-px-s moe-rounded-full moe-cursor-pointer hover:moe-bg-neutral-sunken-0">
          <Heading size="5" className="moe-text-primary">
            {renderTriggerLabel?.(value) ?? selectedLabelList.join(', ')}
          </Heading>
          <MinorChevronDownOutlined />
        </div>
      </Dropdown.Trigger>
      <Dropdown.Menu
        selectionMode={selectionMode}
        onSelectionChange={(value) => {
          onChange?.(Array.from(value) as string[]);
        }}
        selectedKeys={value}
        renderMenu={(menus) => (
          <div className="moe-flex moe-w-full moe-flex-col moe-overflow-hidden">
            <Condition if={isMultiSelect}>
              <Checkbox
                key={ALL_LODGING_TYPES}
                value={ALL_LODGING_TYPES}
                isSelected={value.length === options.length}
                isIndeterminate={value.length > 0 && value.length < options.length}
                className="moe-pt-[18px] moe-px-s moe-pb-[2px]"
                onChange={(isSelected) => {
                  onChange?.(isSelected ? options.map((option) => option.value) : []);
                }}
              >
                <Markup variant="small" className="moe-text-primary">
                  All lodging types
                </Markup>
              </Checkbox>
            </Condition>
            {menus}
          </div>
        )}
      >
        {options.map(({ label, value }) => (
          <Dropdown.MenuItem
            key={value}
            value={value}
            className={cn({
              'moe-bg-transparent': isMultiSelect,
            })}
            // For accessibility warning
            textValue={value}
          >
            {renderLabel?.({ label, value }) ?? label}
          </Dropdown.MenuItem>
        ))}
      </Dropdown.Menu>
    </Dropdown>
  );
});
