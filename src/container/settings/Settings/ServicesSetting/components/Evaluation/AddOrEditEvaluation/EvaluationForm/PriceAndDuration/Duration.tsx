import { Form } from '@moego/ui';
import React, { memo } from 'react';
import { NumberInputV2 } from '../../../../../../../../../components/form/NumberInputV2';
import { RE_INPUT_NUMBER } from '../../../../../../../../../components/form/types';
import { MAX_EVALUATION_DURATION_MINUTES } from '../../../../../../consts';
import { numberToStringV2 } from '../../../../../utils/inputTransformer';
import { useServiceStaffPermissions } from '../../../../AddOrEditComponents/hooks/useServiceStaffPermissions';
import { useEvaluationFormContext } from '../EvaluationFormContext';

export const Duration = memo(() => {
  const { isEdit } = useEvaluationFormContext();
  const { isDisabledForDuration } = useServiceStaffPermissions(isEdit);

  return (
    <Form.Item
      name="duration"
      label="Duration"
      rules={{
        required: 'Duration is required',
        validate: (value) => {
          if (value === 0) {
            return 'Duration must be greater than 0';
          } else if (!value) {
            return 'Duration is required';
          }
          return true;
        },
      }}
      transformer={numberToStringV2}
    >
      <NumberInputV2
        isDisabled={isDisabledForDuration}
        isRequired
        className="moe-w-full"
        placeholder="Duration"
        suffix="mins"
        inputFormat={RE_INPUT_NUMBER}
        maxLength={4}
        maxValue={MAX_EVALUATION_DURATION_MINUTES}
      />
    </Form.Item>
  );
});
