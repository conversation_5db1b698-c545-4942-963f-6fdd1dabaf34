import { MinorChevronUpOutlined } from '@moego/icons-react';
import { Divider } from 'antd';
import React from 'react';
import { useControllableValue } from '../../../../../../../../utils/hooks/useControlledValue';
import { SMTestIds } from '../../../../../../../../config/testIds/shiftManagement';

interface CalendarCollapseProps {
  children?: React.ReactNode;
  isExpand?: boolean;
  defaultIsExpand?: boolean;
  onExpandChange?: (isExpand: boolean) => void;
}

export const CalendarCollapse = (props: CalendarCollapseProps) => {
  const { children } = props;
  const [mergedIsExpand, setMergedIsExpand] = useControllableValue(props, {
    defaultValuePropName: 'defaultIsExpand',
    trigger: 'onExpandChange',
    valuePropName: 'isExpand',
  });

  return (
    <div data-testid={SMTestIds.SettingDateOverrideDrawerCalendarExpandBtn}>
      <div
        className="moe-overflow-hidden"
        style={{
          transition: 'height 0.2s ease-in-out',
          height: mergedIsExpand ? 'auto' : '0px',
        }}
      >
        {children}
      </div>
      <Divider plain className="!moe-mb-m">
        <div
          className="moe-flex moe-items-center moe-gap-xs moe-cursor-pointer"
          onClick={() => {
            setMergedIsExpand(!mergedIsExpand);
          }}
        >
          {mergedIsExpand ? 'Hide the calendar' : 'Expand the calendar'}
          <MinorChevronUpOutlined
            style={{
              transform: mergedIsExpand ? '' : 'rotate(180deg)',
            }}
          />
        </div>
      </Divider>
    </div>
  );
};
