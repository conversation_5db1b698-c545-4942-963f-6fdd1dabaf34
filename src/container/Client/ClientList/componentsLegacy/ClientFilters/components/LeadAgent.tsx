import React, { memo, useMemo } from 'react';
import { valueHelpers } from '../../../components/FilterItems/FilterProperty.utils';
import { type SpecificFilterProps } from '../ClientFilters.config';
import { useFilterState } from '../ClientFilters.hooks';
import { getMultiSelectLabel } from '../ClientFilters.utils';
import { CheckboxFilter } from './common/CheckboxFilter';
import { FilterWrapper } from './common/FilterWrapper';
import { useSelector } from 'amos';
import { selectBusinessStaffs } from '../../../../../../store/staff/staff.selectors';
import { staffMapBox } from '../../../../../../store/staff/staff.boxes';

export const LeadAgent = memo<SpecificFilterProps>(({ type, property }) => {
  const { filter, handleChange, handleRemove } = useFilterState(property);
  const [staffList, staffMap] = useSelector(selectBusinessStaffs, staffMapBox);
  const options = useMemo(() => {
    const result: Array<{
      label: string;
      value: string;
    }> = [];
    staffList.forEach((id) => {
      const staff = staffMap.mustGetItem(id);
      result.push({ label: staff.fullName(), value: id.toString() });
    });

    return valueHelpers.stringValueOptions(result);
  }, [staffList, staffMap]);
  const normalizedValues = valueHelpers.stringValues(filter.values);
  const dropdownValue = getMultiSelectLabel({ options, values: normalizedValues });

  return (
    <FilterWrapper type={type} property={property} dropdownValue={dropdownValue} onClear={handleRemove}>
      <CheckboxFilter options={options} value={normalizedValues} onChange={handleChange} />
    </FilterWrapper>
  );
});
