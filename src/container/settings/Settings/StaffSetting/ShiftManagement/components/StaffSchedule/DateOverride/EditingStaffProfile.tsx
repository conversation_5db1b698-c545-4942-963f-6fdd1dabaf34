import { Avatar, Heading, Text, cn } from '@moego/ui';
import { useSelector } from 'amos';
import React from 'react';
import { selectStaffRole } from '../../../../../../../../store/business/role.selectors';
import { staffMapBox } from '../../../../../../../../store/staff/staff.boxes';
import { isNormal } from '../../../../../../../../store/utils/identifier';

export interface CurrentStaffInfoProps {
  staffId: number;
  day: number;
  rangeInfo: React.ReactNode;
}

export interface EditingStaffProfileProps {
  staffId: number;
  className?: string;
}

export function EditingStaffProfile(props: EditingStaffProfileProps) {
  const { staffId, className } = props;
  const [staff, role] = useSelector(staffMapBox.mustGetItem(staffId), selectStaffRole(staffId));

  if (!isNormal(staffId)) {
    return null;
  }

  return (
    <div
      className={cn(
        'moe-flex moe-items-start moe-gap-x-s moe-p-s moe-bg-neutral-sunken-0 moe-rounded-spacing-xs',
        className,
      )}
    >
      <Avatar.Staff src={staff.avatarPath} name={staff.fullName()} color={staff.getColorCode()} size="s" />
      <div className="!moe-flex-1 !moe-min-w-0">
        <Heading size="4">{staff.firstName}</Heading>
        <Text variant="caption">{role?.name}</Text>
      </div>
    </div>
  );
}
