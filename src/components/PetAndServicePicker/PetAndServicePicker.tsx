import { MajorPlusOutlined } from '@moego/icons-react';
import { Button, toast } from '@moego/ui';
import { useStore } from 'amos';
import React from 'react';
import { type QuickAddPetServicePayload } from '../../store/calendarLatest/calendar.types';
import { customerPetListBox, petMapBox } from '../../store/pet/pet.boxes';
import { ID_ANONYMOUS, isNormal } from '../../store/utils/identifier';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { useAddPetFormModal } from '../AddClientWithPets/hooks/useAddPetFormModal';
import { Condition } from '../Condition';
import { useClientPets } from '../PetInfo/hooks/useClientPets';
import { Switch } from '../SwitchCase';
import { AddPetAndServices, type AddPetAndServicesProps } from './components/AddPetAndServices';
import { PetServiceCardList, type PetServiceCardListProps } from './components/PetServiceCardList';
import { PetsStartSameTime, type PetsStartSameTimeProps } from './components/PetsStartSameTime';

export interface PetAndServicePickerProps
  extends AddPetAndServicesProps,
    PetsStartSameTimeProps,
    PetServiceCardListProps {
  clientId: number;
  isStartSameTimeVisible?: boolean;
  priceContent?: (petId: string, serviceId: string, defaultContent: React.ReactNode) => React.ReactNode;
  renderExtraTips?: (petId: string) => React.ReactNode;
  appointmentEndDate?: string;
}

export function PetAndServicePicker(props: PetAndServicePickerProps) {
  const {
    clientId,
    overlay,
    disabledOverlay,
    currentPetIds,
    isStartSameTime,
    isStartSameTimeVisible,
    onAdd,
    onStartSameTimeChange,
    ...rest
  } = props;
  const store = useStore();
  const allAlivePets = useClientPets(clientId);
  const go2AddPet = useAddPetFormModal(clientId);

  const addNewPetServices = useLatestCallback(async (params?: QuickAddPetServicePayload) => {
    if (!isNormal(clientId)) {
      toast({
        title: 'Please select a client first',
        type: 'error',
      });
      return;
    }
    const noAlivePets = allAlivePets.length === 0;
    const disabledPetIds = currentPetIds.map((id) => Number(id));
    const selectedAllAlivePets = allAlivePets.every((petId) => !!disabledPetIds.find((id) => id === petId));

    // 如果一只活着的pet都没有，走新增逻辑
    // 如果活着的pet都被选中了，走新增逻辑
    if (noAlivePets || selectedAllAlivePets) {
      await go2AddPet();
    }

    /** 之前添加过的pet全部不可以再次选择 */
    const payload: QuickAddPetServicePayload = {
      disabledPetIds,
      isMultiplePets: params?.isMultiplePets,
    };
    const clientAllPetIdList = store.select(customerPetListBox.getList(clientId));
    const clientAllAlivePetIds = clientAllPetIdList
      .map((petId) => store.select(petMapBox.mustGetItem(petId)))
      .filter((pet) => pet.isLive());
    const hasOnlyOnePet = clientAllAlivePetIds.size === 1;
    const hasMultiPets = clientAllAlivePetIds.size > 1;

    const firstPetId = hasOnlyOnePet ? clientAllAlivePetIds.get(0)!.petId : ID_ANONYMOUS;
    if (hasOnlyOnePet && isNormal(firstPetId)) {
      payload.petId = firstPetId;
    }
    if (hasMultiPets) {
      payload.preventAutoSelectPet = true;
    }
    onAdd?.(payload);
  });

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-[16px]">
      <Switch>
        <Switch.Case if={currentPetIds.length}>
          <PetServiceCardList {...rest} currentPetIds={currentPetIds} onAdd={onAdd} />
          <Condition if={!rest.disabled}>
            <div className="moe-flex moe-justify-between moe-items-center">
              <Button
                isDisabled={!onAdd}
                variant="tertiary"
                align="start"
                icon={<MajorPlusOutlined />}
                onPress={() => addNewPetServices({ isMultiplePets: false })}
              >
                Add pet
              </Button>
              <Condition if={isStartSameTimeVisible}>
                <PetsStartSameTime
                  isDisabled={!onStartSameTimeChange}
                  isStartSameTime={isStartSameTime}
                  onStartSameTimeChange={onStartSameTimeChange}
                />
              </Condition>
            </div>
          </Condition>
        </Switch.Case>
        <Switch.Case else>
          <AddPetAndServices overlay={overlay} disabledOverlay={disabledOverlay} onClick={addNewPetServices} />
        </Switch.Case>
      </Switch>
    </div>
  );
}
