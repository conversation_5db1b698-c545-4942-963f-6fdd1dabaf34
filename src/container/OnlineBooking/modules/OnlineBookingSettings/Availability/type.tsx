import range from 'lodash/range';
import { type TimePeriod } from '../../../../../components/WeekTimeScheduleShiftManagement/types';
import { type FullWeekDay } from '../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { createEnum } from '../../../../../store/utils/createEnum';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';

export const AvailabilityTabType = createEnum({
  Grooming: [ServiceItemType.GROOMING, { needBDEnable: false, panel: 'Grooming' }],
  Boarding: [ServiceItemType.BOARDING, { needBDEnable: true, panel: 'Boarding' }],
  Daycare: [ServiceItemType.DAYCARE, { needBDEnable: true, panel: 'Daycare' }],
  Evaluation: [ServiceItemType.EVALUATION, { needBDEnable: true, panel: 'Evaluation' }],
} as const);

export type AvailabilityTabKeyType = (typeof AvailabilityTabType.keys)[number];

export const OB_TIME_SLOTS = [...range(5, 61, 5), ...range(90, 241, 30)];

export type CustomizeArrivalPickupTimeMap = {
  [key in FullWeekDay]?: {
    isEnable: boolean;
    timePeriod: TimePeriod[];
  };
};
