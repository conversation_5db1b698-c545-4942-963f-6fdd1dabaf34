import { Alert } from '@moego/ui';
import React, { memo } from 'react';
import { useQuery } from '../../../../../store/utils/useQuery';
import { getListAllTimeSlots } from '../../../store/appt.actions';
import { currentBusinessIdBox } from '../../../../../store/business/business.boxes';
import { useSelector } from 'amos';
import { selectServiceDetailInAppt } from '../../../store/appt.selectors';
import { CreateApptId } from '../../../store/appt.types';
import { TimeSlotType } from '@moego/api-web/moego/models/smart_scheduler/v1/time_slot_enums';
import { createEnum } from '../../../../../store/utils/createEnum';
import { useGetPetsInfoForApi } from '../../../hooks/useGetPetsInfoForApi';
import { getGroomingServiceAvailability } from '../../../../../store/onlineBooking/actions/private/onlineBookingSettings.actions';
import { TimeAvailabilityType } from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { selectIsEnableSlotCalender } from '../../../../../store/calendarLatest/calendar.selectors';

export interface BySlotWarningBannerProps {}

/**
 * 1. FULLY_BOOKED: 已满
 * 2. breed 不满足: 品种不满足
 * 3. size 不满足: 大小不满足
 * 4. service 不满足: 服务不满足
 */
const TimeSlotWarningLimitationTypesEnum = createEnum({
  fullyBooked: [
    TimeSlotType.FULLY_BOOKED,
    {
      text: 'count',
    },
  ],
  petBreedLimitationNotMet: [
    TimeSlotType.PET_BREED_LIMITATION_NOT_MET,
    {
      text: 'breed',
    },
  ],
  petSizeLimitationNotMet: [
    TimeSlotType.PET_SIZE_LIMITATION_NOT_MET,
    {
      text: 'size',
    },
  ],
  serviceLimitationNotMet: [
    TimeSlotType.SERVICE_LIMITATION_NOT_MET,
    {
      text: 'service',
    },
  ],
});

export const BySlotWarningBanner = memo<BySlotWarningBannerProps>(() => {
  const [businessId, petServiceDetail, isEnableSlotCalender] = useSelector(
    currentBusinessIdBox,
    selectServiceDetailInAppt(CreateApptId),
    selectIsEnableSlotCalender,
  );
  const getPetsInfo = useGetPetsInfoForApi();
  const hasGroomingService = petServiceDetail.some((p) =>
    p.services.some((s) => s.serviceItemType === ServiceItemType.GROOMING),
  );

  const { value, loading } = useQuery(
    getListAllTimeSlots({
      businessId: businessId.toString(),
      petDetails: getPetsInfo(CreateApptId),
    }),
    !!petServiceDetail.length && isEnableSlotCalender && hasGroomingService,
  );
  const { value: availabilityValue } = useQuery(
    getGroomingServiceAvailability({ businessId: businessId.toString() }),
    !!petServiceDetail.length && isEnableSlotCalender && hasGroomingService,
  );

  if (
    !value?.timeSlots?.length ||
    loading ||
    !isEnableSlotCalender ||
    availabilityValue?.timeAvailabilityType !== TimeAvailabilityType.TIME_SLOT
  )
    return null;

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-xs">
      {value.timeSlots.map((timeSlot, index) => {
        if (!TimeSlotWarningLimitationTypesEnum.values.includes(timeSlot.timeSlotType)) return null;

        const warningText =
          TimeSlotWarningLimitationTypesEnum.mapLabels[
            timeSlot.timeSlotType as keyof typeof TimeSlotWarningLimitationTypesEnum.mapLabels
          ].text;

        // TODO: 后端返回 pet 的逻辑还有问题，暂时先不展示这么详细
        // const getPetName = () => {
        //   if (timeSlot.previewBookedInfos.length > 1) {
        //     return `${timeSlot.previewBookedInfos.map((info) => info.petName).join(', ')}'s`;
        //   } else if (timeSlot.previewBookedInfos.length) {
        //     return `${timeSlot.previewBookedInfos[0].petName}'s`;
        //   } else {
        //     return 'The scheduled pet';
        //   }
        // };

        return (
          <Alert key={index} isRounded color="warning">
            The scheduled pet {warningText} exceeds this slot’s limit.
          </Alert>
        );
      })}
    </div>
  );
});

BySlotWarningBanner.displayName = 'BySlotWarningBanner';
