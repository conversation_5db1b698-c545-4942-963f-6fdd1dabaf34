import { type Action, useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import { isNumber, isUndefined } from 'lodash';
import { type ReactNode, useCallback, useEffect, useMemo } from 'react';
import {
  type ComMoegoCommonParamsFilterParamsOperator as FilterParamsOperator,
  type ComMoegoCommonParamsFilterParamsProperty as FilterParamsProperty,
} from '../../../../../openApi/customer-schema';
import { currentBusinessIdBox } from '../../../../../store/business/business.boxes';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { locationMapBox } from '../../../../../store/business/location.boxes';
import { selectAllLocationIdList } from '../../../../../store/business/location.selectors';
import { getReferralSourceList } from '../../../../../store/business/referralSource.actions';
import { referralSourceMapBox } from '../../../../../store/business/referralSource.boxes';
import { selectBusinessReferralSources } from '../../../../../store/business/referralSource.selectors';
import { type CustomerSmartListFilterParams } from '../../../../../store/customer/customer.boxes';
import { getCustomerTagList } from '../../../../../store/customer/customerTag.actions';
import { customerTagMapBox } from '../../../../../store/customer/customerTag.boxes';
import { selectBusinessCustomerTags } from '../../../../../store/customer/customerTag.selectors';
import { getPetSizeList } from '../../../../../store/onlineBooking/actions/private/petSize.actions';
import { petSizeRawMapBox } from '../../../../../store/onlineBooking/settings/petSize.boxes';
import { selectRawPetSizeIds } from '../../../../../store/onlineBooking/settings/petSize.selectors';
import { getPetOptions } from '../../../../../store/pet/pet.actions';
import { petCodeMapBox } from '../../../../../store/pet/petCode.boxes';
import { selectBusinessPetCodes } from '../../../../../store/pet/petCode.selectors';
import { petHairLengthMapBox } from '../../../../../store/pet/petHairLength.boxes';
import { selectBusinessPetHairLengths } from '../../../../../store/pet/petHairLength.selectors';
import { selectPetTypeOptions } from '../../../../../store/pet/petType.selectors';
import { getBusinessServiceArea } from '../../../../../store/serviceArea/serviceArea.actions';
import { serviceAreaMapBox } from '../../../../../store/serviceArea/serviceArea.boxes';
import { selectBusinessServiceAreaIdList } from '../../../../../store/serviceArea/serviceArea.selectors';
import { getStaffList } from '../../../../../store/staff/staff.actions';
import { staffMapBox } from '../../../../../store/staff/staff.boxes';
import { selectBusinessStaffs } from '../../../../../store/staff/staff.selectors';
import { isNormal } from '../../../../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE } from '../../../../../utils/DateTimeUtil';
import { renderCountableNounPlurals } from '../../../../../utils/utils';
import {
  AppointmentDateOptionsMap,
  COFRequestOptions,
  COFStatusOptions,
  FilterPropertyConfigMap,
  OperatorKeyMap,
  OperatorMap,
} from './ClientFilters.config';
import { useMultiSelectOptions } from './ClientFilters.hooks';
import {
  type TransformFilterValuesFilter,
  getMultiSelectLabel,
  getMultiSelectLabelList,
  getRadioLabel,
  isValidFilterValue,
  transformFilterValues,
} from './ClientFilters.utils';

const useDynamicFilterOptions = () => {
  const [businessId] = useSelector(currentBusinessIdBox);
  const [
    business,
    staffList,
    tagList,
    petCodeList,
    petSizeList,
    petHairLengthList,
    sourceList,
    serviceAreaList,
    locationIdList,
    staffMap,
    tagMap,
    petCodeMap,
    petSizeMap,
    petHairLengthMap,
    sourceMap,
    serviceAreaMap,
    locationMap,
  ] = useSelector(
    selectCurrentBusiness(),
    selectBusinessStaffs(),
    selectBusinessCustomerTags(),
    selectBusinessPetCodes(),
    selectRawPetSizeIds(),
    selectBusinessPetHairLengths(),
    selectBusinessReferralSources(),
    selectBusinessServiceAreaIdList(businessId),
    selectAllLocationIdList(),
    staffMapBox,
    customerTagMapBox,
    petCodeMapBox,
    petSizeRawMapBox,
    petHairLengthMapBox,
    referralSourceMapBox,
    serviceAreaMapBox,
    locationMapBox,
  );

  const dispatch = useDispatch();

  const listActionMap = useMemo(
    () => [
      { list: staffList, action: getStaffList },
      { list: tagList, action: getCustomerTagList },
      { list: petSizeList, action: getPetSizeList },
      { list: petCodeList, action: getPetOptions },
      { list: sourceList, action: getReferralSourceList },
      { list: serviceAreaList, action: getBusinessServiceArea },
    ],
    [],
  );

  useEffect(() => {
    if (isNormal(business.id)) {
      const actionListForGetList: Action<Promise<any>>[] = [];
      listActionMap.forEach(({ list, action }) => {
        if (list.size === 0) {
          actionListForGetList.push(action(business.id));
        }
      });
      dispatch(actionListForGetList);
    }
  }, [business.id]);

  const clientTagOptions = useMultiSelectOptions(tagList, tagMap, 'name');
  const preferredGroomerOptions = useMultiSelectOptions(
    staffList.filter((staffId) => {
      const staff = staffMap.mustGetItem(staffId);
      return staff.showOnCalendar;
    }),
    staffMap,
    'fullName',
  );
  const daysOfWeekOptions = useMemo(() => business.daysOfWeekOptionList(), [business]);
  const petCodeOptions = useMultiSelectOptions(petCodeList, petCodeMap, 'description');
  const petWeightOptions = useMultiSelectOptions(petSizeList, petSizeMap, 'name');
  const petHairLengthOptions = useMultiSelectOptions(
    petHairLengthList,
    petHairLengthMap,
    'name',
    (hairLengthRecord) => hairLengthRecord.name,
  );
  const referralSourceOptions = useMultiSelectOptions(sourceList, sourceMap, 'sourceName');
  const serviceAreaOptions = useMultiSelectOptions(serviceAreaList, serviceAreaMap, 'areaName');
  const preferredBusinessOptions = useMultiSelectOptions(locationIdList, locationMap, 'name');

  return useMemo(
    () => ({
      clientTagOptions,
      preferredGroomerOptions,
      daysOfWeekOptions,
      petCodeOptions,
      petWeightOptions,
      petHairLengthOptions,
      referralSourceOptions,
      serviceAreaOptions,
      preferredBusinessOptions,
    }),
    [
      referralSourceOptions,
      petHairLengthOptions,
      petWeightOptions,
      petCodeOptions,
      daysOfWeekOptions,
      preferredGroomerOptions,
      clientTagOptions,
      serviceAreaOptions,
      preferredBusinessOptions,
    ],
  );
};

// TODO(yueyue:p1): refactor, filter item更加原子化，把编辑和回显相关的逻辑统一起来
export const useDisplayBEFilters = () => {
  const [business, petTypeOptions] = useSelector(selectCurrentBusiness(), selectPetTypeOptions());

  const dynamicFilterOptions = useDynamicFilterOptions();
  const {
    clientTagOptions,
    preferredGroomerOptions,
    daysOfWeekOptions,
    petCodeOptions,
    petWeightOptions,
    petHairLengthOptions,
    referralSourceOptions,
    serviceAreaOptions,
    preferredBusinessOptions,
  } = dynamicFilterOptions;

  const dynamicFilterOptionsMap = useMemo(
    () => ({
      [FilterPropertyConfigMap.ClientTag]: clientTagOptions,
      [FilterPropertyConfigMap.PreferredGroomer]: preferredGroomerOptions,
      [FilterPropertyConfigMap.LastApptGroomer]: preferredGroomerOptions,
      [FilterPropertyConfigMap.PetCode]: petCodeOptions,
      [FilterPropertyConfigMap.PetWeight]: petWeightOptions,
      [FilterPropertyConfigMap.PetHairLength]: petHairLengthOptions,
      [FilterPropertyConfigMap.ReferralSource]: referralSourceOptions,
      [FilterPropertyConfigMap.ServiceArea]: serviceAreaOptions,
      [FilterPropertyConfigMap.PreferredBusiness]: preferredBusinessOptions,
    }),
    [
      clientTagOptions,
      preferredGroomerOptions,
      petCodeOptions,
      petWeightOptions,
      petHairLengthOptions,
      referralSourceOptions,
      serviceAreaOptions,
      preferredBusinessOptions,
    ],
  );

  return useCallback(
    (beFilters: CustomerSmartListFilterParams[], isDisplayFullText?: boolean) => {
      const petBreedValues = beFilters.find((filter) => filter.property === FilterPropertyConfigMap.PetBreed)?.values;

      return beFilters
        .map((filter) => {
          const { operator } = filter;
          filter = {
            ...filter,
            operator: (operator ? OperatorKeyMap[operator] : '') as FilterParamsOperator,
          };
          const {
            property,
            value: filterValue,
            values: filterValues,
            operator: filterOperator,
            isCustom,
          } = transformFilterValues(filter) as TransformFilterValuesFilter;
          const { title, options = [] } = FilterPropertyConfigMap.mapLabels[property as FilterParamsProperty] || {};
          let displayValue: ReactNode = '';
          let displayValues: ReactNode[] | undefined = undefined;
          switch (property) {
            case FilterPropertyConfigMap.ClientStatus:
            case FilterPropertyConfigMap.HasCardOnFile:
              displayValue = getRadioLabel({ options, value: filterValue! });
              break;
            case FilterPropertyConfigMap.ClientType:
            case FilterPropertyConfigMap.ClientCreatedFrom:
            case FilterPropertyConfigMap.PetVaccine:
            case FilterPropertyConfigMap.Zipcode:
            case FilterPropertyConfigMap.PerformanceHistory:
              displayValue = getMultiSelectLabel({ options, values: filterValues, isDisplayFullText });
              displayValues = getMultiSelectLabelList({ options, values: filterValues });
              break;
            case FilterPropertyConfigMap.ClientTag:
            case FilterPropertyConfigMap.PreferredGroomer:
            case FilterPropertyConfigMap.LastApptGroomer:
            case FilterPropertyConfigMap.PetCode:
            case FilterPropertyConfigMap.PetWeight:
            case FilterPropertyConfigMap.PetHairLength:
            case FilterPropertyConfigMap.ReferralSource:
            case FilterPropertyConfigMap.ServiceArea:
            case FilterPropertyConfigMap.PreferredBusiness:
              displayValue = getMultiSelectLabel({
                options: dynamicFilterOptionsMap[property as FilterParamsProperty],
                values: filterValues,
                isDisplayFullText,
              });
              displayValues = getMultiSelectLabelList({
                options: dynamicFilterOptionsMap[property],
                values: filterValues,
              });
              break;
            case FilterPropertyConfigMap.PetCnt:
            case FilterPropertyConfigMap.UpcomingApptCnt:
            case FilterPropertyConfigMap.UnpaidInvoiceCnt:
            case FilterPropertyConfigMap.AddressCnt:
            case FilterPropertyConfigMap.EmailCnt:
            case FilterPropertyConfigMap.ReviewCnt:
            case FilterPropertyConfigMap.ReviewRating:
              const radioValue = `${filterValue}-${filterOperator}`;
              displayValue = isValidFilterValue(filterValue)
                ? isCustom
                  ? `${filterOperator} ${filterValue}`
                  : getRadioLabel({ options, value: radioValue })
                : '';
              break;
            case FilterPropertyConfigMap.FirstApptDate:
            case FilterPropertyConfigMap.LastApptDate:
            case FilterPropertyConfigMap.Creationdate:
              const operatorLabel = AppointmentDateOptionsMap.mapLabels[
                isNumber(filterValue) ? AppointmentDateOptionsMap.ReverseRelative : AppointmentDateOptionsMap.Absolute
              ].optionList.find((optionItem) => optionItem.value === filterOperator)?.label;
              displayValue = isUndefined(filterValue)
                ? ''
                : `${operatorLabel} ${
                    isNumber(filterValue)
                      ? `${renderCountableNounPlurals(filterValue, 'day')} ago`
                      : dayjs(filterValue).format(business.dateFormat)
                  }`;
              break;
            case FilterPropertyConfigMap.NextApptDate:
              displayValue = isUndefined(filterValue)
                ? ''
                : `${filterOperator} ${
                    isNumber(filterValue)
                      ? `${renderCountableNounPlurals(filterValue, 'day')} from now`
                      : dayjs(filterValue).format(business.dateFormat)
                  }`;
              break;
            case FilterPropertyConfigMap.TotalApptCnt:
            case FilterPropertyConfigMap.TotalPaid:
              const amount =
                property === FilterPropertyConfigMap.TotalPaid
                  ? `${business.currencySymbol}${filterValue}`
                  : filterValue;
              displayValue = isValidFilterValue(filterValue) ? `${filter.operator} ${amount}` : '';
              break;
            case FilterPropertyConfigMap.PreferredWeekday:
              const isAll = filterValues?.length === options.length;
              displayValue = isAll
                ? 'Everyday'
                : daysOfWeekOptions
                    .filter((option) => filterValues?.includes(option.value))
                    .map((option) => option.simpleLabel)
                    .join(', ');
              break;
            case FilterPropertyConfigMap.PetType:
              const petTypeLabel = petTypeOptions.find((petTypeOption) => petTypeOption.value === filterValue)?.label;

              const petBreedValuesLength = petBreedValues?.length ?? 0;
              const petBreedLabel =
                petBreedValuesLength > 1 ? `${petBreedValuesLength} breeds` : (petBreedValues?.[0] ?? '');

              displayValue = petTypeLabel ? `${petTypeLabel}${petBreedLabel ? ` / ${petBreedLabel}` : ''}` : '';
              break;

            // TODO(yueyue): temporary solution, need to refactor in new filter list.
            case FilterPropertyConfigMap.CardOnFileStatus:
              if (isUndefined(filterOperator) || filterOperator === '') {
                displayValue = '';
                break;
              }
              const status =
                filterOperator === OperatorMap.LessThan
                  ? COFStatusOptions.ExpiringIn
                  : COFStatusOptions.values.find((v) => v === filterValue);
              const { label } = COFStatusOptions.mapLabels[status!];
              const shouldDisplaySuffix = filterOperator !== OperatorMap.EqualTo;
              const suffix = `${(filterValue as number) > 1 ? 'months' : 'month'}`;
              displayValue = `${label} ${filterValue} ${shouldDisplaySuffix ? suffix : ''}`;
              break;
            case FilterPropertyConfigMap.CardOnFileRequest:
              if (isUndefined(filterOperator) || filterOperator === '') {
                displayValue = '';
                break;
              }

              if (!Array.isArray(filterValues) || filterValues.length !== 2) {
                displayValue = '';
                break;
              }

              const requestValue = COFRequestOptions.values.find(
                (v) => COFRequestOptions.mapLabels[v].operator === filterOperator,
              );
              const requestDetail = COFRequestOptions.mapLabels[requestValue!];
              displayValue = `${requestDetail?.label} between ${dayjs(filterValues[0]).format(
                DATE_FORMAT_EXCHANGE,
              )} and ${dayjs(filterValues[1]).format(DATE_FORMAT_EXCHANGE)}`;
              break;
          }
          return {
            title,
            value: displayValue,
            values: displayValues || [displayValue],
            operator,
          };
        })
        .filter(({ title }) => title);
    },
    [dynamicFilterOptions],
  );
};
