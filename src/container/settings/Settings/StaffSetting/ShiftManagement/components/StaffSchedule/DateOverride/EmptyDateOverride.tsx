import React from 'react';
import IconCalendarCheck from '../../../../../../../../assets/svg/calendar-schedule-check.svg';
import { SvgIcon } from '../../../../../../../../components/Icon/Icon';

export function EmptyDateOverride() {
  return (
    <div className="!moe-w-[280px] !moe-text-center !moe-mx-auto !moe-pt-[55px] !moe-pb-[40px]">
      <SvgIcon src={IconCalendarCheck} style={{ fontSize: 24, color: '#888C96' }} />
      <div className="!moe-mt-[8px] !moe-font-medium !moe-text-[14px] !moe-text-[#999] !moe-leading-[18px]">
        Add dates when your availability changes from your regular working hours.
      </div>
    </div>
  );
}
