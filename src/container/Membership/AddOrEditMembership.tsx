import { CalendarPeriod } from '@moego/api-web/google/type/calendar_period';
import { PeriodType } from '@moego/api-web/moego/models/membership/v1/membership_defs';
import { MembershipModelStatus } from '@moego/api-web/moego/models/membership/v1/membership_models';
import { DiscountUnit } from '@moego/api-web/moego/models/membership/v1/redeem_models';
import { Heading, useForm, useFormState } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect } from 'react';
import { useHistory } from 'react-router';
import { useMount } from 'react-use';
import { FullPageLayout } from '../../layout/FullPageLayout';
import { PATH_MEMBERSHIP } from '../../router/paths';
import { getMembership } from '../../store/membership/membership.actions';
import { type MembershipModel, companyMembershipMapBox } from '../../store/membership/membership.boxes';
import { isNormal } from '../../store/utils/identifier';
import { useRouteQueryV2 } from '../../utils/RoutePath';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../utils/hooks/useSerialCallback';
import { useUnsavedConfirmGlobalV2 } from '../../utils/hooks/useUnsavedConfirmGlobalV2';
import { MembershipForm } from './components/MembershipForm';
import { BreedFilter, CoatTypeFilter, PetSizeFilter } from './consts';
import { useMembershipFormIsEditMode } from './hooks/useMembershipFormMode';
import { useSaveMembership } from './hooks/useSaveMembership';
import { handleEditMembershipDoubleConfirm } from './utils/handleEditMembershipDoubleConfirm';

export const AddOrEditMembership = memo(function AddOrEditMembership() {
  const history = useHistory();
  const dispatch = useDispatch();
  const form = useForm<MembershipModel>({
    mode: 'all',
  });
  const [isEditMode] = useMembershipFormIsEditMode();
  const { id } = useRouteQueryV2(PATH_MEMBERSHIP);
  const [companyMembershipMap] = useSelector(companyMembershipMapBox);
  const title = isEditMode ? 'Edit membership' : 'Add new membership';
  const saveMembership = useSaveMembership(isEditMode);
  const handleGoBack = useLatestCallback(() => {
    history.push(PATH_MEMBERSHIP.build({ type: 'list' }));
  });

  const handleGetMembership = useLatestCallback(() => {
    if (id) {
      dispatch(getMembership(id));
    }
  });

  const handleClose = useLatestCallback(() => {
    handleGoBack();
  });

  useEffect(() => {
    if (isEditMode && id) {
      const membership = companyMembershipMap.mustGetItem(id).toJSON();
      if (isNormal(membership.id)) {
        form.reset(membership);
      }
    } else {
      form.reset({
        status: MembershipModelStatus.ACTIVE,
        billingCyclePeriod: {
          period: CalendarPeriod.MONTH,
          value: 1,
        },
        includeServiceExpired: PeriodType.FOLLOW_MEMBERSHIP,
        includeServiceExpiredValue: 1,
        includeServiceExpiredUnit: CalendarPeriod.MONTH,
        serviceDiscounts: [{ unit: DiscountUnit.PERCENT }],
        addOnDiscounts: [{ unit: DiscountUnit.PERCENT }],
        productDiscounts: [{ unit: DiscountUnit.PERCENT }],
        packageDiscounts: [{ unit: DiscountUnit.PERCENT }],
        breedFilter: BreedFilter.All,
        petSizeFilter: PetSizeFilter.All,
        coatFilter: CoatTypeFilter.All,
        customizedBreed: [],
        customizedPetSizes: [],
        customizedCoat: [],
      });
    }
  }, [isEditMode, id, companyMembershipMap]);

  useMount(() => {
    handleGetMembership();
  });

  const { isDirty, dirtyFields } = useFormState({
    control: form.control,
  });

  const handleSave = useSerialCallback(async () => {
    return form.handleSubmit(async (values) => {
      if (isEditMode) {
        const hasStatusChange = !!dirtyFields?.status;
        const isInactive = values.status === MembershipModelStatus.INACTIVE;
        const isNeedConfirmForNextBillingCycle = hasStatusChange || !!dirtyFields.price || !!dirtyFields.taxId;
        if (isNeedConfirmForNextBillingCycle) {
          const confirm2Save = await handleEditMembershipDoubleConfirm(hasStatusChange && isInactive);
          if (!confirm2Save) {
            return;
          }
        }
      }
      try {
        await saveMembership(values);
        form.reset(undefined, { keepValues: true, keepDirty: false, keepDefaultValues: false });
        setTimeout(() => {
          handleGoBack();
          // 等一下 toast 动画
        }, 100);
      } catch (e) {}
    })();
  });

  useUnsavedConfirmGlobalV2({
    showConfirm: isDirty,
    modalProps: {
      title: 'Unsaved changes',
      content: 'You have unsaved changes. Are you sure you’re ready to leave?',
      onConfirm: () => {
        // 这个 CTA 是留在当前页面，所以抛个 error 中断后续流程
        throw Error('Back to edit');
      },
      onCancel: handleGoBack,
      confirmText: 'Back to edit',
      cancelText: 'Leave anyway',
    },
  });

  return (
    <FullPageLayout onClose={handleClose} onSave={handleSave}>
      <div className="moe-w-full moe-flex moe-justify-center moe-mb-xl moe-mt-s">
        <div className="moe-w-[900px]">
          <Heading size="2" className="moe-mb-xl">
            {title}
          </Heading>
          <MembershipForm form={form} />
        </div>
      </div>
    </FullPageLayout>
  );
});
