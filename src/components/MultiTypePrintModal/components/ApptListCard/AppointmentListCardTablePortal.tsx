import { cn } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { BACKEND_DEFAULT_AREA_ID, DEFAULT_AREA_NAME } from '../../../../store/calendarLatest/calendar_data.utils';
import {
  type ApptListPrintCardInfo,
  type ApptListPrintCardPetDetail,
} from '../../../../store/printCard/appointmentList/appointmentList.actions';
import { selectApptListSettings } from '../../../../store/printCard/appointmentList/appointmentList.selectors';
import { selectSceneCareType } from '../../../../store/careType/careType.selectors';
import { Scene } from '../../../../store/service/scene.enum';
import { Switch } from '../../../SwitchCase';
import { LodgingTypeHeader } from '../common/ListCard/LodgingTypeHeader';
import { AppointmentListCardTable } from './AppointmentListCardTable';
import { lodgingTypeSortCompareFn } from '../../hooks/useLodgingTypeSort';

export const AppointmentListCardTablePortal = memo<ApptListPrintCardInfo>((props) => {
  const { serviceItemType, petDetailsByCareType, lodgingTypes, lodgingUnits } = props;
  const [apptListSettings, enableLodgingCareTypes] = useSelector(
    selectApptListSettings,
    selectSceneCareType(Scene.EnableLodging),
  );
  const showLodgingBreakdown = apptListSettings?.lodgingBreakdown?.showLodgingBreakdown;

  const lodgingBreakdownData = useMemo(() => {
    if (!showLodgingBreakdown) {
      return [];
    }

    // Create a map to group by lodgingTypeId
    // map key 的值: '0' 代表 default area，undefined 是 grooming 等无 lodging 的情况，业务不会用上，代码逻辑上允许 undefined 作为 key 的存在
    const lodgingMap = new Map<string | undefined, ApptListPrintCardPetDetail[]>();

    for (const item of petDetailsByCareType) {
      const { petServices, appointment, pet } = item;

      for (const petService of petServices) {
        const { lodgingTypeId } = petService;

        if (!lodgingMap.has(lodgingTypeId)) {
          lodgingMap.set(lodgingTypeId, []);
        }

        const lodgingBreakdown = lodgingMap.get(lodgingTypeId);
        if (lodgingBreakdown?.find((item) => item.appointment.id === appointment.id && item.pet.id === pet.id)) {
          continue;
        }

        lodgingMap.get(lodgingTypeId)?.push({
          petServices,
          appointment,
          pet,
        });
      }
    }

    // Convert the map to the desired output format
    const lodgingBreakdownList = Array.from(lodgingMap.entries())
      .map(([lodgingTypeId, petDetailsByCareType]) => ({
        lodgingTypeId,
        lodgingTypeSort: lodgingTypes?.find((item) => item.id === lodgingTypeId)?.sort,
        petDetailsByCareType,
      }))
      .sort(lodgingTypeSortCompareFn);

    return lodgingBreakdownList;
  }, [petDetailsByCareType, showLodgingBreakdown, lodgingTypes]);

  return (
    <Switch>
      <Switch.Case if={showLodgingBreakdown && enableLodgingCareTypes.includes(serviceItemType)}>
        {lodgingBreakdownData.map(({ lodgingTypeId, petDetailsByCareType }, index) => {
          const {
            name = DEFAULT_AREA_NAME,
            occupiedPetNum = 0,
            maxPetTotalNum,
          } = lodgingTypes?.find((item) => item.id === lodgingTypeId) ?? {};
          const occupancy = maxPetTotalNum
            ? `(${occupiedPetNum}/${maxPetTotalNum})`
            : `(${petDetailsByCareType.length})`;

          return (
            <div key={lodgingTypeId ?? BACKEND_DEFAULT_AREA_ID}>
              <LodgingTypeHeader
                className={cn({
                  'moe-border-t-0': !!index,
                })}
                title={`${name} ${occupancy}`}
              />
              <AppointmentListCardTable
                serviceItemType={serviceItemType}
                petDetailsByCareType={petDetailsByCareType}
                lodgingUnits={lodgingUnits}
                lodgingTypes={lodgingTypes}
              />
            </div>
          );
        })}
      </Switch.Case>
      <Switch.Case else>
        <AppointmentListCardTable
          serviceItemType={serviceItemType}
          petDetailsByCareType={petDetailsByCareType}
          lodgingUnits={lodgingUnits}
          lodgingTypes={lodgingTypes}
        />
      </Switch.Case>
    </Switch>
  );
});
