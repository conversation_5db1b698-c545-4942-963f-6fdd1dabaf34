import { Text } from '@moego/ui';
import React, { useState } from 'react';
import { useBool } from '../../../../../../../utils/hooks/useBool';
import { type PetDetailType } from '../../../type';
import { DiffItem } from './DiffItem';
import { DiffVaccineItem } from './DiffVaccineItem';

export interface DiffVaccineProps<T = PetDetailType['vaccineList'][0]> {
  oldVaccine?: T;
  newVaccine?: T;
  vaccineBindingId: number;
  title: string;
  isError?: boolean;
  onChange?: (v: boolean, ov?: T, n?: T) => void;
}

export const DiffVaccine = (props: DiffVaccineProps) => {
  const { title, vaccineBindingId, oldVaccine, isError, newVaccine, onChange } = props;
  const checked = useBool();
  const [vaccineForm, setVaccineForm] = useState(newVaccine);

  const getDiffNode = (v?: PetDetailType['vaccineList'][number], isOld = false) => {
    if (!v) {
      return {
        value: { vaccineBindingId },
        node: isOld ? <Text variant="small">No record</Text> : null,
      };
    }
    return {
      value: v,
      node: (
        <DiffVaccineItem
          checked={checked.value}
          vaccine={v}
          readonly={isOld}
          isError={isError}
          onChange={(newVal) => {
            setVaccineForm(newVal);
            onChange?.(checked.value, oldVaccine, newVal);
          }}
        />
      ),
    };
  };

  const oldNode = getDiffNode(oldVaccine, true);
  const newNode = getDiffNode(newVaccine);

  return (
    <DiffItem
      title={title}
      oldNode={oldNode}
      newNode={newNode}
      onChange={(v, o) => {
        checked.as(v);
        onChange?.(v, o.value, vaccineForm);
      }}
    />
  );
};
