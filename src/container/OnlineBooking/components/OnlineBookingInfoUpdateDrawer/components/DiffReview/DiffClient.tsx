import { useSelector } from 'amos';
import React, { memo, useCallback, useMemo, useState } from 'react';
import { printFullName } from '../../../../../../store/customer/customer.boxes';
import { OnlineBookingQuestionCategory } from '../../../../../../store/onlineBooking/models/OBQuestion.base';
import { onlineBookingQuestionMapBox } from '../../../../../../store/onlineBooking/settings/questions.boxes';
import { selectOnlineBookingQuestionIdList } from '../../../../../../store/onlineBooking/settings/questions.selectors';
import { type CustomerDetailType, type CustomerPetsUpdateParams, type ReturnNodeAndValue } from '../../type';
import { getPrimaryAddressRenderNode, useGetClientRenderNode, useGetCustomRenderNode } from './DiffReview.utils';
import { DiffHeader } from './components/DiffHeader';
import { DiffItem } from './components/DiffItem';
import { DiffNewAddress } from './components/DiffNewAddress';

export interface DiffClientProps {
  oldVal: CustomerDetailType;
  newVal: CustomerDetailType;
  onChange?: (v: CustomerPetsUpdateParams) => void;
}

export const DiffClient = memo<DiffClientProps>(({ oldVal, newVal, onChange }) => {
  const [visible, setVisible] = useState(false);
  const [questionMap, questionList] = useSelector(
    onlineBookingQuestionMapBox,
    selectOnlineBookingQuestionIdList(OnlineBookingQuestionCategory.ForOwner),
  );
  const getClientRenderNode = useGetClientRenderNode();
  const getCustomRenderNode = useGetCustomRenderNode();
  const clientName = printFullName(oldVal.firstName, oldVal.lastName);

  const handleChange = useCallback((isNew, oldNode, newNode, isCustomQuestion) => {
    setVisible(true);
    if (isCustomQuestion) {
      onChange?.({
        clientCustomQuestionMap: isNew ? newNode?.value : oldNode?.value,
      });
    } else {
      onChange?.({
        customer: isNew ? newNode?.value : oldNode?.value,
      });
    }
  }, []);

  const handleChangePrimaryAddress = useCallback((isNew, oldNode, newNode) => {
    setVisible(true);
    onChange?.(isNew ? newNode?.value : oldNode?.value);
  }, []);

  const diffItemList = useMemo(() => {
    const diffNodes: {
      type: 'question' | 'primaryAddress' | 'newAddresses';
      key: React.Key;
      title: string;
      newNode: ReturnNodeAndValue;
      oldNode: ReturnNodeAndValue;
      isCustomQuestion?: boolean;
    }[] = [];

    // general question diff
    questionList
      .map((id) => questionMap.mustGetItem(id))
      .sort((a, b) => b.sort - a.sort)
      .forEach((item) => {
        const { isCustomQuestion, key } = item;

        const oV = oldVal.questionAnswerList?.find((v) => v.key === item.key);
        const nV = newVal.questionAnswerList?.find((v) => v.key === item.key);

        const oldNode = isCustomQuestion ? getCustomRenderNode(item, oV, nV) : getClientRenderNode(key, oldVal);
        const newNode = isCustomQuestion ? getCustomRenderNode(item, nV, oV) : getClientRenderNode(key, newVal);
        diffNodes.push({
          type: 'question',
          key: item.id,
          title: item.question,
          newNode,
          oldNode,
          isCustomQuestion,
        });
      });

    diffNodes.push({
      type: 'primaryAddress',
      key: 'primaryAddress',
      title: 'Primary Address',
      newNode: getPrimaryAddressRenderNode(newVal),
      oldNode: getPrimaryAddressRenderNode(oldVal),
    });

    if (newVal?.newAddresses?.length) {
      diffNodes.push({
        type: 'newAddresses',
        key: 'newAddresses',
        title: '',
        newNode: { node: true, value: {} },
        oldNode: { node: true, value: {} },
      });
    }

    return diffNodes.filter((item) => {
      const { oldNode, newNode } = item;
      const hidden = newNode.node == null || JSON.stringify(oldNode.value) === JSON.stringify(newNode.value);
      return !hidden;
    });
  }, [getClientRenderNode, getCustomRenderNode, newVal, oldVal, questionList, questionMap]);

  if (!diffItemList.length) return null;

  return (
    <div className="">
      {visible && <DiffHeader type="customer">Updates for {clientName}</DiffHeader>}

      <div className="!moe-space-y-[16px] !moe-mt-[12px]">
        {diffItemList.map((item) => {
          const { type, isCustomQuestion } = item;
          if (type === 'question') {
            return (
              <DiffItem
                key={item.key}
                title={item.title}
                newNode={item.newNode}
                oldNode={item.oldNode}
                onChange={(...args) => {
                  handleChange(...args, isCustomQuestion);
                }}
              />
            );
          } else if (type === 'primaryAddress') {
            return (
              <DiffItem
                key={item.key}
                title={'Primary Address'}
                newNode={item.newNode}
                oldNode={item.oldNode}
                onChange={handleChangePrimaryAddress}
              />
            );
          } else if (type === 'newAddresses') {
            return (
              <DiffNewAddress
                key={item.key}
                initialValue={newVal?.newAddresses ?? []}
                setVisible={setVisible}
                onChange={onChange}
              />
            );
          }
          return null;
        })}
      </div>
    </div>
  );
});
