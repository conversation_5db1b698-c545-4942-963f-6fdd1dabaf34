import React, { type ComponentType, type PropsWithChildren, type ReactNode } from 'react';
import { createEnum } from '../../../../../store/utils/createEnum';

export const MAX_DOMAIN_ELEMENT_LENGTH = 30;
export const MIN_DOMAIN_ELEMENT_LENGTH = 5;
export const MAX_GALLERY_IMAGE_COUNT = 200;

export const NeedCheckPricingPermissions = [
  'showcase',
  'amenities',
  'serviceArea',
  'obConfigTeams',
  'obConfigClientReviews',
];

export type AmenitiesConfigName =
  | 'acceptWalkIns'
  | 'cageFree'
  | 'dogPlayground'
  | 'locallyOwned'
  | 'petSupplies'
  | 'parkingLot'
  | 'selfServeWashStation'
  | 'cashOnly'
  | 'acceptCreditCards'
  | 'applePay'
  | 'androidPay'
  | 'depositNeeded'
  | string;

export interface AmenitiesConfigItem {
  label: string;
  name?: AmenitiesConfigName;
}

export const AmenitiesConfig = createEnum<string, string, AmenitiesConfigItem[]>({
  Features: [
    'Features',
    [
      { name: 'acceptWalkIns', label: 'Accept walk-ins' },
      { name: 'cageFree', label: 'Cage-free' },
      { name: 'dogPlayground', label: 'Dog playground' },
      { name: 'locallyOwned', label: 'Locally owned' },
      { name: 'petSupplies', label: 'Pet supplies' },
      { name: 'parkingLot', label: 'Parking lot' },
      { name: 'selfServeWashStation', label: 'Self-serve wash station' },
    ],
  ],
  Payment: [
    'Payment',
    [
      { name: 'cashOnly', label: 'Cash only' },
      { name: 'acceptCreditCards', label: 'Accept credit cards' },
      { name: 'applePay', label: 'Apple pay' },
      { name: 'androidPay', label: 'Android pay' },
      { name: 'depositNeeded', label: 'Deposit needed' },
    ],
  ],
});

export type PageComponentName =
  | 'gallery'
  | 'businessInfo'
  | 'serviceArea'
  | 'aboutUs'
  | 'showcase'
  | 'amenities'
  | 'obConfigTeams'
  | 'obConfigClientReviews'
  | 'welcomePageMessage';

export interface PageComponent {
  label: React.ReactNode;
  name: PageComponentName;
  must: boolean;
  desc?: ReactNode;
  setupText?: string;
  popover?: ComponentType<PropsWithChildren<{ setItemProps: (v: any) => void }>>;
  calcShouldDisplay?: () => { shouldDisplayLeftCheckBox: boolean; shouldDisplayRightComponent: boolean };
}

export const BusinessInfoSetting = createEnum({
  businessHours: ['businessHours', 'Business hours'],
  contact: ['contact', 'Contact info'],
  address: ['address', 'Business address'],
});

export type BusinessInfoSettingType = 'businessHours' | 'contact' | 'address';

export type BusinessServiceAreaSettingType = 'serviceArea';

export const OnlineBookingActionOptions = createEnum({
  Unpublish: [1, 'Unpublish site'],
  DisableBooking: [2, 'Disable online booking'],
  GoToSetting: [3, 'Go to business settings'],
});

export const DangerModalConfig = createEnum({
  Unpublish: [
    OnlineBookingActionOptions.Unpublish,
    {
      title: 'Unpublish site',
      desc: 'Are you sure you want to unpublished your digital store?',
      contentList: [
        <span>
          Clients <strong>will not</strong> be able to access your storefront page.
        </span>,
        <span>
          Clients <strong>will not</strong> be able to use online booking.
        </span>,
        <span>
          Any pages embedded on your website as well as generated QR codes <strong>will not</strong> function.
        </span>,
      ],
      dangerBtnText: 'Unpublish',
    },
  ],
  Disable: [
    OnlineBookingActionOptions.DisableBooking,
    {
      title: 'Disable online booking',
      desc: 'Are you sure you want to disable your online booking?',
      contentList: [
        <span>
          Clients <strong>will still</strong> be able to access your storefront page.
        </span>,
        <span>
          Clients <strong>will not</strong> be able to use online booking and the “Book now” button will become “Contact
          us to book”.
        </span>,
      ],
      dangerBtnText: 'Disable',
    },
  ],
});

export const LinkPageConfigList = createEnum({
  Storefront: [1, 'Storefront'],
  BookNow: [2, 'Book now'],
});
