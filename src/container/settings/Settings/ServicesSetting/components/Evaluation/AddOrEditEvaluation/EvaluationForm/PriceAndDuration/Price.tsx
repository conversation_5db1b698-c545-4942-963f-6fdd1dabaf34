import { Form, Input } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { selectCurrentBusiness } from '../../../../../../../../../store/business/business.selectors';
import { useServiceStaffPermissions } from '../../../../AddOrEditComponents/hooks/useServiceStaffPermissions';
import { useEvaluationFormContext } from '../EvaluationFormContext';

export const Price = memo(() => {
  const [business] = useSelector(selectCurrentBusiness);
  const { isEdit } = useEvaluationFormContext();
  const { isDisabledForPriceAndTax } = useServiceStaffPermissions(isEdit);

  return (
    <Form.Item name="price" label="Price" rules={{ required: 'Price is required' }}>
      <Input.Number
        isRequired
        isDisabled={isDisabledForPriceAndTax}
        placeholder="Add the price"
        prefix={business.printCurrency()}
        maxValue={9999999999}
      />
    </Form.Item>
  );
});
