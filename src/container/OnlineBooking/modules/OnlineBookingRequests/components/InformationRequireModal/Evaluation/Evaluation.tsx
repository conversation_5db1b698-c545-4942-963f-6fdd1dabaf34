import React, { type FC } from 'react';
import { type EvaluationBriefView } from '@moego/api-web/moego/models/offering/v1/evaluation_models';
import { type EvaluationTestDetailModel } from '@moego/api-web/moego/models/online_booking/v1/evaluation_test_detail_models';
import { type BoardingServiceDetailModel } from '@moego/api-web/moego/models/online_booking/v1/boarding_service_detail_models';
import { type DaycareServiceDetailModel } from '@moego/api-web/moego/models/online_booking/v1/daycare_service_detail_models';
import { EvaluationForm } from './EvaluationForm';
import { MissingEvaluationForm } from './MissingEvaluationForm';
import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type useForm } from '@moego/ui';
import { isEvaluationServiceDetail } from './Evaluation.utils';
import { type InformationRequireFormRecord } from '../InformationRequireModal.types';
import { type ServiceBriefView } from '@moego/api-web/moego/models/offering/v1/service_models';
import { isUndefined } from 'lodash';

interface EvaluationProps {
  petId: string;
  itemKey: string;
  careType: ServiceItemType;
  evaluation?: EvaluationBriefView;
  service?: ServiceBriefView;
  serviceDetail: EvaluationTestDetailModel | BoardingServiceDetailModel | DaycareServiceDetailModel;
  form: ReturnType<typeof useForm<InformationRequireFormRecord>>;
}

export const Evaluation: FC<EvaluationProps> = (props) => {
  const { form, itemKey, service, serviceDetail, evaluation, petId, careType } = props;
  return (
    <div className="moe-flex moe-flex-col moe-gap-y-s">
      {!isEvaluationServiceDetail(serviceDetail) && !isUndefined(service) && !isUndefined(evaluation) && (
        <MissingEvaluationForm
          form={form}
          petId={petId}
          itemKey={itemKey}
          service={service}
          careType={careType}
          evaluation={evaluation}
          serviceDetail={serviceDetail}
        />
      )}
      {isEvaluationServiceDetail(serviceDetail) && (
        <EvaluationForm form={form} itemKey={itemKey} evaluation={evaluation} serviceDetail={serviceDetail} />
      )}
    </div>
  );
};
