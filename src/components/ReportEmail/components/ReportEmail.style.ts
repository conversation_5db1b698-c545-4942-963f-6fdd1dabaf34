import { type CSSProperties } from 'react';

export const TableGlobalStyle: CSSProperties = {
  display: 'table',
  borderCollapse: 'separate',
  boxSizing: 'border-box',
};

export const TextGlobalStyle: CSSProperties = {
  margin: 0,
  color: '#333',
  fontFamily: "'Helvetica Neue', Helvetica, Arial, 'PingFang SC', 'Microsoft YaHei'",
};

export const SectionStyle: CSSProperties = {
  ...TableGlobalStyle,
  padding: 24,
  backgroundColor: '#FFFFFF',
  marginTop: 12,
  borderRadius: 20,
};

export const SectionTitleStyle: CSSProperties = {
  ...TextGlobalStyle,
  fontSize: 18,
  lineHeight: '18px',
  fontWeight: 700,
  color: '#333',
};

export const CommonTextStyle: CSSProperties = {
  ...TextGlobalStyle,
  fontSize: 14,
  fontWeight: 400,
  lineHeight: '20px',
  color: '#333',
};

// feedback

export const QuestionStyle: CSSProperties = {
  ...SectionTitleStyle,
  marginBottom: '8px',
  overflowWrap: 'break-word',
  wordBreak: 'break-word',
  whiteSpace: 'pre-wrap',
};

export const AnswerStyle: CSSProperties = {
  ...CommonTextStyle,
  overflowWrap: 'break-word',
  wordBreak: 'break-word',
  whiteSpace: 'pre-wrap',
};
