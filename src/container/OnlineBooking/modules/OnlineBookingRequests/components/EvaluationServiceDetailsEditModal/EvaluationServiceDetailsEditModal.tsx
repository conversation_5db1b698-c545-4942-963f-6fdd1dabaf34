import { <PERSON><PERSON>, <PERSON><PERSON>, Markup, Modal, Spin, Text } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import { Condition } from '../../../../../../components/Condition';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { getPetDetail } from '../../../../../../store/pet/pet.actions';
import { petMapBox } from '../../../../../../store/pet/pet.boxes';
import { isNormal } from '../../../../../../store/utils/identifier';
import { getPetAvatarType } from '../../../../../../utils/BusinessUtil';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { useEvaluationServiceSelect } from './useEvaluationServiceSelect';
import { type EvaluationServiceInfo, useEvaluationStaffSelect } from './useEvaluationStaffSelect';

export interface EvaluationServiceDetailsEditModalProps {
  petId: string;
  evaluationServiceInfo?: EvaluationServiceInfo;
  isEvaluationPickerEnable?: boolean;
  isServiceTimeEnable?: boolean;
  isStaffSelectEnable?: boolean;
  defaultValues?: {
    evaluationId?: string;
    staffId?: string;
  };
  onClose?: () => void;
  onSubmit?: (values: {
    evaluationId?: string;
    staffId?: string;
  }) => void;
}

export const EvaluationServiceDetailsEditModal: React.FC<EvaluationServiceDetailsEditModalProps> = (props) => {
  const dispatch = useDispatch();
  const [business] = useSelector(selectCurrentBusiness);
  const {
    petId,
    defaultValues,
    evaluationServiceInfo,
    isEvaluationPickerEnable,
    isStaffSelectEnable: isStaffSelectEnableOrig,
    isServiceTimeEnable: isServiceTimeEnableOrig,
    onClose,
    onSubmit,
  } = props;

  /** main logic and ui */
  const isStaffSelectEnable = !!evaluationServiceInfo && isStaffSelectEnableOrig;
  const isServiceTimeOpen = !!evaluationServiceInfo?.serviceStartTime && isServiceTimeEnableOrig;
  const { evaluationId, getEvaluationValue, renderServicePicker } = useEvaluationServiceSelect(
    defaultValues?.evaluationId,
  );
  const { getStaffId, renderStaffSelector } = useEvaluationStaffSelect(
    defaultValues?.staffId,
    // 同时开启时，staff picker 会依赖内部最新的 evaluationId
    isEvaluationPickerEnable && isStaffSelectEnable
      ? {
          ...evaluationServiceInfo,
          evaluationId,
        }
      : evaluationServiceInfo,
  );
  const handleConfirm = useSerialCallback(async () => {
    const evaluationId = getEvaluationValue();
    const staffId = getStaffId();
    onSubmit?.({
      evaluationId,
      staffId,
    });
  });

  /** pet info display */
  const [pet] = useSelector(petMapBox.mustGetItem(+petId));
  const loadPetInfo = useSerialCallback(async () => {
    await dispatch(getPetDetail(+petId));
  });
  useEffect(() => {
    if (isNormal(petId) && !isNormal(pet.petId)) {
      loadPetInfo();
    }
    // 只需要拉一次，所以只监听 petId
  }, [petId]);

  const fieldsNum = (isEvaluationPickerEnable ? 1 : 0) + (isStaffSelectEnable ? 1 : 0);

  return (
    <Modal
      isOpen
      title={fieldsNum > 1 ? 'Edit service details' : 'Edit service detail'}
      size="s"
      autoCloseOnConfirm={false}
      onConfirm={handleConfirm}
      onClose={onClose}
      confirmButtonProps={{
        isLoading: handleConfirm.isBusy(),
      }}
    >
      <Spin
        classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-w-full moe-h-full' }}
        isLoading={loadPetInfo.isBusy()}
      >
        <div className="moe-flex moe-items-start moe-flex-col moe-gap-y-s">
          <Text variant="small">To schedule this booking request, please complete the information below.</Text>
          <Condition if={loadPetInfo.isBusy() || isNormal(pet.petId)}>
            <div className="moe-flex moe-items-center moe-gap-x-xs">
              <Avatar.Pet src={pet.avatarPath} type={getPetAvatarType(pet.petTypeId)} size="xs" color="neutral" />
              <Heading size="5">{pet.petName}</Heading>
              <Text variant="small" className="moe-text-tertiary">
                ({pet.breed})
              </Text>
            </div>
          </Condition>
          <Condition if={isServiceTimeOpen}>
            <div className="moe-flex moe-gap-xxs">
              <Markup variant="small">Service time:</Markup>
              <Text variant="small">
                {business.formatDateTime(
                  dayjs(evaluationServiceInfo?.appointmentDate).setMinutes(
                    evaluationServiceInfo?.serviceStartTime || 0,
                  ),
                )}
              </Text>
            </div>
          </Condition>
          {isEvaluationPickerEnable && renderServicePicker()}
          {isStaffSelectEnable && renderStaffSelector()}
        </div>
      </Spin>
    </Modal>
  );
};
