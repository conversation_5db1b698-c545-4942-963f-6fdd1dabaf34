import { Method } from '@moego/api-web/moego/models/message/v1/message_enums';
import { MajorClockOutlined } from '@moego/icons-react';
import { But<PERSON>, Modal } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect, useState } from 'react';
import {
  ModalScheduleMessage,
  OperateType,
} from '../../../../components/MessageSendBox/components/ScheduledMessage/ModalScheduleMessage';
import { WithSMSLimitUpgrade } from '../../../../components/Pricing/WithLimitComponents';
import { WithPricingEnableUpgrade } from '../../../../components/Pricing/WithPricingComponents';
import { previewAutoMessage, sendAutoMessage } from '../../../../store/autoMessage/autoMessage.actions';
import { AutoMessageType, type AutoMessageTypeValue } from '../../../../store/autoMessage/autoMessage.boxes';
import { selectAutoMessageTemplate } from '../../../../store/autoMessage/autoMessage.selectors';
import { customerMapBox } from '../../../../store/customer/customer.boxes';
import { createEnum } from '../../../../store/utils/createEnum';
import { useBool } from '../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { type AlertProps } from './TicketCreatedAlerts';

const TicketAlertMode = createEnum({
  Create: [
    AutoMessageType.AppointmentBooked,
    {
      messageType: 'created',
      alertTitle: 'Appointment created',
      alertBody: 'A new appointment has been successfully created. Would you like to send an update message?',
      autoMessageType: AutoMessageType.AppointmentBooked,
    },
  ],
  Update: [
    AutoMessageType.AppointmentRescheduled,
    {
      messageType: 'updated',
      alertTitle: 'Appointment rescheduled',
      alertBody: 'This appointment has been rescheduled. Send update message?',
      autoMessageType: AutoMessageType.AppointmentRescheduled,
    },
  ],
  Cancel: [
    AutoMessageType.AppointmentCancelled,
    {
      messageType: 'cancelled',
      alertTitle: 'Appointment cancelled',
      alertBody: 'This appointment has been cancelled. Send update message?',
      autoMessageType: AutoMessageType.AppointmentCancelled,
    },
  ],
  MovedToWaitlist: [
    AutoMessageType.AppointmentMovedToWaitlist,
    {
      messageType: 'moved to waitlist',
      alertTitle: 'Appointment moved to waitlist',
      alertBody: 'This appointment has been moved to waitlist. Would you like to send an update message?',
      autoMessageType: AutoMessageType.AppointmentMovedToWaitlist,
    },
  ],
});

const TicketAlertModeRepeat = createEnum({
  Create: [
    AutoMessageType.AppointmentBooked,
    {
      messageType: 'created',
      alertTitle: 'Send update message',
      alertBody:
        'Repeat appointments have been created successfully. Would you like to send update message for the 1st newly created appointment?',
      autoMessageType: AutoMessageType.AppointmentBooked,
    },
  ],
  Update: [
    AutoMessageType.AppointmentRescheduled,
    {
      messageType: 'updated',
      alertTitle: 'Send update message',
      alertBody:
        'Appointments have been scheduled successfully. Would you like to send update message for the 1st rescheduled appointment?',
      autoMessageType: AutoMessageType.AppointmentRescheduled,
    },
  ],
});

export const MessageAlert = memo<AlertProps & { isCancelLoading?: boolean }>(
  ({ customerId, ticketId, onCancel, onSubmit, mode, isRepeat, isCancelLoading }) => {
    const [autoMessage, customer] = useSelector(
      selectAutoMessageTemplate(mode),
      customerMapBox.mustGetItem(customerId),
    );
    // if allow auto message then check smsLimit
    const allowAutoMessage = customer.sendAutoMessage > 0;
    const alertData = (isRepeat ? TicketAlertModeRepeat : TicketAlertMode).mapLabels[
      autoMessage.type as AutoMessageTypeValue
    ];
    const [content, setContent] = useState('');
    const showDialog = useBool(false);
    const dispatch = useDispatch();
    const handleSendMessage = useSerialCallback(async () => {
      try {
        await dispatch(sendAutoMessage({ appointmentId: ticketId, customerId, autoTemplateType: mode }));
        await onSubmit?.();
      } catch {
        onCancel();
      }
    });

    const getPreviewContent = useLatestCallback(async () => {
      const previewContent = await dispatch(
        previewAutoMessage({
          forAppointment: {
            appointmentId: ticketId + '',
            type: alertData.autoMessageType,
          },
        }),
      );
      setContent(previewContent);
    });

    useEffect(() => {
      getPreviewContent();
    }, [ticketId, autoMessage, alertData]);

    return (
      <Modal
        isOpen
        size="s"
        title={alertData.alertTitle}
        showCloseButton={false}
        classNames={{
          buttonsWrapper: 'moe-w-full',
        }}
        footer={
          <div className="moe-flex moe-justify-between moe-w-full">
            <Button
              variant="tertiary"
              onPress={showDialog.open}
              icon={<MajorClockOutlined />}
              isDisabled={isCancelLoading}
            >
              Schedule for later
            </Button>

            <div className="moe-flex moe-justify-end moe-gap-[16px]">
              <Button variant="secondary" onPress={onCancel} isLoading={isCancelLoading}>
                Not this time
              </Button>

              <WithPricingEnableUpgrade permission="autoMessage">
                {(onCapture1) => (
                  <WithSMSLimitUpgrade>
                    {(onCapture2) => (
                      <Button
                        onPress={onCapture1 || (allowAutoMessage ? onCapture2 : undefined) || handleSendMessage}
                        isLoading={handleSendMessage.isBusy()}
                        isDisabled={isCancelLoading}
                      >
                        Yes
                      </Button>
                    )}
                  </WithSMSLimitUpgrade>
                )}
              </WithPricingEnableUpgrade>
              <ModalScheduleMessage
                customerId={Number(customerId)}
                customerName={`${customer.firstName} ${customer.lastName}`}
                phoneNumber={customer.phoneNumber}
                visible={showDialog.value}
                appointmentId={ticketId}
                content={{
                  content: content,
                }}
                autoType={alertData.autoMessageType}
                handleClose={() => {
                  showDialog.close();
                  onCancel();
                }}
                opType={OperateType.Create}
                method={Method.SMS}
              />
            </div>
          </div>
        }
      >
        <div className="moe-text-[16px] moe-font-[400] moe-leading-[24px] moe-tracking-[0.16px]">
          {alertData.alertBody}
        </div>
      </Modal>
    );
  },
);
