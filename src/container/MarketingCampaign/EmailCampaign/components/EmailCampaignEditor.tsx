import { useDispatch, useSelector } from 'amos';
import { Input } from 'antd';
import classNames from 'classnames';
import React, { forwardRef, memo, useEffect, useImperativeHandle, useRef } from 'react';
import SvgIconDeleteNewSvg from '../../../../assets/svg/icon-delete-new.svg';
import SvgIconSortableSvg from '../../../../assets/svg/icon-sortable.svg';
import { alertApi } from '../../../../components/Alert/AlertApi';
import { Condition } from '../../../../components/Condition';
import { SvgIcon } from '../../../../components/Icon/Icon';
import { Loading } from '../../../../components/Loading/Loading';
import {
  calculateAttachmentSize,
  handleFileUpload,
} from '../../../../components/RichTextEditor/RichEditorPlugins/upload.utils';
import { checkButtonInContentIsValid } from '../../../../components/RichTextEditor/RichEditorPlugins/utils';
import { RichTextEditor, type RichTextEditorRef } from '../../../../components/RichTextEditor/RichTextEditor';
import { EDITOR_CLASS_CONTAINER } from '../../../../components/RichTextEditor/const';
import { toastApi } from '../../../../components/Toast/Toast';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { addClientFilterList } from '../../../../store/customer/clientFilters.action';
import { isNormal } from '../../../../store/utils/identifier';
import { useBool } from '../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { MAX_CONTENT_LENGTH, MAX_EMAIL_SUBJECT_LENGTH, MAX_UPLOAD_FILE_SIZE } from '../../const';
import { useEmailClientListFilters } from '../hooks/useEmailClientListFilters';
import { useEmailEditorTipsVisble } from '../hooks/useEmailEditorTipsVisible';
import { EmailCampaignEditorTips } from './EmailCampaignEditorTips';
import { MoreFilterView } from './EmailCampaignSelectRecipient.style';
import { useRouteState } from '../../../../utils/RoutePath';
import { PATH_MARKETING_CAMPAIGN_PANEL } from '../../../../router/paths';

interface AttachmentItemProps {
  fileName: string;
  size: number;
  handleRemoveAttachment: () => void;
}

const AttachmentItem = memo(({ fileName, handleRemoveAttachment, size }: AttachmentItemProps) => {
  return (
    <div className="!moe-flex !moe-items-center !moe-justify-between !moe-h-[32px] !moe-px-[12px] !moe-minw-[170px] !moe-rounded-[4px] !moe-mr-[8px] !moe-bg-[#F2F3F6]">
      <div>
        {fileName}
        {`(${calculateAttachmentSize(size)})`}
      </div>
      <SvgIcon
        className="!moe-text-[#D0021B] !moe-cursor-pointer"
        onClick={handleRemoveAttachment}
        src={SvgIconDeleteNewSvg}
        size={20}
      />
    </div>
  );
});

interface EmailCampaignEditorProps {
  defaultValue?: string;
  subject: string;
  content: string;
  onContentChange: (value: string) => void;
  onSubjectChange: (value: string) => void;
  onAttachmentChange: ({ name, url, type, size }: { name: string; url: string; type: string; size: number }) => void;
  attachment: { name: string; url: string; type: string; size: number };
  onDirty?: () => void;
  setContentAfterInit: boolean;
  templateId: string;
}

export interface EmailCampaignEditorRef {
  checkContentIsValid: () => boolean;
}

export const EmailCampaignEditor = forwardRef<EmailCampaignEditorRef, EmailCampaignEditorProps>(
  (
    {
      onContentChange,
      onSubjectChange,
      defaultValue,
      subject,
      content,
      onAttachmentChange,
      attachment,
      onDirty,
      setContentAfterInit,
      templateId,
    }: EmailCampaignEditorProps,
    ref,
  ) => {
    const richEditorRef = useRef<RichTextEditorRef>(null);
    const tipsVisible = useEmailEditorTipsVisble();
    const loading = useBool(false);
    const subjectError = useBool(false);
    const contentError = useBool(false);
    const [business] = useSelector(selectCurrentBusiness());
    const dispatch = useDispatch();
    const routeState = useRouteState(PATH_MARKETING_CAMPAIGN_PANEL);
    const { filters } = useEmailClientListFilters(templateId, routeState?.customerType);

    // default filter
    useEffect(() => {
      if (isNormal(business.id)) {
        dispatch(addClientFilterList(filters));
      }
    }, [business.id, filters]);

    const handleRemoveAttachment = () => {
      onAttachmentChange({
        name: '',
        url: '',
        type: '',
        size: 0,
      });
    };

    const handleUploadAttachment = useSerialCallback(async (file?: File) => {
      if (attachment.name && attachment.url) {
        alertApi.warn('You can only upload one attachment at a time');
        return;
      }
      loading.open();
      try {
        const res = await handleFileUpload({
          file,
          maxSize: MAX_UPLOAD_FILE_SIZE,
        });
        if (res) {
          onAttachmentChange(res);
        }
      } finally {
        loading.close();
      }
    });

    const checkContentIsValid = useLatestCallback(() => {
      if (!checkButtonInContentIsValid(content)) {
        alertApi.warn(
          <span className="!moe-text-[#fff]">
            Button destination URL cannot be blank.
            <span
              className="!moe-text-[#fff] !moe-underline !moe-cursor-pointer !moe-ml-[12px]"
              onClick={() => {
                richEditorRef.current?.openInvalidButtonDialog();
              }}
            >
              Add URL
            </span>
          </span>,
        );
        return false;
      }
      if (!subject || !content) {
        toastApi.error('Please fill all required fields.');
        return false;
      }
      if (subject.length > MAX_EMAIL_SUBJECT_LENGTH) {
        toastApi.error(`The email subject cannot be more than ${MAX_EMAIL_SUBJECT_LENGTH} characters.`);
        return false;
      }
      if (content.length > MAX_CONTENT_LENGTH) {
        toastApi.error('Email content exceeds the maximum length.');
        return false;
      }
      return true;
    });

    useImperativeHandle(ref, () => ({
      checkContentIsValid,
    }));

    const handleContentBlur = useLatestCallback((value: string) => {
      const contentHasError = !value;
      contentError.as(contentHasError);
    });

    const handleSubjectBlur = useLatestCallback((e: React.FocusEvent<HTMLInputElement>) => {
      const isSubjectError = e.target.value.length > MAX_EMAIL_SUBJECT_LENGTH || e.target.value.length === 0;
      subjectError.as(isSubjectError);
    });

    return (
      <div className="!moe-flex !moe-w-[100%] !moe-mb-[24px]">
        <div className="!moe-w-[100%] !moe-flex !moe-flex-col !moe-items-center !moe-bg-[#fff] !moe-min-h-[100%] !moe-py-[40px] !moe-rounded-[8px] !moe-relative !moe-min-w-[748px]">
          <div
            className="!moe-text-brand !moe-cursor-pointer !moe-text-[14px] !moe-absolute !moe-top-[40px] !moe-right-[24px] !moe-flex !moe-items-center"
            onClick={tipsVisible.toggle}
          >
            <SvgIcon size={16} src={SvgIconSortableSvg} className="!moe-mr-[4px]"></SvgIcon>
            {tipsVisible.value ? 'Hide Tips' : 'Show Tips'}
          </div>
          <div className="!moe-w-[700px]">
            <div className="!moe-text-[20px] !moe-text-[#333] !moe-mb-[32px] !moe-font-bold">
              Customize your campaign content
            </div>
            <div className="!moe-text-[14px] !moe-text-[#333] !moe-mb-[4px]">Email subject</div>
            <div className="!moe-mb-[32px]">
              <Input
                value={subject}
                placeholder="Enter your email subject"
                onChange={(e) => {
                  onDirty?.();
                  onSubjectChange(e.target.value);
                }}
                onBlur={handleSubjectBlur}
                className={classNames({
                  '!moe-border-[#D0021B]': subjectError.value,
                })}
              />
              <Condition if={subjectError.value}>
                <div className="!moe-text-[12px] !moe-text-[#D0021B] !moe-mt-[4px]">
                  {subject.length > MAX_EMAIL_SUBJECT_LENGTH
                    ? `The email subject can not exceed ${MAX_EMAIL_SUBJECT_LENGTH} character.`
                    : 'The email subject can not be empty.'}
                </div>
              </Condition>
            </div>
            <div className="!moe-text-[14px] !moe-text-[#333] !moe-mb-[4px]">
              Email content
              <Condition if={!!attachment.name && !!attachment.url}>
                <div className="!moe-h-[32px] !moe-my-[4px] !moe-flex !moe-justify-start">
                  <AttachmentItem
                    size={attachment.size}
                    fileName={attachment.name}
                    handleRemoveAttachment={handleRemoveAttachment}
                  />
                </div>
              </Condition>
            </div>
            <Loading loading={loading.value}>
              <RichTextEditor
                ref={richEditorRef}
                defaultValue={defaultValue}
                value={content}
                onChange={onContentChange}
                onFocus={tipsVisible.open}
                onDirty={onDirty}
                onBlur={handleContentBlur}
                handleUploadAttachment={handleUploadAttachment}
                hasError={contentError.value}
                setContentAfterInit={setContentAfterInit}
                options={{
                  plugins: 'image link emoticons lists quickbars preview code',
                  toolbar: [
                    {
                      name: 'redo&undo',
                      items: ['undo', 'redo'],
                    },
                    {
                      name: 'main',
                      items: [
                        'textformat',
                        'image',
                        'emoticons',
                        'addbutton',
                        'link',
                        'addhighlightsection',
                        'addvariable',
                        'attachment',
                        'importhtml',
                        'code',
                      ],
                    },
                  ],
                  min_height: 680,
                  max_height: 680,
                  statusbar: false,
                  quickbars_insert_toolbar: false,
                  quickbars_image_toolbar: 'alignleft aligncenter alignright | imageedit imagelink',
                  quickbars_selection_toolbar:
                    'bold italic underline strikethrough alignment fontsize | quicklink forecolor',
                  body_class: EDITOR_CLASS_CONTAINER,
                  contextmenu: false,
                  paste_data_images: false,
                  font_size_formats: 'Heading=20pt Text=16pt Caption=14pt',
                  content_style: 'body { line-height: 1.4; }',
                  invalid_elements: 'script',
                }}
              />
            </Loading>
          </div>
        </div>
        <MoreFilterView visible={tipsVisible.value}>
          <EmailCampaignEditorTips />
        </MoreFilterView>
      </div>
    );
  },
);
