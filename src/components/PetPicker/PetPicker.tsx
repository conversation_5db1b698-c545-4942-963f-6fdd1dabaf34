import { LegacySelect as Select, type LegacySelectProps as SelectProps, type SelectValueType } from '@moego/ui';
import { useSelector } from 'amos';
import { isArray } from 'lodash';
import React, { type ReactElement, useMemo } from 'react';
import SvgIconAvatarDogSvg from '../../assets/svg/icon-avatar-dog.svg';
import { ApptTestIds } from '../../config/testIds/apptDrawer';
import { type PetRecord, customerPetListBox, petMapBox } from '../../store/pet/pet.boxes';
import { ID_ANONYMOUS, isNormal } from '../../store/utils/identifier';
import { getPetAvatarType } from '../../utils/BusinessUtil';
import { useBool } from '../../utils/hooks/useBool';
import { useControllableValue } from '../../utils/hooks/useControlledValue';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { useAddPetFormModal } from '../AddClientWithPets/hooks/useAddPetFormModal';
import { AddTextButton } from '../AddTextButton/AddTextButton';
import { Avatar } from '../Avatar/Avatar';
import { PetDetailCard } from '../PetInfo/PetDetailCard';
import { PetDeactivateReason, PetDeactivateReasonMap } from '../PetInfo/hooks/useClientPets';
import { MultipleMenu } from './MultipleMenu';
import { RoundAvatar } from './RoundAvatar';
import { SelectPetItem } from './SelectPetItem';
import { SelectedPetLabel } from './SelectedPetLabel';

export type PetPickerProps<IsMulti extends boolean = boolean, V = SelectValueType<IsMulti, number>> = {
  clientId: number;
  value: V;
  onChange?: (petId: V) => void;
  disabledPetIds?: number[];
  defaultVisible?: boolean;
  isDisabled?: boolean;
  classNames?: SelectProps['classNames'];
  renderPetName?: (pet: PetRecord) => React.ReactNode;
  footerHidden?: boolean;
  isMultiplePets?: IsMulti;
  autoFocus?: boolean;
};

type ValueType = number | number[];

type PetSicker = <IsMulti extends boolean = false>(props: PetPickerProps<IsMulti>) => ReactElement;

export const PetPicker = function <IsMulti extends boolean = boolean, V = SelectValueType<IsMulti, number>>(
  props: PetPickerProps<IsMulti, V>,
) {
  const {
    disabledPetIds,
    defaultVisible,
    clientId,
    isDisabled,
    renderPetName,
    footerHidden,
    classNames,
    isMultiplePets,
    autoFocus,
  } = props;
  const [value, onChange] = useControllableValue<ValueType>(props, {
    defaultValue: isMultiplePets ? [] : ID_ANONYMOUS,
  });
  const go2AddPet = useAddPetFormModal(clientId);
  const [customerPetList, petMap] = useSelector(customerPetListBox, petMapBox);
  const focus = useBool();

  const allPets = customerPetList.getList(clientId);
  const alivePets = useMemo(() => {
    const alivePets = allPets
      .filter((petId) => {
        const pet = petMap.mustGetItem(petId);
        const isAlive = pet.isLive();
        return isAlive;
      })
      .toArray();
    return alivePets.map((petId) => {
      const pet = petMap.mustGetItem(petId);

      return {
        value: petId,
        label: String(petId),
        title: pet.petName,
        isDisabled: disabledPetIds?.includes(petId),
      };
    });
  }, [allPets, petMap, disabledPetIds]);

  const handleClickNewPet = useLatestCallback(async () => {
    const petId = await go2AddPet();
    onChange?.(isMultiplePets && isArray(value) ? [...value, petId] : petId);
  });

  const prefixContent = useMemo(() => {
    if (isArray(value) || (isArray(value) && value.length)) {
      return undefined;
    }

    const validatePetId = isNormal(value);
    const selectedPet = petMap.mustGetItem(value);

    if (!validatePetId || isMultiplePets) {
      return <RoundAvatar src={SvgIconAvatarDogSvg} active={focus.value} />;
    }

    return (
      <Avatar
        size="40px"
        type={getPetAvatarType(selectedPet!.petTypeId)}
        src={selectedPet!.avatarPath}
        {...(!selectedPet!.isLive() && {
          passAway: true,
          tooltip:
            PetDeactivateReasonMap[
              selectedPet.deactivateReason in PetDeactivateReason
                ? (selectedPet.deactivateReason as PetDeactivateReason)
                : PetDeactivateReason.PASS_AWAY
            ],
        })}
      />
    );
  }, [focus.value, value, petMap]);

  if (isDisabled && !isArray(value)) {
    return <PetDetailCard showNotes={false} petId={value} />;
  }

  return (
    <div data-testid={ApptTestIds.ApptPetServiceSelectPetBtn}>
      <Select
        isMultiple={isMultiplePets}
        value={value as SelectValueType<IsMulti, number>}
        onChange={(value) => {
          if (!isMultiplePets && isNormal(value as number)) {
            onChange?.(value);
            return;
          }
          onChange?.([value].flat());
        }}
        autoFocus={autoFocus}
        isDisabled={isDisabled}
        prefix={prefixContent}
        defaultOpen={defaultVisible}
        options={alivePets}
        onFocus={focus.open}
        onBlur={focus.close}
        placeholder={isMultiplePets ? 'Search or select a pet' : 'Select pet'}
        isSearchable={!!isMultiplePets}
        formatOptionLabel={(option) => {
          const { value } = option;
          return isNormal(value) ? (
            <SelectedPetLabel isMultiple={isMultiplePets} petId={value} renderPetName={renderPetName} />
          ) : (
            <div className="moe-text-[#bfbfbf]">Select pet</div>
          );
        }}
        classNames={{
          dropdownIndicator: 'moe-self-auto',
          ...classNames,
        }}
        renderItem={(item) => {
          if (item.type === 'option') {
            const { value: petId } = item.data;
            const disabled = !!disabledPetIds?.includes(petId);
            return (
              <SelectPetItem
                key={petId}
                petId={petId}
                isSelected={isArray(value) && value.includes(petId)}
                isMultiple={isMultiplePets}
                onSelect={(petId) => {
                  if (disabled) {
                    return;
                  }
                  onChange?.(petId);
                }}
                disabled={disabled}
                renderPetName={renderPetName}
                data-testid={ApptTestIds.ApptPetServiceSelectPetName}
              />
            );
          }
          return null;
        }}
        renderMenu={
          isMultiplePets
            ? (menu) => {
                return <MultipleMenu menu={menu} onPress={handleClickNewPet} />;
              }
            : undefined
        }
        footer={
          footerHidden ? null : (
            <AddTextButton
              className="moe-px-[20px]"
              onClick={handleClickNewPet}
              textClassName="!moe-font-bold !moe-text-[16px] !moe-text-brand"
              data-testid={ApptTestIds.ApptPetServiceNewPetBtn}
            >
              New pet
            </AddTextButton>
          )
        }
      />
    </div>
  );
} as PetSicker;
