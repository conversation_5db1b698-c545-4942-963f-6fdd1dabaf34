import { Checkbox, DatePicker, Heading, LegacySelect as Select, Text } from '@moego/ui';
import { useSelector } from 'amos';
import type { Dayjs } from 'dayjs';
import { uniqBy } from 'lodash';
import { flattenDeep } from 'lodash/fp';
import React, { useEffect, useMemo, type ReactElement, type ReactNode } from 'react';
import { useSetState } from 'react-use';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { type PlaygroupsPrintCard } from '../../store/playgroups/playgroups.actions';
import { type ActivityPrintCardInfo } from '../../store/printCard/activityCard/activityCard.actions';
import { type PrintCardInfo } from '../../store/printCard/appointmentCard/appointmentCard.actions';
import { type ApptListPrintCardInfo } from '../../store/printCard/appointmentList/appointmentList.actions';
import { type StayPrintCardInfo } from '../../store/printCard/stayCard/stayCard.actions';
import { type EnumValues } from '../../store/utils/createEnum';
import { computeUnits } from '../../utils/calculator';
import { CareTypeServicesSelector } from '../CareTypeServicesSelector/CareTypeServicesSelector';
import { Condition } from '../Condition';
import { PrintModal, type PrintModalProps } from '../PrintModal/PrintModal';
import {
  PrintCardType,
  filterApptCardList,
  type MultiTypePrintModalState,
  type Staff,
} from './MultiTypePrintModal.options';
import { MultiTypePrintModalGlobalStyle } from './MultiTypePrintModal.style';
import { ActivityCardContent } from './components/ActivityCard/ActivityCardContent';
import { ApptCardContent } from './components/ApptCard/ApptCardContent';
import { AppointmentListCardContent } from './components/ApptListCard/AppointmentListCardContent';
import { BoardingArrivalCard } from './components/BoardingArrivalCard/BoardingArrivalCard';
import { BoardingDepartureCard } from './components/BoardingDepartureCard/BoardingDepartureCard';
import { PlaygroupCard } from './components/PlaygroupCard/PlaygroupCard';
import { Settings } from './components/Settings';
import { StayCardContent } from './components/StayCard/StayCardContent';
import { type BoardingPrintCardContent } from './components/common/BoardingCard/BoardingCard.types';
import { useDate } from './hooks/useDate';
import { useFilterAppointmentListData } from './hooks/useFilterAppointmentListData';
import { useGetData } from './hooks/useGetData';
import { useStaffList } from './hooks/useStaffList';
import { selectSceneCareTypeAsOptions } from '../../store/careType/careType.selectors';
import { Scene } from '../../store/service/scene.enum';
import { useGetDefaultCareType } from './hooks/useGetDefaultCareType';

type BaseProps = {
  cardTypeOptions?: EnumValues<typeof PrintCardType>[];
} & Pick<PrintModalProps, 'isOpen' | 'onClose'>;

export function MultiTypePrintModal(
  props: BaseProps & {
    groomingId?: number;
  },
): ReactElement;

export function MultiTypePrintModal(
  props: BaseProps & {
    date: Dayjs;
    staffList?: Staff[];
    serviceItemTypes?: number[];
    /**
     * 当打开 print modal 时，默认展示的 card 类型
     */
    defaultCardType?: EnumValues<typeof PrintCardType>;
    title?: string;
  },
): ReactElement;

export function MultiTypePrintModal(
  props: BaseProps & {
    groomingId?: number;
    date?: Dayjs;
    staffList?: Staff[];
    serviceItemTypes?: number[];
    defaultCardType?: EnumValues<typeof PrintCardType>;
    title?: string;
  },
) {
  const {
    isOpen,
    onClose,
    cardTypeOptions = PrintCardType.values,
    groomingId,
    date: dateProp,
    staffList: staffListProp,
    serviceItemTypes,
    defaultCardType = cardTypeOptions[0],
    title,
  } = props;
  const [business, careTypeOptions] = useSelector(
    selectCurrentBusiness(),
    selectSceneCareTypeAsOptions(Scene.PrintStayOrActivityCard),
  );

  const getDefaultCareType = useGetDefaultCareType();

  if (groomingId && (dateProp || staffListProp)) {
    throw new Error('GroomingId and (Date || StaffList) cannot be set at the same time.');
  }

  const [date, setDate] = useDate(dateProp);
  const { selectedStaff, setSelectedStaff, staffList } = useStaffList(staffListProp);
  const [state, setState] = useSetState<MultiTypePrintModalState>({
    selectedCardType: defaultCardType,
    isPetsCheckedInOnly: false,
    careType: getDefaultCareType(serviceItemTypes),
    serviceFilters: [],
  });
  const { selectedCardType, isPetsCheckedInOnly, careType, serviceFilters } = state;

  const {
    loadingContent,
    loadingSettings,
    data: originalData,
    loading,
  } = useGetData({
    isOpen,
    groomingId,
    date,
    modalState: state,
  });
  const filterData = useFilterAppointmentListData();

  const data = useMemo(() => {
    if (selectedCardType === PrintCardType.ApptList) {
      return filterData(originalData as ApptListPrintCardInfo[]);
    }
    return originalData;
  }, [selectedCardType, originalData, filterData]);

  useEffect(() => {
    if (isOpen) {
      setState({
        selectedCardType: defaultCardType,
        careType: getDefaultCareType(serviceItemTypes),
        serviceFilters: [],
      });
    }
  }, [isOpen]);

  const handleOnClose = () => {
    onClose?.();
    // 重置为外部值。
    if (dateProp) {
      setDate(dateProp);
    }
  };

  const activityData = (data as ActivityPrintCardInfo[])[0] ?? {};
  const showActivityCardNoData =
    selectedCardType === PrintCardType.Activity &&
    data &&
    (activityData?.feedingInstructions ?? []).length === 0 &&
    (activityData?.medicationInstructions ?? []).length === 0 &&
    (activityData?.addOns ?? []).length === 0;

  const showNoData = showActivityCardNoData || data.length === 0;
  const showCheckedInOnly = (
    [
      PrintCardType.BoardingDeparture,
      PrintCardType.BoardingArrival,
      PrintCardType.ApptList,
      PrintCardType.Stay,
      PrintCardType.Activity,
      PrintCardType.Playgroup,
    ] as number[]
  ).includes(selectedCardType);
  const showTotalPets = ([PrintCardType.Activity, PrintCardType.Stay, PrintCardType.Appt] as number[]).includes(
    selectedCardType,
  );

  const isApptListCardType = selectedCardType === PrintCardType.ApptList;

  const renderSettings = () => {
    return (
      <Settings
        cardTypeOptions={cardTypeOptions}
        isLoading={loadingSettings}
        cardType={selectedCardType}
        onChange={(value) => setState({ selectedCardType: value })}
      />
    );
  };

  const renderContent = () => {
    switch (selectedCardType) {
      case PrintCardType.ApptList:
        return <AppointmentListCardContent data={data as ApptListPrintCardInfo[]} date={business.formatDate(date)} />;
      case PrintCardType.BoardingDeparture:
        return <BoardingDepartureCard data={data as BoardingPrintCardContent[]} date={business.formatDate(date)} />;
      case PrintCardType.BoardingArrival:
        return <BoardingArrivalCard data={data as BoardingPrintCardContent[]} date={business.formatDate(date)} />;
      case PrintCardType.Stay:
        return <StayCardContent data={data as StayPrintCardInfo[]} />;
      case PrintCardType.Activity:
        return <ActivityCardContent data={data as ActivityPrintCardInfo[]} date={business.formatDate(date)} />;
      case PrintCardType.Appt: {
        const filteredApptCardList = filterApptCardList(data as PrintCardInfo[], selectedStaff).sort(
          (a, b) => a.appointment.appointmentStartTime - b.appointment.appointmentStartTime,
        );
        return <ApptCardContent data={filteredApptCardList} />;
      }
      case PrintCardType.Playgroup:
        return <PlaygroupCard data={data as PlaygroupsPrintCard[]} date={business.formatDate(date)} />;

      default:
        return null;
    }
  };

  const getPetCountString = () => {
    let petCount = 0;
    if (selectedCardType === PrintCardType.Appt) {
      const filterList = filterApptCardList(data as PrintCardInfo[], selectedStaff);
      petCount = uniqBy(filterList, 'petId').length;
    }
    if (selectedCardType === PrintCardType.Stay) {
      petCount = uniqBy(data as StayPrintCardInfo[], 'petId').length;
    }
    if (selectedCardType === PrintCardType.Activity) {
      // 在数组里解构 null 会报错
      const typedData = (data as ActivityPrintCardInfo[]).map((item) => [
        ...(item.feedingInstructions ?? []),
        ...(item.medicationInstructions ?? []),
        ...(item.addOns ?? []),
      ]);
      petCount = uniqBy(flattenDeep(typedData), 'petId').length;
    }
    return computeUnits(petCount, 'pet').join(' ');
  };

  const getDatePickerLabel = () => {
    switch (selectedCardType) {
      case PrintCardType.BoardingDeparture:
        return 'End date:';
      case PrintCardType.BoardingArrival:
        return 'Start date:';
      case PrintCardType.Stay:
        return 'Check in date:';
      default:
        return 'Date:';
    }
  };

  const getNoDataDescription = () => {
    switch (selectedCardType) {
      case PrintCardType.BoardingDeparture:
        return 'No boarding pets scheduled for departure on this date.';
      case PrintCardType.BoardingArrival:
        return 'No boarding pets scheduled for arrival on this date.';
      case PrintCardType.Activity:
        return 'No tasks scheduled for this date';
      case PrintCardType.Playgroup:
        return 'No playgroups available';
      default:
        return 'No appointments scheduled for this date';
    }
  };

  const renderFooter = () => {
    let children: ReactNode = null;
    const isStayAndActivity = ([PrintCardType.Stay, PrintCardType.Activity] as number[]).includes(selectedCardType);
    if (!groomingId) {
      const datePickerLabel = getDatePickerLabel();
      children = (
        <div className="moe-flex moe-items-center">
          <DatePicker
            onChange={(nextDate) => {
              if (nextDate) setDate(nextDate);
            }}
            value={date}
            format={business.dateFormat}
            label={datePickerLabel}
            classNames={{
              base: 'moe-flex-row moe-inline-flex',
              label: isStayAndActivity ? 'moe-mr-[8px]' : 'moe-mr-s',
              inputWrapper: isStayAndActivity ? 'moe-w-[148px]' : 'moe-w-[150px]',
            }}
            isDisabled={loading}
          />
          <Condition if={isStayAndActivity}>
            <Select
              options={careTypeOptions}
              value={careType}
              onChange={(value) => setState({ careType: value })}
              label="Care type:"
              classNames={{
                formItemWrapper: 'moe-flex-row moe-inline-flex moe-ml-s',
                formItemLabel: 'moe-mr-[8px]',
                control: 'moe-w-[188px]',
              }}
            />
          </Condition>
          <Condition if={isApptListCardType}>
            <CareTypeServicesSelector
              className="moe-ml-s moe-w-[188px]"
              value={serviceFilters}
              onChange={(filters) => setState({ serviceFilters: filters })}
            />
          </Condition>
          <Condition if={selectedCardType === PrintCardType.Appt}>
            <Select
              value={selectedStaff}
              onChange={(id) => {
                if (id) setSelectedStaff(id);
              }}
              options={staffList.map((staff) => ({ label: staff.name, value: staff.id }))}
              label="Selected staff:"
              classNames={{
                formItemWrapper: 'moe-flex-row moe-inline-flex moe-ml-s',
                formItemLabel: 'moe-mr-s',
                control: 'moe-w-[150px]',
              }}
              isDisabled={loading}
            />
          </Condition>
          <Condition if={showCheckedInOnly}>
            <Checkbox
              isSelected={isPetsCheckedInOnly}
              onChange={(value) => setState({ isPetsCheckedInOnly: value })}
              classNames={{ base: 'moe-inline-flex moe-ml-s' }}
            >
              Checked in pets only
            </Checkbox>
          </Condition>
        </div>
      );
    }

    return (
      <div className="moe-flex">
        <Condition if={children}>
          <div className="moe-mr-s">{children}</div>
        </Condition>
        <Condition if={showTotalPets}>
          <div className="moe-flex moe-items-center">
            <Heading size="6">Total:</Heading>
            <Text variant="small" className="moe-ml-xxs">
              {getPetCountString()}
            </Text>
          </div>
        </Condition>
      </div>
    );
  };

  return (
    <>
      <MultiTypePrintModalGlobalStyle />
      <PrintModal
        title={title || 'Print'}
        isOpen={isOpen}
        content={renderContent()}
        isLoadingContent={loadingContent}
        loadingContentClassName="moe-w-[700px]"
        noDataClassName="moe-w-[700px]"
        contentClassName="moe-w-[700px]"
        contentBodyClassName="moe-w-[612px]"
        containerClassName="moe-h-[90vh]"
        sideContent={renderSettings()}
        isDisabled={loading || data.length === 0}
        isNoData={showNoData}
        onClose={handleOnClose}
        footerLeftSide={renderFooter()}
        noDataDescription={getNoDataDescription()}
      />
    </>
  );
}
