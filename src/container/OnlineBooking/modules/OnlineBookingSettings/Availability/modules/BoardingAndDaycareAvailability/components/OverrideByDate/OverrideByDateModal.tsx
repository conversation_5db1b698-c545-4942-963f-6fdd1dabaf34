import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Checkbox, Condition, DateRangePicker, Modal, type ModalProps, Text, cn } from '@moego/ui';
import { useBoolean } from 'ahooks';
import { useSelector } from 'amos';
import { type Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';
import { selectCurrentBusiness } from '../../../../../../../../../store/business/business.selectors';
import { type TimeRangeOverridesRecord } from '../../../../../../../../../store/onlineBooking/settings/availabilityBD.types';
import { dateMessageToDayjs, stringToDateMessage } from '../../../../../../../../../utils/utils';
import { DefaultWorkTimePeriod } from '../../BoardingAndDaycareAvailability.options';
import { useBDArrivalPickupTime } from '../../hooks/useBDArrivalPickupTime';
import { TimePeriodWithService } from '../ArrivalPickupTime/CustomizeArrivalPickupTimeSelectorRow';
import { useSerialCallback } from '@moego/tools';

interface OverrideByDateModalProps extends Omit<ModalProps, 'ref'> {
  serviceItemType: ServiceItemType;
  defaultOverrideByDate: TimeRangeOverridesRecord;
  onSave: (overrideByDate: TimeRangeOverridesRecord) => Promise<void>;
  disabledDate: (date: Dayjs, viewDate: Dayjs | null) => boolean;
}

export const OverrideByDateModal: React.FC<OverrideByDateModalProps> = (props) => {
  const { serviceItemType, defaultOverrideByDate, isOpen, onSave, disabledDate, ...rest } = props;
  const [overrideByDate, setOverrideByDate] = useState<TimeRangeOverridesRecord>(defaultOverrideByDate);
  const [business] = useSelector(selectCurrentBusiness());
  const {
    arrivalPickUpTimeVal: { isCustomized },
  } = useBDArrivalPickupTime(serviceItemType);
  const [isStartValidation, isStartValidationActions] = useBoolean();
  const [viewDate, setViewDate] = useState<Dayjs | null>(null);

  const isEvaluation = serviceItemType === ServiceItemType.EVALUATION;
  const isValid = overrideByDate.startDate && overrideByDate.endDate;
  const showErrorMessage = isStartValidation && !isValid;

  const startDateValue = overrideByDate.startDate ? dateMessageToDayjs(overrideByDate.startDate) : null;
  const endDateValue = overrideByDate.endDate ? dateMessageToDayjs(overrideByDate.endDate) : null;

  const handleChange = (value: Partial<TimeRangeOverridesRecord>) => {
    setOverrideByDate({ ...overrideByDate, ...value });
  };

  const handleSave = useSerialCallback(async () => {
    isStartValidationActions.setTrue();
    if (!isValid) return;
    isStartValidationActions.setFalse();
    await onSave(overrideByDate);
  });

  useEffect(() => {
    if (isOpen) {
      setOverrideByDate(defaultOverrideByDate);
    }
  }, [isOpen]);

  return (
    <Modal
      title="Override by date"
      confirmText="Save"
      isOpen={isOpen}
      onConfirm={handleSave}
      autoCloseOnConfirm={false}
      confirmButtonProps={{
        isDisabled: handleSave.isBusy(),
      }}
      {...rest}
    >
      <div className="moe-flex moe-flex-col moe-gap-y-m moe-mt-m">
        <div className="moe-flex moe-flex-col moe-gap-y-s">
          {/* <Heading size="5">Variant {index + 1}</Heading> */}
          <div
            className={cn('moe-flex moe-items-center moe-gap-x-s moe-pb-s', {
              'moe-border-divider moe-border-b': overrideByDate.isAvailable,
            })}
          >
            <DateRangePicker
              className={'moe-w-[300px]'}
              format={business.dateFormat}
              disabledDate={(date) => disabledDate(date, viewDate)}
              placeholder={['Select date', 'Select date']}
              errorMessage={showErrorMessage ? 'Please select date(s)' : null}
              isInvalid={showErrorMessage}
              onOpenChange={(open) => {
                if (open) {
                  setViewDate(startDateValue ?? null);
                }
              }}
              onViewDatesChange={(value) => {
                setViewDate(value?.[0] ?? null);
              }}
              onChange={(value) => {
                const [startDate, endDate] = (value ?? [null, null]) as [Dayjs, Dayjs];

                handleChange({
                  startDate: startDate ? stringToDateMessage(startDate) : null,
                  endDate: endDate ? stringToDateMessage(endDate) : null,
                });
              }}
              value={[startDateValue, endDateValue]}
            />
            <div className={cn('moe-flex moe-items-center moe-gap-x-xs', { 'moe-mb-m': showErrorMessage })}>
              <Checkbox
                isSelected={!overrideByDate.isAvailable}
                onChange={(isSelected) => {
                  handleChange({
                    isAvailable: !isSelected,
                    // when not available, reset time period
                    ...(isSelected ? { timePeriod: [DefaultWorkTimePeriod] } : {}),
                  });
                }}
              />
              <Text variant="regular">Not available</Text>
            </div>
          </div>
          <Condition if={overrideByDate.isAvailable}>
            <TimePeriodWithService
              value={overrideByDate.dayTimeRanges}
              onChange={(dayTimeRanges) => {
                handleChange({ dayTimeRanges });
              }}
              serviceItemType={serviceItemType}
              showCanTakePets={isCustomized}
              buttonProps={{
                variant: 'primary',
                color: 'transparent',
              }}
              buttonClassName="moe-gap-xs -moe-right-m"
              timeRangePickerClassName="moe-flex-none moe-w-[300px]"
              countInputClassName="moe-w-auto"
              className={cn('moe-gap-x-s', isEvaluation && !isCustomized ? 'moe-w-fit' : 'moe-w-[calc(100%-88px)]')}
            />
          </Condition>
        </div>
      </div>
    </Modal>
  );
};
