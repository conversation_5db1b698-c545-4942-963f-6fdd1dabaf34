import { MinorInfoOutlined } from '@moego/icons-react';
import { type ColumnDef, Tooltip, createColumnHelper } from '@moego/ui';
import React, { useMemo } from 'react';
import { type OnlineBookingRequestRecord } from '../../../../../../../store/onlineBooking/models/OnlineBookingRequest';
import { type RecordProps } from '../../../../../../../store/utils/RecordMap';
import { ActionCell } from '../components/ActionCell';
import { ClientCell } from '../components/ClientCell';
import { PaymentCell } from '../components/PaymentCell';
import { PetCell } from '../components/PetCell';
import { ServiceCell } from '../components/ServiceCell/ServiceCell';
import { SubmittedTimeCell } from '../components/SubmittedTimeCell';
import { TimeCell } from '../components/TimeCell/TimeCell';

export type Data = RecordProps<OnlineBookingRequestRecord>;
export interface OnlineBookingRequestColumns {
  createdAt: () => ColumnDef<Data, string>;
  client: () => ColumnDef<Data, string>;
  pet: () => ColumnDef<Data, string>;
  service: () => ColumnDef<Data, string>;
  payment: () => ColumnDef<Data, string>;
  startDate: () => ColumnDef<Data, string>;
  action: () => ColumnDef<Data, string>;
}

const columnHelper = createColumnHelper<Data>();

const OnlineBookingRequestColumnsMap: OnlineBookingRequestColumns = {
  createdAt: () =>
    columnHelper.accessor('createTime', {
      id: 'createdAt',
      header: 'Submitted at',
      enableSorting: true,
      size: 156,
      cell: (props) => <SubmittedTimeCell createTime={props.getValue()} />,
    }),
  client: () => ({
    id: 'Client',
    header: 'Client',
    size: 218,
    cell: ClientCell,
  }),
  pet: () => ({
    id: 'Pet',
    header: 'Pet',
    minSize: 328,
    maxSize: 360,
    cell: PetCell,
  }),
  service: () => ({
    id: 'Service',
    header: 'Service',
    minSize: 288,
    maxSize: 320,
    cell: ServiceCell,
  }),
  startDate: () =>
    columnHelper.accessor('appointmentDate', {
      id: 'startDate',
      header: 'Appointment date',
      enableSorting: true,
      size: 230,
      cell: TimeCell,
    }),
  payment: () => ({
    id: 'payment',
    header: () => (
      <div className=" moe-flex">
        <div className="moe-mr-[4px]">Payment</div>
        <Tooltip
          side="top"
          content="Payment status, prepay amount, prepay percentage of total ticket"
          className="moe-w-[200px]"
        >
          <MinorInfoOutlined className="moe-text-icon-tertiary" />
        </Tooltip>
      </div>
    ),
    minSize: 128,
    cell: PaymentCell,
  }),
  action: () => ({
    accessorKey: 'id',
    id: 'Action',
    header: 'Action',
    size: 180,
    cell: ActionCell,
    meta: {
      sticky: 'right',
      classNames: 'moe-pl-[16px]',
    },
  }),
};

export const useOnlineBookingRequestColumns: () => ColumnDef<Data, string>[] = () => {
  return useMemo(() => Object.values(OnlineBookingRequestColumnsMap).map((column) => column()), []);
};
