import React, { memo, useMemo } from 'react';
import { ClearlyFilterValues, type SpecificFilterProps } from '../FilterProperty.options';
import { CheckboxFilter } from './common/CheckboxFilter';
import { FilterWrapper } from './common/FilterWrapper';
import { useFilterProperty } from '../../hooks/useFilterProperty';
import { getMultiSelectLabel } from '../FilterProperty.utils';
import { useSelector } from 'amos';
import { selectBusinessStaffs } from '../../../../../../store/staff/staff.selectors';
import { staffMapBox } from '../../../../../../store/staff/staff.boxes';

// use staff list

export const LeadAgent = memo<SpecificFilterProps>(({ type, property }) => {
  const { filter, handleChange, handleRemove } = useFilterProperty(property);
  const [staffList, staffMap] = useSelector(selectBusinessStaffs, staffMapBox);
  const staffOptionList = useMemo(() => {
    return staffList.toArray().map((id) => ({ label: staffMap.mustGetItem(id).fullName(), value: id.toString() }));
  }, [staffList, staffMap]);
  const dropdownValue = getMultiSelectLabel({ options: staffOptionList, values: filter.values });

  return (
    <FilterWrapper type={type} property={property} dropdownValue={dropdownValue} onClear={handleRemove}>
      <CheckboxFilter
        options={staffOptionList}
        value={ClearlyFilterValues(staffOptionList, filter.values)}
        onChange={handleChange}
      />
    </FilterWrapper>
  );
});
