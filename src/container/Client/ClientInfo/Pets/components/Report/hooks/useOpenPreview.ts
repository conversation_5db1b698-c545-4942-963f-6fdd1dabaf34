import { URL_CLIENT } from '../../../../../../../config/host/const';
import { getPreviewURL } from '../../../../../../settings/Settings/GroomingReportSetting/TemplatePreview/PreviewPortalLinkModal';
import { useOpenDailyEmail } from './useOpenDailyEmail';
import { useOpenGroomingEmail } from './useOpenGroomingEmail';

export const getDailyPreviewURL = (previewId: string, reportId?: string) => {
  return reportId
    ? `${URL_CLIENT}/daily/report/${previewId}?reportId=${reportId}`
    : `${URL_CLIENT}/daily/report/${previewId}`;
};

interface OpenLinkParams {
  uuid: string;
  reportId?: string;
}

export const useOpenPreview = () => {
  const openGroomingEmail = useOpenGroomingEmail();
  const openDailyEmail = useOpenDailyEmail();

  const openGroomingLink = (params: OpenLinkParams) => {
    window.open(getPreviewURL(params.uuid, Number(params.reportId)), '_blank', 'noopener,noreferrer');
  };

  const openDailyLink = (params: OpenLinkParams) => {
    window.open(getDailyPreviewURL(params.uuid, params.reportId), '_blank', 'noopener,noreferrer');
  };

  return {
    openGroomingLink,
    openDailyLink,
    openDailyEmail,
    openGroomingEmail,
  };
};
