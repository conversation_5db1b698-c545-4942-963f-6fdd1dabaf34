import { MinorEditOutlined, MinorTrashOutlined } from '@moego/icons-react';
import { AlertDialog, type ColumnDef, IconButton, createColumnHelper, toast } from '@moego/ui';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useSelector } from 'amos';
import { useMount } from 'ahooks';
import React from 'react';
import { useHistory } from 'react-router';
import { PATH_PAYMENT_DEPOSIT_ACTION } from '../../../../../../router/paths';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { deleteDepositRule, type DepositRuleVM } from '../../../../../../store/payment/actions/private/deposit.actions';
import { getAllCompanyFullServiceInfoList } from '../../../../../../store/service/actions/public/service.actions';
import { DepositEllipsisLines } from './DepositEllipsisLines';
import { DepositTable } from './DepositTable';
import { useFetchDepositRulesList } from './hooks/useDepositRulesList';
import { DepositDisplay } from './DepositDisplay';
import { useServiceIdMapping } from './hooks/useServiceIdMapping';
import { useDisplayBEFilters } from '../../../../../Client/ClientList/components/hooks/useDisplayBEFilters';
import { type BusinessRecord } from '../../../../../../store/business/business.boxes';
import { DepositRulesOBCAlert } from '../../../../../PaymentFlow/shared/DepositRulesAlert';

interface DepositSettingProps {
  business: BusinessRecord;
  loading: boolean;
  setAddDepositButtonDisabled: (disabled: boolean) => void;
}

const columnHelper = createColumnHelper<DepositRuleVM>();

export const DepositSetting = ({ loading, setAddDepositButtonDisabled }: DepositSettingProps) => {
  const history = useHistory();
  const dispatch = useDispatch();
  const [business] = useSelector(selectCurrentBusiness);
  const serviceIdMapping = useServiceIdMapping();
  const displayBEFilters = useDisplayBEFilters();
  const isMoeGoPay = business.preferMoeGoPay();

  useMount(() => {
    dispatch(getAllCompanyFullServiceInfoList({ withAddon: false }));
  });

  const { tableLoading, activeRulesList, inactiveRulesList, fetchDepositRules } =
    useFetchDepositRulesList(setAddDepositButtonDisabled);

  React.useEffect(() => {
    fetchDepositRules();
  }, [fetchDepositRules, business.id]);

  const mergedLoading = tableLoading || loading;

  const edit = (id: string) => {
    history.push(PATH_PAYMENT_DEPOSIT_ACTION.queried({ id }, { action: 'edit' }));
  };

  const remove = (id: string) =>
    AlertDialog.open({
      title: 'Delete deposit rule',
      content: 'Are you sure you want to delete this deposit rule? Please note this action is irreversible.',
      cancelText: 'Cancel',
      confirmText: 'Delete',
      confirmButtonProps: { color: 'danger' },
      onConfirm: async () => {
        await dispatch(deleteDepositRule(id));
        fetchDepositRules();
        AlertDialog.close();
        toast({
          type: 'success',
          title: 'Deposit rule deleted',
        });
      },
    });

  const rulesColumns: ColumnDef<DepositRuleVM, string>[] = [
    columnHelper.accessor((row) => row.ruleName, {
      id: 'ruleName',
      header: 'Rule name',
      meta: {
        className: ['moe-font-bold'],
      },
      size: 200,
    }),
    columnHelper.display({
      id: 'clientGroup',
      header: 'Client group',
      cell: ({ row }) => {
        const clientGroup = row.original.clientGroup;
        if (!clientGroup || Object.keys(clientGroup).length === 0) {
          return <>Everyone</>;
        }
        try {
          const json = JSON.parse(clientGroup.existingCustomersFilterJson || '{}') || {};
          const texts: string[] = [];
          if (clientGroup.newVisitors) {
            texts.push('New visitors');
          }
          if (clientGroup.existingCustomers) {
            if (!json.filters?.length) {
              texts.push('All existing clients');
            } else {
              texts.push(...displayBEFilters(json.filters || []).map(({ title, value }) => `${title}: ${value}`));
            }
          }
          return <DepositEllipsisLines limit={3} texts={texts} unit="criteria" />;
        } catch {
          return <>Invalid filter</>;
        }
      },
    }),
    columnHelper.display({
      id: 'serviceType',
      header: 'Service type',
      size: 200,
      cell: ({ row }) => {
        if (!row.original.serviceType || Object.keys(row.original.serviceType).length === 0) {
          return <>All services</>;
        }

        const texts = [];
        const { BOARDING, DAYCARE, GROOMING } = ServiceItemType;
        const services = [
          { type: BOARDING, label: 'boarding services' },
          { type: DAYCARE, label: 'daycare services' },
          { type: GROOMING, label: 'grooming services' },
        ];

        for (const service of services) {
          const values = row.original.serviceType[service.type];
          if (!values) {
            continue;
          }
          if (values.isAll) {
            texts.push(`All ${service.label}`);
          } else {
            const serviceNames = values.serviceIds.map((id) => serviceIdMapping.get(+id)?.name || id);
            texts.push(...serviceNames);
          }
        }

        const text = texts.join(', ');
        return (
          <div className="moe-flex moe-flex-col moe-gap-[4px]">
            <div className="moe-line-clamp-4">{text || '/'}</div>
          </div>
        );
      },
    }),
    columnHelper.display({
      id: 'dateRange',
      header: 'Date range',
      size: 160,
      cell: ({ row }) => {
        const range = row.original.dateRange;
        if (!range || !range[0] || !range[1]) {
          return 'No expiration';
        }
        return (
          <>
            {range[0].format('MM/DD/YYYY')} -<br />
            {range[1].format('MM/DD/YYYY')}
          </>
        );
      },
    }),
    columnHelper.display({
      id: 'deposit',
      header: 'Deposit',
      size: 120,
      cell: ({ row }) => <DepositDisplay depositConfig={row.original.depositConfig} business={business} />,
    }),
    columnHelper.display({
      id: 'action',
      header: 'Action',
      cell: (props) => (
        <div className="moe-flex moe-items-center moe-gap-[16px] moe-w-[112px]">
          <IconButton icon={<MinorEditOutlined />} tooltip="Edit rule" onPress={() => edit(props.row.id)} />
          <IconButton icon={<MinorTrashOutlined />} tooltip="Delete rule" onPress={() => remove(props.row.id)} />
        </div>
      ),
      size: 112,
    }),
  ];

  return (
    <div className="moe-flex moe-flex-col moe-w-full">
      {!isMoeGoPay ? <DepositRulesOBCAlert className="moe-mt-s" /> : null}
      <DepositTable
        title="Active deposit rules"
        dataSource={activeRulesList}
        columns={rulesColumns}
        loading={mergedLoading}
        active
      />
      <DepositTable
        title="Inactive deposit rules"
        dataSource={inactiveRulesList}
        columns={rulesColumns}
        loading={mergedLoading}
        active={false}
      />
    </div>
  );
};
