import React, { type FC } from 'react';
import { type CareTypeServiceDateTimeProps } from './timeCell.types';
import { Text } from '@moego/ui';
import dayjs from 'dayjs';
import { useSelector } from 'amos';
import { selectCurrentBusiness } from '../../../../../../../../store/business/business.selectors';
import { Condition } from '../../../../../../../../components/Condition';
import { T_MINUTE } from 'monofile-utilities/lib/consts';
import { selectCompanyCareTypeNameMap } from '../../../../../../../../store/careType/careType.selectors';

/**
 * it should be like:
 *   - <Daycare>: 01/26, 01/28, 01/30
 *   - <Daycare>: 01/26, 09:00 am - 05:00 pm
 */
export const DaycareDateTime: FC<CareTypeServiceDateTimeProps> = (props) => {
  const { showCareTypeName, rowData } = props;
  const { services } = rowData;

  const [business, companyCareTypeNameMap] = useSelector(selectCurrentBusiness, selectCompanyCareTypeNameMap);

  const firstDaycareServiceDetail = services?.find((service) => service.services.some((s) => s.daycare));
  const {
    startDate,
    startTime = 0,
    endTime = 0,
    specificDates = [],
  } = firstDaycareServiceDetail?.services.find((s) => s.daycare)?.daycare?.service || {};

  const appointmentDateDay = dayjs(startDate || specificDates[0]);

  const renderDateTime = () => {
    if (specificDates?.length > 1) {
      return (
        <Text as="span" variant="regular-short" className="moe-text-primary">
          {specificDates.map((date) => dayjs(date).format(business.dateFormatMD)).join(', ')}
        </Text>
      );
    }

    return (
      <div>
        <Text as="span" variant="regular-short" className="moe-text-primary moe-whitespace-nowrap">
          {appointmentDateDay.format(business.dateFormatMD)},&nbsp;{business.formatFixedTime(startTime * T_MINUTE)}
        </Text>
        <Condition if={endTime}>
          <Text as="span" variant="regular-short" className="moe-text-primary moe-whitespace-nowrap">
            &nbsp;-&nbsp;
            {business.formatFixedTime(endTime * T_MINUTE)}
          </Text>
        </Condition>
      </div>
    );
  };

  return (
    <div className="moe-text-primary">
      <Condition if={showCareTypeName}>
        <Text as="span" variant="regular-short" className="moe-font-bold">
          {companyCareTypeNameMap.Daycare}:&nbsp;
        </Text>
      </Condition>
      {renderDateTime()}
    </div>
  );
};
