/**
 * Full calendar callback hooks
 * 1. on event drop
 * 2. on event resize
 */

import type { EventDropArg } from '@fullcalendar/common';
import { type EventResizeDoneArg } from '@fullcalendar/interaction';
import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch } from 'amos';
import dayjs, { type Dayjs } from 'dayjs';
import { omit } from 'lodash';
import { useSetState } from 'react-use';
import { updateLodgingAddPlaceholderCard } from '../../../../store/calendarLatest/actions/private/lodgingCalendar.actions';
import { getIsDefaultArea } from '../../../../store/calendarLatest/calendar_data.utils';
import { getLodgingUnitIdFromCardUUID } from '../../../../store/calendarLatest/card.utils';
import {
  LodgingCalendarPlaceholderCardTypeMap,
  type LodgingViewPetUsage,
  lodgingApptCardMapBox,
} from '../../../../store/calendarLatest/lodgingCalendar.boxes';
import { isNormal } from '../../../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE } from '../../../../utils/DateTimeUtil';
import { useBool } from '../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useSplitLodgingEventModal } from './useSplitLodgingEventModal';
import { useLazySelectors } from '../../../../utils/unstable/createSelectAccessor';

export interface LodgingCardRescheduleInfo {
  cardId: string;
  newLodgingUnitId?: string;
  newStartDate?: Dayjs;
  newEndDate?: Dayjs;
  startTime?: number;
  endTime?: number;
}

interface FullCalendarDropResizeState extends LodgingCardRescheduleInfo {
  revertEvent: () => void;
}

export const isSameDate = (date1: Date | null, date2: Date | null) => dayjs(date1).isSame(date2, 'date');
export const setNewDate = (date1: Date | null, date2: Date | null) => {
  if (!date1 || !date2) {
    return date2;
  }
  return dayjs(date1).year(date2.getFullYear()).month(date2.getMonth()).date(date2.getDate()).toDate();
};

// 如果卡片的 unitId 与 oldResource 的 unitId 不同，则表示卡片被移除
export const isRemovedEvent = (eventInfo: EventDropArg | EventResizeDoneArg) => {
  const { event } = eventInfo;
  const eventUnitId = getLodgingUnitIdFromCardUUID(event.id);
  const oldResource = 'oldResource' in eventInfo ? eventInfo.oldResource : undefined;
  return oldResource && eventUnitId !== oldResource.id;
};

function checkTheSameDayForLodging(startDate: Date | null, petList: LodgingViewPetUsage[]): boolean {
  if (!startDate) {
    return false;
  }
  const startDay = dayjs(startDate).format(DATE_FORMAT_EXCHANGE);
  const allSpecificDates = petList
    .filter((pet) => pet.dateType === PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE)
    .flatMap((pet) => pet.specificDates);
  return allSpecificDates.includes(startDay);
}

export const useFullCalendarDropResize = () => {
  const [lodgingApptCardMap] = useLazySelectors(lodgingApptCardMapBox);
  const dispatch = useDispatch();
  const openSplitLodgingEventModal = useSplitLodgingEventModal();

  const rescheduleModalVisible = useBool();
  const [state, setState] = useSetState<FullCalendarDropResizeState>({
    cardId: '',
    newLodgingUnitId: '',
    newStartDate: dayjs(),
    newEndDate: dayjs(),
    startTime: 0,
    endTime: 0,
    revertEvent: () => {},
  });

  const handleEventDropResize = useLatestCallback((eventInfo: EventDropArg | EventResizeDoneArg) => {
    const { event, oldEvent, view } = eventInfo;

    const newResource = 'newResource' in eventInfo ? eventInfo.newResource : undefined;

    // 如果 event 被移除时，卡片在拖拽状态，则不会被移除，需要手动移除
    if (isRemovedEvent(eventInfo)) {
      event.remove();
      return;
    }

    // id will be undefined if the lodging unit is same before drop and after drop
    const { id: newLodgingUnitId } = newResource || {};

    const { start: oldStartDate, end: oldEndDate } = oldEvent;
    const { start: startDate, end: endDate, id, extendedProps } = event;
    const { appointmentId, type } = extendedProps;

    const { currentStart, currentEnd } = view;
    const { isDaycareApptCard, serviceItemType, petList, isSplitLodgingsCard } = lodgingApptCardMap().mustGetItem(id);

    if (isSplitLodgingsCard) {
      eventInfo.revert();
      openSplitLodgingEventModal(appointmentId);
      return;
    }

    const isEveryDay = petList.some((pet) => pet.dateType === PetDetailDateType.PET_DETAIL_DATE_EVERYDAY);
    const isDefaultArea = getIsDefaultArea(newLodgingUnitId || '');
    const isBoardingApptCard = serviceItemType === ServiceItemType.BOARDING;

    if (isBoardingApptCard && isDefaultArea) {
      eventInfo.revert();
      return;
    }

    // mark the event start time always be 12:00 and the end time is the next day 11:00
    const finalStartDate = setNewDate(oldStartDate, startDate);
    // daycare 肯定在一天内，所以取startDate 和 endTime即可
    const endMinutes = dayjs(endDate).getMinutes();
    const daycareEndDate = dayjs(startDate).setMinutes(endMinutes).toDate();
    const finalEndDate = isDaycareApptCard ? setNewDate(oldEndDate, daycareEndDate) : setNewDate(oldEndDate, endDate);
    event.setStart(finalStartDate!);
    event.setEnd(finalEndDate);

    const startDateChange = !isSameDate(finalStartDate, oldStartDate);
    const endDateChange = !isSameDate(finalEndDate, oldEndDate);

    const dateIsChange = startDateChange || endDateChange;
    const noChanges = !dateIsChange && !newLodgingUnitId;
    // It's not allowed to change end date same with start date when appointment date crossed over days before
    const isStartDateSameWithEndDate = !isDaycareApptCard && isSameDate(finalStartDate, finalEndDate);

    // if the "changed" date is out of the current view range, revert the event
    const dateOutOfRange =
      (finalStartDate! < currentStart && startDateChange) || (finalEndDate! > currentEnd && endDateChange);

    // 如果卡片是 daycare，时间选择了 every day，那么禁止拖动卡片。
    const daycareDateChangeForEveryDay = isDaycareApptCard && isEveryDay && dateIsChange;
    // 如果卡片是 daycare，选择了 certain day，那么禁止将卡片拖动到同一天。比如：选择了 9 号和 10 号，那么 9 号不能拖动到 10 号。
    const isTheSameDayForLodging =
      isDaycareApptCard && dateIsChange && checkTheSameDayForLodging(finalStartDate, petList);
    if (
      noChanges ||
      isStartDateSameWithEndDate ||
      dateOutOfRange ||
      daycareDateChangeForEveryDay ||
      isTheSameDayForLodging
    ) {
      eventInfo.revert();
      return;
    }

    // Add / New appointment placeholder card should not show reschedule modal
    if (!isNormal(appointmentId)) {
      if (type === LodgingCalendarPlaceholderCardTypeMap.Add) {
        dispatch(
          updateLodgingAddPlaceholderCard({
            cardId: id,
            lodgingUnitId: newLodgingUnitId,
            startDate: dateIsChange ? dayjs(finalStartDate) : undefined,
            endDate: dateIsChange ? dayjs(finalEndDate) : undefined,
          }),
        );
      }
      return;
    }
    setState({
      cardId: id,
      newLodgingUnitId,
      newStartDate: dateIsChange ? dayjs(finalStartDate) : undefined,
      newEndDate: dateIsChange ? dayjs(finalEndDate) : undefined,
      startTime: dayjs(startDate).getMinutes(),
      endTime: dayjs(endDate).getMinutes(),
      revertEvent: eventInfo.revert,
    });
    setTimeout(rescheduleModalVisible.open, 500);
  });

  return {
    event: {
      handleEventDropResize,
      handleRevertEvent: state.revertEvent,
    },
    rescheduleInfo: omit(state, 'revertEvent'),
    rescheduleModal: {
      visible: rescheduleModalVisible.value,
      handleClose: rescheduleModalVisible.close,
      dateIsChange: !!state.newStartDate,
    },
  };
};
