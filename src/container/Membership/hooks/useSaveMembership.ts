import { CalendarPeriod } from '@moego/api-web/google/type/calendar_period';
import { type DiscountDef, PeriodType } from '@moego/api-web/moego/models/membership/v1/membership_defs';
import { MembershipModelBillingCycle } from '@moego/api-web/moego/models/membership/v1/membership_models';
import { DiscountUnit, TargetSubType, TargetType } from '@moego/api-web/moego/models/membership/v1/redeem_models';
import { useDispatch, useSelector } from 'amos';
import { toastApi } from '../../../components/Toast/Toast';
import { currentAccountIdBox } from '../../../store/account/account.boxes';
import { createMembership, updateMembership } from '../../../store/membership/membership.actions';
import { type MembershipModel } from '../../../store/membership/membership.boxes';
import { AllServicesItemType } from '../../../store/service/category.boxes';
import { selectAllCategoriesOfAllServices } from '../../../store/service/category.selectors';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { ReportActionName } from '../../../utils/reportType';
import { reportData } from '../../../utils/tracker';
import { defaultLimitedValue } from '../consts';

const serviceType = [
  AllServicesItemType.boarding,
  AllServicesItemType.daycare,
  AllServicesItemType.grooming,
  AllServicesItemType.serviceAddon,
];
const typeMap = {
  [AllServicesItemType.boarding]: { type: TargetType.SERVICE, subType: TargetSubType.BOARDING },
  [AllServicesItemType.daycare]: { type: TargetType.SERVICE, subType: TargetSubType.DAYCARE },
  [AllServicesItemType.grooming]: { type: TargetType.SERVICE, subType: TargetSubType.GROOMING },
  [AllServicesItemType.serviceAddon]: { type: TargetType.ADDON, subType: TargetSubType.UNSPECIFIED },
};
/**
 * create or update membership
 * @param isEditMode
 * @returns
 */
export const useSaveMembership = (isEditMode: boolean) => {
  const dispatch = useDispatch();
  const accountId = useSelector(currentAccountIdBox);

  const [allServices] = useSelector(selectAllCategoriesOfAllServices(accountId, false, serviceType));

  const findServiceType = (serviceId: string | null | undefined): { type: TargetType; subType: TargetSubType } => {
    if (!serviceId) return typeMap[AllServicesItemType.boarding];
    const type =
      serviceType
        .flatMap((key) => allServices[key].map((item) => ({ ...item, highType: key })))
        .flatMap((item) => item.options.map((option) => ({ ...option, highType: item.highType })))
        .find((option) => +serviceId == option.value)?.highType || AllServicesItemType.boarding;
    return typeMap[type];
  };
  // eslint-disable-next-line sonarjs/cognitive-complexity
  return useLatestCallback(async (source: MembershipModel) => {
    if (source.taxId) {
      source.taxId = `${source.taxId}`;
    }
    let membershipDiscountBenefits: DiscountDef[] = [];
    if (source.includedDiscountTypes && source.includedDiscountTypes.length > 0) {
      if (source.includedDiscountTypes.includes(TargetType.SERVICE)) {
        const serviceDiscount: DiscountDef[] =
          source.serviceDiscounts?.map((item) => ({
            targetType: TargetType.SERVICE,
            targetIds: item.ids ?? [],
            value: item.count ? item.count : 0,
            unit: item.unit ?? DiscountUnit.PERCENT,
            isAll: item.isAll ?? false,
          })) || [];
        membershipDiscountBenefits = membershipDiscountBenefits.concat(serviceDiscount);
      }
      if (source.includedDiscountTypes.includes(TargetType.ADDON)) {
        const addOnDiscount: DiscountDef[] =
          source.addOnDiscounts?.map((item) => ({
            targetType: TargetType.ADDON,
            targetIds: item.ids ?? [],
            value: item.count ? item.count : 0,
            unit: item.unit ?? DiscountUnit.PERCENT,
            isAll: item.isAll ?? false,
          })) || [];
        membershipDiscountBenefits = membershipDiscountBenefits.concat(addOnDiscount);
      }
      if (source.includedDiscountTypes.includes(TargetType.PRODUCT)) {
        const productDiscount: DiscountDef[] =
          source.productDiscounts?.map((item) => ({
            targetType: TargetType.PRODUCT,
            targetIds: item.ids ?? [],
            value: item.count ? item.count : 0,
            unit: item.unit ?? DiscountUnit.PERCENT,
            isAll: item.isAll ?? false,
          })) || [];
        membershipDiscountBenefits = membershipDiscountBenefits.concat(productDiscount);
      }
      if (source.includedDiscountTypes.includes(TargetType.PACKAGE)) {
        const packageDiscount: DiscountDef[] =
          source.packageDiscounts?.map((item) => ({
            targetType: TargetType.PACKAGE,
            targetIds: item.ids ?? [],
            value: item.count ? item.count : 0,
            unit: item.unit ?? DiscountUnit.PERCENT,
            isAll: item.isAll ?? false,
          })) || [];
        membershipDiscountBenefits = membershipDiscountBenefits.concat(packageDiscount);
      }
    }
    source.billingCycle = MembershipModelBillingCycle.UNSPECIFIED;
    source.customizedBreed =
      source.customizedBreed?.filter((item) => {
        // 过滤掉只选择了 petType 没有选择任何一个 breed 的项
        return !(!item.isAll && item.breeds.length == 0);
      }) || [];
    // 如果全选时传入 customizedBreed，那么当 breed, coat, petSize 增加新选项时，customizedBreed, customizedCoat, customizedPetSizes 的值就不正确了，于是统一传空数组
    !source.breedFilter && (source.customizedBreed = []);
    !source.coatFilter && (source.customizedCoat = []);
    !source.petSizeFilter && (source.customizedPetSizes = []);
    source.customizedBreed = source.customizedBreed.map((item) => {
      const { isAll, breeds } = item;
      return {
        ...item,
        breeds: isAll ? [] : breeds,
      };
    });

    if (!isEditMode) {
      delete source.id;
      await dispatch(createMembership(source, membershipDiscountBenefits, findServiceType));
      reportData(ReportActionName.createMembership);
      toastApi.success('New membership added');
    } else {
      await dispatch(
        updateMembership({
          id: source.id!,
          revision: source.revision,
          membershipDef: source,
          membershipDiscountBenefits:
            (source?.includedDiscountTypes?.length ?? 0) > 0
              ? {
                  discounts: membershipDiscountBenefits,
                }
              : undefined,
          membershipQuantityBenefits:
            (source?.includeServices?.length ?? 0) > 0 && source.enableQuantityBenefits
              ? {
                  quantities: source.includeServices!.map((item) => {
                    const typeUnion = findServiceType(item.serviceId);
                    return {
                      targetId: item.serviceId + '',
                      targetType: typeUnion.type,
                      targetSubType: typeUnion.subType,
                      limitedValue: item.unlimited ? defaultLimitedValue.toString() : item.count + '',
                      isLimited: !item.unlimited,
                    };
                  }),
                  periodType: source.includeServiceExpired || PeriodType.FOLLOW_MEMBERSHIP,
                  specifiedPeriod: {
                    period:
                      source.includeServiceExpired === PeriodType.FOLLOW_MEMBERSHIP
                        ? (source.billingCyclePeriod.period ?? CalendarPeriod.MONTH)
                        : (source.includeServiceExpiredUnit ?? CalendarPeriod.MONTH),
                    value:
                      source.includeServiceExpired === PeriodType.FOLLOW_MEMBERSHIP
                        ? (source.billingCyclePeriod.value ?? 1)
                        : (source.includeServiceExpiredValue ?? 1),
                  },
                }
              : undefined,
        }),
      );
      toastApi.success(`Membership ${source.name} updated`);
    }
  });
};
