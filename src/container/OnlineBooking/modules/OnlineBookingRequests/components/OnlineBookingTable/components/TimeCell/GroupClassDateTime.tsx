import React, { type FC } from 'react';
import dayjs from 'dayjs';
import { useSelector } from 'amos';
import { selectCurrentBusiness } from '../../../../../../../../store/business/business.selectors';
import { T_MINUTE } from 'monofile-utilities/lib/consts';
import { Text } from '@moego/ui';
import { withPl } from '../../../../../../../../utils/calculator';
import { type CareTypeServiceDateTimeProps } from './timeCell.types';

/**
 * it should be like: 01/26 - 01/28, 9:00 am - 10:00 am
 */
export const GroupClassDateTime: FC<CareTypeServiceDateTimeProps> = (props) => {
  const { rowData } = props;
  const { appointmentDate, appointmentEndTime, appointmentStartTime, appointmentEndDate, services } = rowData;

  const [business] = useSelector(selectCurrentBusiness);

  const appointmentDateDay = dayjs(appointmentDate);
  const appointmentEndDateDay = dayjs(appointmentEndDate);
  const endTime = appointmentEndTime * T_MINUTE;
  const startTime = appointmentStartTime * T_MINUTE;

  const formattedDate = [appointmentDateDay, appointmentEndDateDay]
    .map((d) => d.format(business.dateFormatMD))
    .join(' - ');

  const formattedTime = [startTime, endTime].map((t) => business.formatFixedTime(t)).join(' - ');

  const firstGroupClassServiceDetail = services?.find((service) => service.services.some((s) => s.groupClass));
  const { numSessions = 0 } =
    firstGroupClassServiceDetail?.services.find((s) => s.groupClass)?.groupClass?.service || {};

  return (
    <div>
      <Text variant="regular-short">
        {formattedDate}, {formattedTime}
      </Text>
      <Text variant="small" className="moe-text-tertiary moe-mt-xxs">
        {withPl(numSessions, 'session')}
      </Text>
    </div>
  );
};
