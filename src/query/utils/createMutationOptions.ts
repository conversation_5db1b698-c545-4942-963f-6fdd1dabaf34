import { useMutation, type UseMutationOptions } from '@tanstack/react-query';

export function createMutationOptions<TData = unknown, TError = unknown, TVariables = void, TContext = unknown>(
  config: UseMutationOptions<TData, TError, TVariables, TContext>,
) {
  const getOptions = () => config;

  const useMutationHook = (options?: UseMutationOptions<TData, TError, TVariables, TContext>) => {
    return useMutation({ ...config, ...options });
  };

  getOptions.useMutation = useMutationHook;

  return getOptions;
}
