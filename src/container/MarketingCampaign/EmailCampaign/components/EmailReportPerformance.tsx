import { useDispatch, useSelector } from 'amos';
import React, { useEffect } from 'react';
import { useHistory } from 'react-router';
import { useSetState } from 'react-use';
import { Condition } from '../../../../components/Condition';
import { ErrorBoundary } from '../../../../components/ErrorBoundary/ErrorBoundary';
import { type PaginationOptions } from '../../../../components/Table/Table.types';
import { PATH_CUSTOMER_DETAIL } from '../../../../router/paths';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import {
  type EmailCampaignReportBooking,
  type EmailCampaignReportDetail,
  type EmailCampaignReportRecipient,
  getEmailCampaignBookings,
  getEmailCampaignRecipients,
} from '../../../../store/marketingCampaign/emailCampaign.action';
import { EmailCampaignPerformance } from '../../../../store/marketingCampaign/emailCampaign.util';
import { type PageParams } from '../../../../store/utils/PagedList';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useOpenApptDetailDrawer } from '../../../../utils/hooks/useOpenApptDetailDrawer';
import { type SerialCallback, useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { StyledTable } from '../../../settings/components/StyledTable';
import { useBookingsNum } from '../hooks/useBookingsNum';
import { useRecipients } from '../hooks/useRecipients';
import { useReportBookings } from '../hooks/useReportBookings';
import { EmailReportDashboard } from './EmailReportDashboard';
import { EmailReportFilterCriteria } from './EmailReportFilterCriteria';
import { PerformanceBox } from './EmailReportPerformance.style';

const DefaultPageSize = 10;

export interface EmailReportPerformanceProps {
  detail?: EmailCampaignReportDetail | null;
  getDetail: SerialCallback<() => Promise<void>>;
}

export function EmailReportPerformance(props: EmailReportPerformanceProps) {
  const { detail, getDetail } = props;
  const { id } = detail ?? { id: undefined };
  const history = useHistory();
  const dispatch = useDispatch();
  const [state, setState] = useSetState<{
    recipients: EmailCampaignReportRecipient[];
    appointments: EmailCampaignReportBooking[];
    activeKey: number;
    pagination: PageParams & { total: number };
  }>({
    recipients: [],
    appointments: [],
    activeKey: EmailCampaignPerformance.Sent,
    pagination: { pageNo: 1, pageSize: DefaultPageSize, total: 0 },
  });
  const { activeKey } = state;
  const [permissions] = useSelector(selectCurrentPermissions());

  const canViewClientProfile = permissions.has('viewIndividualClientProfile');
  const isBookings = activeKey === EmailCampaignPerformance.Bookings;
  const { num: bookingsNum } = useBookingsNum(id);
  const { openApptDetailDrawer } = useOpenApptDetailDrawer();
  const getList = useSerialCallback(async (params?: Partial<PageParams>) => {
    if (!id) {
      return;
    }
    const { pagination } = state;
    if (isBookings) {
      const { appointments, pagination: lastPagination } = await dispatch(
        getEmailCampaignBookings(id, { ...pagination, ...params }),
      );
      const { pageNo, pageSize, total } = lastPagination;
      setState({
        appointments,
        pagination: { pageNo: Number(pageNo), pageSize: Number(pageSize), total: Number(total) },
      });
      return;
    }
    const { recipients, pagination: lastPagination } = await dispatch(
      getEmailCampaignRecipients(id, { ...pagination, ...params }, activeKey),
    );
    const { pageNo, pageSize, total } = lastPagination;

    setState({
      recipients,
      pagination: { pageNo: Number(pageNo), pageSize: Number(pageSize), total: Number(total) },
    });
  });
  const loading = getList.isBusy();
  const onOpenBookingModal = (id: string) => {
    openApptDetailDrawer({
      ticketId: +id,
    });
  };
  const { columns } = useRecipients({ activeKey, getList, getDetail });
  const { columns: bookingColumns } = useReportBookings({
    activeKey,
    getList,
    onOpenBookingModal,
  });

  const onChangePagination = useLatestCallback((pagination: PaginationOptions) => {
    const nextPageSize = pagination.pageSize ?? DefaultPageSize;
    getList({ pageNo: pagination.current ?? 1, pageSize: nextPageSize });
  });

  useEffect(() => {
    getList({ pageNo: 1, pageSize: DefaultPageSize });
  }, [activeKey, id]);

  return (
    <PerformanceBox className="!moe-bg-white !moe-p-[24px] !moe-rounded-[8px] !moe-mt-[20px]">
      <div className="!moe-mb-[24px] !moe-text-[20px] !moe-text-[#333] !moe-font-bold !moe-leading-[24px]">
        Performance
      </div>
      <EmailReportDashboard
        detail={detail}
        activeKey={state.activeKey}
        onChangeActive={(activeKey) => setState({ activeKey })}
        bookingsNum={bookingsNum}
      />
      <div className="!moe-mt-[24px] !moe-mb-[16px] !moe-text-[16px] !moe-text-[#333] !moe-leading-[20px] !moe-font-bold">
        Recipients
      </div>
      {/* Too much logic in filter, in case of error */}
      <ErrorBoundary fallbackRender={() => null}>
        <Condition if={activeKey === EmailCampaignPerformance.Sent}>
          <EmailReportFilterCriteria searchString={detail?.clientFilter || ''} />
        </Condition>
      </ErrorBoundary>
      {activeKey === EmailCampaignPerformance.Bookings ? (
        <StyledTable
          rowKey={(record: EmailCampaignReportBooking) => record.id}
          loading={loading}
          pagination={{ ...state.pagination, current: state.pagination.pageNo }}
          rowClassName="!moe-cursor-pointer"
          columns={bookingColumns}
          dataSource={loading ? [] : state.appointments}
          onChange={onChangePagination}
          onRow={(record) => ({
            onClick: () => onOpenBookingModal(record.id),
          })}
          locale={{
            emptyText: <div className="!moe-text-left !moe-text-sm !moe-text-[#666]">No recipients have booked.</div>,
          }}
        />
      ) : (
        <StyledTable
          rowKey={(record: EmailCampaignReportRecipient) => record.recipientId}
          loading={loading}
          pagination={{ ...state.pagination, current: state.pagination.pageNo }}
          rowClassName="!moe-cursor-pointer"
          columns={columns}
          dataSource={loading ? [] : state.recipients}
          onChange={onChangePagination}
          onRow={(record) => ({
            onClick: () => {
              if (!canViewClientProfile) return;
              history.push(PATH_CUSTOMER_DETAIL.queried({ goBack: true }, { customerId: Number(record.customerId) }));
            },
          })}
          locale={{
            emptyText: (
              <div className="!moe-text-left !moe-text-sm !moe-text-[#666]">
                No recipients have {EmailCampaignPerformance.mapLabels[activeKey]?.toLowerCase()}.
              </div>
            ),
          }}
        />
      )}
    </PerformanceBox>
  );
}
