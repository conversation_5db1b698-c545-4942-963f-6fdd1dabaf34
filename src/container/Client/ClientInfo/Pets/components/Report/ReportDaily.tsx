import { type DailyReportConfigDef } from '@moego/api-web/moego/models/appointment/v1/daily_report_defs';
import { ReportCardStatus, SendMethod } from '@moego/api-web/moego/models/appointment/v1/daily_report_enums';
import { Button, Table, type TableProps } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { memo } from 'react';
import { useAsync, useSetState } from 'react-use';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { getDailyReportCardList } from '../../../../../../store/overview/actions/public/reportCard.action';
import { isNormal } from '../../../../../../store/utils/identifier';
import { dateMessageToDayjs } from '../../../../../../utils/utils';
import { useOpenPreview } from './hooks/useOpenPreview';
import { type ReportCardProps } from './type';

export const ReportDaily = memo((props: ReportCardProps) => {
  const { petId } = props;

  const dispatch = useDispatch();
  const [{ pageIndex, pageSize }, setState] = useSetState<{ pageIndex: number; pageSize: number }>({
    pageIndex: 1,
    pageSize: 10,
  });
  const [business] = useSelector(selectCurrentBusiness());
  const { openDailyLink, openDailyEmail } = useOpenPreview();

  const columns: TableProps<DailyReportConfigDef>['columns'] = [
    {
      id: 'serviceDate',
      header: 'Date of report',
      size: 150,
      cell: ({ row }) => {
        return business.formatDate(dateMessageToDayjs(row.original.serviceDate));
      },
    },
    {
      id: 'sendTime',
      header: 'Last sent',
      size: 200,
      cell: ({ row }) => {
        return business.formatDateTime(dayjs(row.original.sendTime));
      },
    },
    {
      id: 'sendMethod',
      header: 'Send via',
      size: 100,
      cell: ({ row }) => {
        const { sendMethod } = row.original;
        return sendMethod === SendMethod.EMAIL ? 'Email' : sendMethod === SendMethod.SMS ? 'SMS' : '';
      },
    },
    {
      header: 'Action',
      cell: ({ row }) => {
        const { sendMethod, uuid, id } = row.original;
        return (
          <Button
            variant="tertiary"
            onPress={() => {
              if (sendMethod === SendMethod.EMAIL) {
                openDailyEmail({ value: row.original });
              } else {
                openDailyLink({
                  uuid,
                  reportId: id,
                });
              }
            }}
          >
            View
          </Button>
        );
      },
    },
  ];

  const { value, loading } = useAsync(async () => {
    if (!isNormal(petId)) return;
    const { pagination, reportConfigs } = await dispatch(
      getDailyReportCardList({
        businessId: String(business.id),
        // serviceItemTypes 空则全部
        filter: {
          serviceItemTypes: [],
          petId: String(petId),
          status: ReportCardStatus.REPORT_CARD_SENT,
          dailyReportIds: [],
        },
        pagination: { pageNum: pageIndex, pageSize },
      }),
    );
    return {
      pagination,
      reportConfigs,
    };
  }, [pageIndex, pageSize, petId, business]);

  const { reportConfigs = [], pagination } = value || {};

  return (
    <Table
      data={reportConfigs}
      getRowId={(row) => row.id}
      columns={columns}
      isLoading={loading}
      pagination={{
        pageIndex: pageIndex,
        pageSize: pageSize,
        totalSize: pagination?.total || 0,
      }}
      onPaginationChange={(v) => {
        setState({
          pageIndex: v.pageIndex,
          pageSize: v.pageSize,
        });
      }}
    />
  );
});
