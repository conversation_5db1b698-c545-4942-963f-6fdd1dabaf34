import { useDispatch, useSelector } from 'amos';
import React, { memo, type ReactNode } from 'react';
import { apptDetailDrawerBox, apptDetailDrawerEditBox } from '../../../../../store/calendarLatest/calendar.boxes';
import { ApptNavigationTabsV3 } from '../../../../Calendar/latest/AwesomeCalendar.utils';
import { ApptTicketCommentPanel } from '../ApptTicketCommentPanel/ApptTicketCommentPanel';
import { ApptActivityLog } from '../ApptActivityLog/ApptActivityLog';
import { QuickAddFeedMedication } from '../../../components/EditPetFeedMedication/QuickAddFeedMedication';
import { setApptDetailDrawerEdit } from '../../../../../store/calendarLatest/actions/private/calendar.actions';
import { type CommentNoteType } from '../../../../../store/calendarLatest/calendar.utils';
import { ApptPetBelongings } from '../ApptPetBelongings/ApptPetBelongings';
import { ApptInfoPanel } from '../ApptInfoPanel/ApptInfoPanel';
import { cn, Spin } from '@moego/ui';
import { ApptDetailNavigation } from '../ApptDetailNavigation';

export const ApptTicketSideInfo = memo(() => {
  const dispatch = useDispatch();
  const [{ ticketId, loading }, editInfo] = useSelector(apptDetailDrawerBox, apptDetailDrawerEditBox);

  let tabContent: ReactNode = null;
  switch (editInfo.activeTab) {
    case ApptNavigationTabsV3.TICKET_COMMENT:
      tabContent = (
        <ApptTicketCommentPanel
          ticketId={ticketId}
          defaultPetId={editInfo.petNotesDefaultPetId}
          activeTab={editInfo.ticketCommentsActiveTab}
          onTabChange={(key) => dispatch(setApptDetailDrawerEdit({ ticketCommentsActiveTab: key as CommentNoteType }))}
        />
      );
      break;
    case ApptNavigationTabsV3.ACTIVITY_LOG:
      tabContent = <ApptActivityLog ticketId={ticketId} />;
      break;
    case ApptNavigationTabsV3.FEEDING_AND_MEDICATION:
      tabContent = (
        <div className="moe-h-full moe-flex">
          <QuickAddFeedMedication
            appointmentId={String(ticketId)}
            goBack={() => {
              dispatch(setApptDetailDrawerEdit({ activeTab: ApptNavigationTabsV3.INFO }));
            }}
          />
        </div>
      );
      break;
    case ApptNavigationTabsV3.PET_BELONGINGS:
      tabContent = <ApptPetBelongings />;
      break;
    case ApptNavigationTabsV3.INFO:
      // 直接用 Spin 套住整个容器会有 flex 高度问题，所以单独处理了一下
      tabContent = (
        <>
          <ApptInfoPanel ticketId={ticketId} />
          {loading && <Spin className="!moe-absolute moe-top-1/2 moe-left-1/2 moe-z-[10] " />}
        </>
      );
      break;
  }

  return (
    <>
      <ApptDetailNavigation
        appointmentId={String(ticketId)}
        className="moe-pt-[32px]"
        activeTab={editInfo.activeTab}
        onChangeActiveTab={(activeTab) => dispatch(setApptDetailDrawerEdit({ activeTab }))}
      />
      <div
        className={cn('moe-flex-1 moe-relative moe-min-w-0 moe-overflow-auto', {
          'moe-opacity-[0.5] moe-cursor-not-allowed moe-pointer-events-none': loading,
        })}
      >
        {tabContent}
      </div>
    </>
  );
});
