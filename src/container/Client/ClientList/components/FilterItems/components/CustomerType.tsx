import { memo } from 'react';
import { ClearlyFilterValues, CustomerTypeOptions, type SpecificFilterProps } from '../FilterProperty.options';
import React from 'react';
import { useFilterProperty } from '../../hooks/useFilterProperty';
import { getMultiSelectLabel } from '../FilterProperty.utils';
import { CheckboxFilter } from './common/CheckboxFilter';
import { FilterWrapper } from './common/FilterWrapper';

export const CustomerType = memo<SpecificFilterProps>(({ type, property }) => {
  const { filter, handleChange, handleRemove } = useFilterProperty(property);
  const dropdownValue = getMultiSelectLabel({ options: CustomerTypeOptions, values: filter.values });

  return (
    <FilterWrapper type={type} property={property} dropdownValue={dropdownValue} onClear={handleRemove}>
      <CheckboxFilter
        options={CustomerTypeOptions}
        onChange={handleChange}
        value={ClearlyFilterValues(CustomerTypeOptions, filter.values)}
      />
    </FilterWrapper>
  );
});
