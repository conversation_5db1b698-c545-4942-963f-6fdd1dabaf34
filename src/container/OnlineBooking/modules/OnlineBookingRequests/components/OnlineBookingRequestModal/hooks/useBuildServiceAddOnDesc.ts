import {
  type BoardingAddOnDetail,
  type DaycareAddOnDetail,
} from '@moego/api-web/moego/api/online_booking/v1/booking_request_api';
import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { isNil } from 'lodash/fp';
import { DateTypeWordingMap, SelectDateTypeEnum } from '../../../../../../../components/DateType/DateType.utils';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import {
  type MergedOBAddOnDetail,
  type MergedOBServiceDetail,
} from '../../../../../../../store/onlineBooking/models/OnlineBookingLatestRequest';
import { onlineBookingLatestRequestMapBox } from '../../../../../../../store/onlineBooking/onlineBooking.boxes';
import { ID_ANONYMOUS } from '../../../../../../../store/utils/identifier';
import { useFormatInstanceOccurrence } from '../../../../../../../utils/hooks/useFormatInstanceOccurrence';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';

interface ServiceDetailWithDateTypeInfo {
  /** The name of the service / add-on */
  serviceName: string;
  /** The flag to indicate if the add-on service is everyday */
  isEveryday?: boolean;
  /** date type */
  dateType?: PetDetailDateType | undefined;
  /**
   * start date
   * Use start_date when date_type is PET_DETAIL_DATE_DATE_POINT
   */
  startDate?: string | undefined;
  /** quantity per day */
  quantityPerDay?: number;
  /** The specific dates of the add-on service */
  specificDates?: string[];
  requireDedicatedStaff?: boolean;
}
/**
 * 处理 add-on 在 drawer 中的展示逻辑
 * 这里传值假设都是同一只 pet 下，使用时需预先过滤
 */

export const useBuildServiceAddOnDesc = (bookingRequestId?: number) => {
  const [business, request] = useSelector(
    selectCurrentBusiness,
    onlineBookingLatestRequestMapBox.mustGetItem(bookingRequestId ?? ID_ANONYMOUS),
  );
  const formatInstanceOccurrence = useFormatInstanceOccurrence();

  const buildServiceAddOnDescWithoutDatePoints = useLatestCallback(
    (mainCareType: ServiceItemType, addOnDetail: ServiceDetailWithDateTypeInfo) => {
      const { serviceName } = addOnDetail;
      const resultTextList = [serviceName];
      if (mainCareType === ServiceItemType.BOARDING) {
        const { dateType, specificDates = [], quantityPerDay = 1, requireDedicatedStaff = false } = addOnDetail;
        if (dateType && dateType !== PetDetailDateType.PET_DETAIL_DATE_DATE_POINT) {
          resultTextList.push(' (');
          switch (dateType) {
            case PetDetailDateType.PET_DETAIL_DATE_EVERYDAY:
            case PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY:
              resultTextList.push(SelectDateTypeEnum.mapLabels[dateType].label);
              break;
            case PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE:
              resultTextList.push(specificDates.map((date) => business.formatFixedDate(date)).join(', '));
              break;
            case PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY:
            case PetDetailDateType.PET_DETAIL_DATE_LAST_DAY:
            case PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY:
              resultTextList.push(DateTypeWordingMap[dateType]);
              break;
            default:
              // dateType 的 case 已覆盖，纯占位
              break;
          }

          if (!requireDedicatedStaff) {
            resultTextList.push(`, x${quantityPerDay} per day`);
          }

          resultTextList.push(')');
        }
      } else if (
        mainCareType === ServiceItemType.DAYCARE &&
        // 非 requiredDedicatedStaff 的 daycare add-on 才展示日期
        !(addOnDetail as unknown as DaycareAddOnDetail).requireDedicatedStaff
      ) {
        const { specificDates, isEveryday, quantityPerDay } = addOnDetail as unknown as DaycareAddOnDetail;
        if (isEveryday) {
          resultTextList.push(' (Everyday');
        } else {
          resultTextList.push(' (');
          resultTextList.push(specificDates.map((date) => business.formatFixedDate(date)).join(', '));
        }
        resultTextList.push(`, x${quantityPerDay} per day`);
        resultTextList.push(')');
      }

      // grooming 暂无须做处理（包括未来 dog walking）
      return resultTextList.join('');
    },
  );

  const buildAddOnDescList = useLatestCallback(
    (mainCareType: ServiceItemType, addOnDetailList: MergedOBAddOnDetail[]) => {
      const isMainGrooming = mainCareType === ServiceItemType.GROOMING;
      const isMainBoarding = mainCareType === ServiceItemType.BOARDING;
      const isAddOnDetailSupportDatePoint = (detail: MergedOBAddOnDetail) => {
        return (
          (detail.serviceItemType === ServiceItemType.BOARDING &&
            (detail as unknown as BoardingAddOnDetail).dateType === PetDetailDateType.PET_DETAIL_DATE_DATE_POINT) ||
          (detail.serviceItemType === ServiceItemType.DAYCARE &&
            (detail as unknown as DaycareAddOnDetail).requireDedicatedStaff) ||
          detail.serviceItemType === ServiceItemType.GROOMING
        );
      };

      // date point 类型的 add-on 需要合并展示，先做准备
      const datePointAddOns = addOnDetailList.filter(isAddOnDetailSupportDatePoint);
      const datePointListMap = new Map<string, string[]>();
      datePointAddOns.forEach((detail) => {
        const serviceItemType = detail.serviceItemType;
        const { addOnId, startDate, specificDates } = detail as unknown as BoardingAddOnDetail & DaycareAddOnDetail;
        if (!datePointListMap.has(addOnId)) {
          datePointListMap.set(addOnId, []);
        }
        const realStartDate = serviceItemType === ServiceItemType.DAYCARE ? specificDates[0] : startDate;
        if (realStartDate) {
          datePointListMap.get(addOnId)?.push(realStartDate);
        }
      });

      /**
       * 开始处理整个列表，涉及合并的只处理第一个元素
       */
      const resultList: string[] = [];
      const datePointFlagMap = new Map<string, boolean>();
      addOnDetailList.forEach((detail) => {
        if (isAddOnDetailSupportDatePoint(detail)) {
          if (datePointFlagMap.get(detail.addOnId)) return;
          const { serviceName } = detail;
          const dateType = (detail as ServiceDetailWithDateTypeInfo)?.dateType;
          const currentDatePointList = datePointListMap.get(detail.addOnId) || [];
          // grooming 的 add-on 不需要处理
          if (currentDatePointList.length && !isMainGrooming) {
            resultList.push(
              `${serviceName} (${currentDatePointList.map((date) => business.formatFixedDate(date)).join(', ')})`,
            );
          } else if (dateType && isMainBoarding) {
            const dateTypeName = DateTypeWordingMap[dateType];
            resultList.push(`${serviceName} (${dateTypeName})`);
          } else {
            resultList.push(serviceName);
          }
          datePointFlagMap.set(detail.addOnId, true);
          return;
        }
        resultList.push(buildServiceAddOnDescWithoutDatePoints(mainCareType, detail));
      });

      return resultList;
    },
  );

  const buildServiceDescList = useLatestCallback((serviceDetailList: MergedOBServiceDetail[]) => {
    const [mainServiceDetail, ...additionalServiceDetailList] = serviceDetailList;
    if (!mainServiceDetail) return [];
    const { serviceName, occurrence, numSessions, startTime } = mainServiceDetail;

    const occurrenceLabel =
      occurrence &&
      numSessions &&
      !isNil(startTime) &&
      formatInstanceOccurrence({
        occurrence,
        sessionCount: numSessions,
        startTime: dayjs(request.appointmentDate).setMinutes(startTime),
      });

    const resultList: string[] = [occurrenceLabel ? `${serviceName} (${occurrenceLabel})` : serviceName];

    const datePointListMap = new Map<string, string[]>();
    additionalServiceDetailList
      // 当下只对 grooming 做特殊处理，未来再扩展考虑
      ?.filter((detail) => detail.serviceItemType === ServiceItemType.GROOMING)
      .forEach((detail) => {
        const { serviceId, startDate } = detail;
        if (!datePointListMap.has(serviceId)) {
          datePointListMap.set(serviceId, []);
        }
        if (startDate) {
          datePointListMap.get(serviceId)?.push(startDate);
        }
      });

    const datePointFlagMap = new Map<string, boolean>();
    additionalServiceDetailList?.forEach((detail) => {
      if (detail.serviceItemType === ServiceItemType.GROOMING) {
        if (datePointFlagMap.has(detail.serviceId)) return;
        const { serviceName, dateType } = detail;
        const currentDatePointList = datePointListMap.get(detail.serviceId) || [];
        const currentDatePointName = currentDatePointList.map((date) => business.formatFixedDate(date)).join(', ');
        const currentDateTypeName = dateType ? DateTypeWordingMap[dateType] : '';
        const timeDescription = currentDatePointName || currentDateTypeName || '';

        const mainServiceCareType = mainServiceDetail.serviceItemType;
        if (mainServiceCareType === ServiceItemType.DAYCARE) {
          resultList.push(serviceName);
        } else {
          const serviceWithTime = serviceName + (timeDescription ? ` (${timeDescription})` : '');
          resultList.push(serviceWithTime);
        }
        datePointFlagMap.set(detail.serviceId, true);
      }
    });

    return resultList;
  });

  return { buildAddOnDescList, buildServiceDescList };
};
