import { useDispatch } from 'amos';
import { useCallback, useState } from 'react';
import {
  type DepositRuleVM,
  listDepositRules,
} from '../../../../../../../store/payment/actions/private/deposit.actions';

export const useFetchDepositRulesList = (setAddDepositButtonDisabled: (disabled: boolean) => void) => {
  const [activeRulesList, setActiveRulesList] = useState<DepositRuleVM[]>([]);
  const [inactiveRulesList, setInactiveRulesList] = useState<DepositRuleVM[]>([]);
  const [tableLoading, setTableLoading] = useState(true);

  const dispatch = useDispatch();
  const fetchDepositRules = useCallback(async () => {
    setTableLoading(true);
    try {
      const { activeRules, inactiveRules, total } = await dispatch(listDepositRules());
      setActiveRulesList(activeRules);
      setInactiveRulesList(inactiveRules);
      setAddDepositButtonDisabled(total >= 10);
    } catch {
      setActiveRulesList([]);
      setInactiveRulesList([]);
      setAddDepositButtonDisabled(false);
    }

    setTableLoading(false);
  }, [setAddDepositButtonDisabled, dispatch]);

  return {
    activeRulesList,
    inactiveRulesList,
    tableLoading,
    fetchDepositRules,
  };
};
