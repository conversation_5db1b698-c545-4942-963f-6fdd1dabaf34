import { Heading, Text } from '@moego/ui';
import React, { memo } from 'react';
import { useInvoiceReinvent } from '../../../../PaymentFlow/hooks/useInvoiceReinvent';
import { SettingsTitle } from '../components/SettingsTitle';
import { OnlineBookingNav } from '../types';
import { CertainClientsSetting } from './CertainClientsSetting/CertainClientsSetting';
import { DefaultSetting } from './DefaultSetting/DefaultSetting';
import { DepositSetting } from './DepositSetting/DepositSetting';
import { PaymentContainer } from './Payment.style';
import { PaymentSettingProvider } from './components/PaymentSettingContext';

export const Payment = memo<{}>(() => {
  const { isNewOrderV4Flow } = useInvoiceReinvent();
  return (
    <PaymentSettingProvider>
      <SettingsTitle
        title={OnlineBookingNav.Payments}
        description={
          <Text variant="small" className="moe-text-tertiary">
            {'Priority: Deposit rules > Customized payment option by clients > Default payment option.'}
          </Text>
        }
      />
      <PaymentContainer className="moe-pb-[20px]">
        {isNewOrderV4Flow ? (
          <>
            <DepositSetting />
            <Heading size="3" className="moe-mb-m">
              Other payment options
            </Heading>
          </>
        ) : null}
        <DefaultSetting />
        <CertainClientsSetting className="moe-mt-[40px]" />
      </PaymentContainer>
    </PaymentSettingProvider>
  );
});
