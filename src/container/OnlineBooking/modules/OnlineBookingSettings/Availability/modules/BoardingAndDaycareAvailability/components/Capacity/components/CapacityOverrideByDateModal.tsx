import { type DateMessage } from '@moego/api-web/google/type/date';
import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import {
  type CapacityOverrideDef,
  type CapacityOverrideDefCapacityDateRangeDef,
} from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_defs';
import { CapacityOverrideUnitType } from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import { Alert, Form, Input, Modal, type ModalProps, Select, numberToString, useForm, useWatch } from '@moego/ui';
import { cloneDeep } from 'lodash';
import React, { useEffect } from 'react';
import { Condition } from '../../../../../../../../../../components/Condition';
import { createStrictEnum } from '../../../../../../../../../../store/utils/createEnum';
import { type CapacityOverrideField, type DateRanges, SelectDateRange } from './SelectDateRange';
import { useSerialCallback } from '@moego/tools';

interface CapacityOverrideByDateModalProps extends Omit<ModalProps, 'ref'> {
  serviceItemType: ServiceItemType;
  onSave: (overrideByDate: CapacityOverrideDef) => Promise<void>;
  item?: CapacityOverrideDef;
}

const CapacityType = createStrictEnum({
  NumberOfPets: [CapacityOverrideUnitType.PET, 'Number of pets'],
  Percent: [CapacityOverrideUnitType.PERCENT, 'Percent'],
});

export const CapacityOverrideByDateModal: React.FC<CapacityOverrideByDateModalProps> = (props) => {
  const { serviceItemType, onSave, item, onClose } = props;

  const form = useForm<CapacityOverrideField>({
    defaultValues: {
      unitType: CapacityOverrideUnitType.PET,
      dateRanges: { 0: [null, null] } as DateRanges,
    },
    mode: 'onBlur',
  });
  const [dateRanges, unitType] = useWatch({
    control: form.control,
    name: ['dateRanges', 'unitType'],
  });
  const dateRangeKeys = Object.keys(dateRanges);

  useEffect(() => {
    if (item) {
      const dates = {} as Record<string, [DateMessage | null, DateMessage | null]>;
      item?.dateRanges?.map((item, index) => {
        dates[index + 1] = [item.startDate, item.endDate];
      });

      form.setValue('unitType', item?.unitType);
      form.setValue('dateRanges', dates);
      form.setValue('capacity', item?.capacity);
    }
  }, [item, form]);

  const handleSave = useSerialCallback(async (data: CapacityOverrideField) => {
    await onSave({
      capacity: data.capacity,
      unitType: data.unitType,
      dateRanges: Object.values(data.dateRanges).map(
        ([startDate, endDate]) => ({ startDate, endDate }) as CapacityOverrideDefCapacityDateRangeDef,
      ),
    });
  });

  const handleSubmit = () => {
    return form.handleSubmit(async (data) => {
      await handleSave(data);
    })();
  };

  return (
    <Modal
      title="Override by date"
      confirmText="Save"
      isOpen
      onConfirm={handleSubmit}
      onClose={onClose}
      autoCloseOnConfirm={false}
      confirmButtonProps={{
        isDisabled: handleSave.isBusy(),
      }}
      size="s"
    >
      <Form form={form} footer={null}>
        <div className="moe-flex moe-flex-col moe-gap-y-m">
          <div className="moe-flex moe-flex-col moe-gap-8px-300">
            <div className="moe-flex moe-flex-col moe-gap-8px-100">
              {dateRangeKeys.map((key, index) => {
                return (
                  <SelectDateRange
                    form={form}
                    serviceItemType={serviceItemType}
                    deletable={dateRangeKeys.length > 1}
                    addable={index === dateRangeKeys.length - 1}
                    itemKey={key}
                    key={key}
                    overrideId={item?.id}
                    onAdd={() => {
                      // 找到下一个可用的键
                      const keys = Object.keys(dateRanges).map(Number);
                      const nextKey = Math.max(...keys) + 1;

                      // 创建新的日期范围对象
                      const newDateRanges = {
                        ...dateRanges,
                        [nextKey]: [null, null],
                      };

                      form.setValue('dateRanges', newDateRanges);
                    }}
                    onDelete={() => {
                      const newDateRange = cloneDeep(dateRanges);
                      delete newDateRange[key];
                      form.setValue('dateRanges', newDateRange);
                    }}
                  />
                );
              })}
            </div>

            <div className="moe-flex moe-gap-8px-200 moe-items-start">
              <Form.Item name="capacity" label="Capacity" rules={{ required: 'Please  enter a value' }}>
                <Input.Number
                  isRequired
                  classNames={{ base: 'moe-flex-1' }}
                  maxValue={999}
                  minValue={0}
                  placeholder="Enter capacity"
                />
              </Form.Item>
              <Form.Item name="unitType" transformer={numberToString}>
                <Select
                  isRequired
                  classNames={{ base: 'moe-flex-1 moe-h-[64px] moe-items-end moe-flex', inputBox: 'moe-flex-1' }}
                >
                  {CapacityType.values.map((value) => {
                    return <Select.Item value={value} key={value} title={CapacityType.mapLabels[value]} />;
                  })}
                </Select>
              </Form.Item>
            </div>

            <Condition if={unitType === CapacityType.NumberOfPets}>
              <Alert
                description="The capacity is set for the daycare service across the entire facility."
                isCloseable={false}
                isBordered
                isRounded
              />
            </Condition>
          </div>
        </div>
      </Form>
    </Modal>
  );
};
