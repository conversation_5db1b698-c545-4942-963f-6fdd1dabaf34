import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useSelector } from 'amos';
import { serviceMapBox } from '../../../store/service/service.boxes';
import { type RenderService } from '../types/types';
import { getMainCareType } from '../utils/getMainCareType';

const { BOARDING, DAYCARE, DOG_WALKING, GROOMING, EVALUATION } = ServiceItemType;

type GroupResult<T = RenderService> = {
  boarding: T[];
  daycare: T[];
  grooming: T[];
  dogWalking: T[];
  evaluation: T[];
};

export const useCategoryService = (renderService: RenderService[]) => {
  const [serviceMap] = useSelector(serviceMapBox);

  const isServiceItemType = (current: RenderService, type: ServiceItemType) => {
    const service = serviceMap.mustGetItem(current.serviceId);
    return current.serviceItemType === type || service.serviceItemType === type;
  };

  const serviceItems = new Set<ServiceItemType>();

  const group = renderService.reduce<GroupResult>(
    (prev, curr) => {
      if (isServiceItemType(curr, BOARDING)) {
        prev.boarding.push(curr);
        serviceItems.add(BOARDING);
      } else if (isServiceItemType(curr, DAYCARE)) {
        prev.daycare.push(curr);
        serviceItems.add(DAYCARE);
      } else if (isServiceItemType(curr, DOG_WALKING)) {
        prev.dogWalking.push(curr);
        serviceItems.add(DOG_WALKING);
      } else if (isServiceItemType(curr, EVALUATION)) {
        prev.evaluation.push(curr);
        serviceItems.add(EVALUATION);
      } else {
        prev.grooming.push(curr);
        serviceItems.add(GROOMING);
      }
      return prev;
    },
    { boarding: [], daycare: [], dogWalking: [], grooming: [], evaluation: [] },
  );

  return {
    ...group,
    mainServiceItemType: getMainCareType(serviceItems),
  };
};
