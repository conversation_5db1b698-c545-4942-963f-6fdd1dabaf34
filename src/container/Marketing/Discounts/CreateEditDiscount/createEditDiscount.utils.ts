import { DiscountCodeType, ExpiryType } from '@moego/api-web/moego/models/marketing/v1/discount_code_enums';
import dayjs from 'dayjs';
import { omit, pick, upperFirst } from 'lodash';
import { type FormInstance } from 'rc-field-form';
import { type DiscountModel } from '../../../../store/discount/discount.boxes';
import { type OutputModelOfInput, formInput } from '../../../../utils/form';
import { renderCountableNounPlurals } from '../../../../utils/utils';
import { ApplyToMap, ClientEligibilityMap } from '../Discounts.config';
import { type CreateDiscountFormProps } from './CreateEditDiscountContext';

export interface CreateDiscountFromProps {
  form: React.RefObject<FormInstance>;
}

export interface SelectedInfo {
  isAll: boolean;
  count: number;
  noun: string;
  nounPlural?: string;
}

export interface GetSelectedDescProps {}

export const getSelectedDesc = ({ isAll, count, noun, nounPlural }: SelectedInfo) => {
  if (isAll) {
    return `all ${noun}s`;
  }

  if (!count) {
    return '';
  }
  return `${renderCountableNounPlurals(count, noun, nounPlural)} selected`;
};

export const getSelectedServiceAddonsDesc = (selectedList: SelectedInfo[]) => {
  if (selectedList.every(({ isAll }) => isAll)) {
    return 'All services & add-ons';
  }

  const selectedDescList = selectedList.map(getSelectedDesc).filter(Boolean);

  return selectedDescList.length ? upperFirst(`${selectedDescList.join(' and ')}`) : '';
};

export const discountInput = formInput<CreateDiscountFormProps>()
  .copy(
    'discountCode',
    'description',
    'type',
    'serviceIds',
    'serviceNames',
    'addOnIds',
    'productIds',
    'clientIds',
    'clientsGroup',
    'applyToType',
    'clientEligibilityType',
    'locationIds',
    'endDate',
    'expiryDef',
  )
  .field('amount', 'num', 'num', 'num')
  .field('applyToService', 'bool', 'binary', 'bool')
  .field('allowedAllServices', 'bool', 'binary', 'bool')
  .field('applyToProduct', 'bool', 'binary', 'bool')
  .field('allowedAllProducts', 'bool', 'binary', 'bool')
  .field('autoApplyAssociation', 'bool', 'binary', 'bool')
  .field('enableOnlineBooking', 'bool', 'binary', 'bool')
  .field('allowedAllClients', 'bool', 'binary', 'bool')
  .field('allowedNewClients', 'bool', 'binary', 'bool')
  .field('chooseClientsGroup', 'bool', 'binary', 'bool')
  .field('chooseSpecificClients', 'bool', 'binary', 'bool')
  .field('chooseLimitUsage', 'bool', 'binary', 'bool')
  .field('chooseLimitNumberPerClient', 'bool', 'binary', 'bool')
  .field('limitUsage', 'num', 'num', 'num')
  .field('startDate', 'date', 'date', 'date');

export const convertFormFieldsToDiscountModel = (formFieldsValue: OutputModelOfInput<typeof discountInput>) => {
  const { applyToType, chooseLimitNumberPerClient, clientEligibilityType, endDate, expiryDef } = formFieldsValue;

  return {
    ...omit(formFieldsValue, [
      'applyToType',
      'applyToService',
      'applyToProduct',
      'chooseSpecificClients',
      'chooseClientsGroup',
      'chooseLimitUsage',
      'chooseLimitNumberPerClient',
      'chooseSpecificGroup',
    ]),
    endDate: endDate?.type === ExpiryType.TIME ? (endDate?.time?.format('YYYY-MM-DD') ?? undefined) : undefined,
    clientsGroup: formFieldsValue.clientsGroup ?? '',
    allowedAllThing: applyToType === ApplyToMap.Everything,
    allowedAllClients: clientEligibilityType === ClientEligibilityMap.Everyone,
    limitNumberPerClient: chooseLimitNumberPerClient ? 1 : 0,
    expiryDef: {
      ...expiryDef,
      time:
        expiryDef?.type === ExpiryType.TIME
          ? expiryDef?.time
            ? expiryDef.time.endOf('day').format('YYYY-MM-DDTHH:mm:ssZ')
            : undefined
          : undefined,
    },
  } as unknown as Omit<DiscountModel, 'expiryTime' | 'expiryType'> & {
    expiryDef: {
      type: ExpiryType;
      time: string | undefined;
    };
  };
};

export const convertDiscountRecordToFormFields = (discount: DiscountModel, isDuplicate?: boolean) => {
  const {
    allowedAllServices,
    allowedAllProducts,
    allowedAllClients,
    allowedAllThing,
    limitNumberPerClient,
    serviceIds,
    addOnIds,
    productIds,
    clientsGroup,
    clientIds,
    limitUsage,
    expiryTime,
    expiryType,
  } = discount;

  return {
    ...pick(discount, [
      isDuplicate ? '' : 'discountCode',
      'description',
      'amount',
      'type',
      'autoApplyAssociation',
      'enableOnlineBooking',
      'allowedAllServices',
      'addOnIds',
      'serviceIds',
      'serviceNames',
      'allowedAllProducts',
      'productIds',
      'allowedAllClients',
      'allowedNewClients',
      'clientIds',
      'clientsGroup',
      'limitUsage',
      'locationIds',
    ]),
    startDate: isDuplicate ? dayjs() : dayjs(discount.startDate),
    endDate: discount.endDate
      ? {
          type: ExpiryType.TIME,
          time: dayjs(discount.endDate),
        }
      : {
          type: ExpiryType.NEVER,
          time: undefined,
        },
    expiryDef: {
      type: expiryType,
      time: expiryType === ExpiryType.NEVER ? undefined : expiryTime ? dayjs(expiryTime) : undefined,
    },
    applyToType: allowedAllThing ? ApplyToMap.Everything : ApplyToMap.LimitToSelected,
    applyToService: Boolean(serviceIds.length) || Boolean(addOnIds.length) || allowedAllServices,
    applyToProduct: Boolean(productIds.length) || allowedAllProducts,
    clientEligibilityType: allowedAllClients ? ClientEligibilityMap.Everyone : ClientEligibilityMap.SpecificGroup,
    chooseClientsGroup: Boolean(clientsGroup),
    chooseSpecificClients: Boolean(clientIds.length),
    chooseLimitUsage: limitUsage > 0,
    chooseLimitNumberPerClient: limitNumberPerClient > 0,
  };
};

export const getDiscountFormDefaultFields = () => ({
  type: DiscountCodeType.PERCENTAGE,
  startDate: dayjs(),
  endDate: {
    type: ExpiryType.NEVER,
    time: undefined,
  },
  applyToType: ApplyToMap.Everything,
  clientEligibilityType: ClientEligibilityMap.Everyone,
  expiryDef: {
    type: ExpiryType.NEVER,
    time: undefined,
  },
});
