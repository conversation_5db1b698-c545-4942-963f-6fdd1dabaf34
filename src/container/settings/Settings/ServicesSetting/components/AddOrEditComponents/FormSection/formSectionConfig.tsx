import { createEnum } from '../../../../../../../store/utils/createEnum';
import { EvaluationSectionDescription } from '../../AddOrEditServiceForm/Evaluation/EvaluationSectionDescription';
import React from 'react';

export const ServiceFormSections = createEnum({
  BasicInfo: ['basicInfo', { title: 'Basic info' }],
  Lodgings: ['lodgings', { title: 'Lodgings' }],
  Businesses: ['businesses', { title: 'Businesses' }],
  AvailableBusinessAndStaff: ['availableBusinessAndStaff', { title: 'Available Business & Staff' }],
  PriceDuration: ['priceDuration', { title: 'Price' }],
  AutoRollover: [
    'autoRollover',
    {
      title: 'Service auto rollover',
      description:
        'Set up rules for this service to automatically roll over to another service after checking in for a certain period of time.',
    },
  ],
  AddonStaff: ['addonStaff', { title: 'Staff' }],
  ApplicableServices: ['applicableServices', { title: 'Applicable services' }],
  PetDetails: ['petDetails', { title: 'Pet details' }],
  BundleService: [
    'bundleService',
    {
      title: 'Default add-on(s)',
      description:
        'Set up rule to automatically include additional add-on(s) with this service during staff appointment scheduling and customer online booking.',
    },
  ],
  PrerequisiteClass: [
    'prerequisiteClass',
    {
      title: 'Prerequisite class',
    },
  ],
  Evaluation: [
    'evaluation',
    {
      title: 'Evaluation',
      description: <EvaluationSectionDescription />,
    },
  ],
});
