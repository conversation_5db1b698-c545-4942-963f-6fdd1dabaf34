import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type DayOfWeekTimeRangeDef } from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_defs';
import { Heading, Radio, RadioGroup } from '@moego/ui';
import React, { memo, useMemo } from 'react';
import { Condition } from '../../../../../../../../../components/Condition';
import { type TimePeriod } from '../../../../../../../../../components/WeekTimeScheduleShiftManagement/types';
import { dayOfWeeks } from '../../../../../../../../../store/business/business.boxes';
import { FullWeekDayList } from '../../../../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import {
  buildWeeklyTimeRange,
  getDayTimeRangeWithTimePeriod,
  getUnionTimePeriod,
} from '../../../../../../../../../store/onlineBooking/settings/availability.utils';
import { createStrictEnum } from '../../../../../../../../../store/utils/createEnum';
import { type CustomizeArrivalPickupTimeMap } from '../../../../type';
import { DefaultWorkTimePeriod } from '../../BoardingAndDaycareAvailability.options';
import { useBDArrivalPickupTime } from '../../hooks/useBDArrivalPickupTime';
import { CustomizeArrivalPickupTimeSelector } from './CustomizeArrivalPickupTimeSelector';
import { MissingArrivalPickupWarning } from './MissingArrivalPickupWarning';

const ArrivalPickupTimeType = createStrictEnum({
  ByBusinessService: [1, 'Limit by business hours and service duration'],
  Customize: [2, 'Customize'],
});

export interface ArrivalPickupTimeProps {
  serviceItemType: ServiceItemType;
}

function fillArrayIfEmpty(timeRange: TimePeriod[]) {
  if (timeRange.length > 0) {
    return timeRange;
  }
  return [DefaultWorkTimePeriod];
}

export const ArrivalPickupTime = memo<ArrivalPickupTimeProps>((props) => {
  const { serviceItemType } = props;
  const isEvaluation = serviceItemType === ServiceItemType.EVALUATION;
  const { arrivalPickUpTimeVal, arrivalPickUpTimeRawVal, updateArrivalPickUpTime } =
    useBDArrivalPickupTime(serviceItemType);

  const [timeInfoMap, rawTimeInfoMap] = useMemo(() => {
    const {
      arrivalTimeRange: { firstWeek: arrivalTimeRangeFirstWeek },
      pickUpTimeRange: { firstWeek: pickUpTimeRangeFirstWeek },
    } = arrivalPickUpTimeVal;
    const {
      arrivalTimeRange: { firstWeek: arrivalTimeRangeFirstWeekRaw },
      pickUpTimeRange: { firstWeek: pickUpTimeRangeFirstWeekRaw },
    } = arrivalPickUpTimeRawVal;
    const result: CustomizeArrivalPickupTimeMap = {};
    const rawResult: CustomizeArrivalPickupTimeMap = {};

    dayOfWeeks.sundayFirst.forEach((dayVal) => {
      const resultRowKey = FullWeekDayList[dayVal];
      const dataSourceRowKey = resultRowKey.toLowerCase() as keyof typeof arrivalTimeRangeFirstWeek;
      const dayArrivalTimeRangeList = arrivalTimeRangeFirstWeek[dataSourceRowKey];
      const dayPickupTimeRangeList = pickUpTimeRangeFirstWeek[dataSourceRowKey];
      const dayArrivalTimeRangeRawList = arrivalTimeRangeFirstWeekRaw[dataSourceRowKey];
      const dayPickupTimeRangeRawList = pickUpTimeRangeFirstWeekRaw[dataSourceRowKey];
      const isEnable = dayArrivalTimeRangeList.length > 0 || dayPickupTimeRangeList.length > 0;

      result[resultRowKey] = {
        isEnable,
        timePeriod: fillArrayIfEmpty(
          isEvaluation ? dayArrivalTimeRangeList : getUnionTimePeriod(dayArrivalTimeRangeList, dayPickupTimeRangeList),
        ),
      };

      rawResult[resultRowKey] = {
        isEnable,
        timePeriod: fillArrayIfEmpty(
          isEvaluation
            ? dayArrivalTimeRangeRawList
            : getUnionTimePeriod(dayArrivalTimeRangeRawList, dayPickupTimeRangeRawList),
        ),
      };
    });
    return [result, rawResult];
  }, [arrivalPickUpTimeVal, isEvaluation]);

  const handleCustomizeArrivalPickupTimeChange = (value: CustomizeArrivalPickupTimeMap) => {
    const arrivalTimeRangeFirstWeek: Partial<DayOfWeekTimeRangeDef> = {};
    const pickUpTimeRangeFirstWeek: Partial<DayOfWeekTimeRangeDef> = {};
    Object.entries(value).forEach(([key, val]) => {
      const dataSourceKey = key.toLowerCase() as keyof DayOfWeekTimeRangeDef;
      const { arrivalTimePeriod, pickUpTimePeriod } = getDayTimeRangeWithTimePeriod(val.timePeriod);
      arrivalTimeRangeFirstWeek[dataSourceKey] = val.isEnable ? arrivalTimePeriod : [];
      pickUpTimeRangeFirstWeek[dataSourceKey] = val.isEnable ? pickUpTimePeriod : [];
    });
    updateArrivalPickUpTime({
      arrivalTimeRange: {
        firstWeek: buildWeeklyTimeRange(arrivalTimeRangeFirstWeek),
        secondWeek: buildWeeklyTimeRange(),
        thirdWeek: buildWeeklyTimeRange(),
        forthWeek: buildWeeklyTimeRange(),
      },
      pickUpTimeRange: {
        firstWeek: buildWeeklyTimeRange(pickUpTimeRangeFirstWeek),
        secondWeek: buildWeeklyTimeRange(),
        thirdWeek: buildWeeklyTimeRange(),
        forthWeek: buildWeeklyTimeRange(),
      },
    });
  };

  const handleIsCustomizedChange = (isCustomized: boolean) => {
    updateArrivalPickUpTime({
      isCustomized,
    });
  };

  return (
    <div className="moe-flex moe-flex-col moe-gap-m">
      <Heading size="6">{isEvaluation ? 'Arrival time' : 'Arrival / pickup time'}</Heading>
      <RadioGroup
        className="moe-gap-s"
        value={String(
          arrivalPickUpTimeVal.isCustomized ? ArrivalPickupTimeType.Customize : ArrivalPickupTimeType.ByBusinessService,
        )}
        onChange={(e) => {
          handleIsCustomizedChange(Number(e) === ArrivalPickupTimeType.Customize);
        }}
      >
        {ArrivalPickupTimeType.values.map((item) => {
          const label =
            isEvaluation && item === ArrivalPickupTimeType.ByBusinessService
              ? 'Any time within business hours'
              : ArrivalPickupTimeType.mapLabels[item];
          return (
            <div key={item}>
              <Radio value={String(item)}>{label}</Radio>
            </div>
          );
        })}
      </RadioGroup>
      <Condition if={arrivalPickUpTimeVal.isCustomized}>
        <CustomizeArrivalPickupTimeSelector
          value={timeInfoMap}
          rawValue={rawTimeInfoMap}
          onChange={handleCustomizeArrivalPickupTimeChange}
          serviceItemType={serviceItemType}
        />
        <MissingArrivalPickupWarning timeInfoMap={timeInfoMap} serviceItemType={serviceItemType} />
      </Condition>
    </div>
  );
});
