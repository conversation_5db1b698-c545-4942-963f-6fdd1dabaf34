import React, { memo } from 'react';
import { type SpecificFilterProps } from '../ClientFilters.config';
import { useFilterState } from '../ClientFilters.hooks';
import { getMultiSelectLabel } from '../ClientFilters.utils';
import { CheckboxFilter } from './common/CheckboxFilter';
import { FilterWrapper } from './common/FilterWrapper';
import { CustomerTypeOptions } from '../../../components/FilterItems/FilterProperty.options';

export const CustomerType = memo<SpecificFilterProps>(({ type, property }) => {
  const { filter, handleChange, handleRemove } = useFilterState(property);

  const dropdownValue = getMultiSelectLabel({ options: CustomerTypeOptions, values: filter.values });

  return (
    <FilterWrapper type={type} property={property} dropdownValue={dropdownValue} onClear={handleRemove}>
      <CheckboxFilter options={CustomerTypeOptions} onChange={handleChange} value={filter.values} />
    </FilterWrapper>
  );
});
