import { MajorEditOutlined, <PERSON><PERSON>ailOutlined, MajorTrashOutlined } from '@moego/icons-react';
import { AlertDialog, Button, IconButton, Tooltip } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { type FC } from 'react';
import { Condition } from '../../../../components/Condition';
import { toastA<PERSON> } from '../../../../components/Toast/Toast';
import { deleteGroupClassInstance, reloadGroupClassListData } from '../../../../store/groupClass/groupClass.actions';
import { groupClassInstanceMapBox } from '../../../../store/groupClass/groupClass.boxes';
import { useCreateEditClassInstanceDrawer } from '../CreateEditClassInstanceDrawer/CreateEditClassInstanceDrawer';
import { useEnrollPetModal } from '../EnrollPetModal/EnrollPetModal';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useHistory } from 'react-router';
import { PATH_MARKETING_CAMPAIGN_PANEL } from '../../../../router/paths';
import { ChosenCustomersWithoutFilterSource } from '../../../../store/customer/chosenCustomers.boxes';
import { setChosenCustomersWithoutFilter } from '../../../../store/customer/chosenCustomers.actions';

export enum GroupClassInstanceAction {
  EnrollPets = 'enrollPets',
  Edit = 'edit',
  Delete = 'delete',
  MassEmail = 'massEmail',
}
export interface GroupClassInstanceActionsProps {
  groupClassInstanceId: string;
  actionList?: GroupClassInstanceAction[];
}

export const GroupClassInstanceActions: FC<GroupClassInstanceActionsProps> = (props) => {
  const {
    groupClassInstanceId,
    actionList = [
      GroupClassInstanceAction.EnrollPets,
      GroupClassInstanceAction.Edit,
      GroupClassInstanceAction.Delete,
      GroupClassInstanceAction.MassEmail,
    ],
  } = props;
  const history = useHistory();
  const [groupClassInstance] = useSelector(groupClassInstanceMapBox.mustGetItem(groupClassInstanceId));
  const { enrolledPets, clients } = groupClassInstance;

  const dispatch = useDispatch();
  const openEnrollPetsModal = useEnrollPetModal();
  const openEditClassInstanceDrawer = useCreateEditClassInstanceDrawer();

  // can only delete if no enrolled pets
  const enableDelete = actionList.includes(GroupClassInstanceAction.Delete) && !enrolledPets.length;
  const enableMassEmail = actionList.includes(GroupClassInstanceAction.MassEmail) && enrolledPets.length > 0;
  const handleEditClassInstance = () => {
    openEditClassInstanceDrawer({ groupClassInstanceId });
  };

  const handleEnrollPets = useLatestCallback(() => {
    openEnrollPetsModal({ groupClassInstanceId });
  });

  const handleDeleteClassInstance = useLatestCallback(() => {
    AlertDialog.open({
      title: 'Confirm delete',
      content: 'Are you sure you want to delete this session?',
      onConfirm: async () => {
        await dispatch(
          deleteGroupClassInstance({
            id: groupClassInstanceId,
          }),
        );
        await dispatch(reloadGroupClassListData());
        toastApi.success('Session deleted.');
      },
    });
  });

  const handleMassEmail = useLatestCallback(() => {
    const clientsWithEmail = new Set(clients.filter((client) => !!client.email).map((client) => client.id));
    const customerIds = enrolledPets.filter((pet) => clientsWithEmail.has(pet.clientId)).map((pet) => pet.clientId);
    // 一个client 多个 pet 实际只会发一个， 这里去下重
    const uniqueCustomerIds = [...new Set(customerIds)];
    dispatch(setChosenCustomersWithoutFilter(uniqueCustomerIds));

    history.push(
      PATH_MARKETING_CAMPAIGN_PANEL.queried(
        { specificCustomer: ChosenCustomersWithoutFilterSource.TrainingGroup },
        { panel: 'create' },
      ),
    );
  });

  const atLeastOneClientWithEmail = clients.some((client) => client.email);

  return (
    <div className="moe-flex moe-gap-x-s moe-items-center">
      <Condition if={actionList.includes(GroupClassInstanceAction.EnrollPets)}>
        <Button variant="tertiary" onPress={handleEnrollPets}>
          Enroll pets
        </Button>
      </Condition>
      <Condition if={actionList.includes(GroupClassInstanceAction.Edit)}>
        <IconButton className="!moe-bg-transparent" icon={<MajorEditOutlined />} onPress={handleEditClassInstance} />
      </Condition>
      <Condition if={enableDelete}>
        <IconButton className="!moe-bg-transparent" icon={<MajorTrashOutlined />} onPress={handleDeleteClassInstance} />
      </Condition>
      <Condition if={enableMassEmail}>
        <Tooltip content={'No clients have email'} side="top" isDisabled={atLeastOneClientWithEmail}>
          <div>
            <IconButton
              isDisabled={!atLeastOneClientWithEmail}
              className="!moe-bg-transparent"
              icon={<MajorMailOutlined />}
              onPress={handleMassEmail}
            />
          </div>
        </Tooltip>
      </Condition>
    </div>
  );
};
