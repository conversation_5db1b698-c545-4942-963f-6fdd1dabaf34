import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { DateLimitType } from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import type dayjs from 'dayjs';
import React, { memo } from 'react';
import { SPECIFIC_DATE_KEY } from '../../../../../../../../store/onlineBooking/onlineBooking.boxes';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../../../utils/DateTimeUtil';
import { useLatestCallback } from '../../../../../../../../utils/hooks/useLatestCallback';
import { OBBookingRangeEnd } from '../../../components/OBBookingRangeEnd';
import { TimePreferenceSettingItem } from '../../GroomingAvailability/modules/TimePreference/components/TimePreferenceSettingItem';
import { useGetBDBookingRangeEnd } from '../hooks/useGetBDBookingRange';

interface BDBookingRangeEndProps {
  serviceItemType: ServiceItemType;
}

export const BDBookingRangeEnd = memo((props: BDBookingRangeEndProps) => {
  const { serviceItemType } = props;
  const {
    endDateType,
    maxEndDateOffset,
    specificEndDate,
    handleUpdateBookingRangeEndByOffset,
    handleUpdateBookingRangeEndBySpecificDate,
  } = useGetBDBookingRangeEnd(serviceItemType);

  const endTimeComponentType =
    endDateType === DateLimitType.DATE_TYPE_OFFSET ? (maxEndDateOffset ?? 0) : SPECIFIC_DATE_KEY;
  const specificDate =
    endDateType === DateLimitType.DATE_TYPE_SPECIFIC && specificEndDate ? specificEndDate : undefined;

  const onChange = useLatestCallback((endTimeType: number | typeof SPECIFIC_DATE_KEY, specificDate?: dayjs.Dayjs) => {
    if (endTimeType === SPECIFIC_DATE_KEY) {
      handleUpdateBookingRangeEndBySpecificDate(specificDate?.format(DATE_FORMAT_EXCHANGE) || '');
    } else {
      handleUpdateBookingRangeEndByOffset(endTimeType);
    }
  });

  return (
    <TimePreferenceSettingItem label={'How far can your client book'} className="!moe-items-start">
      <OBBookingRangeEnd endTimeType={endTimeComponentType} specificDate={specificDate} onChange={onChange} />
    </TimePreferenceSettingItem>
  );
});
