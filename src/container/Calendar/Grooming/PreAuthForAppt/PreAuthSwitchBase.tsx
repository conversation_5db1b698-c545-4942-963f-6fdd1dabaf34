import { useDispatch } from 'amos';
import { Switch, Tooltip } from 'antd';
import classNames from 'classnames';
import { noop } from 'lodash';
import React, { memo, useMemo, useRef } from 'react';
import { alertApi } from '../../../../components/Alert/AlertApi';
import { BaseTextParagraph } from '../../../../components/Text/BaseText';
import { openGlobalModal } from '../../../../components/globals/GlobalModals.store';
import { ApptTestIds } from '../../../../config/testIds/apptDrawer';
import { togglePreAuthOnboardingVisible } from '../../../../store/stripe/preAuth.actions';
import { PRE_AUTH_FEATURE_NAME } from '../../../../store/stripe/preAuth.boxes';
import { isNormal } from '../../../../store/utils/identifier';
import { useBool } from '../../../../utils/hooks/useBool';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { type PreAuthValues } from './PreAuth.types';
import { usePreAuthContext } from './PreAuthContext';
import { preAuthCardHelper } from './preAuthCard.utils';
import { usePreAuthOnboarding } from './usePreAuthOnboarding';
import { useStripeCofLinkMeta } from './useStripeCofLinkMeta';

export interface PreAuthSwitchRef {
  getValues: () => PreAuthValues;
}

interface PreAuthSwitchBaseProps {
  mutexOnboardingPopover?: boolean;
  onRequestCardStart?: () => void;
  onRequestCardFinish?: (isRequested?: boolean) => void;
}

export const PreAuthSwitchBase = memo<PreAuthSwitchBaseProps>(
  ({ mutexOnboardingPopover = true, onRequestCardFinish, onRequestCardStart }) => {
    const {
      customerId,
      isChecked,
      isRepeat,
      isSwitchDisabled,
      switchDisabledType,
      paymentMethodId,
      methodList,
      recentCard,
      updateValues,
    } = usePreAuthContext();

    const dispatch = useDispatch();
    const { cofLinkInfo, fetchCofLinkInfo } = useStripeCofLinkMeta(customerId);
    const { onboarding } = usePreAuthOnboarding();
    const visible = useBool();

    const handleVisibleChange = (value: boolean) => {
      // if onboarding popover is open, close it.
      if (value && mutexOnboardingPopover && onboarding.localVisible.createTicketPopover) {
        dispatch(togglePreAuthOnboardingVisible('createTicketPopover', false));
      }

      visible.as(value);
    };

    const onRequestFinish = async (isRequested?: boolean) => {
      if (isRequested) {
        await fetchCofLinkInfo('refresh');
        updateValues({ preAuthEnable: true });
      }
      onRequestCardFinish?.();
    };

    const afterSent = useRef(noop);
    afterSent.current = onRequestFinish;
    const handleChange = useSerialCallback((checked: boolean) => {
      if (!isNormal(customerId)) {
        alertApi.warning('Please select a client first.');
        return;
      }

      if (checked && !methodList.size && !cofLinkInfo.isValid()) {
        dispatch(
          openGlobalModal({
            requestCof: {
              customerId,
              onClose: (isRequested?: boolean) => {
                afterSent.current?.(isRequested);
              },
              occasion: 'preAuth',
            },
          }),
        );
        onRequestCardStart?.();
        return;
      }

      const values: Partial<PreAuthValues> = {};

      if (checked && !paymentMethodId) {
        const card = recentCard?.id ? recentCard : methodList.get(0)?.id ? methodList.get(0) : undefined;
        if (card) {
          values.preAuthPaymentMethod = card.id;
          values.preAuthCardNumber = preAuthCardHelper.stringify(card.card);
        }
      }

      values.preAuthEnable = checked;
      updateValues(values);
    });

    const toolTipContent = useMemo(() => {
      switch (switchDisabledType) {
        case 'ob':
          return `${PRE_AUTH_FEATURE_NAME} can't be modified for appointments created from Online Booking.`;
        case 'checkedInBSPD':
          return `Pre-auth is in progress and cannot be turned off.
          ${
            isRepeat
              ? ' However, if you would like to disable pre-auth for future appointments in this repeat series, you can do so by going to a future appointment within the series.'
              : ''
          }`;
        case 'preAuthSucceed':
          return `Pre-auth for this appointment has been successfully processed and cannot be turned off.
              ${
                isRepeat
                  ? ' However, if you would like to disable pre-auth for future appointments in this repeat series, you can do so by going to a future appointment within the series.'
                  : ''
              }`;
        default:
          return null;
      }
    }, [switchDisabledType, isRepeat]);

    const hasContent = Boolean(toolTipContent);
    return (
      <Tooltip
        visible={visible.value}
        placement="top"
        onVisibleChange={handleVisibleChange}
        color={isSwitchDisabled ? '#3D414B' : '#fff'}
        trigger={isSwitchDisabled ? 'hover' : 'click'}
        overlayClassName={classNames('!moe-w-[214px]', hasContent ? '' : '!moe-hidden')}
        overlayInnerStyle={{ padding: '8px 16px' }}
        title={
          <BaseTextParagraph fontSize="12px" color="#fff">
            {toolTipContent}
          </BaseTextParagraph>
        }
      >
        <Switch
          disabled={isSwitchDisabled || handleChange.isBusy()}
          checked={isChecked}
          onChange={handleChange}
          data-testid={ApptTestIds.ApptPreAuthSw}
        />
      </Tooltip>
    );
  },
);
