import React, { memo, useMemo, useState } from 'react';
import { cn, Heading, Text } from '@moego/ui';
import { type ServiceEntry } from '../../../../../../../components/ServiceApplicablePicker/types/serviceEntry';
import { SelectStaff } from '../../../../EditServiceItem/components/SelectStaff';
import { type ApptInfoPetServiceInfo } from '../../../../../../../store/calendarLatest/calendar.types';
import { useQuickAddConfig } from '../../../../../modules/QuickAddAppt/hooks/useQuickAddConfig';
import { useDispatch, useSelector } from 'amos';
import { selectPetsInAppt } from '../../../../../store/selectors/appt';
import { useCalcGroomingOnlySchedule } from '../../../../../hooks/useCalcGroomingOnlySchedule';
import { useAddSelectedCalendarStaff } from '../../../../EditPetServiceList/hooks/useAddSelectedCalendarStaff';
import { useCancelableCallback } from '../../../../../../../utils/hooks/useCancelableCallback';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { Switch } from '../../../../../../../components/SwitchCase';
import { Carousel } from '../../../../../../../components/Carousel/Carousel';
import { PetAvatar } from '../../../../PetAvatar/PetAvatar';
import { apptAddOnMapBox, apptInfoMapBox, apptServiceMapBox } from '../../../../../store/appt.boxes';
import { useGetServiceListByPet } from '../../../../../hooks/useGetServiceListByPet';
import { isNormal } from '../../../../../../../store/utils/identifier';
import { updateCalendarTicketInfo } from '../../../../../../../store/calendarLatest/actions/private/calendar.actions';
import { Condition } from '../../../../../../../components/Condition';
import { addApptPetService } from '../../../../../store/appt.actions';

type TimeSlotWrapperRenderChildrenProps = {
  petIndex: number;
  isMultiPet?: boolean;
  isShowRepeat: boolean;
  serviceIndex: number;
  serviceEntry?: ServiceEntry;
  petId: string;
};

export interface TimeSlotWrapperProps {
  className?: string;
  children: React.ReactNode | ((props: TimeSlotWrapperRenderChildrenProps) => React.ReactNode);
  appointmentId: string;
  showAvailableTimeSlotPicker: boolean | undefined;
}

/**
 * Grooming only 情况使用，会遍历全部 services 展示，并提供 staff 更改
 */
export const TimeSlotWrapper = memo<TimeSlotWrapperProps>((props) => {
  const { children, className, appointmentId, showAvailableTimeSlotPicker } = props;

  const [pets] = useSelector(selectPetsInAppt(appointmentId));
  const [apptServiceMap, apptAddOnMap] = useSelector(apptServiceMapBox, apptAddOnMapBox);
  const getServiceListByPet = useGetServiceListByPet();
  const dispatch = useDispatch();

  const petsServiceList = useMemo(() => {
    return pets.map((pet) => ({
      petId: pet.petId,
      services: getServiceListByPet(pet, appointmentId),
    }));
  }, [pets, apptServiceMap, apptAddOnMap]);

  const [activePetId, setActivePetId] = useState(petsServiceList[0]?.petId);

  const [{ clientId: customerId, allPetsStartAtSameTime }] = useQuickAddConfig();
  const [ticket] = useSelector(apptInfoMapBox.mustGetItem(appointmentId));

  const calcGroomingOnlySchedule = useCalcGroomingOnlySchedule();
  const addSelectedStaffs = useAddSelectedCalendarStaff();

  const handleSyncPetServices = useCancelableCallback(async (signal, petId: number, serviceList: ServiceEntry[]) => {
    dispatch(addApptPetService(petId, serviceList, appointmentId, true));
    // 同步新的service内容到box 并且重新计算schedule
    await calcGroomingOnlySchedule({
      petIdsServiceList: [{ petId, serviceList }],
      allPetsStartAtSameTime,
      customerId: isNormal(customerId) ? String(customerId) : ticket.customerId,
      appointmentId,
      signal,
    });

    addSelectedStaffs(serviceList);
    dispatch(updateCalendarTicketInfo({ isDirty: true }));
  });

  const handleStaffChange = useLatestCallback((value: ApptInfoPetServiceInfo) => {
    if (!value?.petId) return;
    const newServices = (petsServiceList.find((petService) => petService.petId === value.petId)?.services || []).map(
      (service) => {
        if (value.id === service.id) {
          return {
            ...service,
            ...value,
            staffId: value.staffId,
          };
        }
        return service;
      },
    );
    handleSyncPetServices(+value.petId, newServices);
  });

  if (!showAvailableTimeSlotPicker) {
    return (
      <>
        {typeof children === 'function'
          ? children({
              petIndex: 0,
              isMultiPet: false,
              isShowRepeat: true,
              serviceIndex: 0,
              petId: petsServiceList[0]?.petId,
            })
          : children}
      </>
    );
  }

  const renderPetService = ({
    petIndex,
    petServices,
    isMultiPet,
    petId,
  }: { petServices: ServiceEntry[] } & Omit<
    TimeSlotWrapperRenderChildrenProps,
    'isShowRepeat' | 'serviceIndex' | 'serviceEntry'
  >) => {
    return petServices.map((service, index, services) => {
      return (
        <div className={cn('moe-flex moe-flex-col moe-gap-s', className)} key={service.id}>
          <Heading size="4">{service.serviceName}</Heading>
          <Condition if={index > 0 || petIndex > 0}>
            <Text variant="small" className="moe-text-tertiary">
              The date and time of this service will follow the settings of the first service.
            </Text>
          </Condition>
          <SelectStaff
            showMultiStaffOption={!showAvailableTimeSlotPicker}
            disableConflictCheck
            className="moe-w-full"
            value={service}
            appointmentId={+appointmentId}
            onChange={handleStaffChange}
          />
          {typeof children === 'function'
            ? children({
                petIndex,
                isMultiPet,
                isShowRepeat: index === services.length - 1,
                serviceIndex: index,
                serviceEntry: service,
                petId,
              })
            : children}
        </div>
      );
    });
  };

  return (
    <div className="moe-flex moe-flex-col moe-gap-l">
      <Switch shortCircuit>
        <Switch.Case if={petsServiceList.length === 1}>
          {petsServiceList.map((petService, petIndex) => {
            return renderPetService({
              petIndex,
              petServices: petService.services,
              petId: petService.petId,
            });
          })}
        </Switch.Case>
        <Switch.Case if={petsServiceList.length > 1}>
          <div className="moe-flex moe-flex-col moe-gap-s">
            <Carousel>
              {petsServiceList.map((item) => {
                return (
                  <div key={item.petId}>
                    <PetAvatar
                      isActive={item.petId === activePetId}
                      className="moe-flex-1 moe-min-w-[240px]"
                      key={item.petId}
                      petId={item.petId}
                      onClick={() => {
                        setActivePetId(item.petId);
                      }}
                    />
                  </div>
                );
              })}
            </Carousel>

            {petsServiceList.map((petService, petIndex) => {
              if (petService.petId !== activePetId) return null;

              return renderPetService({
                petIndex,
                petServices: petService.services,
                isMultiPet: true,
                petId: petService.petId,
              });
            })}
          </div>
        </Switch.Case>
      </Switch>
    </div>
  );
});

TimeSlotWrapper.displayName = 'TimeSlotWrapper';
