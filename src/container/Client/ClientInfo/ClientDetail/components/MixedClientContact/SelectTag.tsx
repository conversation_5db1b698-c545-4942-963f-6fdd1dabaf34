import { Select, Tooltip } from '@moego/ui';
import React from 'react';
import { CustomerContactKinds } from '../../../../../../store/customer/customerContact.boxes';
import { isNil } from 'lodash';
import { MinorInfoOutlined } from '@moego/icons-react';
import { useContactList } from '../hooks/useContactList';

interface SelectTagProps {
  customerId: number;
  value?: number;
  onChange?: (value: number) => void;
}

export function SelectTag(props: SelectTagProps) {
  const { customerId, value, onChange } = props;
  const allList = useContactList(customerId);
  const emergencySize = allList.filter((c) => c.type == CustomerContactKinds.Emergency).length;

  return (
    <div className="moe-flex moe-flex-row moe-items-center moe-gap-x-xxs">
      <Select
        value={isNil(value) ? undefined : String(value)}
        onChange={(e) => {
          onChange?.(Number(e));
        }}
      >
        <Select.Item key={CustomerContactKinds.Additional} title="None" />
        <Select.Item key={CustomerContactKinds.Emergency} title="Emergency contact" />
        <Select.Item key={CustomerContactKinds.Pickup} title="Authorize to pick up pet" />
      </Select>
      {value === CustomerContactKinds.Emergency && emergencySize > 0 ? (
        <Tooltip
          side="top"
          align="center"
          content="Selecting this will replace the previously chosen emergency contact."
        >
          <MinorInfoOutlined />
        </Tooltip>
      ) : null}
    </div>
  );
}
