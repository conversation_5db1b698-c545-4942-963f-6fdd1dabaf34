import { useMemoizedFn } from 'ahooks';
import { useDispatch, useStore } from 'amos';
import { useCallback } from 'react';
import { resetChosenCustomers } from '../../../../../store/customer/chosenCustomers.actions';
import { removeAllClientFilter } from '../../../../../store/customer/clientFilters.action';
import { ClientFilterListSourceMap, clientFiltersMapBox } from '../../../../../store/customer/clientFilters.boxes';
import { selectClientFiltersList } from '../../../../../store/customer/clientFilters.selectors';
import { markViewFilterChanged, resetClientView } from '../../../../../store/customer/clientView.actions';
import { getRequestFilter } from '../../../../../store/customer/clientView.utils';
import { type GetCustomerListInput, getCustomerList } from '../../../../../store/customer/customer.actions';
import { type ClientViewContextModel, useClientViewContext } from './useClientViewContext';

type FetchType = 'init' | 'sort' | 'pagination' | 'refresh';
const FetchTypeConfig: Record<FetchType, { ifAllThenResetChosen: boolean; isClear: boolean }> = {
  init: {
    ifAllThenResetChosen: true,
    isClear: true,
  },
  sort: {
    ifAllThenResetChosen: false,
    isClear: true,
  },
  pagination: {
    ifAllThenResetChosen: false,
    isClear: false,
  },
  refresh: {
    ifAllThenResetChosen: false,
    isClear: false,
  },
};

export function useFilterActions(clientViewContext?: ClientViewContextModel) {
  const dispatch = useDispatch();
  const store = useStore();
  const { source, viewId, initialView, clientList, chosenClientInfo, listRef } =
    clientViewContext ?? useClientViewContext();
  const getFilterParams = useCallback(
    (requiredDefaultFilter: boolean) => {
      const list = store.select(selectClientFiltersList(source, false, viewId));
      const map = store.select(clientFiltersMapBox);
      return getRequestFilter(list.toArray(), map, requiredDefaultFilter);
    },
    [source, viewId],
  );

  const fetch = useMemoizedFn(async (type: FetchType, input?: Omit<GetCustomerListInput, 'source' | 'viewId'>) => {
    const fetchConfig = FetchTypeConfig[type];
    if (chosenClientInfo.isAll && fetchConfig.ifAllThenResetChosen) {
      dispatch(resetChosenCustomers(source, viewId));
    }

    // getCustomerList默认是从businessCustomerListBox中获取filters的
    // 此处手动设置filters，避免使用businessCustomerListBox的filters，否则需要注意清空的时机
    const filters = getFilterParams(false);
    const params: GetCustomerListInput = fetchConfig.isClear
      ? { filters: filters, pageSize: clientList.pageSize, pageNum: 1, ...input, clear: true }
      : { filters: filters, pageSize: clientList.pageSize, pageNum: clientList.pageNum, ...input };
    if (source === ClientFilterListSourceMap.LeadList) {
      await listRef?.current?.getDataList({
        source,
        viewId,
        ...params,
      });
    } else {
      await dispatch(getCustomerList({ source, viewId, ...params }));
    }
  });

  // copied from fetch, but use getLeadList instead of getCustomerList
  // const fetchLeadList = useMemoizedFn(
  //   async (type: FetchType, input?: Omit<GetCustomerListInput, 'source' | 'viewId'>) => {
  //     const fetchConfig = FetchTypeConfig[type];
  //     if (chosenClientInfo.isAll && fetchConfig.ifAllThenResetChosen) {
  //       dispatch(resetChosenCustomers(source, viewId));
  //     }

  //     // getCustomerList默认是从businessCustomerListBox中获取filters的
  //     // 此处手动设置filters，避免使用businessCustomerListBox的filters，否则需要注意清空的时机
  //     const filters = getFilterParams(false);
  //     const params: GetCustomerListInput = fetchConfig.isClear
  //       ? { filters: filters, pageSize: clientList.pageSize, pageNum: 1, ...input, clear: true }
  //       : { filters: filters, pageSize: clientList.pageSize, pageNum: clientList.pageNum, ...input };
  //     const result = await dispatch(getLeadList({ source, viewId, ...params }));
  //     return result;
  //   },
  // );

  // clear filter
  const clear = useCallback(() => {
    dispatch(removeAllClientFilter(source, viewId));
    console.log('clear filter', source, viewId, initialView);
    const minCount =
      source === ClientFilterListSourceMap.ClientList || source === ClientFilterListSourceMap.LeadList ? 1 : 0;
    if (initialView.filterSize > minCount) {
      dispatch(markViewFilterChanged(viewId, true));
    }
  }, [initialView, source, viewId]);

  // reset filter to initial state
  const reset = useCallback(() => {
    dispatch(resetClientView({ source, viewId, resetKeyword: false }));
  }, [source, viewId]);

  // mark filter status changed or not
  const markChanged = useCallback(
    (toStatus: boolean) => {
      dispatch(markViewFilterChanged(viewId, toStatus));
    },
    [viewId],
  );

  return {
    fetch,
    clear,
    reset,
    getFilterParams,
    markChanged,
  };
}
