/*
 * @since 2021-07-01 11:22:25
 * <AUTHOR> <<EMAIL>>
 */

import { type Action, useDispatch, useSelector, useStore } from 'amos';
import classNames from 'classnames';
import React, { memo, useState } from 'react';
import { useAsync } from 'react-use';
import { Condition } from '../../../../components/Condition';
import { toastApi } from '../../../../components/Toast/Toast';
import { useIsWorkingLocation } from '../../../../components/WithFeature/useIsWorkingLocation';
import { switchBusinessWithCheckWorkingLocation } from '../../../../store/business/business.actions';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { getCustomer } from '../../../../store/customer/customer.actions';
import { customerMapBox } from '../../../../store/customer/customer.boxes';
import {
  getGroomingTicketInvoiceQuickbookSyncStatus,
  setGroomingTicketTips,
} from '../../../../store/grooming/grooming.actions';
import { GroomingTicketInvoiceType, InvoiceStatus } from '../../../../store/grooming/grooming.boxes';
import {
  getInvoice,
  getPaymentMethodList,
  getSmartTipConfig,
} from '../../../../store/payment/actions/private/payment.actions';
import { getPaymentSettingInfo } from '../../../../store/payment/actions/public/payment.actions';
import { type IsDeposit } from '../../../../store/payment/payment.boxes';
import {
  type InvoiceModuleType,
  type InvoiceRecord,
  isGroomingTicketInvoice,
  selectInvoice,
  selectPaymentSettingInfo,
} from '../../../../store/payment/payment.selectors';
import { isStripeSmartReaderServerDrivenAvailableCountry } from '../../../../store/stripe/stripeTerminal.boxes';
import { truly } from '../../../../store/utils/utils';
import { useCount } from '../../../../utils/hooks/useCount';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useMoePay } from '../../../Payment/components/useMoePay';
import { DiscountInfo } from './DiscountInfo/DiscountInfo';
import { InvoiceInfo } from './InvoiceInfo/InvoiceInfo';
import { MembershipRedeem } from './MembershipRedeem/MembershipRedeem';
import { type DefaultPaymentMethod, PaymentInfo } from './PaymentInfo';
import { ProductInfo } from './ProductInfo/ProductInfo';
import { useTakePaymentDrawer } from './TakePayment.utils';
import { type LocalProcessingFee, TakePaymentContext } from './TakePaymentContext';
import { TakePaymentModalView } from './TakePaymentModal.style';

export interface LegacyTakePaymentModalProps {
  className?: string;
  onClose: () => void;
  isDeposit?: IsDeposit;
  hideAmountInput?: boolean;
  disabledProduct?: boolean;
  disabledMembership?: boolean;
  onlyOneTimeDiscount?: boolean;

  /**
   * **readonly 表示此值不可变!**
   */
  readonly invoiceId: number;
  readonly module: InvoiceModuleType;
  retailModuleProps?: {
    isApplyingProcessingFee: boolean;
    amount: number;
    methodId: number;
  };
  isChargeNoShowFee?: boolean;
  defaultPaymentMethod?: DefaultPaymentMethod;
  isViewOnly?: boolean;
  appointmentId?: number;
  customerId?: string;
}

export const LegacyTakePaymentModal = memo<LegacyTakePaymentModalProps>(
  ({
    className,
    invoiceId,
    onClose,
    module,
    isDeposit,
    isChargeNoShowFee,
    defaultPaymentMethod,
    retailModuleProps,
    hideAmountInput = false,
    disabledProduct = false,
    disabledMembership = false,
    onlyOneTimeDiscount = false,
  }) => {
    const store = useStore();
    const dispatch = useDispatch();
    const loading = useCount(1);
    const {
      productDrawerVisible,
      discountDrawerVisible,
      membershipDrawerVisible,
      openProductDrawer,
      openDiscountDrawer,
      openMembershipDrawer,
      closeDrawer,
    } = useTakePaymentDrawer();

    const isWorkingLocation = useIsWorkingLocation();
    const [invoice, business] = useSelector(selectInvoice(invoiceId, module), selectCurrentBusiness);

    // 在client history里可能打开无权限location的invoice,此时只允许读
    const isReadOnly = !isWorkingLocation(invoice.businessId + '');

    const pay = useMoePay({
      invoiceId,
      customerId: invoice.customerId,
      module,
      vendor: business.primaryPayType,
      useStripeSmartReaderServerDriven: isStripeSmartReaderServerDrivenAvailableCountry(business.country),
      businessId: invoice.businessId.toString(),
    });

    // https://moego.atlassian.net/browse/ERP-2179, 采用纯前端方式控制 preferred tips 的使用。
    const setPreferredTip = async (invoice: InvoiceRecord) => {
      if (isGroomingTicketInvoice(invoice)) {
        const paymentSettings = store.select(selectPaymentSettingInfo());
        const customer = store.select(customerMapBox.mustGetItem(invoice.customerId));
        const preferredTip = customer.getPreferredTipsAmount(invoice.tipBasedAmount);
        const noDepositPaidAmount = invoice.paidAmount - (invoice.depositInfo?.amount ?? 0);

        if (
          // 用户可能通过 package 或 discount 抵扣掉全部金额，导致 noDepositPaidAmount 为 0，所以还是需要用 invoice 状态兜底
          invoice.status !== InvoiceStatus.Completed &&
          !paymentSettings.skipTipping &&
          invoice.id &&
          !noDepositPaidAmount &&
          !invoice.tipsAmount &&
          invoice.type !== GroomingTicketInvoiceType.NoShow &&
          preferredTip &&
          !isDeposit
        ) {
          await dispatch(setGroomingTicketTips(invoice.id, preferredTip, invoice.updateTime));
          await dispatch(getInvoice(invoiceId, module));
        }
      }
    };

    useAsync(async () => {
      try {
        await dispatch(getInvoice(invoiceId, module));

        const invoice = store.select(selectInvoice(invoiceId, module));
        // 先切换location,再请求 payment,否则会导致payment空白
        await dispatch(switchBusinessWithCheckWorkingLocation(invoice.businessId));

        await Promise.all(
          dispatch(
            [
              getPaymentMethodList(),
              getPaymentSettingInfo(),
              getSmartTipConfig() as unknown as Action<Promise<void>, []>,
              module === 'grooming' && getGroomingTicketInvoiceQuickbookSyncStatus(invoiceId),
            ].filter(truly),
          ),
        );
        await dispatch(getCustomer(invoice.customerId));
        await setPreferredTip(invoice);
      } finally {
        loading.add(-1);
      }
    }, []);

    const [localProcessingFee, setLocalProcessingFee] = useState<LocalProcessingFee>({
      isProcessingFeeShow: false,
      isProcessingFeeLoading: false,
      processingFeeAmountVal: 0,
    });

    const updateLocalProcessingFee = useLatestCallback((newObj: Partial<LocalProcessingFee>) => {
      setLocalProcessingFee({
        ...localProcessingFee,
        ...newObj,
      });
    });

    return (
      <TakePaymentModalView
        className={classNames(className, {
          'is-retail-mode': module === 'retail',
        })}
        visible={true}
        onClose={onClose}
        showClose={false}
        loading={loading.count > 0}
        width={isGroomingTicketInvoice(invoice) ? '1130px' : '518px'}
      >
        <TakePaymentContext.Provider
          value={{
            ...localProcessingFee,
            invoiceId,
            module,
            updateLocalProcessingFee,
          }}
        >
          <div className="!moe-relative content">
            {isGroomingTicketInvoice(invoice) && (
              <InvoiceInfo
                isDeposit={isDeposit}
                addLoading={loading.add}
                invoiceId={invoice.id}
                membershipDrawerVisible={membershipDrawerVisible}
                productDrawerVisible={productDrawerVisible}
                onClickAddProduct={openProductDrawer}
                onClickAddDiscount={openDiscountDrawer}
                onClickAddMembership={openMembershipDrawer}
                readonly={isReadOnly}
                disabledProduct={disabledProduct}
                disabledMembership={disabledMembership}
              />
            )}
            <PaymentInfo
              isDeposit={isDeposit}
              isChargeNoShowFee={isChargeNoShowFee}
              module={module}
              pay={pay}
              invoice={invoice}
              business={business}
              onClose={onClose}
              defaultPaymentMethod={defaultPaymentMethod}
              retailModuleProps={retailModuleProps}
              hideAmountInput={hideAmountInput}
            />
            <ProductInfo visible={productDrawerVisible} onScan={openProductDrawer} onClose={closeDrawer} />
            <DiscountInfo
              visible={discountDrawerVisible}
              invoiceId={invoice.id}
              onClose={closeDrawer}
              onlyOneTimeDiscount={onlyOneTimeDiscount}
            />
            <MembershipRedeem visible={membershipDrawerVisible} invoiceId={invoice.id} onClose={closeDrawer} />
            <Condition if={isReadOnly}>
              <div
                className="moe-absolute moe-top-[60px] moe-left-0 moe-right-0 moe-bottom-0 moe-z-10"
                onClick={() => {
                  toastApi.error('Not the current working location, no permission to edit this invoice.');
                }}
              ></div>
            </Condition>
          </div>
        </TakePaymentContext.Provider>
      </TakePaymentModalView>
    );
  },
);
