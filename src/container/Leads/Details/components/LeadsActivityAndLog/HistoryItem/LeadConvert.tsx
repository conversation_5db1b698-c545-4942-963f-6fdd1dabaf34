import React, { memo, useMemo } from 'react';
import { HistoryItemWrapper } from './HistoryItemWrapper';
import { ConvertVia } from '../../../../LeadsConvertHistory/constants';
import { type HistoryItemParams } from './HistoryItem';
import { type HistoryLogConvert } from '@moego/api-web-v2/backend/proto/customer/v1/customer';

export const LeadConvert = memo<HistoryItemParams<HistoryLogConvert>>(function LeadConvert(props) {
  const { dateTime, isLast, source } = props;
  const label = useMemo(() => {
    return `Converted via ${ConvertVia.mapLabels[source].toLowerCase()}`;
  }, [source]);
  return (
    <HistoryItemWrapper label={label} dateTime={dateTime} isLast={isLast}>
      {null}
    </HistoryItemWrapper>
  );
});
