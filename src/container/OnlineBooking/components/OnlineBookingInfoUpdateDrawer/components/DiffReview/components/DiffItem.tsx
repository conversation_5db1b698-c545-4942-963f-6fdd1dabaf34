import { MinorRightArrowOutlined } from '@moego/icons-react';
import { Checkbox, cn, Heading, Text } from '@moego/ui';
import { isEqual } from 'lodash/fp';
import React, { memo, useEffect } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { useBool } from '../../../../../../../utils/hooks/useBool';
import { type ReturnNodeAndValue } from '../../../type';

interface DiffItemProps {
  title: string;
  newNode: ReturnNodeAndValue;
  oldNode: ReturnNodeAndValue;
  onChange?: (v: boolean, o: ReturnNodeAndValue, n: ReturnNodeAndValue) => void;
  ignoreOldNode?: boolean;
}

export const DiffItem = memo<DiffItemProps>(({ title, oldNode, newNode, onChange, ignoreOldNode }) => {
  const checked = useBool(true);

  // 如果新节点和旧节点的值相同，或者新节点的值为 null like，则无需比较。
  // 否则需要比较，因为可能是删除或者是修改，或者有些值被商家主动删除了，必须diff手动新增
  const hidden = newNode.node == null || isEqual(oldNode.value, newNode.value);

  // TODO: 重构时需要把这个预先设置值的逻辑干掉
  useEffect(() => {
    if (hidden) {
      return;
    }
    onChange?.(checked.value, oldNode, newNode);
  }, [checked.value]);

  if (hidden) return null;

  return (
    <div
      className={cn(
        'moe-relative',
        // flex
        'moe-flex moe-items-start moe-justify-between',
        // size and padding
        'moe-w-full moe-max-w-full moe-p-s',
        // border
        'moe-rounded-m moe-border moe-border-solid',
        checked.value ? 'moe-border-[#f96b18]' : 'moe-border-button',
        'hover:moe-border-[#f96b18]',
        // other styles
        'moe-cursor-pointer',
      )}
      onClick={checked.toggle}
    >
      <div className="moe-flex-1 moe-flex moe-flex-col moe-gap-[8px]">
        <Heading size={5}>{title}</Heading>
        <div className="moe-flex moe-items-start moe-justify-between moe-gap-[8px] moe-w-full">
          <Condition if={!ignoreOldNode}>
            <Text variant="small" as="div" className="moe-opacity-50 moe-max-w-[calc(50%-16px)] moe-flex-1">
              {oldNode.node}
            </Text>
            <div className="moe-h-full moe-flex moe-self-baseline">
              <MinorRightArrowOutlined className="moe-w-[20px] moe-h-[20px]" />
            </div>
          </Condition>
          <Text variant="small" as="div" className="moe-max-w-[calc(50%-16px)] moe-flex-1">
            {newNode.node}
          </Text>
        </div>
      </div>
      <Checkbox
        className="moe-flex-shrink-0 moe-absolute moe-right-s moe-top-s"
        isSelected={checked.value}
        onChange={(selected) => {
          checked.as(selected);
        }}
        onClick={(e) => e.stopPropagation()}
      />
    </div>
  );
});
