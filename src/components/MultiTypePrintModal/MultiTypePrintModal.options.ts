import { type ServiceFilter } from '@moego/api-web/moego/api/appointment/v1/print_card_api';
import { FeedingMedicationScheduleDateType } from '@moego/api-web/moego/models/business_customer/v1/business_pet_feeding_medication_enum';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import dayjs, { type Dayjs } from 'dayjs';
import { flatMapDepth } from 'lodash';
import { T_MINUTE } from 'monofile-utilities/lib/consts';
import { type BusinessRecord } from '../../store/business/business.boxes';
import { type PrintCardInfo } from '../../store/printCard/appointmentCard/appointmentCard.actions';
import {
  type ApptListPrintCardPetDetail,
  type ApptListPrintCardPetServiceView,
} from '../../store/printCard/appointmentList/appointmentList.actions';
import { type StayPrintCardInfo } from '../../store/printCard/stayCard/stayCard.actions';
import { createEnum, type EnumValues } from '../../store/utils/createEnum';
import { isNormal } from '../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE } from '../../utils/DateTimeUtil';
import { formatFeedingMedicationDate } from '../ServiceApplicablePicker/utils/beautifyInstructions';
import { AllCareTypeValue } from '../../store/service/scene.enum';

export const PrintCardType = createEnum({
  ApptList: [1, 'Appointment list'],
  BoardingArrival: [2, 'Boarding arrival list'],
  BoardingDeparture: [3, 'Boarding departure list'],
  Stay: [4, 'Stay card'],
  Activity: [5, 'Activity card'],
  Appt: [6, 'Appointment card'],
  Playgroup: [7, 'Playgroup list'],
} as const);

export const ALL_STAFF_ID = 'ALL_STAFF_ID';
export type Staff = { name: string; id: string };

export interface MultiTypePrintModalState {
  selectedCardType: EnumValues<typeof PrintCardType>;
  isPetsCheckedInOnly: boolean;
  careType: number;
  serviceFilters: ServiceFilter[];
}

export const filterApptCardList = (cardList: PrintCardInfo[], staffId = ALL_STAFF_ID) => {
  if (staffId === ALL_STAFF_ID) {
    return cardList;
  }
  return cardList.filter((card) =>
    card.serviceList.some((service) => {
      if (service.operationList?.length) {
        return service.operationList.some((op) => `${op.staffId}` === staffId);
      }
      return `${service.staffId}` === staffId;
    }),
  );
};

export const getStartTime = (startTime: number) => {
  return Number.isFinite(startTime) ? dayjs().startOf('day').add(startTime, 'minute') : null;
};

export const getFullDateTime = (date: string, startTime: number, dateFormat: string, timeFormat: string) => {
  const startTimeDate = getStartTime(startTime);
  return date
    ? `${dayjs(date).format(dateFormat)} ${startTimeDate?.format(timeFormat)}`
    : `${startTimeDate?.format(timeFormat)}`;
};

export const getFeedingIntro = (
  feeding: StayPrintCardInfo['feedingInstructions'][number],
  business: BusinessRecord,
  options?: {
    showTime?: boolean;
  },
) => {
  const { showTime = true } = options ?? {};
  const {
    timeLabelList,
    timeList,
    feedingAmount,
    feedingUnit,
    feedingType,
    feedingSource,
    feedingInstruction,
    feedingNote,
  } = feeding;

  const time = timeList
    .map((time, index) => {
      return `${timeLabelList[index]}(${business.formatFixedTime(time * T_MINUTE)})`;
    })
    .join(',');

  const subIntro = [`${feedingAmount} ${feedingUnit}`, feedingType, feedingSource, feedingInstruction, feedingNote]
    .filter(Boolean)
    .join(', ');

  return [showTime ? `${time}:` : '', subIntro].filter(Boolean).join(' ');
};

export const getMedicationIntro = (
  medication: StayPrintCardInfo['medicationInstructions'][number],
  business: BusinessRecord,
  options?: {
    showTime?: boolean;
    showDate?: boolean;
  },
) => {
  const { showTime = true, showDate = false } = options ?? {};
  const { timeLabelList, timeList, medicationAmount, medicationUnit, medicationName, medicationNote, selectedDate } =
    medication;
  const { dateType, specificDates } = selectedDate;
  const date = showDate ? formatFeedingMedicationDate(dateType, specificDates) : '';

  const time = timeList
    .map((time, index) => {
      return `${timeLabelList[index]}(${business.formatFixedTime(time * T_MINUTE)})`;
    })
    .join(',');

  const subIntro = [`${medicationAmount} ${medicationUnit}`, medicationName, medicationNote].filter(Boolean).join(', ');
  const dateTime = [date, time].filter(Boolean).join(' ');

  return [showTime ? `${dateTime}:` : '', subIntro].filter(Boolean).join(' ');
};

export const getSortedTimeLabels = (
  instructions: {
    timeList: StayPrintCardInfo['feedingInstructions'][number]['timeList'];
    timeLabelList: StayPrintCardInfo['feedingInstructions'][number]['timeLabelList'];
  }[],
) => {
  return flatMapDepth(
    instructions.map((feeding) => {
      return feeding.timeList.map((time, index) => ({
        time,
        timeLabel: feeding.timeLabelList[index],
      }));
    }),
  )
    .sort((a, b) => a.time - b.time)
    .map((item) => item.timeLabel);
};
export interface GetDefaultCareTypesParams {
  serviceItemTypes?: ServiceItemType[];
  defaultCareTypes: ServiceItemType[];
}
export const getDefaultCareTypes = ({ serviceItemTypes, defaultCareTypes }: GetDefaultCareTypesParams) => {
  return serviceItemTypes?.length ? serviceItemTypes : defaultCareTypes;
};

export const getServiceItemTypesByCareType = (careType?: number) => {
  if (careType === AllCareTypeValue) {
    return [ServiceItemType.BOARDING, ServiceItemType.DAYCARE].join(',');
  }

  if (!isNormal(careType)) {
    return;
  }

  return String(careType);
};

export function getMinStartTime(petServices: ApptListPrintCardPetServiceView[]) {
  return Math.min(...petServices.filter((service) => service.startTime).map((service) => service.startTime!));
}

/**
 * sort priority by time (except for boarding) > client last name
 */
export const appointmentListCardSort = <T extends ApptListPrintCardPetDetail>(
  petDetailsByCareType: T[],
  serviceItemType: ServiceItemType,
) => {
  return petDetailsByCareType.sort((a, b) => {
    const aMinStartTime = getMinStartTime(a.petServices);
    const bMinStartTime = getMinStartTime(b.petServices);
    if (serviceItemType !== ServiceItemType.BOARDING && aMinStartTime !== bMinStartTime) {
      return aMinStartTime - bMinStartTime;
    }

    return a.pet.ownerLastName.toLowerCase().localeCompare(b.pet.ownerLastName.toLowerCase());
  });
};

export interface GetDatesByDateTypeParams {
  selectedDate: StayPrintCardInfo['medicationInstructions'][number]['selectedDate'];
  startDate: string;
  days: number;
}

export const getDatesByDateType = (params: GetDatesByDateTypeParams) => {
  const { selectedDate, startDate, days } = params;
  const { dateType, specificDates } = selectedDate;
  let dates: Dayjs[] = [];
  switch (dateType) {
    case FeedingMedicationScheduleDateType.EVERYDAY_EXCEPT_CHECKOUT_DATE:
      dates = Array.from({ length: days - 1 }, (_, index) => dayjs(startDate).add(index, 'day'));
      break;
    case FeedingMedicationScheduleDateType.EVERYDAY_INCLUDE_CHECKOUT_DATE:
      dates = Array.from({ length: days }, (_, index) => dayjs(startDate).add(index, 'day'));
      break;
    case FeedingMedicationScheduleDateType.SPECIFIC_DATE:
      dates = specificDates.map((date) => dayjs(date));
      break;
  }
  return dates.map((date) => date.format(DATE_FORMAT_EXCHANGE));
};

export interface TransformInstructionsWithDatesParams {
  instructions: StayPrintCardInfo['medicationInstructions'];
  startDate: string;
  days: number;
}

export const transformInstructionsWithDates = (params: TransformInstructionsWithDatesParams) => {
  const { instructions, startDate, days } = params;

  // calculate medication dates by date type
  const instructionsWithDates = instructions.map((medication) => {
    const { selectedDate } = medication;
    const dates = getDatesByDateType({ selectedDate, startDate, days });
    return {
      ...medication,
      dates,
    };
  });

  const fullMedicationDates = new Set(flatMapDepth(instructionsWithDates.map((instruction) => instruction.dates)));

  return {
    instructionsWithDates,
    fullMedicationDates,
  };
};
