import { useDispatch, useSelector } from 'amos';
import { type List } from 'immutable';
import { type ReactNode, createContext, useCallback, useContext, useMemo } from 'react';
import { alertApi } from '../../../../../components/Alert/AlertApi';
import { ComMoegoCommonParamsFilterParamsProperty as FilterParamsProperty } from '../../../../../openApi/customer-schema';
import {
  addClientFilter,
  removeAllClientFilter,
  removeClientFilter,
  setEditingFilter,
  toggleClientFiltersVisible,
} from '../../../../../store/customer/clientFilters.action';
import {
  type ClientFilterListSource,
  ClientFilterListSourceMap,
  ClientFiltersInitState,
  ClientFiltersRecord,
  type FilterEventPropsModel,
  clientFiltersMapBox,
  clientFiltersVisibleBox,
} from '../../../../../store/customer/clientFilters.boxes';
import {
  selectClientFiltersList,
  selectRawClientFiltersList,
} from '../../../../../store/customer/clientFilters.selectors';
import { TEMPORARY_VIEW_ID } from '../../../../../store/customer/clientView.constants';
import { type CustomerSmartListReq } from '../../../../../store/customer/customer.boxes';
import { type RecordInstance, type RecordMap, type RecordProps } from '../../../../../store/utils/RecordMap';
import { uniq } from '../../../../../store/utils/utils';
import {
  ClientStatusOptions,
  DEFAULT_MAX_REQUEST_FILTER_COUNT,
  FilterPropertyConfigMap,
  OperatorMap,
  OverrideFilterPropertyConfigMap,
} from './ClientFilters.config';
import { isValidFilterValue } from './ClientFilters.utils';

export interface ClientFilterContext {
  source: ClientFilterListSource;
  allExpand?: boolean;
  filtersMaxCount?: number;
  readonlyFilterList?: FilterParamsProperty[];
  requiredFilterList?: FilterParamsProperty[];
  extraPropertyFilterList?: FilterParamsProperty[];
}

export const useClientFiltersVisible = () => {
  const [visible] = useSelector(clientFiltersVisibleBox);
  const dispatch = useDispatch();

  const changeVisible = (value: boolean) => {
    dispatch(toggleClientFiltersVisible(value));
  };

  return [visible, changeVisible] as const;
};

export const isBothPetTypeBreed = (filterList: List<string>, source: ClientFilterListSource) => {
  const petTypeKey = ClientFiltersRecord.ownKey(source, FilterPropertyConfigMap.PetType);
  const petBreedKey = ClientFiltersRecord.ownKey(source, FilterPropertyConfigMap.PetBreed);
  return filterList.includes(petTypeKey) && filterList.includes(petBreedKey);
};

/**
 * get filter count show for user
 */
export const useFilterListDisplayCount = () => {
  const { source } = useClientFiltersContext();
  const [clientFiltersList] = useSelector(selectClientFiltersList(source));
  const clientFiltersListSize = clientFiltersList.size;

  return useMemo(() => {
    return isBothPetTypeBreed(clientFiltersList, source) ? clientFiltersListSize - 1 : clientFiltersListSize;
  }, [clientFiltersList]);
};

export const useFilterState = (property: FilterParamsProperty) => {
  const { source, filtersMaxCount = DEFAULT_MAX_REQUEST_FILTER_COUNT } = useClientFiltersContext();
  const [rawClientFilterList, clientFiltersList, clientFilterMap] = useSelector(
    selectRawClientFiltersList(source),
    selectClientFiltersList(source),
    clientFiltersMapBox,
  );
  const dispatch = useDispatch();
  const propertyKey = ClientFiltersRecord.ownKey(source, property);
  // Forbid access to filters that do not exist in the list
  const clientFilter = rawClientFilterList.includes(propertyKey)
    ? clientFilterMap.mustGetItem(propertyKey)
    : ClientFiltersInitState;

  const { isReadonly } = useIsSpecialFilter(property);
  const handleChange = (filter: FilterEventPropsModel, option?: { force?: boolean; isEditing?: boolean }) => {
    if (isReadonly && !option?.force) {
      return;
    }

    // for calc max count: add property to list temporarily
    // change the property that existed in list : uniq
    const tempClientFiltersList = uniq(clientFiltersList.concat(ClientFiltersRecord.ownKey(source, property)));

    // pet type & breed show as 1 filer, but data in store are 2 separate filters
    const bothPetTypeBreed = isBothPetTypeBreed(tempClientFiltersList, source);
    let maxRequestFilterCount = bothPetTypeBreed ? filtersMaxCount + 1 : filtersMaxCount;
    let filterLengthCount = tempClientFiltersList.size;
    // 这个筛选器是用来区分customer 和 lead 的，区分不同场景添加，用户无法修改，所以这里不计算在内
    if (tempClientFiltersList.some((item) => item.includes(FilterParamsProperty.customer_type))) {
      maxRequestFilterCount -= 1;
      filterLengthCount -= 1;
    }

    // warning text is 5 filters always
    if (filterLengthCount > maxRequestFilterCount) {
      alertApi.warn(`Does not support more than ${maxRequestFilterCount} filters`);
      return;
    }

    dispatch([
      addClientFilter({
        ...clientFilter,
        ...filter,
        property,
        source,
      }),
      setEditingFilter(option?.isEditing ? property : undefined),
    ]);
  };

  const handleRemove = (force?: boolean) => {
    if (isReadonly && !force) {
      return;
    }
    dispatch(removeClientFilter(source, property));
  };

  return { filter: clientFilter, handleChange, handleRemove };
};

export interface InitClientFilterOptions {
  addFiltersAction?: () => void;
  clearAllBeforeAdd?: boolean;
}

export const useInitClientFilter = (source: ClientFilterListSource) => {
  const dispatch = useDispatch();

  const defaultAddClientStatus = () => {
    dispatch(
      addClientFilter({
        source,
        property: FilterPropertyConfigMap.ClientStatus,
        operator: OperatorMap.EqualTo,
        value: ClientStatusOptions[0].value,
      }),
    );
  };

  return useCallback(
    ({ addFiltersAction = defaultAddClientStatus, clearAllBeforeAdd }: InitClientFilterOptions = {}) => {
      if (clearAllBeforeAdd) {
        dispatch(removeAllClientFilter(source));
      }
      addFiltersAction();
    },
    [],
  );
};

export const useFilterIsExistBefore = (property: FilterParamsProperty) => {
  const { source } = useClientFiltersContext();
  const [rawClientFilterList] = useSelector(selectRawClientFiltersList(source));
  const propertyKey = ClientFiltersRecord.ownKey(source, property);
  return rawClientFilterList.includes(propertyKey);
};

export function useMultiSelectOptions<V extends RecordInstance<any>, KF extends keyof RecordProps<V>>(
  list: List<V[KF]>,
  map: RecordMap<V, KF>,
  label: keyof V | ((item: V) => ReactNode),
  getValue?: (item: V) => string | number,
) {
  return useMemo(
    () =>
      list
        .map((id) => {
          const record = map.mustGetItem(id);
          return {
            label:
              typeof label === 'function'
                ? label(record)
                : typeof record[label] === 'function'
                  ? record[label]()
                  : record[label],
            value: getValue ? getValue(record) : id,
          };
        })
        .toArray(),
    [list, map, label],
  );
}

export const convertFilterParams = (
  clientFiltersList: List<string>,
  clientFilterMap: RecordMap<ClientFiltersRecord, 'ownKey'>,
) => {
  const filterParamList = clientFiltersList
    .map((filterProperty) => {
      const clientFilter = clientFilterMap.mustGetItem(filterProperty);
      if (!clientFilter.ownKey) {
        return undefined;
      }
      const { property, value, values, valuesForFetch, operator = OperatorMap.In } = clientFilter;
      const operatorConst = OperatorMap.mapLabels[operator] || operator;
      return {
        property,
        value: !Array.isArray(valuesForFetch) && valuesForFetch ? valuesForFetch : value,
        values: Array.isArray(valuesForFetch) && valuesForFetch?.length ? valuesForFetch : values,
        operator: operatorConst,
      };
    })
    .filter(Boolean);
  return (
    filterParamList.size
      ? {
          type: 'TYPE_AND',
          filters: filterParamList.toArray(),
        }
      : undefined
  ) as CustomerSmartListReq['filters'];
};

export const useClientFilterListParams = (source: ClientFilterListSource, viewId: number = TEMPORARY_VIEW_ID) => {
  const [clientFiltersList, clientFilterMap] = useSelector(
    selectClientFiltersList(source, false, viewId),
    clientFiltersMapBox,
  );

  return useMemo(() => convertFilterParams(clientFiltersList, clientFilterMap), [clientFiltersList, clientFilterMap]);
};

export const ClientFiltersContext = createContext<ClientFilterContext>({
  source: ClientFilterListSourceMap.ClientList,
  filtersMaxCount: DEFAULT_MAX_REQUEST_FILTER_COUNT,
  readonlyFilterList: [],
  requiredFilterList: [],
  extraPropertyFilterList: [],
});

export const useClientFiltersContext = () => {
  return useContext(ClientFiltersContext);
};

export const useIsSpecialFilter = (property: FilterParamsProperty) => {
  const { readonlyFilterList, requiredFilterList } = useClientFiltersContext();
  const isReadonly = readonlyFilterList?.includes(property) || false;
  const isRequired = requiredFilterList?.includes(property) || false;
  return { isReadonly, isRequired };
};

export const useIsRequiredButEmpty = (property: FilterParamsProperty) => {
  const { filter } = useFilterState(property);
  const { isRequired } = useIsSpecialFilter(property);
  const isEmpty = !isValidFilterValue(filter.value) && !isValidFilterValue(filter.values?.[0]);

  return isRequired && isEmpty;
};

export const usePropertyConfig = (property: FilterParamsProperty) => {
  const { source } = useClientFiltersContext();
  const {
    title: initTitle,
    defaultExpand: initDefaultExpand,
    shortOverlay: initShortOverlay,
    toolTips,
  } = FilterPropertyConfigMap.mapLabels[property] || {};
  const {
    title: overrideTitle,
    defaultExpand: overrideDefaultExpand,
    shortOverlay: overrideShortOverlay,
  } = OverrideFilterPropertyConfigMap.mapLabels?.[source]?.[property] || {};

  return {
    title: overrideTitle ?? initTitle,
    defaultExpand: overrideDefaultExpand ?? initDefaultExpand,
    shortOverlay: overrideShortOverlay ?? initShortOverlay,
    toolTips,
  };
};
