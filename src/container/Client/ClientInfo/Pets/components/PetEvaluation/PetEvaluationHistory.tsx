import React, { memo } from 'react';
import { Condition } from '../../../../../../components/Condition';
import { Button } from '@moego/ui';
import { reportData } from '../../../../../../utils/tracker';
import { ReportActionName } from '../../../../../../utils/reportType';
import { useViewEvaluationHistoryModal } from '../../../../../Appt/hooks/useViewEvaluationHistoryModal';
import { useDispatch, useSelector } from 'amos';
import { selectPetEvaluationActivities } from '../../../../../../store/evaluation/evaluation.selectors';
import { listEvaluationHistory } from '../../../../../../store/evaluation/evaluation.actions';

export interface PetEvaluationHistoryProps {
  petId: number;
}

export const PetEvaluationHistory = memo<PetEvaluationHistoryProps>((props) => {
  const { petId } = props;
  const [petEvaluationActivities] = useSelector(selectPetEvaluationActivities(String(petId)));
  const dispatch = useDispatch();

  const hasEvaluationHistory = petEvaluationActivities.length > 0;
  const openEvaluationHistoryModal = useViewEvaluationHistoryModal();

  const getPetEvaluationHistory = async () => {
    await dispatch(listEvaluationHistory({ petId: String(petId) }));
  };

  return (
    <Condition if={hasEvaluationHistory}>
      <Button
        variant="tertiary"
        size="s"
        className="moe-shrink-0"
        onPress={() => {
          getPetEvaluationHistory();
          openEvaluationHistoryModal(String(petId));
          reportData(ReportActionName.checkEvaluationHistory);
        }}
      >
        View evaluation history
      </Button>
    </Condition>
  );
});
