import { useDispatch } from 'amos';
import {
  addLatePickUpServiceChargeDetail,
  checkLatePickUpRule,
} from '../../../../../store/service/actions/public/serviceCharge.actions';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { rescheduleBoardingAppointment } from '../../../store/appt.api';

interface UsePrepareCheckoutForInvoiceV4Props {
  appointmentId: string;
  checkoutDate?: string;
}

export function useBeforeCheckoutActionsForInvoiceV4() {
  const dispatch = useDispatch();
  const runRequiredActions = useLatestCallback(async (options: UsePrepareCheckoutForInvoiceV4Props) => {
    const { appointmentId, checkoutDate } = options;
    if (checkoutDate) {
      await dispatch(
        rescheduleBoardingAppointment({
          appointmentId: appointmentId,
          endDate: checkoutDate,
        }),
      );
    }

    const hitLatePickUpRule = await dispatch(checkLatePickUpRule(appointmentId));
    // 已确认幂等，不会重复添加
    if (hitLatePickUpRule) {
      await dispatch(addLatePickUpServiceChargeDetail(appointmentId));
    }
  });

  return {
    runRequiredActions,
  };
}
