import { Alert, Text } from '@moego/ui';
import React from 'react';
import { type ServiceDateInfo } from '../../../store/appt.types';
import { DateTypeWordingMap } from '../../../../../components/DateType/DateType.utils';
import { useBoardingAdditionalServiceScheduleAlert } from './hooks/useBoardingAdditionalServiceScheduleAlert';

export interface BoardingAdditionalServiceAlertProps {
  ownId: string;
  serviceId: string;
  initDates: ServiceDateInfo[];
  draftDates: ServiceDateInfo[];
}

export const BoardingAdditionalServiceAlert: React.FC<BoardingAdditionalServiceAlertProps> = (props) => {
  const { ownId, serviceId, initDates, draftDates } = props;

  const { alertRuleHit, additionalServiceRuleList } = useBoardingAdditionalServiceScheduleAlert({
    ownId,
    serviceId,
    initDates,
    draftDates,
  });

  if (!alertRuleHit) {
    return null;
  }

  return (
    <Alert
      color="information"
      className="moe-border moe-rounded-s moe-border-information"
      classNames={{
        content: 'moe-items-center',
      }}
      isCloseable={false}
    >
      <div>
        <Text variant="small">
          This service includes <b>default service(s)/add-on(s)</b> based on the new length of stay. Please remember to{' '}
          <b>add them manually</b> to ensure accurate billing and service:
        </Text>
        <ul className="moe-list-disc moe-pl-[24px]">
          {additionalServiceRuleList.map((serviceRuleItem) => (
            <li key={serviceRuleItem.service.serviceId}>
              <Text variant="small">
                {serviceRuleItem.service.name} ({DateTypeWordingMap[serviceRuleItem.rule.dateType]})
              </Text>
            </li>
          ))}
        </ul>
      </div>
    </Alert>
  );
};
