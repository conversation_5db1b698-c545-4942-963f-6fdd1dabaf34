import { type GroomingReportCardDef } from '@moego/api-web/moego/api/grooming/v1/grooming_report_api';
import {
  GroomingReportSendMethod,
  GroomingReportStatus,
} from '@moego/api-web/moego/models/grooming/v1/grooming_report_enums';
import { useSerialCallback } from '@moego/tools';
import { Button, Table, type TableProps } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { memo } from 'react';
import { useAsync, useSetState } from 'react-use';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import {
  getGroomingReportCardList,
  getGroomingReportSummaryInfo,
} from '../../../../../../store/overview/actions/public/reportCard.action';
import { isNormal } from '../../../../../../store/utils/identifier';
import { dateMessageToDayjs } from '../../../../../../utils/utils';
import { useOpenPreview } from './hooks/useOpenPreview';
import { type ReportCardProps } from './type';

export const ReportGrooming = memo((props: ReportCardProps) => {
  const { petId } = props;

  const dispatch = useDispatch();
  const [{ pageIndex, pageSize }, setState] = useSetState<{ pageIndex: number; pageSize: number }>({
    pageIndex: 1,
    pageSize: 10,
  });
  const [business] = useSelector(selectCurrentBusiness());
  const { openGroomingEmail, openGroomingLink } = useOpenPreview();

  const handleView = useSerialCallback(async (value: GroomingReportCardDef) => {
    const { uuid, reportCardId, sendMethod } = value;
    if (sendMethod === GroomingReportSendMethod.GROOMING_REPORT_SEND_BY_EMAIL) {
      const res = await dispatch(
        getGroomingReportSummaryInfo({
          id: uuid,
          reportId: reportCardId,
        }),
      );
      openGroomingEmail({ value: res });
    } else {
      openGroomingLink({
        uuid,
        reportId: reportCardId,
      });
    }
  });

  const columns: TableProps<GroomingReportCardDef>['columns'] = [
    {
      id: 'serviceDate',
      header: 'Date of report',
      size: 150,
      cell: ({ row }) => {
        return business.formatDate(dateMessageToDayjs(row.original.serviceDate));
      },
    },
    {
      id: 'sendTime',
      header: 'Last sent',
      size: 200,
      cell: ({ row }) => {
        return business.formatDateTime(dayjs(row.original.sendTime));
      },
    },
    {
      id: 'sendMethod',
      header: 'Send via',
      size: 100,
      cell: ({ row }) => {
        const { sendMethod } = row.original;
        return sendMethod === GroomingReportSendMethod.GROOMING_REPORT_SEND_BY_EMAIL
          ? 'Email'
          : sendMethod === GroomingReportSendMethod.GROOMING_REPORT_SEND_BY_SMS
            ? 'SMS'
            : '';
      },
    },
    {
      header: 'Action',
      cell: ({ row }) => {
        return (
          <Button
            variant="tertiary"
            isLoading={handleView.isBusy()}
            onPress={() => {
              handleView(row.original);
            }}
          >
            View
          </Button>
        );
      },
    },
  ];

  const { value, loading } = useAsync(async () => {
    if (!isNormal(petId)) return;
    const { pagination, groomingReportCards } = await dispatch(
      getGroomingReportCardList({
        businessId: String(business.id),
        companyId: String(business.companyId),
        pagination: { pageNum: pageIndex, pageSize },
        status: GroomingReportStatus.SENT,
        petId: String(petId),
      }),
    );
    return {
      pagination,
      groomingReportCards,
    };
  }, [pageIndex, pageSize, petId, business]);

  const { groomingReportCards = [], pagination } = value || {};

  return (
    <Table
      data={groomingReportCards}
      getRowId={(row) => row.reportCardId}
      columns={columns}
      isLoading={loading}
      pagination={{
        pageIndex: pageIndex,
        pageSize: pageSize,
        totalSize: pagination?.total || 0,
      }}
      onPaginationChange={(v) => {
        setState({
          pageIndex: v.pageIndex,
          pageSize: v.pageSize,
        });
      }}
    />
  );
});
