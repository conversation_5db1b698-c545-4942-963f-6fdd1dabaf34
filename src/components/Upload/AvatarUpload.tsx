/* eslint-disable sonarjs/no-nested-functions */
import {
  AvatarUpload as MoeAvatarUpload,
  type RequestOptions,
  type AvatarUploadProps as MoeAvatarUploadProps,
  type UploadRefConfig,
} from '@moego/ui';
import { compressImageJs, toWebpJs } from '@sourcebug/image-lib';
import { appendQuery } from 'monofile-utilities/lib/query-string';
import React, { forwardRef } from 'react';
import { UploadLoggerKeys } from '../../config/logger/upload';
import { http } from '../../middleware/api';
import { ByteUnit } from '../../utils/common';
import { LoggerModule, logger } from '../../utils/logger';
import { convertFile } from './Upload';
import { type MaxSizeProps, useMaxSizeOnBeforeUpload } from './hooks/useMaxSizeOnBeforeUpload';

const DEFAULT_AVATAR_MAX_SIZE = 2 * ByteUnit.MB;

export interface UploadHandlerFactoryOptions {
  /**
   * 压缩策略
   * - webp: 压缩为 webp 格式
   * - none: 不压缩
   * - any: 保留源格式，进行压缩。但是有些格式压缩率不高，比如 png
   */
  compressStrategy?: 'webp' | 'none' | 'any';
  withFileSize?: boolean;
}

interface UploaderOptions extends Pick<RequestOptions, 'file' | 'onProgress' | 'onError'> {
  onSuccess: (res: { url: string; fileSize?: number }) => void;
}

export const createUploadHandler = (options?: UploadHandlerFactoryOptions) => {
  const { compressStrategy = 'none', withFileSize } = options || {};
  return ({ file, onProgress, onSuccess, onError }: UploaderOptions) => {
    let isAborted = false;
    const upload = () => {
      const data = new FormData();
      convertFile(file)
        .then((file) => {
          if (['webp', 'any'].includes(compressStrategy)) {
            const isToWebp = compressStrategy === 'webp';
            const compress = isToWebp ? toWebpJs : compressImageJs;
            return compress(file, { type: isToWebp ? 'image/webp' : file.type })
              .then((r) => {
                const diff = file.size - r.size;
                logger.get(LoggerModule.UPLOAD).info(UploadLoggerKeys.CONVERT_IMAGE_SUCCEEDED, {
                  diff,
                  preSize: file.size,
                  nowSize: r.size,
                  type: file.type,
                });
                return r;
              })
              .catch((e) => {
                logger.get(LoggerModule.UPLOAD).error(UploadLoggerKeys.CONVERT_IMAGE_FAILED, {
                  error: e,
                  type: file.type,
                  name: file.name,
                });
                return file;
              });
          }
          return file;
        })
        .then((file) => {
          data.append('file', file);
          http
            .open('POST/business/upload/uploadFile', void 0, {
              formData: data,
              onUploadProgress: (e) => {
                if (isAborted) return;
                onProgress(e.total ? e.loaded / e.total : 0);
              },
            })
            .then(
              (res) => {
                if (isAborted) return;
                onSuccess({
                  url: file.name ? appendQuery(res.data.url, { name: file.name }) : res.data.url,
                  /**
                   * Note:
                   * 这里的 `fileSize` keyname 尽量不修改，或者记得同步修改项目中的两处：
                   * 1. `createUploadHandler` function
                   * 2. `PicInfo` interface
                   */
                  ...(withFileSize ? { fileSize: file.size } : {}),
                });
              },
              (err) => {
                if (isAborted) return;
                onError(err);
              },
            );
        });
    };
    upload();

    return {
      abort: () => {
        isAborted = true;
      },
    };
  };
};

export const defaultUploadHandler = createUploadHandler();
export const upload2WebpHandler = createUploadHandler({ compressStrategy: 'webp' });
export const upload2AnyHandler = createUploadHandler({ compressStrategy: 'any' });

interface AvatarUploadProps extends MoeAvatarUploadProps, MaxSizeProps {}

export const AvatarUpload = forwardRef<UploadRefConfig, AvatarUploadProps>(
  ({ onBeforeUpload, maxSize = DEFAULT_AVATAR_MAX_SIZE, ...rest }, ref) => {
    const handleBeforeUpload = useMaxSizeOnBeforeUpload(onBeforeUpload, maxSize);

    return (
      <MoeAvatarUpload customRequest={defaultUploadHandler} {...rest} ref={ref} onBeforeUpload={handleBeforeUpload} />
    );
  },
);
