import { Text, cn } from '@moego/ui';
import React, { memo } from 'react';
import SvgMinorEditOutlinedSvg from '../../../../assets/svg/minor-edit-outlined.svg';
import { Condition } from '../../../Condition';
import { CircleIcon } from '../../../Icon/CircleIcon';
import { SvgIcon } from '../../../Icon/Icon';

export interface BlockInfoProps {
  className?: string;
  label?: React.ReactNode;
  instructions?: string[];
  onEdit?: () => void;
}

export const BlockInfo = memo((props: React.PropsWithChildren<BlockInfoProps>) => {
  const { className, label, instructions, onEdit } = props;

  if (!instructions || instructions.length === 0) {
    return null;
  }

  return (
    <div
      className={cn(
        'moe-bg-[#F3F3F3] moe-rounded-[8px] moe-p-[16px] moe-flex moe-flex-col moe-gap-y-[4px] moe-relative moe-z-0',
        className,
      )}
    >
      {typeof label === 'string' ? (
        <Text variant="caption" className="moe-text-tertiary">
          {label}
        </Text>
      ) : null}
      <div className="moe-text-[16px] moe-leading-[24px] moe-text-[#202020]">
        {instructions.map((item, index) => {
          return (
            <Text variant="regular" key={index}>
              {item}
            </Text>
          );
        })}
      </div>
      <Condition if={onEdit}>
        <CircleIcon iconClassName="moe-absolute moe-top-0 moe-right-0 moe-z-0" content="Edit">
          <SvgIcon src={SvgMinorEditOutlinedSvg} size={20} color="#202020" onClick={onEdit} />
        </CircleIcon>
      </Condition>
    </div>
  );
});
