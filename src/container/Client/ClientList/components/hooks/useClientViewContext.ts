/**
 * @since 2024-01-26 10:03:22
 * <AUTHOR>
 * @description client view context, use this hook to get data under current view
 */
import { useSelector } from 'amos';
import { createContext, useContext, useMemo } from 'react';
import { type ComMoegoCommonParamsFilterParamsProperty as FilterParamsProperty } from '../../../../../openApi/customer-schema';
import { selectPricingPermission } from '../../../../../store/company/company.selectors';
import {
  selectChosenCustomerCount,
  selectChosenCustomerIdList,
  selectChosenCustomers,
} from '../../../../../store/customer/chosenCustomers.selectors';
import {
  type ClientFilterListSource,
  clientFiltersMapBox,
  ClientFilterListSourceMap,
} from '../../../../../store/customer/clientFilters.boxes';
import {
  selectClientFiltersList,
  selectRawClientFiltersList,
} from '../../../../../store/customer/clientFilters.selectors';
import { clientViewMapBox, initialClientViewMapBox } from '../../../../../store/customer/clientView.boxes';
import { selectClientViewList } from '../../../../../store/customer/clientView.selectors';
import { customerMapBox } from '../../../../../store/customer/customer.boxes';
import { selectBusinessCustomers } from '../../../../../store/customer/customer.selectors';
import { selectBusinessPetSummary } from '../../../../../store/pet/pet.selectors';
import { type FilterPropertyConfig } from '../FilterItems/FilterProperty.map';
import { DEFAULT_MAX_REQUEST_FILTER_COUNT } from '../FilterItems/FilterProperty.options';
import { isBothPetTypeBreed } from '../FilterItems/FilterProperty.utils';
import { type LeadsListRef } from '../../../../Leads/LeadsManagement/constants';

interface UseViewContextOptions {
  viewId: number;
  source: ClientFilterListSource;
  filtersMaxCount?: number;
  allFilterList: FilterParamsProperty[];
  filterConfigMap: Record<FilterParamsProperty, FilterPropertyConfig>;
  listRef?: React.RefObject<LeadsListRef>;
}

export function useClientViewData(options: UseViewContextOptions) {
  const { viewId, source, ...filterOptions } = options;
  const [pricingPermission] = useSelector(selectPricingPermission());
  const enableAdvancedFilter = pricingPermission.enable.has('advancedFilter');

  // view info
  const [viewList, viewMap, initialViewMap] = useSelector(
    selectClientViewList(source),
    clientViewMapBox,
    initialClientViewMapBox,
  );
  const currentView = viewMap.mustGetItem(viewId);
  const initialView = initialViewMap.mustGetItem(viewId);

  // filter info
  const [rawClientFilterList, clientFilterList, clientFilterListWithEditing, clientFilterMap] = useSelector(
    selectRawClientFiltersList(source, viewId),
    selectClientFiltersList(source, false, viewId),
    selectClientFiltersList(source, true, viewId),
    clientFiltersMapBox,
  );
  const filtersCount = useMemo(() => {
    let count = isBothPetTypeBreed(clientFilterListWithEditing, source, viewId)
      ? clientFilterListWithEditing.size - 1
      : clientFilterListWithEditing.size;
    // Client List 有内置的 customer_type filter，所以需要减去 2
    if (source === ClientFilterListSourceMap.ClientList || source === ClientFilterListSourceMap.LeadList) {
      count -= 1;
    }
    return count;
  }, [clientFilterListWithEditing, source]);

  // client info
  const [clientMap, clientList, petSummary, chosenClientInfo, chosenClientCount, chosenClientIdList] = useSelector(
    customerMapBox,
    selectBusinessCustomers(source, viewId),
    selectBusinessPetSummary(),
    selectChosenCustomers(source, viewId),
    selectChosenCustomerCount(source, viewId),
    selectChosenCustomerIdList(source, viewId),
  );

  return {
    ...options,
    filtersMaxCount: options.filtersMaxCount || DEFAULT_MAX_REQUEST_FILTER_COUNT,
    enableAdvancedFilter,

    // view info
    viewList,
    viewMap,
    currentView,
    initialViewMap,
    initialView,

    // filter info
    ...filterOptions,
    rawClientFilterList,
    clientFilterList,
    clientFilterListWithEditing,
    clientFilterMap,
    filtersCount,

    // client info
    clientMap,
    clientList,
    petSummary,
    chosenClientInfo,
    chosenClientCount,
    chosenClientIdList,

    // list ref
    listRef: options.listRef,
  };
}

export interface ClientViewContextModel extends ReturnType<typeof useClientViewData> {}

export const ClientViewContext = createContext<ClientViewContextModel | null>(null);

export function useClientViewContext() {
  const context = useContext(ClientViewContext);
  if (!context) {
    throw new Error('useClientViewContext must be used within a ClientViewProvider');
  }

  return context;
}
