import { createContext, useContext } from 'react';
import { type ServiceEntry } from '../../../../../../../../components/ServiceApplicablePicker/types/serviceEntry';

export const TimeSlotWrapperNormalContext = createContext<{
  petsServiceList: {
    petId: string;
    services: ServiceEntry[];
  }[];
  hasGroomingService: boolean;
  isEnableSlotCalender: boolean;
}>({ petsServiceList: [], hasGroomingService: false, isEnableSlotCalender: false });

export const useTimeSlotWrapperNormalContext = () => {
  return useContext(TimeSlotWrapperNormalContext);
};
