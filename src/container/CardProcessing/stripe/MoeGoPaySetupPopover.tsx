import { Tooltip } from '@moego/ui';
import { useSelector } from 'amos';
import React, { useEffect } from 'react';
import { useRouteMatch } from 'react-router';
import { URL_MOEGO_PAY_WIKI } from '../../../config/host/const';
import { PATH_CREDIT_CARD_SETTING_MOEGO_PAY, PATH_GROOMING_CALENDAR } from '../../../router/paths';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { selectMoeGoPaySetupStatus } from '../../../store/stripe/stripe.selectors';
import { useBool } from '../../../utils/hooks/useBool';
import { MoeGoPaySetupPopoverContentView } from './MoeGoPaySetupPopover.style';
import {
  LK_MOEGO_PAY_NEW_FEATURE_POPOVER_GOT_IT,
  LK_MOEGO_PAY_SETUP_POPOVER_GOT_IT,
} from './MoeGoPaySetupPopover.util';
import { stripeAvailableCountryOption } from './StripeSetup';

interface Props {
  isNarrow?: boolean;
  sideOffset?: number;
  alignOffset?: number;
  children?: React.ReactNode;
}

export const MoeGoPaySetupPopover = ({ children, sideOffset, alignOffset, isNarrow }: Props) => {
  const [setupStatusCtx, business] = useSelector(selectMoeGoPaySetupStatus, selectCurrentBusiness);
  const isGotItMoeGoPay = useBool(() => !!localStorage.getItem(LK_MOEGO_PAY_SETUP_POPOVER_GOT_IT));
  const isGotItNewFeature = useBool(() => !!localStorage.getItem(LK_MOEGO_PAY_NEW_FEATURE_POPOVER_GOT_IT));
  const match = useRouteMatch(PATH_GROOMING_CALENDAR.path);
  const isInCardProcessingRoute = !!useRouteMatch(PATH_CREDIT_CARD_SETTING_MOEGO_PAY.path);

  const needSetupMoeGoPay =
    stripeAvailableCountryOption(business.country) &&
    setupStatusCtx.isLoaded &&
    setupStatusCtx.setupStatus === 'notSetup';

  const showSetupMoeGoPayTip = !isGotItMoeGoPay.value && !!needSetupMoeGoPay;

  const isVisible = !!match && (showSetupMoeGoPayTip || !isGotItNewFeature.value);

  const title = showSetupMoeGoPayTip
    ? 'Set up MoeGo Pay to boost revenue and prevent loss.'
    : !isGotItNewFeature.value
      ? "It's here! No more invalid cards on file, enable card authentication now."
      : '';

  useEffect(() => {
    if (isInCardProcessingRoute && !isGotItNewFeature.value) {
      isGotItNewFeature.as(true);
      localStorage.setItem(LK_MOEGO_PAY_NEW_FEATURE_POPOVER_GOT_IT, '1');
    }
  }, [isInCardProcessingRoute, isGotItNewFeature.value]);

  const handleGotIt = () => {
    if (showSetupMoeGoPayTip) {
      localStorage.setItem(LK_MOEGO_PAY_SETUP_POPOVER_GOT_IT, '1');
      isGotItMoeGoPay.as(true);
    }

    if (!isGotItNewFeature.value) {
      localStorage.setItem(LK_MOEGO_PAY_NEW_FEATURE_POPOVER_GOT_IT, '1');
      isGotItNewFeature.as(true);
    }
  };

  const renderContent = () => {
    return (
      <MoeGoPaySetupPopoverContentView>
        <div className="message">{title}</div>
        <div className="actions !moe-flex !moe-flex-row !moe-relative !moe-h-[30px]">
          {showSetupMoeGoPayTip ? (
            <div className="learn-more">
              <a href={URL_MOEGO_PAY_WIKI} target="_blank" rel="noreferrer">
                Learn more
              </a>
            </div>
          ) : null}
          <div className="got-it-button" onClick={handleGotIt}>
            Got it
          </div>
        </div>
      </MoeGoPaySetupPopoverContentView>
    );
  };

  return (
    <Tooltip
      side="right"
      align="center"
      sideOffset={sideOffset}
      alignOffset={alignOffset}
      isOpen={isVisible}
      content={renderContent()}
    >
      <div>{children}</div>
    </Tooltip>
  );
};
