import React, { memo } from 'react';
import SvgIconWarningCircleSvg from '../../../../../assets/svg/icon-warning-circle.svg';
import { SvgIcon } from '../../../../../components/Icon/Icon';

export interface ReviewUpdateTipsProps {
  onClick: () => void;
}

export const ReviewUpdateTips = memo<ReviewUpdateTipsProps>(({ onClick }) => {
  return (
    <div
      className="moe-flex moe-mx-[20px] moe-my-[10px] moe-p-[12px] moe-align-middle moe-justify-between moe-bg-[#FFF7E8] moe-rounded-[8px]"
      onClick={onClick}
    >
      <div className="moe-flex moe-flex-1 moe-items-center">
        <SvgIcon src={SvgIconWarningCircleSvg} size={20} color="#FAAD14" />
        <div className="moe-ml-[6px]">This client has updated the info.</div>
      </div>
      <div className="moe-inline-flex moe-bg-[#FAAD14] moe-rounded-[56px] moe-px-[16px] moe-py-[8px] moe-text-white moe-font-bold moe-text-xs moe-cursor-pointer">
        Review updates
      </div>
    </div>
  );
});
