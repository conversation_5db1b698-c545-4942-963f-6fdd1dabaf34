/*
 * @since 2021-07-01 11:22:25
 * <AUTHOR> <<EMAIL>>
 */

import { OrderSourceType } from '@moego/api-web/moego/models/order/v1/order_enums';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { useTakePayment } from '../../../PaymentFlow/TakePaymentDrawer/useTakePayment';
import { useViewOrder } from '../../../PaymentFlow/ViewOrderDrawer/useViewOrder';
import { useInvoiceReinvent } from '../../../PaymentFlow/hooks/useInvoiceReinvent';
import { LegacyTakePaymentModal, type LegacyTakePaymentModalProps } from './LegacyTakePaymentModal';

export interface TakePaymentModalProps extends LegacyTakePaymentModalProps {
  isCheckout?: boolean;
}

export const TakePaymentModal = memo<TakePaymentModalProps>((props) => {
  // TODO（Sam）：是否需要获取了 order 再判断 order version，再进行分流。
  const { isEnableToNewFlow: originalIsEnableToNewFlow, isNewOrderV4Flow } = useInvoiceReinvent();
  const isEnableToNewFlow = isNewOrderV4Flow || originalIsEnableToNewFlow;
  const { requestTakeDeposit, requestTakePayment, requestTakeNoShowFee, requestTakeCheckout } = useTakePayment();
  const [business] = useSelector(selectCurrentBusiness);

  // TODO: Legacy 实现，正确的情况应该把 view invoice 和 take payment 分开
  const { openViewOrderDrawer } = useViewOrder();
  const withNewFlow = isEnableToNewFlow && props.module === 'grooming';
  if (withNewFlow && props.isViewOnly) {
    openViewOrderDrawer({
      sourceId: props.appointmentId ? String(props.appointmentId) : undefined,
      sourceType: OrderSourceType.APPOINTMENT,
      orderId: props.invoiceId ? String(props.invoiceId) : undefined,
      businessId: business.id ? String(business.id) : undefined,
      module: props.module,
      onClose: props.onClose,
    });
    return null;
  }

  if (withNewFlow) {
    if (props.isDeposit) {
      requestTakeDeposit(props);
    } else if (props.isChargeNoShowFee) {
      requestTakeNoShowFee(props);
    } else if (props.isCheckout && isNewOrderV4Flow) {
      requestTakeCheckout(props);
    } else {
      requestTakePayment(props);
    }

    return null;
  }

  return <LegacyTakePaymentModal {...props} />;
});
