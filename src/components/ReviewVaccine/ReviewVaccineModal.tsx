import { DatePicker, Form, Modal, Spin, Typography, cn, useForm } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useMemo } from 'react';

import {
  type ReviewPetVaccineRequestResult,
  type ReviewPetVaccineRequestResultVaccine,
} from '@moego/api-web/moego/api/business_customer/v1/business_pet_vaccine_request_api';
import { PetType } from '@moego/api-web/moego/models/customer/v1/customer_pet_enums';
import { CompressedAvatar } from '@moego/business-components';
import { MinorFileOutlined, MinorRightArrowOutlined, MinorShowOutlined } from '@moego/icons-react';
import dayjs from 'dayjs';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { approvePetVaccineRequest, declinePetVaccineRequest } from '../../store/customer/vaccineReview.action';
import { dismissPendingReviewNotification } from '../../store/notification/notification.actions';
import { useAsyncCallback } from '../../utils/hooks/useAsyncCallback';
import { toastApi } from '../Toast/Toast';

const mapPetTypeForAvatar = (petType?: PetType): 'cat' | 'dog' | 'other' => {
  if (petType === PetType.CAT) {
    return 'cat';
  }
  if (petType === PetType.DOG) {
    return 'dog';
  }
  return 'other';
};

// Define data structures based on usage and linter errors

type VaccineItemProps = {
  vaccine: ReviewPetVaccineRequestResultVaccine;
  editable?: boolean;
};
const getFileName = (url: string) => {
  // 从url query中获取name
  const urlObj = new URL(url);
  return urlObj.searchParams.get('name') ?? 'document';
};

const VaccineItem = memo<VaccineItemProps>(({ vaccine, editable }) => {
  const [business] = useSelector(selectCurrentBusiness);
  const previewImage = (url: string) => {
    if (url) {
      window.open(url, '_blank', 'noopener noreferrer');
    }
  };
  const expiredDate = useMemo(() => {
    if (vaccine?.expirationDate) {
      return dayjs(`${vaccine.expirationDate.year}-${vaccine.expirationDate.month}-${vaccine.expirationDate.day}`);
    }
    return false;
  }, [vaccine?.expirationDate]);
  if (!vaccine || !vaccine.recordId) {
    return (
      <Typography.Text variant="regular" className="moe-mb-1 moe-text-tertiary moe-text-s">
        No record
      </Typography.Text>
    );
  }
  return (
    <div className="moe-flex moe-flex-col moe-items-start">
      {editable ? (
        <Form.Item
          label="Expiration date"
          name="expirationDate"
          rules={{ required: 'Please enter a expiration date.' }}
        >
          <DatePicker isRequired />
        </Form.Item>
      ) : (
        <Typography.Text
          variant="regular"
          className={cn('moe-mb-1 moe-text-s', editable ? 'moe-text-primary' : 'moe-text-tertiary')}
        >
          Expires: {expiredDate ? business.formatDate(expiredDate) : '-'}
        </Typography.Text>
      )}
      {vaccine?.documentUrls?.map((doc: string) => {
        return (
          <div
            key={doc}
            className="moe-flex moe-flex-row moe-items-center moe-mt-1 moe-cursor-pointer moe-py-[6px]"
            onClick={() => previewImage(doc)}
          >
            <MinorFileOutlined
              className={cn('moe-shrink-0 moe-mr-1', editable ? 'moe-text-primary' : 'moe-text-tertiary')}
            />
            <Typography.Text
              variant="caption"
              className={cn('moe-line-clamp-1', editable ? 'moe-text-primary' : 'moe-text-tertiary')}
            >
              {getFileName(doc)}
            </Typography.Text>
            <MinorShowOutlined
              className={cn('moe-shrink-0 moe-ml-2', editable ? 'moe-text-primary' : 'moe-text-tertiary')}
            />
          </div>
        );
      })}
    </div>
  );
});

type ReviewVaccineItemProps = {
  vaccineBefore: ReviewPetVaccineRequestResultVaccine;
  vaccineAfter: ReviewPetVaccineRequestResultVaccine;
};

const ReviewVaccineItem = memo<ReviewVaccineItemProps>(({ vaccineBefore, vaccineAfter }) => {
  return (
    <div className="moe-my-4 moe-p-4 moe-border moe-border-gray-200 moe-rounded-4">
      <div className="moe-flex moe-flex-row moe-justify-between moe-items-center">
        <Typography.Heading size={5} color="text-gray-700">
          {vaccineAfter?.vaccineName}
        </Typography.Heading>
      </div>
      <div className="moe-flex moe-flex-row moe-justify-between moe-items-start moe-mt-3 moe-gap-3">
        <div className="moe-flex-1">
          <VaccineItem vaccine={vaccineBefore} />
        </div>
        <MinorRightArrowOutlined className="moe-w-5 moe-mt-[2px] moe-h-5 moe-shrink-0 moe-text-gray-400" />
        <div className="moe-flex-1">
          <VaccineItem editable vaccine={vaccineAfter} />
        </div>
      </div>
    </div>
  );
});

export interface ReviewVaccineModalProps {
  petVaccineRequestId: string;
  notificationId: number;
  data: ReviewPetVaccineRequestResult;
  onClose: () => void;
}

export const ReviewVaccineModal = memo<ReviewVaccineModalProps>(
  ({ onClose, data, petVaccineRequestId, notificationId }) => {
    const [business] = useSelector(selectCurrentBusiness);
    const form = useForm({
      mode: 'all',
      defaultValues: {
        expirationDate: data?.petVaccineAfter?.expirationDate
          ? dayjs(
              `${data.petVaccineAfter.expirationDate.year}-${data.petVaccineAfter.expirationDate.month}-${data.petVaccineAfter.expirationDate.day}`,
            )
          : undefined,
      },
    });
    const dispatch = useDispatch();

    const handleSubmit = useAsyncCallback(async () => {
      return form.handleSubmit(async (data) => {
        if (!data.expirationDate) {
          toastApi.error('Please select a valid expiration date');
          return;
        }
        await approvePetVaccineRequest({
          id: petVaccineRequestId,
          notificationId: notificationId?.toString(),
          expirationDate: {
            year: data.expirationDate?.year(),
            month: data.expirationDate?.month() + 1,
            day: data.expirationDate?.date(),
          },
        });
        toastApi.success('Vaccine updated approved');
        await dispatch(dismissPendingReviewNotification(notificationId));
        onClose();
      })();
    });

    const handleCancel = useAsyncCallback(async () => {
      await declinePetVaccineRequest({
        id: petVaccineRequestId,
        notificationId: notificationId?.toString(),
        force: false,
      });
      await dispatch(dismissPendingReviewNotification(notificationId));
      toastApi.success('Vaccine updated declined');
      onClose();
    });

    return (
      <Modal
        isOpen
        className={cn('moe-w-[600px]')}
        classNames={{
          body: 'moe-pb-4 moe-pt-2',
        }}
        title="Review vaccine update"
        isMaskCloseable={false}
        autoCloseOnConfirm={false}
        autoCloseOnTertiary={false}
        confirmText="Approve and update"
        cancelText="Decline"
        onClose={onClose}
        onConfirm={handleSubmit}
        onCancel={handleCancel}
        cancelButtonProps={{
          isLoading: handleCancel.loading,
          isDisabled: handleSubmit.loading,
        }}
        confirmButtonProps={{
          isLoading: handleSubmit.loading,
          isDisabled: handleCancel.loading,
        }}
      >
        <Spin classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-w-full moe-h-full' }} isLoading={false}>
          <div className="moe-flex moe-flex-row moe-gap-6 moe-mb-4 moe-p-1">
            <div className="moe-w-2/3">
              <Typography.Heading size={6} className="moe-text-tertiary">
                Client name
              </Typography.Heading>
              <Typography.Text variant="regular" className="moe-text-primary">
                {data.client.firstName} {data.client.lastName}
                {data.client.phoneNumber ? ` (${business.formatPhoneNumber(data.client.phoneNumber)})` : ''}
              </Typography.Text>
            </div>
            <div className="moe-w-1/3">
              <Typography.Heading size={6} className="moe-text-tertiary">
                Submitted at
              </Typography.Heading>
              <Typography.Text variant="regular" className="moe-text-primary">
                {business.formatDateTimeStringWithTZ(data.createTime)}
              </Typography.Text>
            </div>
          </div>
          <div className="moe-mb-4 moe-p-1">
            <div className="moe-flex moe-flex-row moe-gap-[12px] moe-items-center moe-mb-3 p-2 bg-gray-50 rounded-md">
              <CompressedAvatar.Pet
                size="m"
                type={mapPetTypeForAvatar(data.pet.petType)}
                src={data.pet.avatarPath}
                name={data.pet.petName}
              />
              <div className="moe-flex moe-flex-col moe-gap-0.5">
                <Typography.Heading size={5}>{data.pet.petName}</Typography.Heading>
                {data.pet.breed && (
                  <Typography.Text variant="caption" className="moe-text-tertiary">
                    {data.pet.breed}
                  </Typography.Text>
                )}
              </div>
            </div>
            <Form form={form} footer={null}>
              <ReviewVaccineItem vaccineBefore={data.petVaccineBefore} vaccineAfter={data.petVaccineAfter} />
            </Form>
          </div>
        </Spin>
      </Modal>
    );
  },
);
