import { useSelector } from 'amos';
import { isNil } from 'lodash';
import { T_MINUTE } from 'monofile-utilities/lib/consts';
import React, { type ReactNode, useCallback, useMemo } from 'react';
import SvgIconMessageSvg from '../../../../assets/svg/icon-message.svg';
import { SvgIcon } from '../../../../components/Icon/Icon';
import { type MessageCenterState } from '../../../../router/paths';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { referralSourceMapBox } from '../../../../store/business/referralSource.boxes';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import { type OnlineBookingAbandonRecordModel } from '../../../../store/onlineBooking/abandonBooking.boxes';
import { staffMapBox } from '../../../../store/staff/staff.boxes';
import { type EnumKeys, createEnum } from '../../../../store/utils/createEnum';
import { ID_ANONYMOUS, isNormal } from '../../../../store/utils/identifier';
import { c_primary } from '../../../../style/_variables';
import { useRedirectMessage } from '../../../../utils/BusinessUtil';
import { transformFrequencyToString } from '../../../../utils/transformFrequencyToString';
import { getPreferredDayText } from '../../../Client/ClientInfo/ClientDetail/components/PreferredDayInput';
import { type InfoItem } from './BookingInfo';

export const BookingRequestModalLabelMap = createEnum({
  clientName: ['clientName', 'Client name'],
  phoneNumber: ['phoneNumber', 'Contact'],
  email: ['email', 'Email'],
  address: ['address', 'Address'],
  referralSource: ['referralSource', 'Referral source'],
  preferredGroomer: ['preferredGroomer', 'Preferred groomer'],
  preferredFrequency: ['preferredFrequency', 'Preferred frequency'],
  preferredDayOfTheWeek: ['preferredDayOfTheWeek', 'Preferred day of the week'],
  preferredTimeOfTheDay: ['preferredTimeOfTheDay', 'Preferred time of the day'],
  petName: ['petName', 'Pet name'],
  petImage: ['petImage', 'Pet image'],
  services: ['services', 'Services'],
  addons: ['addons', 'Add-ons'],
  petType: ['petType', 'Pet type'],
  petBreed: ['petBreed', 'Pet breed'],
  fixed: ['fixed', 'Fixed'],
  coatType: ['coatType', 'Coat type'],
  birthday: ['birthday', 'Birthday'],
  gender: ['gender', 'Gender'],
  vaccine: ['vaccine', 'Vaccine'],
  weight: ['weight', 'Weight'],
  vetName: ['vetName', 'Vet name'],
  vetPhoneNumber: ['vetPhoneNumber', 'Vet phone number'],
  emergencyContact: ['emergencyContact', 'Emergency contact'],
  healthIssues: ['healthIssues', 'Health issues'],
  behavior: ['behavior', 'Behavior'],
  vetAddress: ['vetAddress', 'Vet address'],
  room: ['room', 'Room'],
  lodging: ['lodging', 'Lodging'],
  feedings: ['feedings', 'Feeding'],
  medications: ['medications', 'Medication'],
  appointmentDate: ['appointmentDate', 'Appointment date'],
  staff: ['staff', 'Staff'],
});

export type onPreview = (options: { visible: boolean; avatarPath: string; petTypeId: number }) => void;

export type EnumQuestionsValue =
  | ReactNode
  | {
      className?: string;
      renderValue: (onClick: onPreview) => ReactNode;
    };

export type EnumQuestions = {
  [key in EnumKeys<typeof BookingRequestModalLabelMap>]?: EnumQuestionsValue;
};

export const replaceUnderscoreWithSpace = (str: string) => {
  return str.replace(/_/g, ' ');
};

export const getLabelValueList = (enumQuestions: EnumQuestions, customQuestionsList: InfoItem[]) => {
  const enumQuestionsList: InfoItem[] = Object.entries(enumQuestions).map(([key, value]) => {
    const { className, renderValue } =
      typeof value === 'object' && value && 'renderValue' in value ? value : { className: '', renderValue: value };

    return {
      className,
      label: BookingRequestModalLabelMap.mapLabels[key],
      value: renderValue,
    };
  });
  return enumQuestionsList.concat(customQuestionsList);
};

export const useIsShowItem = () => {
  const stableLabels = useMemo(
    () => [
      BookingRequestModalLabelMap.mapLabels[BookingRequestModalLabelMap.clientName],
      BookingRequestModalLabelMap.mapLabels[BookingRequestModalLabelMap.phoneNumber],
      BookingRequestModalLabelMap.mapLabels[BookingRequestModalLabelMap.email],
      BookingRequestModalLabelMap.mapLabels[BookingRequestModalLabelMap.address],
      BookingRequestModalLabelMap.mapLabels[BookingRequestModalLabelMap.petName],
      BookingRequestModalLabelMap.mapLabels[BookingRequestModalLabelMap.petImage],
      BookingRequestModalLabelMap.mapLabels[BookingRequestModalLabelMap.services],
      BookingRequestModalLabelMap.mapLabels[BookingRequestModalLabelMap.addons],
    ],
    [],
  );

  return useCallback((infoItem: InfoItem) => {
    const { label, value } = infoItem;
    return stableLabels.includes(label) || typeof value === 'number' || value;
  }, []);
};

export const usePreferenceValues = (preference: OnlineBookingAbandonRecordModel['preference']) => {
  const [business, referralSourceMap, staffMap] = useSelector(selectCurrentBusiness, referralSourceMapBox, staffMapBox);

  return useMemo(() => {
    if (!preference) {
      return {};
    }

    const {
      referralSourceId,
      preferredGroomerId,
      preferredFrequencyDay,
      preferredFrequencyType,
      preferredDay,
      preferredTime,
    } = preference;

    const referralSourceName = referralSourceMap.mustGetItem(referralSourceId)?.sourceName;
    const preferredGroomerName = staffMap.mustGetItem(preferredGroomerId)?.fullName()?.trim();
    const preferredFrequency = transformFrequencyToString(preferredFrequencyDay, preferredFrequencyType);
    const preferredDayText = getPreferredDayText(preferredDay, business.daysOfWeekOptionList());
    const preferredStartTime = preferredTime[0] && business.formatFixedTime(preferredTime[0]! * T_MINUTE);
    const preferredEndTime = preferredTime[1] && business.formatFixedTime(preferredTime[1]! * T_MINUTE);
    const preferredTimeText =
      !isNil(preferredStartTime) && preferredEndTime ? `${preferredStartTime}-${preferredEndTime}` : '';

    return {
      preferredTimeText: preferredTimeText || '',
      preferredFrequency,
      referralSourceName: referralSourceName || '',
      preferredGroomerName: preferredGroomerName || '',
      preferredDayText: preferredDayText === 'Select day' ? '' : preferredDayText,
    };
  }, [business, referralSourceMap, staffMap, preference]);
};

export const useClientInfoContactValue = (
  customerId?: number,
  phoneNumber?: string,
  // state 用于abandon模块传递数据
  state?: MessageCenterState,
) => {
  const [business, permissions] = useSelector(selectCurrentBusiness, selectCurrentPermissions);
  const handleToMessage = useRedirectMessage(customerId || ID_ANONYMOUS, state);

  const clientPhoneNumber = business.formatPhoneNumber(phoneNumber);
  const contactSuffix =
    isNormal(customerId) && phoneNumber && permissions.has('viewMessageCenter') ? (
      <SvgIcon
        size={18}
        src={SvgIconMessageSvg}
        color={c_primary}
        className="!moe-ml-[8px]"
        onClick={handleToMessage}
      />
    ) : null;

  return useMemo(() => {
    return (
      <span className="rr-mask">
        {clientPhoneNumber} {contactSuffix}
      </span>
    );
  }, [business, permissions, customerId, phoneNumber]);
};
