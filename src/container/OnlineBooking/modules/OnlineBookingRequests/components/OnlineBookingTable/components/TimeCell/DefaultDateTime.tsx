import React, { type FC } from 'react';
import { type CareTypeServiceDateTimeProps } from './timeCell.types';
import { Text } from '@moego/ui';
import dayjs from 'dayjs';
import { useSelector } from 'amos';
import { selectCurrentBusiness } from '../../../../../../../../store/business/business.selectors';
import { T_MINUTE } from 'monofile-utilities/lib/consts';
import { Condition } from '../../../../../../../../components/Condition';

/**
 * default date time grooming / dog walking, should be like: 01/25, 10:00 am - 12:00 pm
 */
export const DefaultDateTime: FC<CareTypeServiceDateTimeProps> = (props) => {
  const { rowData } = props;
  const { appointmentDate, appointmentStartTime, appointmentEndTime, noStartTime } = rowData;

  const [business] = useSelector(selectCurrentBusiness);

  const appointmentDateDay = dayjs(appointmentDate);

  return (
    <div>
      <Text as="span" variant="regular-short" className="moe-text-primary moe-whitespace-nowrap">
        {appointmentDateDay.format(business.dateFormatMD)}
        {!noStartTime && `, ${business.formatFixedTime(appointmentStartTime * T_MINUTE)}`}
      </Text>
      <Condition if={appointmentEndTime}>
        <Text as="span" variant="regular-short" className="moe-text-primary moe-whitespace-nowrap">
          &nbsp;-&nbsp;
          {business.formatFixedTime((appointmentEndTime ?? 0) * T_MINUTE)}
        </Text>
      </Condition>
    </div>
  );
};
