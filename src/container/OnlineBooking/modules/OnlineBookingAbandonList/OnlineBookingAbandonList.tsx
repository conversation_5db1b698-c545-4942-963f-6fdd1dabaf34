import { useDispatch, useSelector } from 'amos';
import Checkbox, { type CheckboxChangeEvent } from 'antd/lib/checkbox';
import React, { memo, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useMeasure } from 'react-use';
import { Condition } from '../../../../components/Condition';
import { WithPermission } from '../../../../components/GuardRoute/WithPermission';
import { usePricingEnableUpgrade } from '../../../../components/Pricing/pricing.hooks';
import {
  type ComMoegoServerGroomingWebParamsSearchAbandonedClientParam$SortOrder as SortOrder,
  type ComMoegoServerGroomingWebParamsSearchAbandonedClientParam$SortProperty as SortProperty,
} from '../../../../openApi/grooming-schema';
import { ClientFilterListSourceMap } from '../../../../store/customer/clientFilters.boxes';
import {
  type OnlineBookingAbandonRecordModel,
  onlineBookingAbandonMapBox,
} from '../../../../store/onlineBooking/abandonBooking.boxes';
import {
  selectChosenAbandoned,
  selectOnlineBookingAbandonList,
} from '../../../../store/onlineBooking/abandonBooking.selectors';
import { AbandonStepEnum } from '../../../../store/onlineBooking/abandonFilters.boxes';
import {
  addChosenAbandoned,
  addUnchosenAbandoned,
  getOnlineBookingAbandonList,
  toggleIsAllChosen,
} from '../../../../store/onlineBooking/actions/private/abandonBooking.actions';
import { getPetVaccineList } from '../../../../store/pet/petVaccine.actions';
import { getAllBusinessBasicServiceInfoList } from '../../../../store/service/actions/public/service.actions';
import { isNormal } from '../../../../store/utils/identifier';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { AllInOneCxt } from '../../../Calendar/latest/CalendarLodgingOverview';
import { FilterPropertyConfigMap } from '../../../Client/ClientList/componentsLegacy/ClientFilters/ClientFilters.config';
import { useClientFiltersVisible } from '../../../Client/ClientList/componentsLegacy/ClientFilters/ClientFilters.hooks';
import { ClientSelectedFilters } from '../../../Client/ClientList/componentsLegacy/ClientSelectedFilters';
import { StyledTable } from '../../../settings/components/StyledTable';
import { BookingOverview } from '../../components/BookingOverview/BookingOverview';
import { type UpdateRecordType } from '../../components/OnlineBookingInfoUpdateDrawer/type';
import { useOnlineBookingUpdate } from '../OnlineBookingRequests/hooks/useOnlineBookingUpdate';
import { AbandonExtraActions } from './AbandonActions/AbandonActions';
import { AbandonBookingRequestModal } from './AbandonBookingRequestModal/AbandonBookingRequestModal';
import { useOpenApptOrBookingRequestModal } from './AbandonBookingRequestModal/hooks/useOpenApptOrBookingRequestModal';
import { AbandonExport } from './AbandonExport/AbandonExport';
import { AbandonedFilters } from './AbandonFilters/AbandonFilters';
import { useFilterParams } from './AbandonFilters/AbandonFilters.hooks';
import { AbandonPagination } from './AbandonPagination/AbandonPagination';
import { AbandonPermission } from './AbandonPermission/AbandonPermission';
import { AbandonRecoveryMessage } from './AbandonRecoveryMessage/AbandonRecoveryMessage';
import { AbandonSearch } from './AbandonSearch/AbandonSearch';
import { OnlineBookingAbandonListView } from './OnlineBookingAbandonList.style';
import { useAbandonRouteProtect } from './hooks/useRouteProtect';
import { ABANDON_LIST_ROW, useTableColumns } from './hooks/useTableColumns';
import { useToAbandonSchedule } from './hooks/useToAbandonSchedule';

export const OnlineBookingAbandonList = memo(() => {
  const dispatch = useDispatch();
  const filterParam = useFilterParams();
  const [abandonList, onlineBookingAbandonMap, { unchosenList, chosenList, isAll }] = useSelector(
    selectOnlineBookingAbandonList(),
    onlineBookingAbandonMapBox,
    selectChosenAbandoned(),
  );
  const allInOne = useContext(AllInOneCxt);

  const [topContentRef, { height: topContentHeight }] = useMeasure<HTMLDivElement>();
  const [tableScrollY, setTableScrollY] = useState<string>('');

  useEffect(() => {
    setTableScrollY(`calc(100vh - ${(topContentHeight || 0) + 265}px)`);
  }, [topContentHeight]);

  const { access } = usePricingEnableUpgrade('abandonedBookings');
  const dataSource = useMemo(() => {
    const rawDataSource = abandonList.getList().map((id) => onlineBookingAbandonMap.mustGetItem(id));
    const noAccessDataSource = rawDataSource.sort((a, b) => {
      const largerStepFirst =
        (AbandonStepEnum.mapLabels[b.abandonStep]?.step ?? 0) - (AbandonStepEnum.mapLabels[a.abandonStep]?.step ?? 0);
      const newClientFirst = +a.customerId - +b.customerId;
      return largerStepFirst || newClientFirst;
    });
    return access ? rawDataSource : noAccessDataSource;
  }, [abandonList, access]);

  const [clientFiltersVisible] = useClientFiltersVisible();

  const openApptOrBookingRequestModal = useOpenApptOrBookingRequestModal();
  const [abandonBookingFlowId, setAbandonBookingFlowId] = useState('');
  const abandonSchedule = useToAbandonSchedule(abandonBookingFlowId);

  const selectedRowKeys = useMemo(
    () => (isAll ? abandonList.getList().filter((id) => !unchosenList.includes(id)) : chosenList),
    [isAll, chosenList, unchosenList, abandonList],
  );

  const onSelect = useCallback((record: OnlineBookingAbandonRecordModel, selected: boolean) => {
    const id = record.bookingFlowId;
    if (selected) {
      dispatch(addChosenAbandoned(id));
    } else {
      dispatch(addUnchosenAbandoned(id));
    }
  }, []);

  // 仅在点击左上角 checkbox 时触发，和下方单页数据的选中状态不联动
  const onSelectAll = useCallback((e: CheckboxChangeEvent) => {
    const checked = e.target.checked;
    dispatch([toggleIsAllChosen(checked)]);
  }, []);

  const handleRowClick = (record: OnlineBookingAbandonRecordModel) => {
    const { appointment, bookingFlowId } = record;
    if (isNormal(appointment?.id)) {
      openApptOrBookingRequestModal(appointment);
    } else {
      setAbandonBookingFlowId(bookingFlowId);
    }
  };

  useAbandonRouteProtect();

  const handleLoad = useCallback(async () => {
    dispatch(getOnlineBookingAbandonList({ filter: filterParam, clear: true, pageNum: 1 }));
  }, [filterParam]);

  useEffect(() => {
    dispatch([
      // 此处的调用是因为schedule时会打到createTicket页面，该页面grooming only，不考虑加boarding daycare
      getAllBusinessBasicServiceInfoList(),
      getPetVaccineList(),
    ]);
    handleLoad();
  }, []);

  useEffect(() => {
    allInOne.refreshOBAbandon = handleLoad;
    return () => {
      allInOne.refreshOBAbandon = null;
    };
  }, [handleLoad]);

  const openReviewUpdate = useOnlineBookingUpdate();

  const handleShowUpdateModal = useLatestCallback(async (record: UpdateRecordType) => {
    setAbandonBookingFlowId('');
    openReviewUpdate({
      customerId: record.customerId,
      bookFlowId: record.bookFlowId,
      async onSchedule(v) {
        if (v.bookFlowId) {
          await abandonSchedule(true);
        }
      },
      onClose(v) {
        v && handleLoad();
      },
    });
  });

  const columns = useTableColumns({ onOpenReviewUpdate: handleShowUpdateModal });

  return (
    <>
      <WithPermission permissions="canAccessAbandonBookingsMetrics">
        <BookingOverview />
      </WithPermission>

      <OnlineBookingAbandonListView className="!moe-relative moe-mt-[16px]">
        <div className="!moe-flex !moe-flex-1 !moe-overflow-hidden">
          <div className="!moe-flex-1 moe-flex moe-flex-col !moe-overflow-hidden moe-bg-white moe-rounded-[8px] moe-p-[24px] !moe-px-0 !moe-pt-0">
            <div ref={topContentRef}>
              <div className="moe-flex !moe-justify-between !moe-items-center">
                <div className="!moe-font-bold	!moe-text-[16px]">Recoverable booking list for the last 30 days</div>
                <AbandonRecoveryMessage />
              </div>
              <div className="moe-flex !moe-justify-between !moe-items-center moe-mt-[16px]">
                <Condition if={access}>
                  <AbandonSearch defaultKeyword={abandonList.filter.query?.keyword ?? ''} />
                  <div className="moe-flex moe-items-center moe-ml-[16px] moe-space-x-[16px]">
                    <AbandonExport />
                    <AbandonExtraActions />
                  </div>
                </Condition>
              </div>

              <Condition if={access}>
                <div className="moe-flex moe-mt-[16px]">
                  <ClientSelectedFilters
                    source={ClientFilterListSourceMap.AbandonedList}
                    requiredFilterList={[FilterPropertyConfigMap.AbandonedStatus]}
                    className="!moe-px-0 moe-w-full"
                  ></ClientSelectedFilters>
                </div>
              </Condition>
            </div>
            <div className="table-wrapper moe-flex-1 moe-relative">
              <StyledTable
                tableLayout="auto"
                dataSource={dataSource}
                columns={columns}
                rowSelection={{
                  preserveSelectedRowKeys: true,
                  selectedRowKeys,
                  onSelect,
                  columnTitle: <Checkbox checked={isAll && unchosenList?.length === 0} onChange={onSelectAll} />,
                  // 反馈说容易误触发onRow.click,所以阻止一下
                  renderCell(checked, record, index, originNode) {
                    return (
                      <label
                        className="moe-flex moe-items-center moe-w-full moe-h-full moe-cursor-pointer moe-justify-center"
                        onClick={(event) => event.stopPropagation()}
                      >
                        {originNode}
                      </label>
                    );
                  },
                }}
                onRow={(record) => ({
                  onClick: () => handleRowClick(record),
                  className: ABANDON_LIST_ROW,
                  style: {
                    cursor: 'pointer',
                  },
                })}
                rowKey={'bookingFlowId'}
                pagination={false}
                scroll={{ x: 'max-content', y: tableScrollY }}
                showSorterTooltip={false}
                loading={abandonList.isLoading()}
                className="!moe-rounded-b-none"
                onChange={(_pagination, _filters, _sorter, { action }) => {
                  const { pageSize, current: pageNum } = _pagination;
                  const hasChangedSort = action === 'sort';
                  const currentSorter = Array.isArray(_sorter) ? _sorter[0] : _sorter;
                  const newSort = hasChangedSort
                    ? {
                        order: (currentSorter.order === 'descend' ? 'DESC' : 'ASC') as SortOrder,
                        property: currentSorter.columnKey as SortProperty,
                      }
                    : abandonList.filter.sort || undefined;

                  dispatch(
                    getOnlineBookingAbandonList({
                      clear: hasChangedSort,
                      sort: newSort,
                      pageSize: pageSize || 10,
                      pageNum: hasChangedSort ? 1 : pageNum || 1,
                    }),
                  );
                }}
              />
              <AbandonPagination />
              <AbandonPermission total={abandonList.total} />
            </div>
          </div>

          <AbandonedFilters
            visible={clientFiltersVisible}
            source={ClientFilterListSourceMap.AbandonedList}
            onChange={handleLoad}
          />
        </div>
        <AbandonBookingRequestModal
          bookingFlowId={abandonBookingFlowId}
          onClose={() => setAbandonBookingFlowId('')}
          onReviewUpdate={handleShowUpdateModal}
        />
      </OnlineBookingAbandonListView>
    </>
  );
});
