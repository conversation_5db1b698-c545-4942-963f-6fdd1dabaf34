import { cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { throttle } from 'lodash';
import React, { memo, useEffect, useRef, useState } from 'react';
import { DndProvider } from 'react-dnd';
import { Redirect, Switch, useHistory } from 'react-router';
import { GuardRoute } from '../../../../components/GuardRoute/GuardRoute';
import { Breadcrumb } from '../../../../layout/Breadcrumb';
import { LayoutContainer } from '../../../../layout/LayoutContainer';
import { LayoutWithLeftNav } from '../../../../layout/SettingLayout';
import { SubAsideNavMenu } from '../../../../layout/components/SubAsideNavMenu';
import { type OnlineBookingNewSettingsParams, PATH_ONLINE_BOOKING_NEW_SETTINGS } from '../../../../router/paths';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import {
  getOnlineBookingLatestPreference,
  getOnlineBookingPreference,
  getOnlineBookingProfile,
} from '../../../../store/onlineBooking/actions/private/onlineBookingPreference.actions';
import { selectOnlineBookingPreference } from '../../../../store/onlineBooking/onlineBookingPreference.selectors';
import { getStaffList } from '../../../../store/staff/staff.actions';
import { isNormal } from '../../../../store/utils/identifier';
import { useRouteParams } from '../../../../utils/RoutePath';
import { dndManager } from '../../../../utils/dndManager';
import { GridCol } from '../../../Layout/GridLayout';
import { Agreement } from './Agreement/Agreement';
import { Availability } from './Availability/Availability';
import { BookingNotification } from './BookingNotification/BookingNotification';
import { BookingSite } from './BookingSite/BookingSite';
import { MobileGroomingV2 } from './MobileGrooming/MobileGrooming';
import { OBEntryContext } from './OnlineBookingEntryContext';
import { Overview } from './Overview/Overview';
import { Payment } from './Payment/Payment';
import { Questions } from './Questions/Questions';
import { useGetBreadcrumbList } from './hooks/useGetBreadcrumbList';
import { useGetOBNavList } from './hooks/useGetOBNavList';
import { type OnlineBookingNav, OnlineBookingPanel } from './types';
import { AvailableService } from './PetService/AvailableService';
import { useLimitationPreload } from '../../../settings/Settings/StaffSetting/ShiftManagement/components/Limitation/hooks/useLimitationPreload';
import { getOnlineBookingStaffAvailable } from '../../../../store/onlineBooking/actions/private/onlineBooking.actions';

export const OnlineBookingSettings = memo(() => {
  const [business, preference] = useSelector(selectCurrentBusiness, selectOnlineBookingPreference);
  const history = useHistory();
  const dispatch = useDispatch();
  const scrollContainer = useRef<HTMLDivElement>(null);
  const [scrollBottom, setScrollBottom] = useState(999);

  const { panel = OnlineBookingPanel.Overview } = useRouteParams(PATH_ONLINE_BOOKING_NEW_SETTINGS);
  const navList = useGetOBNavList();

  const getScrollBottom = () => {
    const { scrollHeight = 0, scrollTop = 0, offsetHeight = 0 } = scrollContainer.current || {};
    return scrollHeight - scrollTop - offsetHeight;
  };

  const handleScroll = throttle(() => {
    setScrollBottom(getScrollBottom());
  }, 300);

  useEffect(() => {
    scrollContainer.current?.addEventListener('scroll', handleScroll);
    return () => {
      scrollContainer.current?.removeEventListener('scroll', handleScroll);
    };
  }, [preference.businessId]);

  useLimitationPreload();

  useEffect(() => {
    if (isNormal(business.id)) {
      dispatch(getOnlineBookingStaffAvailable());
      // 坑！必须保证 getOnlineBookingPreference 在 getOnlineBookingProfile 之前成功返回！否则后者会更新
      // onlineBookingPreferenceMapBox，导致 OB 的 Payment 设置那边以为 preference 已经加载完成了
      dispatch(getOnlineBookingPreference()).then(() => dispatch(getOnlineBookingProfile()));
      dispatch(getStaffList());
      dispatch(getOnlineBookingLatestPreference());
    }
  }, [business.id]);

  const breadcrumbList = useGetBreadcrumbList([
    {
      label: 'Settings',
      route: PATH_ONLINE_BOOKING_NEW_SETTINGS.build(),
      RoutePath: PATH_ONLINE_BOOKING_NEW_SETTINGS,
    },
  ]);

  if (!isNormal(preference.businessId)) {
    return null;
  }

  return (
    <LayoutContainer
      className={cn('moe-overflow-auto moe-pr-0 moe-bg-[#fff] moe-font-manrope moe-pt-[32px] moe-pb-[0px]')}
      scrollerProviderRef={scrollContainer}
    >
      <LayoutWithLeftNav
        breadcrumb={<Breadcrumb list={breadcrumbList} className="!moe-ml-[16px]" />}
        col={GridCol.COL_12}
        className="moe-ml-[16px]"
        leftNav={
          <SubAsideNavMenu
            className="moe-w-[204px] moe-flex-shrink-0"
            navList={navList}
            onClick={(item) => {
              if (panel === item.id) {
                return;
              }
              history.push(
                PATH_ONLINE_BOOKING_NEW_SETTINGS.build({
                  panel: item.id as unknown as OnlineBookingNewSettingsParams['panel'],
                }),
              );
            }}
            activeId={panel as OnlineBookingNav | OnlineBookingPanel}
          />
        }
      >
        <div className="moe-pl-[16px] moe-flex-1">
          <DndProvider manager={dndManager}>
            <OBEntryContext.Provider
              value={{
                scrollBottom,
                bookingMainSettingsHeight: scrollContainer.current?.offsetHeight ?? 0,
                scrollContainer: scrollContainer.current,
              }}
            >
              <Switch>
                <GuardRoute
                  authType="business"
                  permissions="canAccessOBAutomationAndConfiguration"
                  path={PATH_ONLINE_BOOKING_NEW_SETTINGS.build({ panel: 'overview' })}
                  component={Overview}
                />
                <GuardRoute
                  authType="business"
                  permissions="canAccessOBAutomationAndConfiguration"
                  path={PATH_ONLINE_BOOKING_NEW_SETTINGS.build({ panel: 'booking_site' })}
                  component={BookingSite}
                />
                <GuardRoute
                  authType="business"
                  permissions="canAccessOBAutomationAndConfiguration"
                  path={PATH_ONLINE_BOOKING_NEW_SETTINGS.build({ panel: 'availability' })}
                  component={Availability}
                />
                <GuardRoute
                  authType="business"
                  permissions="canAccessOBAutomationAndConfiguration"
                  path={PATH_ONLINE_BOOKING_NEW_SETTINGS.build({ panel: 'service' })}
                  component={AvailableService}
                />
                <GuardRoute
                  authType="business"
                  permissions="canAccessOBAutomationAndConfiguration"
                  path={PATH_ONLINE_BOOKING_NEW_SETTINGS.build({ panel: 'questions' })}
                  component={Questions}
                />
                <GuardRoute
                  authType="business"
                  permissions="canAccessOBAutomationAndConfiguration"
                  path={PATH_ONLINE_BOOKING_NEW_SETTINGS.build({ panel: 'agreement' })}
                  component={Agreement}
                />
                <GuardRoute
                  authType="business"
                  permissions="canAccessOBAutomationAndConfiguration"
                  path={PATH_ONLINE_BOOKING_NEW_SETTINGS.build({ panel: 'payments' })}
                  component={Payment}
                />
                <GuardRoute
                  authType="business"
                  permissions="canAccessOBAutomationAndConfiguration"
                  path={PATH_ONLINE_BOOKING_NEW_SETTINGS.build({ panel: 'notification' })}
                  component={BookingNotification}
                />
                <GuardRoute
                  authType="business"
                  permissions="canAccessOBAutomationAndConfiguration"
                  path={PATH_ONLINE_BOOKING_NEW_SETTINGS.build({ panel: 'mobile_grooming' })}
                  component={MobileGroomingV2}
                />
                <Redirect to={PATH_ONLINE_BOOKING_NEW_SETTINGS.build({ panel: 'overview' })} />
              </Switch>
            </OBEntryContext.Provider>
          </DndProvider>
        </div>
      </LayoutWithLeftNav>
    </LayoutContainer>
  );
});
