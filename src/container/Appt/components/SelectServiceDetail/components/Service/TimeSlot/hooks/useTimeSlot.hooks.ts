import { type Action, useDispatch, useStore } from 'amos';
import { selectPetsInAppt } from '../../../../../../store/selectors/appt';
import { useState } from 'react';
import { useGetPetsInfoForApi } from '../../../../../../hooks/useGetPetsInfoForApi';
import { isNormal } from '../../../../../../../../store/utils/identifier';
import { updatePetService } from '../../../../../../store/appt.api';
import { addApptPetService } from '../../../../../../store/appt.actions';
import { useGetServiceListByPet } from '../../../../../../hooks/useGetServiceListByPet';
import { type ServiceEntry } from '../../../../../../../../components/ServiceApplicablePicker/types/serviceEntry';

export function useTimeSlotEditPetService(appointmentId: string) {
  const store = useStore();
  const dispatch = useDispatch();
  const getPetsInfoForApi = useGetPetsInfoForApi();
  const getServiceListByPet = useGetServiceListByPet();

  const [petsSnapshot] = useState(
    store.select(selectPetsInAppt(appointmentId)).map((pet) => ({
      petId: pet.petId,
      services: getServiceListByPet(pet, appointmentId),
    })),
  );

  const handleSubmitTimeSlotPetService = async ({ repeatType }: { repeatType?: number } = {}) => {
    if (isNormal(appointmentId)) {
      const newPets = getPetsInfoForApi(appointmentId);

      await dispatch(
        updatePetService({
          appointmentId,
          petDetails: newPets,
          repeatAppointmentModifyScope: repeatType,
        }),
      );
    }
  };

  const handleClearTimeSlotPetService = () => {
    const actions: Action[] = [];

    petsSnapshot.forEach((pet) => {
      actions.push(
        addApptPetService(pet.petId, pet.services, isNormal(appointmentId) ? appointmentId : undefined, true),
      );
    });

    dispatch(actions);
  };

  const updateTimeSlotPetServiceValue = (value: Partial<ServiceEntry>, id: string) => {
    const actions: Action[] = [];
    const petsServices = store.select(selectPetsInAppt(appointmentId)).map((pet) => ({
      petId: pet.petId,
      services: getServiceListByPet(pet, appointmentId),
    }));

    petsServices.forEach((pet) => {
      actions.push(
        addApptPetService(
          pet.petId,
          pet.services.map((s) =>
            s.id === id
              ? {
                  ...s,
                  ...Object.fromEntries(Object.entries(value || {}).filter(([k]) => k in s)),
                }
              : s,
          ),
          isNormal(appointmentId) ? appointmentId : undefined,
          true,
        ),
      );
    });

    dispatch(actions);
  };

  return {
    handleSubmitTimeSlotPetService,
    handleClearTimeSlotPetService,
    petsSnapshot,
    updateTimeSlotPetServiceValue,
  };
}
