import { MajorChevronDownOutlined } from '@moego/icons-react';
import { Avatar, Button, Dropdown, Heading, Tag } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { type Key, memo, useState } from 'react';
import { useHistory } from 'react-router';
import SvgIconSendShareSvg from '../../../../../assets/svg/icon-send-share.svg';
import { WithPermission, usePermissionCheck } from '../../../../../components/GuardRoute/WithPermission';
import { SvgIcon } from '../../../../../components/Icon/Icon';
import { WithPricingEnableUpgrade } from '../../../../../components/Pricing/WithPricingComponents';
import { PATH_SETTING } from '../../../../../router/paths';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import {
  updateOnlineBookingLatestPreference,
  updateOnlineBookingPreference,
} from '../../../../../store/onlineBooking/actions/private/onlineBookingPreference.actions';
import { selectOnlineBookingLatestPreference } from '../../../../../store/onlineBooking/onlineBookingPreference.selectors';
import { ID_LOADING } from '../../../../../store/utils/identifier';
import { useBizIdReadyEffect } from '../../../../../utils/hooks/useBizIdReadyEffect';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { DangerModal } from '../BookingSite/components/DangerModal';
import { OnlineBookingActionOptions } from './Overview.config';
import { getLocationDetail } from '../../../../../store/business/location.actions';

export const BusinessProfile = memo(() => {
  const [business, preference] = useSelector(selectCurrentBusiness, selectOnlineBookingLatestPreference);
  const dispatch = useDispatch();
  const history = useHistory();
  const [modalOptionsId, setModalOptionsId] = useState(ID_LOADING);

  useBizIdReadyEffect(() => {
    dispatch(getLocationDetail(business.id.toString()));
  }, []);

  const bookingEnable = preference.bookingEnable;
  const isPublished = preference.isPublished;

  const handleMenuClick = (key: Key) => {
    const optionsId = +key;
    if (optionsId === OnlineBookingActionOptions.GoToSetting) {
      history.push(PATH_SETTING.build({ panel: 'business' }));
    } else {
      setModalOptionsId(optionsId);
    }
  };

  const enableBooking = useSerialCallback(async () => {
    await dispatch(updateOnlineBookingPreference({ isEnable: 1 }));
  });

  /**
   * open ob at same time as publish site
   */
  const publishSite = useSerialCallback(async () => {
    await dispatch([
      updateOnlineBookingLatestPreference({ isPublished: true, isNew: false }),
      updateOnlineBookingPreference({ isEnable: 1 }),
    ]);
  });

  const openBookingLink = () => {
    window.open(preference.storefrontLink, '_blank');
  };

  const haveOnlineBookingSwitchPermission = usePermissionCheck({
    permissions: 'onlineBookingSwitch',
  });

  return (
    <div className="moe-border moe-border-solid moe-border-divider moe-rounded-spacing-xs moe-p-spacing-m moe-bg-white moe-font-manrope">
      <div className="moe-flex moe-font-medium">
        <Avatar.Business src={business.avatarPath} size="l"></Avatar.Business>
        <div className="moe-flex-1 moe-ml-[24px] moe-flex moe-flex-col moe-gap-y-[20px]">
          <div className="moe-flex moe-justify-between">
            <div className="moe-flex moe-flex-col moe-gap-y-xs moe-text-primary">
              <Heading size="3">{business.businessName}</Heading>
              <div className="moe-text-regular rr-mask">{business.printAddress()}</div>
              <div className="moe-text-regular rr-mask">{business.formatPhoneNumber(business.phoneNumber)}</div>
            </div>

            <div className="moe-flex moe-items-start moe-flex-shrink-0 moe-ml-[20px] moe-gap-x-[20px]">
              <Tag
                color={bookingEnable ? 'success' : 'danger'}
                label={`Booking: ${bookingEnable ? 'on' : 'off'}`}
                isBordered={false}
              ></Tag>
              <Tag
                color={isPublished ? 'success' : 'danger'}
                label={`${isPublished ? 'Live' : 'Unpublished'}`}
                isBordered={false}
              ></Tag>
            </div>
          </div>

          <div className="moe-flex moe-flex-col moe-gap-y-[4px]">
            <Heading size="6">Storefront URL:</Heading>
            <div className="moe-flex moe-items-center">
              <span className="moe-text-tertiary moe-text-sm-20">{decodeURIComponent(preference.storefrontLink)}</span>
              <SvgIcon
                onClick={openBookingLink}
                src={SvgIconSendShareSvg}
                className="!moe-ml-[8px] !moe-cursor-pointer"
                color="#333"
                size={16}
              />
            </div>
          </div>
          <div className="moe-flex moe-gap-x-s">
            {!bookingEnable && isPublished && (
              <WithPermission permissions="onlineBookingSwitch">
                <WithPricingEnableUpgrade permission="onlineBooking" overrideEvent="onPress">
                  <Button onPress={enableBooking} isLoading={enableBooking.isBusy()}>
                    Enable online booking
                  </Button>
                </WithPricingEnableUpgrade>
              </WithPermission>
            )}
            {isPublished ? (
              <Button onPress={openBookingLink}>View Live site</Button>
            ) : (
              <WithPermission permissions="onlineBookingSwitch">
                <WithPricingEnableUpgrade permission="onlineBooking" overrideEvent="onPress">
                  <Button onPress={publishSite} isLoading={publishSite.isBusy()}>
                    Publish site
                  </Button>
                </WithPricingEnableUpgrade>
              </WithPermission>
            )}
            <Dropdown trigger="press">
              <Dropdown.TriggerButton variant="secondary" suffix={<MajorChevronDownOutlined />}>
                Options
              </Dropdown.TriggerButton>
              <Dropdown.Menu onAction={handleMenuClick}>
                {OnlineBookingActionOptions.values.map((value) => {
                  const disabled =
                    (value === OnlineBookingActionOptions.Unpublish && !isPublished) ||
                    (value === OnlineBookingActionOptions.Unpublish && !haveOnlineBookingSwitchPermission) ||
                    (value === OnlineBookingActionOptions.DisableBooking && !haveOnlineBookingSwitchPermission) ||
                    (value === OnlineBookingActionOptions.DisableBooking && !bookingEnable);
                  return (
                    <Dropdown.MenuItem key={value} isDisabled={disabled}>
                      {OnlineBookingActionOptions.mapLabels[value]}
                    </Dropdown.MenuItem>
                  );
                })}
              </Dropdown.Menu>
            </Dropdown>
          </div>
        </div>
      </div>

      <DangerModal
        visible={[OnlineBookingActionOptions.Unpublish, OnlineBookingActionOptions.DisableBooking].includes(
          modalOptionsId,
        )}
        onClose={() => setModalOptionsId(ID_LOADING)}
        optionsId={modalOptionsId}
      />
    </div>
  );
});
