import { Checkbox, cn, Form, LegacySelect as Select, Switch, Text, Tooltip, type useForm, useWatch } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { useEffect, useMemo, useRef } from 'react';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';

import { type FormItemProps } from '@moego/ui/dist/esm/components/Form/FormItem';
import { get } from 'lodash';
import { PATH_MEMBERSHIP } from '../../../../router/paths';
import { type MembershipModel, companyMembershipMapBox } from '../../../../store/membership/membership.boxes';
import { useRouteQueryV2 } from '../../../../utils/RoutePath';
import { queryAllBusinessRetailProducts } from '../../../../store/retail/retailProduct.actions';
import { selectAllBusinessProductList } from '../../../../store/retail/retailProduct.selectors';
import { useMount } from 'ahooks';

export function ProductSelect({
  form,
  formItemName,
  formItemSelectAllName,
  rules,
  disabled = false,
  acceptAll = false,
  disabledIds = [],
}: {
  form: ReturnType<typeof useForm<MembershipModel>>;
  formItemName: any;
  formItemSelectAllName?: any;
  rules?: FormItemProps<any, any>['rules'];
  disabled?: boolean;
  acceptAll?: boolean;
  disabledIds?: number[];
}) {
  const dispatch = useDispatch();
  const { id } = useRouteQueryV2(PATH_MEMBERSHIP);
  const [business, companyMembershipMap, productionList] = useSelector(
    selectCurrentBusiness,
    companyMembershipMapBox,
    selectAllBusinessProductList(),
  );
  const hasInit = useRef<boolean>(false);

  const [formItemValue] = useWatch({
    control: form.control,
    name: [formItemName],
  });
  const [formItemSelectAllNameValue] = useWatch({
    control: form.control,
    name: [formItemSelectAllName],
  });

  useMount(() => {
    dispatch(queryAllBusinessRetailProducts());
  });

  const onSelectAll = (checked: boolean) => {
    if (checked) {
      const data = productionList
        .map((item) => item.options)
        .flat(1)
        .map((option) => option.value);
      form.setValue(formItemName, data);
    } else {
      form.setValue(formItemName, []);
    }
  };

  useEffect(() => {
    const isAllServiceSelect = productionList.reduce((pre, cur) => {
      return pre && cur.options.every((option: any) => formItemValue?.includes?.(option.value + ''));
    }, true);

    form.setValue(formItemSelectAllName, isAllServiceSelect);
  }, [formItemValue, productionList]);

  const [productionOptions, productName] = useMemo(() => {
    const selectProduction: string[] = [];
    const memoOptions = productionList.map((product) => {
      return {
        ...product,
        options: product.options.map((tempOption) => {
          if (formItemValue?.includes?.(tempOption.value + '')) {
            selectProduction.push(tempOption.label);
            return {
              ...tempOption,
              isSelected: true,
              isDisabled: false,
            };
          } else {
            return {
              ...tempOption,
              isDisabled: disabledIds.includes(+tempOption.value),
            };
          }
        }),
      };
    });
    return [memoOptions, selectProduction.join(', ')];
  }, [productionList, formItemValue, disabledIds]);

  const findName = (id: string | number) => {
    return productionList
      .map((item) => item.options)
      .flat(1)
      .find((option) => option.value == id)?.label;
  };

  useEffect(() => {
    if (!productionList || productionList.length === 0) {
      return;
    }
    if (id) {
      if (hasInit.current) return;
      hasInit.current = true;
      const membership = companyMembershipMap.mustGetItem(id).toJSON();
      if (get(membership, formItemSelectAllName)) {
        onSelectAll(true);
      }
    }
  }, [id, productionList]);

  return (
    <Tooltip
      key={'service-select' + formItemName}
      content={productName}
      container={document.getElementById('benefit-form-id') || document.body}
      side="top"
      isDisabled={productName.length === 0}
    >
      <div>
        <Form.Item name={formItemName} rules={rules}>
          <Select
            className="moe-w-[414px]"
            multipleMode="value"
            options={productionOptions}
            isRequired
            isDisabled={disabled}
            isMultiple
            placeholder="Select products"
            filterOption={(option, input) => {
              return option?.data.label.toLowerCase().includes(input.toLowerCase());
            }}
            renderItem={(option) => {
              const { data } = option as unknown as {
                data: { value: number; label: string; price: number; isDisabled: boolean; isSelected: boolean };
              };
              return (
                <div
                  key={data.value}
                  className={cn(
                    'moe-flex moe-items-center moe-gap-xs moe-justify-between moe-w-full',
                    data.isDisabled && 'moe-opacity-50',
                  )}
                >
                  <div className="moe-flex moe-items-center moe-gap-xs">
                    <Checkbox.Icon isDisabled={data.isDisabled} isSelected={data.isSelected} />
                    {data.label}
                  </div>
                  <Text variant="small" className="moe-flex-shrink-0">
                    {business.formatAmount(data.price as number)}
                  </Text>
                </div>
              );
            }}
            renderMultipleValues={(values) => {
              if (formItemSelectAllNameValue) {
                return 'All products';
              }
              return values
                .map((value) => {
                  return findName(value.value);
                })
                .join(', ');
            }}
            formatOptionLabel={(item) => {
              return findName(item.value);
            }}
            renderMenu={(menu) => {
              return (
                <div className="moe-w-full moe-flex moe-flex-col moe-h-[336px]">
                  <div className="moe-relative moe-flex-1 moe-overscroll-y-auto moe-min-h-0">{menu}</div>
                  {acceptAll && (
                    <div className="moe-relative moe-h-[52px] moe-flex-shrink-0 moe-p-[16px] moe-border-t-divider moe-border-t-[1px]">
                      <Form.Item name={formItemSelectAllName} />
                      <Switch
                        isSelected={formItemSelectAllNameValue}
                        isDisabled={
                          !((formItemValue?.length || 0) >= (disabledIds?.length || 0) || formItemSelectAllNameValue)
                        }
                        onChange={onSelectAll}
                      >
                        All products (including new products)
                      </Switch>
                    </div>
                  )}
                </div>
              );
            }}
          />
        </Form.Item>
      </div>
    </Tooltip>
  );
}
