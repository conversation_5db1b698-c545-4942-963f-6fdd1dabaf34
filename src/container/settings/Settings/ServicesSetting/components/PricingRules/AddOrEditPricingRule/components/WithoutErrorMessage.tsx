import { Input, LegacySelect as Select, LegacySelectProps } from '@moego/ui';
import React, { forwardRef } from 'react';

type InputNumberProps = Parameters<typeof Input.Number>[0];

/**
 * 过滤掉 Input 的错误展示，因为错误信息需要自定义展示位置
 */
export const InputNumberWithoutErrorMessage = forwardRef<HTMLInputElement, InputNumberProps>(function PriceValueInput(
  { errorMessage, ...props },
  ref,
) {
  return <Input.Number {...props} ref={ref} />;
});

export const SelectWithoutErrorMessage = forwardRef<any, LegacySelectProps>(function SelectWithoutErrorMessage(
  { errorMessage, ...props },
  ref,
) {
  return <Select {...props} ref={ref} />;
});
