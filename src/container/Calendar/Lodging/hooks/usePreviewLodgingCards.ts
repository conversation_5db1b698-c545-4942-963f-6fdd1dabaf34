import { useEffect } from 'react';
import ReactDOM from 'react-dom';
import { useGetFullCalendarApi } from '../../latest/AwesomeCalendarProvider';
import { type LodgingCalendarEvent } from '../LodgingCalendar.types';

/**
 * must be used in a component that is a child of <LodgingCalendarLanding>
 */
export const usePreviewLodgingCards = (
  eventList?: LodgingCalendarEvent[],
  placeholderCards?: LodgingCalendarEvent[],
) => {
  const getFullCalendarApi = useGetFullCalendarApi();
  const calendarApi = getFullCalendarApi();

  useEffect(() => {
    if (!calendarApi) {
      return;
    }

    const allEvents = [...(placeholderCards || []), ...(eventList || [])];

    ReactDOM.unstable_batchedUpdates(() => {
      calendarApi.batchRendering(() => {
        calendarApi.addEventSource(allEvents);
      });
    });

    return () => {
      ReactDOM.unstable_batchedUpdates(() => {
        calendarApi.removeAllEvents();
      });
    };
  }, [calendarApi, eventList, placeholderCards]);
};
