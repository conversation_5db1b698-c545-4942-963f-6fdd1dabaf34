import { MinorChevronUpOutlined } from '@moego/icons-react';
import { Dropdown, Text } from '@moego/ui';
import React from 'react';
import { DateOverrideType } from '../../../../../../../../store/staffSchedule/staffSchedule.types';
import { useBool } from '../../../../../../../../utils/hooks/useBool';
import { SMTestIds } from '../../../../../../../../config/testIds/shiftManagement';

// DateOverrideType: 0: Ongoing, 1: History 用下标映射一下
// DateOverrideType 改成 createEnum() 会更好，但是需求时间有限，要处理已存的类型问题太多
const TABS_OPTIONS_VALUE = [DateOverrideType.Ongoing, DateOverrideType.History];
const TABS_OPTIONS_LABEL = ['Ongoing', 'History'];

interface OverrideSwitcherProps {
  value: DateOverrideType;
  onChange: (value: DateOverrideType) => void;
}

export const OverrideSwitcher = (props: OverrideSwitcherProps) => {
  const { value, onChange } = props;
  const isDropdownVisible = useBool(false);

  return (
    <Dropdown
      data-testid={SMTestIds.SettingDateOverrideTypeDropdown}
      isOpen={isDropdownVisible.value}
      onOpenChange={isDropdownVisible.toggle}
    >
      <Dropdown.Trigger>
        <div className="moe-cursor-pointer moe-flex moe-items-center moe-gap-xs">
          <Text variant="small">{TABS_OPTIONS_LABEL[value]}</Text>
          <MinorChevronUpOutlined
            style={{
              transition: 'transform 0.2s ease-in-out',
              transform: isDropdownVisible.value ? 'rotate(180deg)' : 'rotate(0deg)',
            }}
          />
        </div>
      </Dropdown.Trigger>
      <Dropdown.Menu onAction={(key) => onChange(Number(key))} selectedKeys={[`${value}`]}>
        {TABS_OPTIONS_VALUE.map((v) => {
          return <Dropdown.Item key={`${v}`} title={TABS_OPTIONS_LABEL[Number(v)]} />;
        })}
      </Dropdown.Menu>
    </Dropdown>
  );
};
