import { QueryClient } from '@tanstack/react-query';
import { GC_TIME, STALE_TIME } from './utils/constants';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      staleTime: STALE_TIME.NONE,
      cacheTime: GC_TIME.MIN5,
      refetchOnWindowFocus(query) {
        return (
          query.state.status === 'success' &&
          (query.state.isInvalidated || query.state.dataUpdatedAt < Date.now() - GC_TIME.MIN5)
        );
      },
    },
    mutations: { retry: false },
  },
});

if (__DEV__) {
  window.__TANSTACK_QUERY_CLIENT__ = queryClient;
}
