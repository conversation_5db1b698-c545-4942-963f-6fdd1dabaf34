import { MinorEditOutlined } from '@moego/icons-react';
import { Alert, Heading, Text } from '@moego/ui';
import React from 'react';
import type { WorkingSlotValue } from '../../../../../../../../store/staff/staff.boxes';
import { WorkingSlotDailySettingTable, type WorkingSlotDailySettingValue } from './WorkingSlotDailySettingTable';
import { WorkingSlotListTable, type WorkingSlotListValue } from './WorkingSlotListTable';
import { FullWeekDayList } from '../../../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { useOnboardingTips } from '../../../hooks/useOnboardingTips';
import { useEnableMultiPetBySlotFeature } from '../../../../../../../Calendar/latest/components/SlotCalendar/hooks/useSlotCalendarFeature';

interface WorkingSlotProps {
  className?: string;
  day: number;
  value: WorkingSlotValue;
  onChange?: (v: WorkingSlotValue, dayIndex: number) => void;
  onCopy?: (v: WorkingSlotValue, dayIndex: number) => void;
  isDisabled?: boolean;
  hideDailyWorkingHour?: boolean;
}

export const WorkingSlot = (props: WorkingSlotProps) => {
  const { value, onChange, day, onCopy, className, isDisabled, hideDailyWorkingHour } = props;
  const dayLabel = FullWeekDayList[day];
  const { slotDailySetting, slotHourSettingList } = value;

  const { isVisible, hideTips } = useOnboardingTips('hideStaffScheduleBySlotTips');

  const isEnableOBBySlot = useEnableMultiPetBySlotFeature();

  const handleDailySettingChange = (v: WorkingSlotDailySettingValue) => {
    onChange?.(
      {
        ...value,
        slotDailySetting: v,
      },
      day,
    );
  };

  const handleSlotListChange = (v: WorkingSlotListValue) => {
    onChange?.(
      {
        ...value,
        slotHourSettingList: v,
      },
      day,
    );
  };

  const handleCopy = () => {
    onCopy?.(value, day);
  };

  const renderWorkingSlotListTitle = () => {
    return (
      <div>
        <Heading size="5" className="moe-flex moe-items-center moe-gap-[4px] moe-mb-[12px]">
          <span>{`${dayLabel}’s slot`}</span>
        </Heading>
        {isVisible && (
          <div className="moe-my-[12px]">
            <Alert
              isRounded
              color="information"
              classNames={{
                content: 'moe-items-center',
              }}
              onClose={() => hideTips()}
              description={
                <Text variant="small">
                  Set start time and pet maximum for each slot. If you need to set a limitation, click{' '}
                  <MinorEditOutlined className="moe-cursor-pointer moe-inline-block moe-align-bottom moe-text-[20px]" />
                  <span className="moe-whitespace-wrap">for specific limitations.</span>
                </Text>
              }
            />
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={className}>
      {isEnableOBBySlot && (
        <WorkingSlotDailySettingTable
          title={`${dayLabel}’s daily setting`}
          value={slotDailySetting}
          onChange={handleDailySettingChange}
          onCopy={handleCopy}
          isDisabled={isDisabled}
          hideDailyWorkingHour={hideDailyWorkingHour}
        />
      )}

      <WorkingSlotListTable
        className={isEnableOBBySlot ? 'moe-pt-m' : ''}
        title={renderWorkingSlotListTitle()}
        validTimeRange={hideDailyWorkingHour ? null : [slotDailySetting.startTime, slotDailySetting.endTime]}
        value={slotHourSettingList || []}
        onChange={handleSlotListChange}
        onDelete={(index) => {
          const nextSlotList = slotHourSettingList.slice(0, index).concat(slotHourSettingList.slice(index + 1));
          handleSlotListChange(nextSlotList);
        }}
        isDisabled={isDisabled}
      />
    </div>
  );
};
