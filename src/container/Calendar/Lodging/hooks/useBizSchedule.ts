import { useDispatch, useSelector } from 'amos';
import { currentBusinessIdBox } from '../../../../store/business/business.boxes';
import { type WorkingHourTimeRange } from '../../../../store/staff/staff.boxes';
import { getBizScheduleOpeningHour } from '../../../../store/staffSchedule/staffSchedule.actions';
import { bizScheduleMapBox } from '../../../../store/staffSchedule/staffSchedule.boxes';
import { type WeekTimeValueLegacy } from '../../../../store/staffSchedule/staffSchedule.types';
import { useBizIdReadyEffect } from '../../../../utils/hooks/useBizIdReadyEffect';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { type TWeeks, repeatOnWeeks } from '../../../CreateTicket/interfaces';

export function useBizSchedule() {
  const dispatch = useDispatch();

  const [businessId] = useSelector(currentBusinessIdBox);
  const [bizSchedule] = useSelector(bizScheduleMapBox.mustGetItem(businessId));
  const getBizSchedule = useSerialCallback(() => {
    dispatch(getBizScheduleOpeningHour(businessId));
  });

  useBizIdReadyEffect(() => {
    getBizSchedule();
  }, []);

  return bizSchedule;
}

/**
 * 转换时间数据
 *
 * @example transformTimeDateToDate({ 'monday': [{ startTime: 0, endTime: 60 }] }) --> [[{ startTime: 0, endTime: 60 }]]
 */
export function transformTimeDateToDate(timeDate?: WeekTimeValueLegacy) {
  if (!timeDate) {
    return [];
  }
  const result: WorkingHourTimeRange[][] = [];
  const lowCaseRepeatOnWeeks = repeatOnWeeks.map((week) => week.toLowerCase());

  Object.entries(timeDate).forEach(([key, value]) => {
    const dayIndex = lowCaseRepeatOnWeeks.indexOf(key.toLowerCase() as TWeeks);

    result[dayIndex] = value;
  });

  return result;
}
