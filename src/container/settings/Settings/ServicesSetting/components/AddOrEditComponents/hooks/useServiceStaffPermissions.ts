import { useSelector } from 'amos';
import { selectCurrentPermissions } from '../../../../../../../store/business/role.selectors';

export const useServiceStaffPermissions = (isEditMode: boolean) => {
  const [permissions] = useSelector(selectCurrentPermissions);
  const canAddService = permissions.has('addService');

  const isDisabledForDuration = isEditMode ? !permissions.has('updateServiceDuration') : !canAddService;
  const isDisabledForPriceAndTax = isEditMode ? !permissions.has('updateServicePriceAndTax') : !canAddService;
  const isDisabledForPetWeight = isEditMode ? !permissions.has('managePetWeightRange') : !canAddService;
  const isDisabledForPetType = isEditMode ? !permissions.has('managePetTypeAndBreed') : !canAddService;
  const isDisabledForRemains = isEditMode ? !permissions.has('updateService') : !canAddService;

  return {
    isDisabledForDuration,
    isDisabledForPriceAndTax,
    isDisabledForPetWeight,
    isDisabledForPetType,
    isDisabledForRemains,
  };
};
