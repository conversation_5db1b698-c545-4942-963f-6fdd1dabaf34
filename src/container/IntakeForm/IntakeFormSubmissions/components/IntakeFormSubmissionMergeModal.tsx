/*
 * @since 2021-01-05 13:26:27
 * <AUTHOR> <<EMAIL>>
 */

import { useDispatch, useSelector } from 'amos';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import React, { Fragment, type ReactNode, memo, useEffect } from 'react';
import { Avatar } from '../../../../components/Avatar/Avatar';
import { Button } from '../../../../components/Button/Button';
import { type VisibleModalProps } from '../../../../components/Modal/Modal';
import { toastApi } from '../../../../components/Toast/Toast';
import { currentAccountIdBox } from '../../../../store/account/account.boxes';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { getCustomerOverview } from '../../../../store/customer/customer.actions';
import { printAddress } from '../../../../store/customer/customerAddress.selectors';
import { acceptIntakeFormSubmission } from '../../../../store/intakeForm/intakeForm.actions';
import {
  type IntakeFormPetModel,
  IntakeFormQuestionCategory,
  type IntakeFormQuestionSnapshotModel,
  IntakeFormQuestionType,
} from '../../../../store/intakeForm/intakeForm.boxes';
import {
  selectIntakeFormSubmission,
  selectIntakeFormSubmissionConflictCustomer,
  selectIntakeFormSubmissionConflictPets,
  selectIntakeFormSubmissionForm,
} from '../../../../store/intakeForm/intakeForm.selectors';
import { PetGender } from '../../../../store/pet/pet.boxes';
import { PetTypeRecord, petTypeMapBox } from '../../../../store/pet/petType.boxes';
import { petVaccineMapBox } from '../../../../store/pet/petVaccine.boxes';
import { getStripeCustomerPaymentMethodList } from '../../../../store/stripe/actions/public/stripe.actions';
import { stripeCustomerPaymentMethodListBox, stripePaymentMethodMapBox } from '../../../../store/stripe/stripe.boxes';
import { ID_ANONYMOUS } from '../../../../store/utils/identifier';
import { getPetAvatarType } from '../../../../utils/BusinessUtil';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { jsonParse } from '../../../../utils/utils';
import { IntakeFormSubmissionMergeModalView } from './IntakeFormSubmissionMergeModal.style';
import { useGetCustomerWording } from '../../../Leads/hooks/useGetCustomerWording';

export interface IntakeFormSubmissionMergeModalProps extends VisibleModalProps {
  className?: string;
  id: number | undefined;
}

type Keys =
  | 'Pet_name'
  | 'Pet_type'
  | 'Pet_breed'
  | 'Pet_image'
  | 'Fixed'
  | 'Hair_length'
  | 'Birthday'
  | 'Gender'
  | 'Vaccine'
  | 'Weight'
  | 'Vet_name'
  | 'Vet_phone_number'
  | 'Emergency_contact'
  | 'Health_issues'
  | 'Behavior'
  | 'Vet_address';

type PetsQuestionKeyMapProps = {
  [T in Keys]: keyof IntakeFormPetModel;
};

type Merge<M, N> = Omit<M, Extract<keyof M, keyof N>> & N;

export type IntakeFormPetModelWithVaccine = Merge<
  IntakeFormPetModel,
  {
    vaccineList: (
      | {
          documentUrls: string[];
          expirationDate: string;
          vaccineBindingId: number;
          vaccineId: number;
          vaccineName: string;
        }
      | {
          vaccineId: number;
          expirationDate: string;
          vaccineDocument: string;
        }
    )[];
  }
>;

const lines = [
  ['First name', 'firstName'] as const,
  ['Last name', 'lastName'] as const,
  ['Phone number', 'phoneNumber'] as const,
  ['Email', 'email'] as const,
] as const;

const PetsQuestionKeyMap: PetsQuestionKeyMapProps = {
  Pet_name: 'petName',
  Pet_type: 'petTypeId',
  Pet_breed: 'breed',
  Pet_image: 'avatarPath',
  Fixed: 'fixed',
  Hair_length: 'hairLength',
  Birthday: 'birthday',
  Gender: 'gender',
  Vaccine: 'vaccineList',
  Weight: 'weight',
  Vet_name: 'vetName',
  Vet_phone_number: 'vetPhone',
  Emergency_contact: 'emergencyContactPhone',
  Health_issues: 'healthIssues',
  Behavior: 'behavior',
  Vet_address: 'vetAddress',
};

export const IntakeFormSubmissionMergeModal = memo<IntakeFormSubmissionMergeModalProps>(
  ({ className, id, onClose }) => {
    const formId = id ?? ID_ANONYMOUS;
    const [
      {
        customer,
        questionList,
        createTime,
        isConflict,
        isPhoneConflict,
        isCardRequired,
        isCardShow,
        newCard,
        existingCustomerId,
        petList,
      },
      business,
      petTypeMap,
      existingCustomerInfo,
      form,
      stripePaymentMethodMap,
      conflictPets,
      vaccineMap,
      currentAccountId,
    ] = useSelector(
      selectIntakeFormSubmission(formId),
      selectCurrentBusiness,
      petTypeMapBox,
      selectIntakeFormSubmissionConflictCustomer(formId),
      selectIntakeFormSubmissionForm(formId),
      stripePaymentMethodMapBox,
      selectIntakeFormSubmissionConflictPets(formId),
      petVaccineMapBox,
      currentAccountIdBox,
    );
    const [methodList] = useSelector(stripeCustomerPaymentMethodListBox.getList(existingCustomerId ?? ID_ANONYMOUS));
    const firstStripeMethod = methodList.size ? stripePaymentMethodMap.mustGetItem(methodList.get(0)!) : void 0;
    const dispatch = useDispatch();
    const getCustomerWording = useGetCustomerWording();
    const handleMerge = useSerialCallback(async () => {
      await dispatch(acceptIntakeFormSubmission(id!, true));
      toastApi.success('The profile is merged successfully!');
      onClose();
    });

    useEffect(() => {
      if (id && existingCustomerId) {
        if (isCardShow) {
          dispatch(getStripeCustomerPaymentMethodList(existingCustomerId));
        }
        if (!isPhoneConflict) {
          dispatch(getCustomerOverview(existingCustomerId));
        }
      }
    }, [id]);

    let key = 0;

    const renderMeta = (
      label: ReactNode,
      value: ReactNode,
      required: boolean,
      className: string = '',
      extraNode: React.ReactNode | null = null,
    ) => {
      return (
        <div className={'meta ' + className} key={key++}>
          <div className="label">{required && typeof label === 'string' ? label + '*' : label}</div>
          <div className="value">
            {value} {extraNode}
          </div>
        </div>
      );
    };

    const getClassNameByCheck = (cur: string | number, next?: string | number) => {
      return cur ? (cur === next ? '' : 'conflict') : next ? 'new' : '';
    };
    const check = (key: (typeof lines)[number][1]) => {
      if (key === 'phoneNumber' && isPhoneConflict) {
        return 'red';
      }
      if (key === 'email' && isConflict && !isPhoneConflict) {
        return 'red';
      }
      const old = existingCustomerInfo?.get(key) ?? '';
      const newly = customer[key];
      return getClassNameByCheck(old, newly);
    };

    const checkSimplePetsValue = (key: (typeof PetsQuestionKeyMap)[Keys], index: number) => {
      if (key === PetsQuestionKeyMap.Vaccine) {
        return 'new';
      }
      const old =
        conflictPets[index]?.get(key as keyof Omit<IntakeFormPetModel, 'petQuestionAnswers' | 'vaccineList'>) ?? '';
      const newly = petList[index][key] as string | number;
      return getClassNameByCheck(old, newly);
    };

    function renderCustomMeta(q: IntakeFormQuestionSnapshotModel, answer: string | undefined) {
      switch (q.questionType) {
        case IntakeFormQuestionType.Checkbox:
          return renderMeta(
            q.question,
            jsonParse(answer, []).map((v, i) => <div key={i}>{v}</div>),
            q.isRequired > 0,
            'custom new',
          );
        default:
          return renderMeta(q.question, answer, q.isRequired > 0, 'custom new');
      }
    }

    const renderInfoTitle = (title: string, showFirst: boolean) => (
      <tr>
        <td>{showFirst && <div className="block">{title}</div>}</td>
        <td>
          <div className="block">{title}</div>
        </td>
      </tr>
    );

    const renderPetInfo = (
      index: number,
      questionInfo: IntakeFormQuestionSnapshotModel,
      petInfo?: IntakeFormPetModelWithVaccine,
    ) => {
      const { isShow, type, key } = questionInfo;
      if (!isShow || type !== IntakeFormQuestionCategory.ForPet || key === 'Vaccine_document' || !petInfo) {
        return null;
      }
      const {
        petTypeId,
        birthday,
        avatarPath,
        gender,
        vaccineList,
        weight,
        emergencyContactName,
        emergencyContactPhone,
      } = petInfo;
      const isRequired = questionInfo.isRequired > 0;

      const attrKey = PetsQuestionKeyMap[key as Keys];
      let valueNode: ReactNode = '';
      switch (key) {
        case 'Pet_name':
        case 'Pet_breed':
        case 'Fixed':
        case 'Hair_length':
        case 'Vet_name':
        case 'Vet_phone_number':
        case 'Health_issues':
        case 'Behavior':
        case 'Vet_address':
          valueNode = petInfo[attrKey as keyof IntakeFormPetModel];
          break;
        case 'Pet_type':
          valueNode = petTypeMap.mustGetItem(PetTypeRecord.ownId(petTypeId, currentAccountId)).typeName;
          break;
        case 'Pet_image':
          valueNode = (
            <Avatar src={avatarPath} type={getPetAvatarType(petTypeId)} size="large" className="remove-border-radius" />
          );
          break;
        case 'Birthday':
          valueNode = business.formatFixedDate(birthday);
          break;
        case 'Gender':
          valueNode = PetGender.mapLabels[gender];
          break;
        case 'Vaccine':
          valueNode = vaccineList?.map((v, i) => (
            <div key={i} className="vaccine-row">
              {`${vaccineMap.mustGetItem(v.vaccineId).name} (${business.formatFixedDate(v.expirationDate)})`}
            </div>
          ));
          break;
        case 'Weight':
          valueNode = `${weight} ${business.unitOfWeight}`;
          break;
        case 'Emergency_contact':
          valueNode =
            emergencyContactName && emergencyContactPhone
              ? `${emergencyContactName} (${emergencyContactPhone})`
              : emergencyContactName || emergencyContactPhone;
          break;
        default:
          return null;
      }
      return renderMeta(key, valueNode, isRequired, checkSimplePetsValue(attrKey, index));
    };

    return (
      <IntakeFormSubmissionMergeModalView
        className={className}
        title={`Merge ${getCustomerWording()} profile`}
        visible={id !== void 0}
        onClose={onClose}
        width="984px"
        height="656px"
        bodyStyle={{ maxHeight: 'calc(100vh - 40px)' }}
        footer={
          <div className="footer">
            <Button btnType="primary" buttonRadius="circle" fill={false} onClick={useLatestCallback(() => onClose())}>
              Cancel
            </Button>
            <Button
              btnType="primary"
              buttonRadius="circle"
              fill={true}
              onClick={handleMerge}
              loading={handleMerge.isBusy()}
              className="ml-24"
            >
              Merge
            </Button>
          </div>
        }
      >
        <div className="tips">
          By merging, conflicts will be overridden and new information will be added to the existing profile.
        </div>
        <div className="tips">
          <span className="indicator conflict" />
          Conflicts
          <span className="indicator new" />
          New Information
        </div>
        <div className="diff">
          <table>
            <tbody>
              <tr>
                <td>
                  <div className="title">Existing {getCustomerWording()} profile</div>
                </td>
                <td>
                  <div className="title">
                    <div>{form.title}</div>
                    <div className="desc">
                      Submitted at {business.formatDateTime((createTime as number) * T_SECOND)}
                    </div>
                  </div>
                </td>
              </tr>
              {renderInfoTitle(`${getCustomerWording({ capitalize: true })} information`, true)}
              {lines.map(([label, key]) => (
                <tr key={key}>
                  <td>
                    {renderMeta(
                      label,
                      key === 'phoneNumber' && isPhoneConflict ? customer[key] : existingCustomerInfo?.get(key),
                      true,
                      check(key),
                    )}
                  </td>
                  <td>{renderMeta(label, customer[key], true, check(key))}</td>
                </tr>
              ))}
              {questionList.map((q) => {
                const isRequired = q.isRequired > 0;
                if (!q.isShow || q.type !== IntakeFormQuestionCategory.ForOwner) {
                  return null;
                }
                switch (q.key) {
                  case 'First_name':
                  case 'Last_name':
                  case 'Email':
                  case 'Phone_number':
                    return null;
                  case 'Address':
                    return printAddress(customer) ? (
                      <tr key={q.key}>
                        <td />
                        <td className="rr-mask">{renderMeta('Address', printAddress(customer), isRequired, 'new')}</td>
                      </tr>
                    ) : null;
                  default:
                    return customer.answersMap?.[q.key] ? (
                      <tr key={q.key}>
                        <td />
                        <td>{renderCustomMeta(q, customer.answersMap?.[q.key])}</td>
                      </tr>
                    ) : null;
                }
              })}
              {isCardShow ? (
                <tr>
                  <td>
                    {renderMeta(
                      'Credit card',
                      firstStripeMethod
                        ? `**** **** **** ${firstStripeMethod.card?.last4}(${firstStripeMethod.card?.brand})`
                        : '',
                      !!isCardRequired,
                    )}
                  </td>
                  <td>
                    {renderMeta(
                      'Credit card',
                      newCard ? `**** **** **** ${newCard.card?.last4}(${newCard.card?.brand})` : '',
                      !!isCardRequired,
                      newCard && firstStripeMethod ? 'card-conflict' : newCard ? 'new' : '',
                      newCard && firstStripeMethod ? (
                        <span style={{ color: '#D0021B' }}>
                          (This card will not be added since there is an existing card on file.)
                        </span>
                      ) : null,
                    )}
                  </td>
                </tr>
              ) : null}
              {petList
                ? petList.map((pet, index) => {
                    return (
                      <Fragment key={index}>
                        {renderInfoTitle('Pet information', !!conflictPets[index])}
                        {questionList.map((q) => {
                          const attrKey = PetsQuestionKeyMap[q.key as Keys];
                          const value = pet[attrKey];
                          // value 没有值时不展示
                          if ((!value && typeof value !== 'number') || (Array.isArray(value) && !value.length)) {
                            return null;
                          }
                          return (
                            <tr key={q.formDetailId}>
                              <td>{renderPetInfo(index, q, conflictPets[index])}</td>
                              <td>{renderPetInfo(index, q, pet as IntakeFormPetModelWithVaccine)}</td>
                            </tr>
                          );
                        })}
                      </Fragment>
                    );
                  })
                : null}
            </tbody>
          </table>
        </div>
      </IntakeFormSubmissionMergeModalView>
    );
  },
);
