import { memo, useMemo } from 'react';
import { ClearlyFilterValues, type SpecificFilterProps } from '../FilterProperty.options';
import { FilterWrapper } from './common/FilterWrapper';
import { useFilterProperty } from '../../hooks/useFilterProperty';
import { getMultiSelectLabel } from '../FilterProperty.utils';
import { CheckboxFilter } from './common/CheckboxFilter';
import { useSelector } from 'amos';
import { leadActionStatusMapBox } from '../../../../../../store/leads/leadActionStatus.boxes';
import { selectLeadActionStatusIdList } from '../../../../../../store/leads/leadActionStatus.selectors';
import React from 'react';

export const LeadActionStatus = memo<SpecificFilterProps>(({ type, property }) => {
  const { filter, handleChange, handleRemove } = useFilterProperty(property);
  const [leadActionStatusList, leadActionStatusMap] = useSelector(
    selectLeadActionStatusIdList(),
    leadActionStatusMapBox,
  );
  const leadActionStatusOptionList = useMemo(() => {
    return leadActionStatusList.toArray().map((id) => ({ label: leadActionStatusMap.mustGetItem(id).name, value: id }));
  }, [leadActionStatusList, leadActionStatusMap]);
  const dropdownValue = getMultiSelectLabel({ options: leadActionStatusOptionList, values: filter.values });

  return (
    <FilterWrapper type={type} property={property} dropdownValue={dropdownValue} onClear={handleRemove}>
      <CheckboxFilter
        options={leadActionStatusOptionList}
        value={ClearlyFilterValues(leadActionStatusOptionList, filter.values)}
        onChange={handleChange}
      />
    </FilterWrapper>
  );
});
