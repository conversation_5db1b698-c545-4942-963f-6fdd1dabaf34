import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Heading, Table, cn } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useContext, useRef } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { AddonItemType, ServiceType, serviceCategoryMapBox } from '../../../../../../../store/service/category.boxes';
import { useVisibleEffect } from '../../../../../../../utils/hooks/hooks';
import { OBEntryContext } from '../../../OnlineBookingEntryContext';
import { useGetServicesColumn } from './hooks/useGetServicesColumn';
import { useAvailableServiceList } from './hooks/useAvailableServiceList';

export interface AvailableServiceTableProps {
  categoryId: number;
  serviceType: number;
  serviceItemType: ServiceItemType;
  isMultiCareType: boolean;
}

export const AvailableServiceTable = memo(function AvailableServiceTable(props: AvailableServiceTableProps) {
  const ref = useRef<HTMLDivElement>(null);
  const newOBEntryContext = useContext(OBEntryContext);
  const { categoryId, serviceType, serviceItemType, isMultiCareType } = props;
  const [category] = useSelector(serviceCategoryMapBox.mustGetItem(categoryId));
  const isGroomingOrAddon =
    (serviceType === ServiceType.Service && serviceItemType === ServiceItemType.GROOMING) ||
    (serviceType === ServiceType.Addon && serviceItemType === AddonItemType.serviceAddon);
  const serviceList = useAvailableServiceList({ serviceType, serviceItemType, categoryId });

  const columns = useGetServicesColumn(serviceType, serviceItemType, categoryId, isMultiCareType);

  const visible = useVisibleEffect(ref, [serviceList]);
  const isGroupClass = serviceItemType === ServiceItemType.GROUP_CLASS;

  return (
    <Condition if={serviceList.length > 0}>
      <div>
        <Condition if={!isGroupClass}>
          <Heading size="4" className="moe-mb-s">
            {category.name || 'Uncategorized'}
          </Heading>
        </Condition>
        <div
          ref={ref}
          className={cn({
            'moe-h-[50px]': !visible,
            'moe-w-[1370px]': isGroomingOrAddon,
          })}
        >
          {/* 这里为了性能，不用 Condition */}
          {visible ? (
            <Table
              classNames={{
                headWrapper: 'moe-top-[-32px]',
                headCell: 'moe-pl-0',
                bodyCell: 'group-[[data-hovered]]/body-row:moe-bg-neutral-default moe-pl-0',
                bodySelectionCell: 'group-[[data-hovered]]/body-row:moe-bg-neutral-default',
                bodyExpansionCell: 'group-[[data-hovered]]/body-row:moe-bg-neutral-default',
              }}
              data={serviceList}
              columns={columns}
              getRowId={(row) => `${row.serviceId}`}
              stickyContainer={newOBEntryContext.scrollContainer ?? undefined}
            />
          ) : null}
        </div>
      </div>
    </Condition>
  );
});
