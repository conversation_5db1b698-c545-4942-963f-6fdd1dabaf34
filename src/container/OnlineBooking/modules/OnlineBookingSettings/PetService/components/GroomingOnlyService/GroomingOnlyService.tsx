import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Tabs } from '@moego/ui';
import React, { memo, useMemo } from 'react';
import { AvailableServiceList } from '../AvailableServiceTable/AvailableServiceList';

export const GroomingOnlyService = memo(() => {
  const defaultSelectedTab = `${ServiceType.SERVICE}-${ServiceItemType.GROOMING}`;

  const tabList = useMemo(() => {
    return [
      {
        key: `${ServiceType.SERVICE}-${ServiceItemType.GROOMING}`,
        serviceType: ServiceType.SERVICE,
        serviceItemType: ServiceItemType.GROOMING,
        label: 'Services',
      },
      {
        key: `${ServiceType.ADDON}-${ServiceItemType.GROOMING}`,
        serviceType: ServiceType.ADDON,
        serviceItemType: ServiceItemType.GROOMING,
        label: 'Add-ons',
      },
    ];
  }, []);

  return (
    <Tabs defaultSelectedKey={defaultSelectedTab} className="moe-w-[1394px] moe-pr-m">
      {tabList.map((tab) => {
        return (
          <Tabs.Item key={tab.key} label={tab.label}>
            <AvailableServiceList
              serviceType={tab.serviceType}
              serviceItemType={tab.serviceItemType}
              isMultiCareType={false}
            />
          </Tabs.Item>
        );
      })}
    </Tabs>
  );
});
