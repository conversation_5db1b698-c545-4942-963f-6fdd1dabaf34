import { useSelector } from 'amos';
import { useCallback, useMemo } from 'react';
import ImageDrivingTimeUpgrade1Png from '../../assets/image/driving-time-upgrade-1.png';
import ImageDrivingTimeUpgrade2Png from '../../assets/image/driving-time-upgrade-2.png';
import ImageMapViewV2Upgrade1Png from '../../assets/image/map-view-v2-upgrade-1.png';
import ImageMapViewV2Upgrade2Png from '../../assets/image/map-view-v2-upgrade-2.png';
import ImageMapViewV2Upgrade3Png from '../../assets/image/map-view-v2-upgrade-3.png';
import ImageServiceAreaUpgrade1Png from '../../assets/image/service-area-upgrade-1.png';
import ImageServiceAreaUpgrade2Png from '../../assets/image/service-area-upgrade-2.png';
import ImageServiceAreaUpgrade3Png from '../../assets/image/service-area-upgrade-3.png';
import IconOverrideMobile from '../../assets/image/staff-schedule-date-override-mobile.png';
import IconOverride from '../../assets/image/staff-schedule-date-override.png';
import IconRotatingMobile from '../../assets/image/staff-schedule-rotating-mobile.png';
import IconRotating from '../../assets/image/staff-schedule-rotating.png';
import IconBookBySlot from '../../assets/image/staff-schedule-by-slot.png';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { type PricingPermissionKey } from '../../store/company/company.boxes';
import { type ImageCarouseProps } from '../ImageCarouse/ImageCarouse';

type UpgradeGuideConfig = {
  modalConfig?: {
    carouselContainerStyle?: React.CSSProperties | Record<string, number | string>;
  };
  imgConfigList: ImageCarouseProps['imgConfigList'];
};

const GeneralUpgradeGuideConfig: UpgradeGuideConfig = {
  imgConfigList: [],
};

const SalonStaffManagement: ImageCarouseProps['imgConfigList'] = [
  {
    imgUrl: IconOverride,
    title: 'Quick date override',
    description:
      'Set adjusted availability for specific dates that can override your regular hours. Enjoy greater flexibility and higher productivity!',
    descriptionStyle: { width: 400 },
  },
  {
    imgUrl: IconRotating,
    title: 'Build rotating schedule',
    description:
      'Build a rotating schedule that automatically alternate for desired frequency and reflected real-time in online booking.',
    descriptionStyle: { width: 400 },
  },
  {
    imgUrl: IconBookBySlot,
    title: 'Book by slot',
    description:
      'Slot-based booking lets staff manage their work freely within time blocks, boosting store flexibility and efficiency.',
    descriptionStyle: { width: 400 },
  },
];

const MobileStaffManagement: ImageCarouseProps['imgConfigList'] = [
  {
    imgUrl: IconOverrideMobile,
    title: 'Quick date override',
    description:
      'Set adjusted availability for specific dates that can override your regular hours. Enjoy greater flexibility and higher productivity!',
    descriptionStyle: { width: 400 },
  },
  {
    imgUrl: IconRotatingMobile,
    title: 'Build rotating schedule',
    description:
      'Build a rotating schedule that automatically alternate for desired frequency and reflected real-time in online booking.',
    descriptionStyle: { width: 400 },
  },
];

const useUpgradeGuideConfigMap = () => {
  const [business] = useSelector(selectCurrentBusiness);
  const rotatingScheduleConfig = useMemo<UpgradeGuideConfig>(() => {
    const imgConfigList = business.isMobileGrooming() ? MobileStaffManagement : SalonStaffManagement;
    return {
      imgConfigList,
    };
  }, [business]);

  return useMemo<{
    [K in PricingPermissionKey]?: UpgradeGuideConfig;
  }>(
    () => ({
      displayDrivingInfo: {
        modalConfig: {
          carouselContainerStyle: {
            '--img-carouse-height': '445px',
          },
        },
        imgConfigList: [
          {
            imgUrl: ImageDrivingTimeUpgrade1Png,
            title: 'Display driving time and distance',
            titleStyle: { marginTop: 30 },
            description:
              'View driving time and distance between appointments to enhance flexibility in scheduling and planning.',
            descriptionStyle: { width: 400 },
          },
          {
            imgUrl: ImageDrivingTimeUpgrade2Png,
            title: 'Display driving time and distance',
            titleStyle: { marginTop: 30 },
            description:
              'View driving time and distance between appointments to enhance flexibility in scheduling and planning.',
            descriptionStyle: { width: 400 },
          },
        ],
      },
      serviceArea: {
        imgConfigList: [
          {
            imgUrl: ImageServiceAreaUpgrade1Png,
            title: 'Overall service area solution',
            description: 'Set up your service area accurately and flexibly by drawing or simply entering zipcodes.',
            descriptionStyle: { width: 400 },
          },
          {
            imgUrl: ImageServiceAreaUpgrade2Png,
            title: 'Overall service area solution',
            description: 'Assign staff to certain areas recurringly and combine with staff shifts seamlessly.',
            descriptionStyle: { width: 400 },
          },
          {
            imgUrl: ImageServiceAreaUpgrade3Png,
            title: 'Overall service area solution',
            description: 'Visualize client request location on map and check sales report by service area.',
            descriptionStyle: { width: 400 },
          },
        ],
      },
      rotatingSchedule: rotatingScheduleConfig,
      bookBySlot: {
        imgConfigList: SalonStaffManagement,
      },
      smartWaitList: {
        modalConfig: {
          carouselContainerStyle: {
            '--img-carouse-height': '430px',
          },
        },
        imgConfigList: [
          {
            imgUrl: 'https://dcgll7qxxap6x.cloudfront.net/p/0/2024/1/6f7ef05f-8251-40d9-af6a-fab897b6b70b.gif',
            title: 'Intelligent waitlist',
            description:
              'Upgrade to clear up your waitlist and fill up last minute cancellation faster with MoeGo intelligent waitlist',
            descriptionStyle: { width: 400 },
          },
        ],
      },
      mapViewV2: {
        imgConfigList: [
          {
            imgUrl: ImageMapViewV2Upgrade1Png,
            title: 'New map view is here!',
            description:
              'Effortlessly view staff shifts and routes on specific days with color codes and service area overlays.',
            descriptionStyle: { width: 400 },
            bgBoxStyle: {
              backgroundPosition: 'bottom 28px center',
            },
          },
          {
            imgUrl: ImageMapViewV2Upgrade2Png,
            title: 'New map view is here!',
            description: 'Easily view client addresses and staff routes to enhance planning and scheduling efficiency.',
            descriptionStyle: { width: 400 },
            bgBoxStyle: {
              backgroundPosition: 'bottom 28px center',
            },
          },
          {
            imgUrl: ImageMapViewV2Upgrade3Png,
            title: 'New map view is here!',
            description:
              "Track your staff's real-time locations and routes on the map for enhanced management and efficiency.",
            descriptionStyle: { width: 400 },
            bgBoxStyle: {
              backgroundPosition: 'bottom 28px center',
            },
          },
        ],
      },
    }),
    [rotatingScheduleConfig],
  );
};

export const useGetUpgradeGuideConfig = () => {
  const upgradeGuideConfigMap = useUpgradeGuideConfigMap();
  return useCallback(
    (key: PricingPermissionKey) => upgradeGuideConfigMap[key] || GeneralUpgradeGuideConfig,
    [upgradeGuideConfigMap],
  );
};
