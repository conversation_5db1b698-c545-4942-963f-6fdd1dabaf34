import { OrderSourceType } from '@moego/api-web/moego/models/order/v1/order_enums';
import { MajorMoreOutlined } from '@moego/icons-react';
import { Button, Dropdown, IconButton } from '@moego/ui';
import React, { useMemo } from 'react';
import { type GroomingTicketRecord } from '../../../../../../store/grooming/grooming.boxes';
import { useViewEstimateOrInvoiceByApptId } from '../../../../../PaymentFlow/ViewInvoiceDrawer/ViewInvoiceDrawer';
import { useViewOrder } from '../../../../../PaymentFlow/ViewOrderDrawer/useViewOrder';
import { checkIsEstimate } from '../../../../../PaymentFlow/ViewInvoiceDrawer/utils';
import { useSerialCallback } from '@moego/tools';

enum Actions {
  Receipt = 'receipt',
}

interface HistoryActionsProps {
  record: GroomingTicketRecord;
  refreshTableList: () => void;
}

export const HistoryActions = (props: HistoryActionsProps) => {
  const { record, refreshTableList } = props;
  const openViewEstimateOrInvoice = useViewEstimateOrInvoiceByApptId();
  const handleViewEstimateOrInvoice = useSerialCallback(() => {
    return openViewEstimateOrInvoice({
      apptId: record.id.toString(),
    });
  });

  const actionItems = useMemo(() => {
    return [record.orderId ? <Dropdown.Item key={Actions.Receipt}>View receipt(s)</Dropdown.Item> : null].filter(
      Boolean,
    ) as React.ReactElement[];
  }, [record.orderId]);
  const { openViewOrderDrawer } = useViewOrder();

  return (
    <div className="moe-flex moe-gap-s">
      <Button
        variant="tertiary"
        size="s"
        onPress={handleViewEstimateOrInvoice}
        isLoading={handleViewEstimateOrInvoice.isBusy()}
      >
        {checkIsEstimate(record.appointmentStatus) ? 'View estimate' : 'View invoice'}
      </Button>
      <div onClick={(e) => e.stopPropagation()}>
        <Dropdown>
          <Dropdown.Trigger>
            <IconButton variant="primary" icon={<MajorMoreOutlined />} color="transparent" />
          </Dropdown.Trigger>
          <Dropdown.Menu
            onAction={(action) => {
              if (action === Actions.Receipt) {
                openViewOrderDrawer({
                  sourceId: String(record.id),
                  sourceType: OrderSourceType.APPOINTMENT,
                  businessId: String(record.businessId),
                  module: 'grooming',
                  onClose: () => {
                    refreshTableList();
                  },
                });
              }
            }}
          >
            {actionItems}
          </Dropdown.Menu>
        </Dropdown>
      </div>
    </div>
  );
};
