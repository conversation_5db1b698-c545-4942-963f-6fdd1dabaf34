import { Heading, Markup, Tag, Text, cn } from '@moego/ui';
import React, { type ReactNode } from 'react';
import ImageMoegoPay2Png from '../../../assets/image/moego-pay-2.png';
import ImageSetuppageBg1Png from '../../../assets/image/setuppage-bg-1.png';
import ImageSetuppageBg2Png from '../../../assets/image/setuppage-bg-2.png';
import SvgIconCheckFilled2Svg from '../../../assets/svg/icon-check-filled-2.svg';
import SvgIconContactSvg from '../../../assets/svg/icon-contact.svg';
import SvgIconCreditCard2Svg from '../../../assets/svg/icon-credit-card-2.svg';
import SvgIconWalletSvg from '../../../assets/svg/icon-wallet.svg';
import { ImgIcon, SvgIcon } from '../../../components/Icon/Icon';
import { WithPricingEnableUpgrade } from '../../../components/Pricing/WithPricingComponents';
import { PRE_AUTH_FEATURE_NAME } from '../../../store/stripe/preAuth.boxes';
import { c_secondary } from '../../../style/_variables';
import { HintForMoeGoPaySetup } from '../../Calendar/Grooming/PreAuthForAppt/Onboarding/HintForMoeGoPaySetup';
import { getPurchaseHardwareLink } from '../../CardProcessing/CardProcessing.utils';
import { useSelector } from 'amos';
import { selectCurrentStaff } from '../../../store/staff/staff.selectors';

interface Props {
  className?: string;
  handleSetupMoeGoPay: () => void;
}

export const SetupPage = ({ className, handleSetupMoeGoPay }: Props) => {
  const [staff] = useSelector(selectCurrentStaff());
  const tagList = [
    {
      icon: SvgIconWalletSvg,
      text: 'All-in-one',
      bgcolor: '#fff7e8',
    },
    {
      icon: SvgIconCreditCard2Svg,
      text: 'Faster checkout',
      bgcolor: '#fff7e8',
    },
    {
      icon: SvgIconContactSvg,
      text: '24/7 support',
      bgcolor: '#FFF7F0',
    },
  ];

  const featureList: Array<{ name: string; desc: ReactNode }> = [
    {
      name: 'pos',
      desc: 'POS for salon and mobile',
    },
    {
      name: 'reduceLoss',
      desc: 'Reduce loss with Prepayment and Deposit',
    },
    {
      name: 'processingFee',
      desc: 'Processing fee paid by clients',
    },
    {
      name: 'boostTips',
      desc: 'Boost tips with Smart Tip',
    },
    {
      name: 'preAuth',
      desc: (
        <HintForMoeGoPaySetup showIgnore={false}>
          Secure your revenue with {PRE_AUTH_FEATURE_NAME}
          <Tag
            className="moe-mb-[2px] moe-ml-xs moe-rounded-[38px]"
            label="New"
            variant="filled"
            classNames={{
              label: 'moe-text-icon-brand moe-font-medium moe-cursor-pointer',
              base: 'moe-bg-brand-subtle',
            }}
          />
        </HintForMoeGoPaySetup>
      ),
    },
  ];

  return (
    <div
      className={cn(
        'moe-relative moe-flex moe-justify-start moe-px-[60px] -moe-mx-[20px] -moe-mb-[20px] moe-rounded-[8px] moe-flex-1 moe-bg-no-repeat moe-bg-cover moe-min-h-[708px]',
        className,
      )}
      style={{
        backgroundImage: `url(https://dcgll7qxxap6x.cloudfront.net/u/0/2024/11/82c96b33-8ff2-403b-bfb9-4a20ab4cba0f.png)`,
      }}
    >
      <div className="moe-flex moe-max-w-[510px] moe-py-[60px] moe-z-20">
        <div className="moe-flex-1">
          <img src={ImageMoegoPay2Png} className="!moe-w-[160px] moe-mb-[12px]" />
          <Heading size={2}>Trusted digital payment service for pet industry</Heading>
          <div className="moe-mt-xl moe-flex moe-gap-[12px]">
            {tagList.map((tag, i, arr) => (
              <div
                className="moe-flex moe-items-center moe-justify-center moe-gap-xxs moe-px-[12px] moe-py-[8px] moe-rounded-full moe-bg-neutral-default"
                key={tag.text}
              >
                <SvgIcon src={tag.icon} size={20} />
                <Markup variant="regular" className="moe-text-[14px]">
                  {tag.text}
                </Markup>
              </div>
            ))}
          </div>

          <div className="moe-mt-s">
            <Text variant="regular" className="moe-leading-m moe-font-bold moe-tracking-[0.16px]">
              Convenient payment experience to boost revenue and reduce loss.
            </Text>
            {featureList.map((feature, i) => (
              <div
                key={feature.name}
                className={`moe-flex moe-items-center ${i === 0 ? 'moe-mt-s' : 'moe-mt-[8px]'} moe-gap-xs`}
              >
                <SvgIcon src={SvgIconCheckFilled2Svg} color={c_secondary} size={16} />
                <Text variant="regular" className="moe-leading-m moe-tracking-[0.16px]">
                  {feature.desc}
                </Text>
              </div>
            ))}
          </div>
          <div className="moe-mt-xl moe-flex">
            <WithPricingEnableUpgrade permission="stripe">
              <span
                onClick={handleSetupMoeGoPay}
                className="!moe-bg-brand-bold hover:!moe-bg-brand-bold-hover !moe-py-[8px] !moe-px-[18px] !moe-text-white !moe-font-bold !moe-text-[16px] !moe-rounded-full !moe-cursor-pointer !moe-leading-[20px] !moe-flex !moe-items-center !moe-h-[40px]"
              >
                Get started
              </span>
            </WithPricingEnableUpgrade>
            <a
              href={getPurchaseHardwareLink(staff.isEnterpriseRelatedStaff())}
              target="_blank"
              className="!moe-ml-[16px] !moe-py-[8px] !moe-px-[18px] !moe-border-[#CDCDCD] !moe-border !moe-border-solid !moe-rounded-full !moe-text-[16px] !moe-font-bold !moe-text-[#333] hover:!moe-text-[#333] !moe-flex !moe-items-center hover:!moe-bg-[#F2F3F7] !moe-h-[40px]"
              rel="noreferrer"
            >
              Purchase hardware
            </a>
          </div>
        </div>
      </div>

      <ImgIcon
        src={ImageSetuppageBg1Png}
        width={578}
        className="moe-absolute moe-left-[739px] moe-top-[107px] moe-max-h-[600px] moe-z-10"
      />
      <ImgIcon
        src={ImageSetuppageBg2Png}
        width={453}
        className="moe-absolute moe-left-[314px] moe-top-[63px] moe-z-0"
      />
    </div>
  );
};
