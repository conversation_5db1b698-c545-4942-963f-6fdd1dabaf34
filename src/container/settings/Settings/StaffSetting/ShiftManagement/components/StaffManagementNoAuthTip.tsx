import React from 'react';
import { useUpgradeGuideModal } from '../../../../../../components/UpgradeGuideModal/useUpgradeGuideModal';
import { MajorCrown } from '@moego/icons-react';
import { <PERSON><PERSON>, Button } from '@moego/ui';
import type { EnumValues } from '../../../../../../store/utils/createEnum';
import { AvailabilityType } from '../../../../../../store/staffSchedule/staffSchedule.types';
import { usePricingPermissionAccess } from '../../../../../../components/Pricing/pricing.hooks';

export interface StaffManagementNoAuthTipProps {
  availabilityType: EnumValues<typeof AvailabilityType>;
}

export const StaffManagementNoAuthTip = ({ availabilityType }: StaffManagementNoAuthTipProps) => {
  const permissionKey = availabilityType === AvailabilityType.BY_TIME ? 'rotatingSchedule' : 'bookBySlot';
  const { access: hasAuth } = usePricingPermissionAccess(permissionKey);
  const { renderUpgradeGuideModal, openUpgradeGuideModal } = useUpgradeGuideModal({
    permissionKey,
  });

  return !hasAuth ? (
    <>
      <Alert
        isRounded
        isCloseable={false}
        classNames={{
          base: 'moe-mb-m moe-bg-brand-subtle',
          content: 'moe-items-center',
          innerContent: 'moe-w-full',
        }}
        icon={<MajorCrown />}
        description={
          <div className="moe-flex moe-items-center moe-justify-between">
            {availabilityType === AvailabilityType.BY_TIME
              ? 'Unlock hassle-free scheduling experience with new shift management solution.'
              : 'Upgrade to unlock powerful features for slot setting!'}

            <Button onPress={openUpgradeGuideModal} size="s" variant="primary">
              Upgrade
            </Button>
          </div>
        }
      />
      {renderUpgradeGuideModal()}
    </>
  ) : null;
};
