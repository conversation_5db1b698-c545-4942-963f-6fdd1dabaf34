import { useNextZIndex } from '@moego/ui';
import { useSelector } from 'amos';
import React, { type CSSProperties, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { type PricingPermissionKey } from '../../store/company/company.boxes';
import { selectPricingPermission } from '../../store/company/company.selectors';
import { useBool } from '../../utils/hooks/useBool';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { usePortalElement } from '../../utils/hooks/usePortalElement';
import { UpgradeGuideModal } from './UpgradeGuideModal';
import { useGetUpgradeGuideConfig } from './useUpgradeGuideModal.config';

export interface UseUpgradeGuideModalProps {
  permissionKey: PricingPermissionKey;
  container?: HTMLDivElement;
}

/**
 * 支持轮播图的 Upgrade 弹窗
 * 可根据 permissionKey，生成对应带轮播图配置的 UpgradeGuideModal 组件，以及触发函数
 */
export const useUpgradeGuideModal = (props: UseUpgradeGuideModalProps) => {
  const { permissionKey, container } = props;
  const [pricingPermission] = useSelector(selectPricingPermission);
  const hasPermission = pricingPermission.enable.has(permissionKey);
  const showUpgradeGuideModal = useBool();
  const zIndex = useNextZIndex();

  const containerRef = usePortalElement();
  const getUpgradeGuideConfig = useGetUpgradeGuideConfig();
  const { imgConfigList, modalConfig } = useMemo(
    () => getUpgradeGuideConfig(permissionKey),
    [getUpgradeGuideConfig, permissionKey],
  );

  const renderUpgradeGuideModal = useLatestCallback((props?: { style?: CSSProperties }) => {
    const { style } = props ?? {};
    return createPortal(
      <UpgradeGuideModal
        permissionKey={permissionKey}
        imgConfigList={imgConfigList}
        visible={showUpgradeGuideModal.value}
        onVisibleChange={(v) => {
          showUpgradeGuideModal.as(v);
        }}
        style={{ zIndex, ...style }}
        {...modalConfig}
      />,
      container ?? containerRef.current,
    );
  });

  const withPermissionCheck = useLatestCallback(<T extends any[]>(cb: (...params: T) => void) => {
    return (...params: T) => {
      if (!pricingPermission.enable.has(permissionKey)) {
        showUpgradeGuideModal.open();
        return;
      }
      cb(...params);
    };
  });

  return {
    openUpgradeGuideModal: showUpgradeGuideModal.open,
    renderUpgradeGuideModal,
    withPermissionCheck,
    hasPermission,
  };
};
