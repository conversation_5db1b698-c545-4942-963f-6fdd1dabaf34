import { Alert, AlertDialog, Heading, Text } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { useState } from 'react';
import { currentBusinessIdBox } from '../../../../../../../../../store/business/business.boxes';
import { getClosedDateList } from '../../../../../../../../../store/business/closedDate.actions';
import { getHolidays } from '../../../../../../../../../store/business/holiday.actions';
import { AvailableTimeTypeKind } from '../../../../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import {
  selectDraftAvailabilitySettings,
  selectRawAvailabilitySettings,
} from '../../../../../../../../../store/onlineBooking/settings/availabilityGrooming.selectors';
import { useBizWorkingHour } from '../../../../../../../../settings/Settings/StaffSetting/ShiftManagement/hooks/useBizOpeningHour';
import { AvailableTimeForm } from './AvailableStaffTimeForm';
import { StaffShiftSync } from './StaffShiftSync';
import {
  AvailableByWhichType,
  BY_SLOT_RESET_KEY,
  BY_WORKING_HOUR_RESET_KEY,
  GENERAL_RESET_KEY,
  SMAvailabilityTypeToOBAvailabilityType,
} from '../AvailableByWhichType';
import { syncWithStaffRegularWorkingHour } from '../../../../../../../../../store/onlineBooking/settings/availabilityGrooming.boxes';
import { TimePreference } from '../TimePreference/TimePreference';
import type { EnumValues } from '../../../../../../../../../store/utils/createEnum';
import {
  getSyncStaffAvailabilityState,
  resetAvailabilityDraftSettingsProperty,
  setAvailabilityDraftSettings,
  syncStaffAvailabilityState,
} from '../../../../../../../../../store/onlineBooking/actions/private/availabilityGrooming.actions';
import { StaffClient } from '../../../../../../../../../middleware/clients';
import { AvailabilityType } from '../../../../../../../../../store/staffSchedule/staffSchedule.types';
import { useSerialCallback } from '../../../../../../../../../utils/hooks/useSerialCallback';
import { StaffListBox } from './StaffListBox';
import { StaffScheduleDrawerProvider } from '../../../../../../../../settings/Settings/StaffSetting/ShiftManagement/components/StaffSchedule/StaffScheduleDrawerContext';
import { useBizIdReadyEffect } from '../../../../../../../../../utils/hooks/useBizIdReadyEffect';
import { useEnableMultiPetBySlotFeature } from '../../../../../../../../Calendar/latest/components/SlotCalendar/hooks/useSlotCalendarFeature';

export const TeamSchedule = () => {
  const dispatch = useDispatch();
  const [rawSettings, draftSettings, currentBusinessId, sync] = useSelector(
    selectRawAvailabilitySettings(),
    selectDraftAvailabilitySettings(),
    currentBusinessIdBox,
    syncWithStaffRegularWorkingHour,
  );
  const isEnableOBBySlot = useEnableMultiPetBySlotFeature();

  const [availabilityType, setAvailabilityType] = useState<EnumValues<typeof AvailabilityType>>(
    AvailabilityType.BY_TIME,
  );
  const { isDisableSelectTime = false } = draftSettings;
  const isOBSettingTypeDifferent = draftSettings.availableTimeType + 1 !== availabilityType;

  const onSyncChange = useSerialCallback(async (nextSync: boolean) => {
    return new Promise<void>((resolve) => {
      if (nextSync) {
        const alertContent = isOBSettingTypeDifferent ? (
          <Text variant="small">{`The availability type for this salon is set to 
            ‘${AvailabilityType.mapLabels[availabilityType]}’ in Staff > Shift Management. Enabling Sync will also change the availability type in Online Booking to 
            ‘${AvailabilityType.mapLabels[availabilityType]}’ with the same availability set up.`}</Text>
        ) : (
          <Text variant="small">
            <Text variant="small">
              Enabling <span className="moe-font-bold">Sync</span> will update Online Booking availabilities to the same
              with Shift Management set up.
            </Text>
          </Text>
        );

        AlertDialog.open({
          title: 'Staff shift sync',
          content: alertContent,
          onConfirm: async () => {
            const availabilityType = await getSMAvailabilityTypeAsync();
            const obAvailabilityType = SMAvailabilityTypeToOBAvailabilityType(availabilityType);
            triggerOBAvailabilityTypeChange(obAvailabilityType);
            await dispatch(syncStaffAvailabilityState());
            resolve();
          },
          onClose: () => {
            resolve();
          },
        });
      } else {
        // 关闭 sync，回退到之前的状态
        triggerOBAvailabilityTypeChange(rawSettings.availableTimeType);
        dispatch(syncStaffAvailabilityState()).then(resolve);
      }
    });
  });

  // book by slot 开白用户可以同步 slot 配置，非开白用户不行
  const getWhitelistSyncRenderSetting = () => {
    if (isEnableOBBySlot) {
      const alertVisible = !sync && isOBSettingTypeDifferent;

      return {
        showSyncSwitcher: true,
        showSyncAlert: alertVisible,
        showSyncAlertInForm: sync,
        isSwitchTypeDisabled: sync,
        isFormDisabled: sync,
      };
    } else {
      const isWorkingHour = draftSettings.availableTimeType === AvailableTimeTypeKind.ByWorkingHour;

      return {
        showSyncSwitcher: isWorkingHour,
        showSyncAlert: false,
        showSyncAlertInForm: false,
        isSwitchTypeDisabled: false,
        isFormDisabled: sync && isWorkingHour,
      };
    }
  };
  const { showSyncSwitcher, showSyncAlert, showSyncAlertInForm, isSwitchTypeDisabled, isFormDisabled } =
    getWhitelistSyncRenderSetting();

  const triggerOBAvailabilityTypeChange = (obAvailabilityType: EnumValues<typeof AvailableTimeTypeKind>) => {
    /**
     * 切换 availability type 的时候需要重置之前下方的草稿态
     * 1. by working hour 下，重置 by slot 的草稿态
     * 2. by slot 下，重置 by working hour 的草稿态
     * 3. disable time selection 下，重置 by working hour 和 by slot 的草稿态
     */

    const otherResetKeys =
      obAvailabilityType === AvailableTimeTypeKind.ByWorkingHour
        ? BY_WORKING_HOUR_RESET_KEY
        : obAvailabilityType === AvailableTimeTypeKind.BySlot
          ? BY_SLOT_RESET_KEY
          : [...BY_WORKING_HOUR_RESET_KEY, ...BY_SLOT_RESET_KEY];

    dispatch([
      resetAvailabilityDraftSettingsProperty(...GENERAL_RESET_KEY, ...otherResetKeys),
      setAvailabilityDraftSettings({ availableTimeType: obAvailabilityType }),
    ]);
  };

  const getSMAvailabilityTypeAsync = async () => {
    const { availabilityType } = await StaffClient.getBusinessStaffAvailabilityType({
      businessId: `${currentBusinessId}`,
    });

    setAvailabilityType(availabilityType);
    return availabilityType;
  };

  useBizWorkingHour();

  useBizIdReadyEffect(() => {
    Promise.all([
      getSMAvailabilityTypeAsync(),
      dispatch([getClosedDateList(currentBusinessId), getHolidays(currentBusinessId), getSyncStaffAvailabilityState()]),
    ]);
  }, []);

  return (
    <div className="moe-flex moe-flex-col">
      <Heading size="4">Team schedule</Heading>
      {showSyncSwitcher && (
        <StaffShiftSync isSync={sync} onSyncChange={onSyncChange} isLoading={onSyncChange.isBusy()} />
      )}

      <AvailableByWhichType
        className="moe-mt-m"
        isDisabled={isSwitchTypeDisabled}
        availableType={draftSettings.availableTimeType}
        onAvailabilityTypeChange={triggerOBAvailabilityTypeChange}
      />

      {!isDisableSelectTime && (
        <>
          {showSyncAlert && (
            <Alert
              isRounded
              className="moe-mt-[12px]"
              color="warning"
              description={
                <>
                  The availability type in <span className="moe-font-bold">{'Staff > Shift management'}</span> is
                  different, please turn on sync to maintain the same type.
                </>
              }
            />
          )}

          <StaffScheduleDrawerProvider>
            <StaffListBox>
              {(currentStaffId) => (
                <AvailableTimeForm
                  className="moe-p-[20px]"
                  staffId={currentStaffId}
                  isDisabled={isFormDisabled}
                  showAlert={showSyncAlertInForm}
                />
              )}
            </StaffListBox>
          </StaffScheduleDrawerProvider>
          <TimePreference className="moe-mt-[40px]" />
        </>
      )}
    </div>
  );
};
