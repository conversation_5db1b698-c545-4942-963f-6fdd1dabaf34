import { FeedingMedicationScheduleDateType } from '@moego/api-web/moego/models/business_customer/v1/business_pet_feeding_medication_enum';
import { SelectFeedingMedicationDateTypeEnum } from '../../DateType/DateType.utils';

export interface InstructionOption {
  times: string;
  amount?: string;
  unit?: string;
  type?: string;
  source?: string;
  instruction?: string;
  note?: string;
  dateType?: FeedingMedicationScheduleDateType;
  specificDates?: string[];
}

export const formatFeedingMedicationDate = (
  dateType: FeedingMedicationScheduleDateType = FeedingMedicationScheduleDateType.UNSPECIFIED,
  specificDates?: string[],
) => {
  if (dateType === FeedingMedicationScheduleDateType.SPECIFIC_DATE) {
    return specificDates?.join(', ') || '';
  }
  return dateType ? (SelectFeedingMedicationDateTypeEnum.mapLabels[dateType] ?? '') : '';
};

export const beautifyInstructions = (instructionOption: InstructionOption, { appendPeriod = true } = {}) => {
  const { times, amount, unit, type, source, instruction, note, dateType, specificDates } = instructionOption;

  const date = formatFeedingMedicationDate(dateType, specificDates);
  const dateTime = [date, times].filter(Boolean).join(' ');
  const amountUnit = [amount, unit].filter(Boolean).join(' ');
  const dateTimeAndAmountUnit = [dateTime, amountUnit].filter(Boolean).join(': ');
  const typeSourceInstruction = [type, source, instruction, note].filter(Boolean).join(', ');

  let result = [dateTimeAndAmountUnit, typeSourceInstruction].filter(Boolean).join(', ');

  if (appendPeriod) {
    result += '.';
  }

  return result;
};
