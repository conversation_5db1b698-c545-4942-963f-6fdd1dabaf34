import classNames from 'classnames';
import { isNil } from 'lodash';
import { type RuleRender } from 'rc-field-form/lib/interface';
import React, { memo, type ReactNode } from 'react';
import SvgIconEditNewSvg from '../../assets/svg/icon-edit-new.svg';
import { StyledCheckbox } from '../Checkbox/Checkbox.style';
import { SvgIcon } from '../Icon/Icon';
import { PanelCheckboxView } from './PanelCheckbox.style';
import { StyledFormItem } from './VerticalInputItem';

export interface InputPanelProps {
  panelTitle?: ReactNode;
  placeholder?: string;
  onClickPanel?: () => void;
  className?: string;
}

export const validateInputPanel = (checkboxFormItemName: string, errorMsg: string): RuleRender => {
  return ({ getFieldValue }) => ({
    validator(_, inputValue) {
      const isInvalidValue = isNil(inputValue) || (Array.isArray(inputValue) && inputValue.length === 0);
      if (getFieldValue(checkboxFormItemName) && isInvalidValue) {
        return Promise.reject(new Error(errorMsg));
      }
      return Promise.resolve();
    },
  });
};

export const InputPanel = memo<InputPanelProps>(({ panelTitle, placeholder, onClickPanel, className }) => {
  return (
    <div
      className={classNames(
        'moe-flex-1 moe-flex moe-items-center moe-justify-between moe-h-[32px] moe-mt-[16px] moe-px-[12px] moe-rounded-[8px] moe-border moe-border-solid moe-border-[#dee1e5] moe-cursor-pointer hover:moe-border-dark moe-text-sm moe-font-medium',
        className,
      )}
      onClick={onClickPanel}
    >
      <span className={panelTitle ? 'moe-text-[#333]' : 'moe-text-[#ccc]'}>{panelTitle || placeholder}</span>
      <SvgIcon src={SvgIconEditNewSvg} color="var(--moe-color-icon-brand)" size={16} />
    </div>
  );
});

export interface PanelCheckboxProps extends InputPanelProps {
  className?: string;
  checkboxFormItemName?: string;
  panelFormItemName?: string;
  title: ReactNode;
  onCheck?: (checked: boolean) => void;
}

export const PanelCheckbox = memo<PanelCheckboxProps>(
  ({ className = '', checkboxFormItemName, title, panelTitle, panelFormItemName, onClickPanel, onCheck }) => {
    return (
      <PanelCheckboxView className={className}>
        <StyledFormItem name={checkboxFormItemName} valuePropName="checked" className="moe-min-h-[18px]">
          <StyledCheckbox className="moe-font-bold" onChange={(e) => onCheck?.(e.target.checked)}>
            {title}
          </StyledCheckbox>
        </StyledFormItem>
        <StyledFormItem name={panelFormItemName}>
          <InputPanel panelTitle={panelTitle} onClickPanel={onClickPanel} />
        </StyledFormItem>
      </PanelCheckboxView>
    );
  },
);
