/*
 * @since 2020-09-07 12:40:57
 * <AUTHOR> <<EMAIL>>
 */

import { cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { Tooltip as AntTooltip, Dropdown, Popconfirm } from 'antd';
import dayjs from 'dayjs';
import { T_MINUTE, noop } from 'monofile-utilities/lib/consts';
import React, { type HTMLAttributes, memo, useCallback, useEffect, useMemo } from 'react';
import { useHistory, useLocation } from 'react-router';
import { useSetState } from 'react-use';
import IconIconManagementSvg from '../../../../../assets/icon/icon-management.svg';
import { BusinessName } from '../../../../../components/BusinessName';
import { Button } from '../../../../../components/Button/Button';
import { Condition } from '../../../../../components/Condition';
import { ImgIcon } from '../../../../../components/Icon/Icon';
import { modalApi } from '../../../../../components/Modal/Modal';
import { TicketPaidStatusCell } from '../../../../../components/Payment/components/TicketPaidStatusCell';
import { Tooltip } from '../../../../../components/Popup/Tooltip';
import { Table } from '../../../../../components/Table/Table';
import { type ColumnOptions, type PaginationOptions } from '../../../../../components/Table/Table.types';
import { usePaginationOptions } from '../../../../../components/Table/Table.utils';
import { WithMultiLocation } from '../../../../../components/WithFeature/WithMultiLocation';
import { useNewAccountStructure } from '../../../../../components/WithFeature/useNewAccountStructure';
import { useGetClosestScroller } from '../../../../../layout/components/ScrollerProvider';
import { PATH_CUSTOMER_BOOKINGS, PATH_WAITING_EDIT } from '../../../../../router/paths';
import { AutoMessageType } from '../../../../../store/autoMessage/autoMessage.boxes';
import { switchBusiness } from '../../../../../store/business/business.actions';
import { type BusinessRecord } from '../../../../../store/business/business.boxes';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { selectCurrentPermissions } from '../../../../../store/business/role.selectors';
import { customerMapBox } from '../../../../../store/customer/customer.boxes';
import {
  groomingCancelTicket,
  groomingGetCustomerTickets,
  groomingRemoveTicket,
} from '../../../../../store/grooming/grooming.actions';
import {
  GroomingTicketListType,
  GroomingTicketNoShowStatus,
  type GroomingTicketRecord,
  GroomingTicketStatus,
  SortType,
  bookingTableLoadingEventsBox,
  groomingTicketMapBox,
} from '../../../../../store/grooming/grooming.boxes';
import { selectGroomingCustomerTickets } from '../../../../../store/grooming/grooming.selectors';
import { type PetRecord, petMapBox } from '../../../../../store/pet/pet.boxes';
import { type ServiceRecord, serviceMapBox } from '../../../../../store/service/service.boxes';
import { type StaffRecord, staffMapBox } from '../../../../../store/staff/staff.boxes';
import { type RecordMap } from '../../../../../store/utils/RecordMap';
import { LegacyBool } from '../../../../../store/utils/createEnum';
import { ID_ANONYMOUS, isNormal } from '../../../../../store/utils/identifier';
import { deleteWaitList } from '../../../../../store/waitList/actions/public/waitList.actions';
import { DATE_FORMAT_DAY_SHORT, DATE_FORMAT_EXCHANGE } from '../../../../../utils/DateTimeUtil';
import { useRouteState } from '../../../../../utils/RoutePath';
import { useApptDetailDrawerOnClose } from '../../../../../utils/hooks/useApptDetailDrawerCloseCallback';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { useOpenApptDetailDrawer } from '../../../../../utils/hooks/useOpenApptDetailDrawer';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { permissionAlertAsync } from '../../../../../utils/message';
import { computedDiffTimeString } from '../../../../../utils/utils';
import { TakePaymentModal } from '../../../../Calendar/Grooming/TakePaymentModal/TakePaymentModal';
import { TicketCreatedAlerts } from '../../../../Calendar/Grooming/TicketCreatedAlerts/TicketCreatedAlerts';
import { waitlistPreferenceToFormValue } from '../../../../CreateWaitList/utils/utils';
import { useObRequestAndNotifyModal } from '../../../../OnlineBooking/components/OnlineBookingRequestActionModal/OnlineBookingRequestActionModal.hooks';
import { type OnlineBookingRequestActionModalProps } from '../../../../OnlineBooking/components/OnlineBookingRequestActionModal/OnlineBookingRequestActionModal.options';
import { OnlineBookingRequestModal } from '../../../../OnlineBooking/modules/OnlineBookingRequests/components/OnlineBookingRequestModal/OnlineBookingRequestModal';
import { useWaitDetail } from '../../../../WaitDetail/hooks/useWaitDetail';
import { useExpectedRender } from '../../../../WaitList/hooks/useExpectedRender';
import { DropdownItem, DropdownList } from '../../../ClientList/componentsLegacy/ClientListHeader.style';
import { ActionDetail } from '../../components/Container.style';
import { ActionButtonList, BookingTableView, CancelReasonRow } from '../Bookings.style';
import { ChargeNoShowFeeModal } from './ChargeNoShowFeeModal';
import { PetStaffServiceCell } from './PetStaffServiceCell';

export interface BookingTableProps {
  className?: string;
  type: number;
  customerId: number;
}

export interface BookingTableState {
  noShowId?: number;
  active?: GroomingTicketRecord;
  rescheduleTicketId?: number;
}

export type BookingTableAction = 'detail' | 'delete' | 'book' | 'no-show' | 'no-show-and-charge' | 'no-show-charge';

export const BookingTable = memo<BookingTableProps>(({ className, customerId, type }) => {
  const { pathname } = useLocation();
  const { rescheduleTicketId } = useRouteState(PATH_CUSTOMER_BOOKINGS) || {};
  const { openApptDetailDrawer } = useOpenApptDetailDrawer();
  const [state, setState] = useSetState<BookingTableState>({
    noShowId: void 0,
    active: void 0,
    rescheduleTicketId,
  });
  const [bookings, business, noShow, isBookingTableLoading] = useSelector(
    selectGroomingCustomerTickets(customerId, type),
    selectCurrentBusiness,
    groomingTicketMapBox.mustGetItem(state.noShowId ?? ID_ANONYMOUS),
    bookingTableLoadingEventsBox,
  );

  // 这两个类型的要 skip cancel
  const shouldSkipCancel = type === GroomingTicketListType.History || type === GroomingTicketListType.Upcoming;

  const dispatch = useDispatch();
  const history = useHistory();

  const handleAction = useLatestCallback(async (record: GroomingTicketRecord, action: BookingTableAction) => {
    switch (action) {
      case 'delete':
        await dispatch(groomingRemoveTicket(record.id));
        await dispatch(groomingGetCustomerTickets({ customerId, type, skipCancel: shouldSkipCancel }));
        break;
      case 'book':
        history.push(PATH_WAITING_EDIT.build({ ticketId: record.id }));
        break;
      case 'detail':
        openApptDetailDrawer({
          ticketId: record.id,
        });
        break;
      case 'no-show':
        await dispatch(groomingCancelTicket(record.id, LegacyBool.toggle(record.noShow)));
        break;
      case 'no-show-charge':
        setState({
          noShowId: record.id,
        });
        break;
      case 'no-show-and-charge':
        await dispatch(groomingCancelTicket(record.id, LegacyBool.toggle(record.noShow)));
        setState({
          noShowId: record.id,
        });
        break;
    }
  });

  const handleCloseChargeNoShow = useLatestCallback(() => {
    setState({
      noShowId: void 0,
    });
    dispatch(groomingGetCustomerTickets({ customerId, type, skipCancel: shouldSkipCancel }));
  });
  const getMainScroller = useGetClosestScroller();

  useEffect(() => {
    // clear route state
    history.replace(pathname);
  }, []);

  useApptDetailDrawerOnClose(() => {
    dispatch(groomingGetCustomerTickets({ customerId, type: GroomingTicketListType.Upcoming, skipCancel: true }));
    dispatch(groomingGetCustomerTickets({ customerId, type: GroomingTicketListType.History, skipCancel: true }));
    dispatch(groomingGetCustomerTickets({ customerId, type: GroomingTicketListType.Waiting }));
  });

  return (
    <BookingTableView>
      {state.active?.bookOnlineStatus && (
        <OnlineBookingRequestModal
          id={state.active.id}
          onClose={(updated) => {
            setState({
              active: void 0,
            });
            updated && dispatch(groomingGetCustomerTickets({ customerId, type, skipCancel: shouldSkipCancel }));
          }}
        />
      )}
      {noShow === void 0 || !isNormal(state.noShowId) ? null : noShow.noShowStatus ===
        GroomingTicketNoShowStatus.Initial ? (
        <ChargeNoShowFeeModal
          onClose={(submitted) =>
            !submitted &&
            setState({
              noShowId: void 0,
            })
          }
          bookingId={noShow.id}
          visible={true}
        />
      ) : (
        <TakePaymentModal
          onClose={handleCloseChargeNoShow}
          isChargeNoShowFee={true}
          invoiceId={noShow.invoiceId}
          module="grooming"
        />
      )}
      {state.rescheduleTicketId && (
        <TicketCreatedAlerts
          ticketId={state.rescheduleTicketId}
          customerId={customerId}
          mode={AutoMessageType.AppointmentRescheduled}
          onClose={() => setState({ rescheduleTicketId: void 0 })}
        />
      )}

      <Table<number, BookingTableRecord>
        className={className}
        rowKeys={bookings.getList()}
        useRecord={useCallback(
          (id: number) => useSelector(groomingTicketMapBox.mustGetItem(id), staffMapBox, serviceMapBox, petMapBox),
          [],
        )}
        columns={useBookingColumns(
          type,
          business,
          handleAction,
          getMainScroller,
          bookings.filter.orderType,
          customerId,
        )}
        loading={bookings.isLoading() || isBookingTableLoading}
        pagination={usePaginationOptions(bookings.total, bookings.pageNum, bookings.pageSize)}
        onChange={useLatestCallback((pagination: PaginationOptions, [sort]: ColumnOptions<BookingTableRecord>[]) => {
          const orderType = sort?.order === 'desc' ? SortType.DESC : SortType.ASC;
          const clear = pagination.pageSize !== bookings.pageSize || orderType !== bookings.filter.orderType;
          return dispatch(
            groomingGetCustomerTickets({
              customerId,
              type,
              clear,
              orderType,
              pageNum: clear ? 1 : pagination.current,
              pageSize: pagination.pageSize,
              skipCancel: shouldSkipCancel,
            }),
          );
        })}
        getRowProps={useLatestCallback(
          ([item]: BookingTableRecord): HTMLAttributes<HTMLTableRowElement> => ({
            onClick: (e) => {
              if (type !== GroomingTicketListType.Waiting) {
                e.stopPropagation();
                handleAction(item, 'detail');
              }
            },
            style: type === GroomingTicketListType.Waiting ? {} : { cursor: 'pointer' },
          }),
        )}
        renderExtra={useLatestCallback(([record]: BookingTableRecord) => {
          return !record.cancelReason?.length ? null : (
            <CancelReasonRow>
              <p className="cancel-reason-title">Cancel reason:</p>
              <AntTooltip title={record.cancelReason} color={'#9b9b9b'} placement="topLeft">
                <p className="cancel-reason-content">{record.cancelReason}</p>
              </AntTooltip>
            </CancelReasonRow>
          );
        })}
      />
    </BookingTableView>
  );
});

export type BookingTableRecord = [
  GroomingTicketRecord,
  RecordMap<StaffRecord, 'id'>,
  RecordMap<ServiceRecord, 'serviceId'>,
  RecordMap<PetRecord, 'petId'>,
];

export const useBookingColumns = (
  type: number,
  business: BusinessRecord,
  handleAction: (record: GroomingTicketRecord, action: BookingTableAction) => void,
  getScroller: (node: HTMLElement) => HTMLElement,
  timeOrder: number,
  customerId: number,
) => {
  const [permissions] = useSelector(selectCurrentPermissions);
  const dispatch = useDispatch();
  const [customer] = useSelector(customerMapBox.mustGetItem(customerId));

  const { expectedFields } = useExpectedRender();
  const showObRequestAndNotifyModal = useObRequestAndNotifyModal();
  const openWaitDetail = useWaitDetail();
  const isWaitList = type === GroomingTicketListType.Waiting;
  const { isNewAndHasMultipleLocation } = useNewAccountStructure('working');
  const shouldSkipCancel = type === GroomingTicketListType.History || type === GroomingTicketListType.Upcoming;

  const timeColumn: ColumnOptions<BookingTableRecord>[] = isWaitList
    ? [
        {
          title: 'Added time',
          order: timeOrder === SortType.ASC ? 'asc' : 'desc',
          sortable: true,
          render: ([record]) => {
            const { createAt = '' } = record.waitListDTO || {};
            return (
              <div className="moe-flex moe-flex-col">
                <span className="moe-text-[14px] moe-text-[#333]">{business.formatFixedDate(createAt)}</span>
                <span className="moe-text-[14px] moe-text-[#999]">
                  ( {computedDiffTimeString(dayjs(createAt).valueOf() / 1000, DATE_FORMAT_EXCHANGE)})
                </span>
              </div>
            );
          },
        },
      ]
    : [
        {
          className: 'date-time moe-max-w-[240px]',
          title: 'Date & time',
          order: timeOrder === SortType.ASC ? 'asc' : 'desc',
          sortable: true,
          render: ([record]) => {
            if (!record.appointmentDate) {
              return '';
            }
            const startDate = business.formatFixedDate(record.appointmentDate);
            const startTime = business.formatFixedTime(record.appointmentStartTime * T_MINUTE);
            const endDate = business.formatFixedDate(record.appointmentEndDate);
            const endTime = business.formatFixedTime(record.appointmentEndTime * T_MINUTE);
            const startDay = dayjs(record.appointmentDate, DATE_FORMAT_EXCHANGE).format(DATE_FORMAT_DAY_SHORT);
            const endDay = dayjs(record.appointmentEndDate, DATE_FORMAT_EXCHANGE).format(DATE_FORMAT_DAY_SHORT);

            const isSameDate = startDate === endDate;

            return (
              <div>
                {`${startDate} ${startDay} ${startTime} - ${isSameDate ? '' : `${endDate} ${endDay} `}${endTime}`}
              </div>
            );
          },
        },
      ];

  const expectColumn: ColumnOptions<BookingTableRecord>[] = isWaitList
    ? [
        {
          title: 'Expected',
          render: ([record]) => {
            if (!record.waitListDTO) {
              return '';
            }
            const { staffPreference = {}, timePreference = {}, datePreference = {} } = record.waitListDTO;
            const afterValue = waitlistPreferenceToFormValue({ staffPreference, timePreference, datePreference });
            return (
              <div className="">
                {expectedFields.date.render(afterValue)}
                <span className="moe-mx-[2px]">/</span>
                {expectedFields.time.render(afterValue)}
                <span className="moe-mx-[2px]">/</span>
                {expectedFields.staff.render(afterValue)}
              </div>
            );
          },
        },
      ]
    : [];

  const handleDelete = useLatestCallback(async (record: GroomingTicketRecord) => {
    if (record.waitListDTO?.realWaitListId) {
      if (record.bookOnlineStatus) {
        const type = 'delete';
        const notifyActionParams: OnlineBookingRequestActionModalProps = {
          groomingId: record.id,
          type,
          hasPetParentAppAccount: customer.hasPetParentAppAccount,
          shouldShowRefund: record.prepaidAmount > record.refundAmount,
        };
        // 先通知，再delete
        await showObRequestAndNotifyModal({ ...notifyActionParams, skipSubmit: true });
      }
      await dispatch(deleteWaitList({ waitListId: record.waitListDTO.realWaitListId }));
      dispatch(groomingGetCustomerTickets({ customerId, type, skipCancel: shouldSkipCancel }));
      return;
    }
    handleAction(record, 'delete');
  });

  const handleOpenWaitList = useSerialCallback(async (record: GroomingTicketRecord) => {
    if (record.waitListDTO?.realWaitListId) {
      if (isNormal(record.businessId) && business.id !== Number(record.businessId)) {
        await dispatch(switchBusiness(Number(record.businessId)));
      }

      openWaitDetail({
        id: String(record.waitListDTO.realWaitListId),
        mask: true,
        onRefresh() {
          dispatch(groomingGetCustomerTickets({ customerId, type, skipCancel: shouldSkipCancel }));
        },
        onDelete: async () => await handleDelete(record),
      });
      return;
    }
    handleAction(record, 'book');
  });

  return useMemo(() => {
    const columns: ColumnOptions<BookingTableRecord>[] = [
      ...timeColumn,
      {
        className: 'pet',
        title: isWaitList ? 'Pet / Service' : 'Pet / Staff / Service',
        render: ([record, staffMap, serviceMap, petMap]) => {
          const { evaluationServiceList, petServiceList } = record;
          let EvaluationBlock = null;
          let ServiceBlock = null;

          if (evaluationServiceList?.length) {
            EvaluationBlock = evaluationServiceList.map((item) => {
              return (
                <PetStaffServiceCell
                  key={item.evaluationServiceDetailId}
                  petName={petMap.mustGetItem(Number(item.petId)).petName}
                  serviceName={serviceMap.mustGetItem(Number(item.serviceId)).name}
                />
              );
            });
          }

          ServiceBlock = petServiceList.map((item) => {
            const staffName =
              !isWaitList && item.staffIds?.length
                ? item.staffIds
                    .filter(isNormal)
                    .map((staffId) => staffMap.mustGetItem(+staffId).firstName)
                    .join(', ')
                : undefined;
            return (
              <PetStaffServiceCell
                key={item.petDetailId}
                petName={petMap.mustGetItem(Number(item.petId)).petName}
                serviceName={serviceMap.mustGetItem(Number(item.serviceId)).name}
                staffName={staffName}
              />
            );
          });
          return (
            <>
              {EvaluationBlock}
              {ServiceBlock}
            </>
          );
        },
      },
      ...expectColumn,
      {
        className: 'cost',
        title: 'Estimated total',
        render: ([record]) => business.formatAmount(record.subTotalAmount),
      },
      {
        className: 'paid-status',
        title: 'Payment status & amount',
        render: ([record]) => (
          <TicketPaidStatusCell
            paidStatus={record.paidStatus()}
            paidAmount={record.paidAmount}
            prepaidAmount={record.prepaidAmount}
            refundAmount={record.refundAmount}
          />
        ),
      },
      {
        className: 'status',
        title: 'Status',
        render: ([record]) => {
          if (record.appointmentStatus !== GroomingTicketStatus.Cancelled) {
            return (
              <div className={'status ' + [GroomingTicketStatus.mapLabels[record.appointmentStatus]]}>
                {record.bookOnlineStatus ? 'Pending' : GroomingTicketStatus.mapLabels[record.appointmentStatus]}
              </div>
            );
          }
          const isNoShow = LegacyBool.truly(record.noShow);
          const isNoShowProcessing = isNoShow && record.noShowStatus !== GroomingTicketNoShowStatus.Initial;
          const isNoShowCharged = isNoShowProcessing && record.noShowStatus === GroomingTicketNoShowStatus.Completed;
          const disableByOtherLocation = record.businessId !== business.id + ''; // 跨location不可以操作

          const hasCancelPermission = permissions.has('cancelOrDeleteTicket');

          return (
            <div className="status Cancelled" onClick={(e) => e.stopPropagation()}>
              <Dropdown
                trigger={['click']}
                getPopupContainer={getScroller}
                placement="bottomRight"
                overlay={
                  <DropdownList onClick={(e) => e.stopPropagation()}>
                    <DropdownItem
                      onClick={isNoShowProcessing ? noop : () => handleAction(record, 'no-show')}
                      disabled={isNoShowProcessing || disableByOtherLocation}
                    >
                      {isNoShow ? 'Cancel no-show' : 'Mark as no-show'}
                    </DropdownItem>
                    <DropdownItem
                      onClick={() => {
                        if (isNoShowCharged) {
                          // Marked as now show, 已支付，点击无响应
                          return;
                        }
                        if (isNoShowProcessing || isNoShow) {
                          // Marked as now show, 已创建 Invoice 但未完成支付，点击弹出 Take payment 弹窗
                          handleAction(record, 'no-show-charge');
                          return;
                        }
                        // 未创建 Invoice
                        if (isNoShow) {
                          // 已 No-show
                          handleAction(record, 'no-show-charge');
                        } else {
                          // 未 No-show
                          handleAction(record, 'no-show-and-charge');
                        }
                      }}
                      disabled={isNoShowCharged || disableByOtherLocation}
                    >
                      {isNoShow ? 'Charge no-show fee' : 'Mark as no-show and charge no-show fee'}
                    </DropdownItem>
                    <Tooltip
                      destroyTooltipOnHide
                      theme="black"
                      overlay="Please request “cancel / delete ticket” permission from the business owner"
                      disabled={hasCancelPermission}
                      placement="bottom"
                    >
                      <DropdownItem
                        className={cn({
                          '!moe-text-disabled': !hasCancelPermission,
                        })}
                        onClick={async () => {
                          await permissionAlertAsync(permissions, 'cancelOrDeleteTicket');
                          modalApi.confirmDelete({
                            content:
                              'Are you sure to permanently delete this appointment? This action can not be reversed.',
                            onOk: () => handleAction(record, 'delete'),
                          });
                        }}
                      >
                        Delete this appointment
                      </DropdownItem>
                    </Tooltip>
                  </DropdownList>
                }
              >
                <div className="info" onClick={(e) => e.stopPropagation()}>
                  <span>Cancelled</span>
                  <ImgIcon src={IconIconManagementSvg} width={20} />
                </div>
              </Dropdown>
              {isNoShow && (
                <div
                  className="no-show"
                  onClick={() => {
                    handleAction(record, 'no-show-charge');
                  }}
                >
                  <div>{isNoShowProcessing ? 'No-show fee' : 'No-show'}</div>
                  {isNoShowProcessing && (
                    <div>Charged: {isNoShowCharged && business.formatAmount(record.noShowFee)}</div>
                  )}
                </div>
              )}
            </div>
          );
        },
      },
      isWaitList
        ? {
            className: 'actions',
            title: 'Action',
            render: ([record]) => {
              return (
                <ActionButtonList>
                  <Button
                    onClick={() => handleOpenWaitList(record)}
                    loading={handleOpenWaitList.isBusy()}
                    btnType="primary"
                    buttonRadius="circle"
                    size="sm"
                  >
                    Book now
                  </Button>
                  <Popconfirm
                    title={'Are you sure to delete this booking?'}
                    okText="Yes"
                    okType="danger"
                    onConfirm={() => {
                      handleDelete(record);
                    }}
                    placement="topRight"
                  >
                    <Button btnType="danger" buttonRadius="circle" size="sm">
                      Delete
                    </Button>
                  </Popconfirm>
                </ActionButtonList>
              );
            },
          }
        : {
            className: 'id',
            title: 'Booking ID',
            render: ([record]) => {
              const businessId = record.businessId;
              return (
                <div className="">
                  <ActionDetail>#{record.id}</ActionDetail>
                  <Condition if={!!businessId}>
                    <WithMultiLocation scene="all">
                      <BusinessName businessId={businessId} className="moe-text-[#999]" />
                    </WithMultiLocation>
                  </Condition>
                </div>
              );
            },
          },
    ];

    if (isWaitList && isNewAndHasMultipleLocation) {
      columns.splice(-1, 0, {
        className: 'business',
        title: 'Business',
        render: ([record]) => {
          const businessId = record.businessId;
          if (businessId) {
            return <BusinessName businessId={record.businessId} className="moe-text-base moe-text-[#666]" />;
          }
          return '';
        },
      });
    }

    return columns;
  }, [type, business, getScroller, timeOrder, isWaitList, isNewAndHasMultipleLocation, permissions]);
};
