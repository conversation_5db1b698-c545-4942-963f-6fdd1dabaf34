import { Heading, Tag } from '@moego/ui';
import React from 'react';
import { SwitchBusinessDropdown } from '../../../../../../components/Business/SwitchBusinessDropdown';
import { useSelector } from 'amos';
import { syncWithStaffRegularWorkingHour } from '../../../../../../store/onlineBooking/settings/availabilityGrooming.boxes';
import { MinorCircleFilled } from '@moego/icons-react';
import cn from 'classnames';
import { useEnableMultiPetBySlotFeature } from '../../../../../Calendar/latest/components/SlotCalendar/hooks/useSlotCalendarFeature';

interface ShiftManagementTitleProps {
  onBeforeBusinessChange?: () => void | Promise<void>;
}

export const ShiftManagementTitle = (props: ShiftManagementTitleProps) => {
  const { onBeforeBusinessChange } = props;
  const [obAvailabilitySync] = useSelector(syncWithStaffRegularWorkingHour);
  const isEnableOBBySlot = useEnableMultiPetBySlotFeature();

  return (
    <div className="moe-flex moe-items-center moe-flex-grow-[1]">
      <Heading size="2" className="moe-text-primary">
        Staff shift management
      </Heading>
      <SwitchBusinessDropdown scene="working" onBeforeChange={onBeforeBusinessChange} />
      {isEnableOBBySlot && (
        <Tag
          className="moe-max-w-fit moe-ml-[12px]"
          color="neutral"
          variant="filled"
          icon={
            <MinorCircleFilled
              className={cn('moe-w-[6px] moe-h-[6px]', {
                'moe-text-success': obAvailabilitySync,
                'moe-text-[#CDCDCD]': !obAvailabilitySync,
              })}
            />
          }
          label={obAvailabilitySync ? 'Synced to Online booking' : 'Unsynced from Online Booking'}
        />
      )}
    </div>
  );
};
