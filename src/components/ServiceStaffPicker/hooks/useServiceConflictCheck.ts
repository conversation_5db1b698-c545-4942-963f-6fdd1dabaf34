import { useEffect } from 'react';
import { useSetState } from 'react-use';
import {
  type StaffConflictParams,
  StaffConflictType,
  checkStaffConflict,
} from '../../../container/CreateTicket/hooks/useValidate';
import { isNormal } from '../../../store/utils/identifier';
import { useBool } from '../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';

export interface ServiceConflictCheckParams {
  appointmentDate?: string;
  ticketId?: number;
  staffId?: number;
  startTime?: number;
  serviceTime: number | string;
  isMultiple?: boolean;
}

export function useServiceConflictCheck(checkParams: ServiceConflictCheckParams, disableConflictCheck?: boolean) {
  const { appointmentDate, ticketId, staffId, startTime, serviceTime, isMultiple } = checkParams;
  const [state, setState] = useSetState({
    showError: false,
    errorMsg: '',
  });
  const loading = useBool(false);
  const clearError = useLatestCallback(() => {
    setState({ showError: false });
  });

  const checkConflictMsg = useLatestCallback(async (params: StaffConflictParams) => {
    let msg: string | undefined = undefined;
    try {
      loading.open();
      const {
        data: { isNotConflict, type },
      } = await checkStaffConflict(params);
      msg = isNotConflict ? undefined : StaffConflictType[type];
    } finally {
      loading.close();
    }
    return msg;
  });

  const validateServiceStaff = useSerialCallback(async () => {
    const duration = typeof serviceTime === 'string' ? Number(serviceTime) : serviceTime;
    const isInValidateParams =
      !isNormal(staffId) || !Number.isFinite(duration) || !Number.isFinite(startTime) || isMultiple || !appointmentDate;

    if (isInValidateParams) {
      clearError();
      return;
    }
    const params: StaffConflictParams = {
      staffId,
      startTime,
      duration,
      appointmentTime: appointmentDate,
    };
    if (ticketId) {
      params.groomingId = +ticketId;
    }
    const msg = await checkConflictMsg(params);
    if (msg) {
      setState({
        showError: true,
        errorMsg: msg,
      });
    } else {
      clearError();
    }
  });

  useEffect(() => {
    if (!disableConflictCheck) {
      validateServiceStaff();
    }
  }, [appointmentDate, disableConflictCheck, ticketId, staffId, startTime, serviceTime, isMultiple]);

  return {
    conflictCheckLoading: loading.value,
    conflictErrorMsg: state.errorMsg,
    showConflictErrorTip: state.showError && !!state.errorMsg,
    closeConflictErrorTip: useLatestCallback(() => setState({ showError: false })),
  };
}
