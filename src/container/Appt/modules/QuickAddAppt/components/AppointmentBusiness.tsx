import React, { type FC } from 'react';
import { useEnableFulfillmentFlow } from '../../../../../utils/hooks/useEnableFulfillmentFlow';
import { FormItemLabel } from '@moego/ui/dist/esm/components/Form/FormItemLabel';
import { SingleLocationSelector } from '../../../../../components/Business/SingleLocationSelector';
import { useDefaultLocationValue } from '../../../../../components/Business/hooks/useLocationValue';
import { useDispatch, useSelector } from 'amos';
import { switchBusiness } from '../../../../../store/business/business.actions';
import { useSerialCallback } from '@moego/tools';
import { useNewAccountStructure } from '../../../../../components/WithFeature/useNewAccountStructure';
import { customerMapBox } from '../../../../../store/customer/customer.boxes';
import { selectBusiness } from '../../../../../store/business/business.selectors';
import { MinorWarningFilled } from '@moego/icons-react';
import { Text } from '@moego/ui';

export interface AppointmentBusinessProps {
  customerId: number;
}

export const AppointmentBusiness: FC<AppointmentBusinessProps> = (props) => {
  const { customerId } = props;
  const enableFulfillmentFlow = useEnableFulfillmentFlow();
  const { isSingleLocation } = useNewAccountStructure('working');

  const [locationId, setLocationId] = useDefaultLocationValue({
    scene: 'working',
    preferred: 'current',
  });

  const [customer] = useSelector(customerMapBox.mustGetItem(customerId));
  const [preferBusiness] = useSelector(selectBusiness(Number(customer.preferredBusinessId)));

  const dispatch = useDispatch();

  const onChangeBusiness = useSerialCallback(async (id: string) => {
    setLocationId(id);
    await dispatch(switchBusiness(Number(id)));
    // clear the selected pet and service after switching business
  });

  if (!enableFulfillmentFlow || isSingleLocation) {
    return null;
  }

  return (
    <div>
      <FormItemLabel>Business</FormItemLabel>
      <SingleLocationSelector
        scene="working"
        value={locationId}
        onChange={onChangeBusiness}
        isLoading={onChangeBusiness.isBusy()}
      />
      {customer.preferredBusinessId !== locationId && (
        <div className="moe-flex moe-items-center moe-mt-xxs moe-gap-x-xxs moe-text-warning">
          <MinorWarningFilled />
          <Text variant="small">
            Different from {customer.firstName}&apos;s preferred one ({preferBusiness.businessName}).
          </Text>
        </div>
      )}
    </div>
  );
};
