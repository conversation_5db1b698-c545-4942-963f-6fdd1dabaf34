import { Input } from '@moego/ui';
import React, { forwardRef, useImperativeHandle } from 'react';
import { OBTestIds } from '../../../../../../config/testIds/onlineBooking';
import { type PaymentSettingFormRef, usePaymentSettingFormState } from './PaymentSettingContext';

const { SettingPaymentDepositCancellationPolicy } = OBTestIds;

interface DepositFormProps {
  defaultValue: string;
}

export const DepositForm = forwardRef<PaymentSettingFormRef, DepositFormProps>((props, ref) => {
  const [value, setValue] = React.useState(props.defaultValue || '');
  const { onDirty } = usePaymentSettingFormState('deposit');

  useImperativeHandle(ref, () => ({
    validate: async () => {
      if (!value) {
        throw new Error('Cancellation policy is required');
      }
      return {
        prepayPolicy: value,
      };
    },
  }));

  const handleChange = (value: string) => {
    setValue(value);
    onDirty();
  };

  return (
    <Input.TextArea
      value={value}
      onChange={handleChange}
      label="Cancellation Policy"
      isRequired
      placeholder="Please enter the cancellation policy here."
      data-testid={SettingPaymentDepositCancellationPolicy}
    />
  );
});
