import { Tooltip } from 'antd';
import React from 'react';

export interface BizOpenStatusProps {
  className?: string;
  label?: string;
  msg?: string;
}

export function BizOpenStatus(props: BizOpenStatusProps) {
  const { label, msg } = props;

  if (label) {
    return (
      <Tooltip
        title={msg}
        overlayInnerStyle={{
          backgroundColor: '#3D414B',
          borderRadius: 4,
          padding: '8px 16px',
          fontSize: 12,
          lineHeight: '16px',
          fontWeight: 500,
        }}
      >
        <span className="!moe-text-[12px] !moe-text-[#333] !moe-leading-[16px] !moe-font-medium !moe-cursor-default moe-whitespace-nowrap">
          {label}
        </span>
      </Tooltip>
    );
  }

  return null;
}
