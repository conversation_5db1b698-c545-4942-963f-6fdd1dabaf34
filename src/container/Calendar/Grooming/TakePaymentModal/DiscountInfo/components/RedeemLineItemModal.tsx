import { Checkbox, Modal, Text, cn } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useState } from 'react';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { useFloatableHost } from '../../../../../../utils/hooks/useFloatableHost';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';

interface RedeemLineItemModalProps {
  visible: boolean;
  zIndex?: number;
  lineItems: {
    id: string;
    name: string;
    price: number;
    quantity: number;
    petName: string;
    petBreed: string;
  }[];
  onConfirm: (ids: string[], isSelectAll?: boolean) => void;
  onClose: () => void;
}

export const RedeemLineItemModal = memo<RedeemLineItemModalProps>((props) => {
  const { visible, zIndex, lineItems, onConfirm, onClose } = props;
  const [selectedLineItemIds, setSelectedLineItemIds] = useState<string[]>(lineItems.map((item) => item.id));
  const [business] = useSelector(selectCurrentBusiness);

  const handleConfirm = useSerialCallback(async () => {
    if (!selectedLineItemIds.length) {
      return;
    }
    onConfirm(selectedLineItemIds, selectedLineItemIds.length === lineItems.length);
  });

  return (
    <Modal
      isOpen={visible}
      zIndex={zIndex}
      onClose={onClose}
      title="Select one service for discount"
      confirmButtonProps={{
        isDisabled: !selectedLineItemIds.length,
        isLoading: handleConfirm.isBusy(),
      }}
      onConfirm={handleConfirm}
      confirmText="Apply"
    >
      <section className="moe-flex moe-flex-col moe-gap-m">
        {lineItems.map((item) => (
          <section
            key={item.id}
            className={cn(
              'moe-flex moe-gap-s moe-p-s moe-h-[96px] moe-rounded-[8px] moe-cursor-pointer',
              selectedLineItemIds.includes(item.id) ? 'moe-border-2 moe-border-brand' : 'moe-border moe-border-button',
            )}
            onClick={() => {
              if (selectedLineItemIds.includes(item.id)) {
                setSelectedLineItemIds(selectedLineItemIds.filter((id) => id !== item.id));
              } else {
                setSelectedLineItemIds([...selectedLineItemIds, item.id]);
              }
            }}
          >
            <Checkbox isSelected={selectedLineItemIds.includes(item.id)} />
            <section className="moe-flex-1 moe-flex moe-justify-between">
              <section className="moe-flex-1 moe-flex moe-flex-col moe-justify-between">
                <Text variant="regular-short">{item.name}</Text>
                <Text variant="small" className="moe-text-tertiary">
                  x{item.quantity}
                </Text>
                <Text variant="caption" className="moe-text-secondary">
                  {item.petName && item.petBreed ? `${item.petName}(${item.petBreed})` : ''}
                </Text>
              </section>
              <Text variant="regular-short">{business.formatAmount(item.price)}</Text>
            </section>
          </section>
        ))}
      </section>
    </Modal>
  );
});

export const useRedeemLineItemModal = () => {
  const { mountModal } = useFloatableHost<{
    ids: string[];
    isSelectAll?: boolean;
  }>();

  return useLatestCallback(async (lineItems: any[]) => {
    const { promise, closeFloatable } = mountModal(({ visible, zIndex }) => {
      return (
        <RedeemLineItemModal
          visible={visible}
          zIndex={zIndex}
          lineItems={lineItems}
          onConfirm={(ids, isSelectAll) => {
            closeFloatable({ ids, isSelectAll });
          }}
          onClose={() => {
            closeFloatable();
          }}
        />
      );
    });
    return promise;
  });
};
