import { useDispatch, useSelector } from 'amos';
import React, { useEffect } from 'react';
import {
  OBStaffWorkingSlotMapBox,
  syncWithStaffRegularWorkingHour,
} from '../../../../../../../../../../store/onlineBooking/settings/availabilityGrooming.boxes';
import { WeekSlotSchedule } from '../../../../../../../../../settings/Settings/StaffSetting/ShiftManagement/components/StaffSchedule/WorkingSlot/WeekSlotSchedule';
import type { StaffRecord } from '../../../../../../../../../../store/staff/staff.boxes';
import { selectStaffWeekSlotRangeAt } from '../../../../../../../../../../store/staffSchedule/staffSchedule.selectors';
import dayjs from 'dayjs';
import { useEnableMultiPetBySlotFeature } from '../../../../../../../../../Calendar/latest/components/SlotCalendar/hooks/useSlotCalendarFeature';

export interface WorkingSlotProps {
  className?: string;
  staff: StaffRecord;
  isDisabled?: boolean;
}

export const WorkingSlot = (props: WorkingSlotProps) => {
  const { staff, isDisabled, className } = props;
  const dispatch = useDispatch();
  const [OBStaffWorkingSlot, SMStaffWorkingSlot, isSynced] = useSelector(
    OBStaffWorkingSlotMapBox.mustGetItem(staff.id),
    selectStaffWeekSlotRangeAt(staff.id, dayjs()),
    syncWithStaffRegularWorkingHour,
  );
  // 非白名单用户 by slot 配置是没有 sync 的
  const isEnableOBBySlot = useEnableMultiPetBySlotFeature();
  const weekSlotDataForDisplay = isEnableOBBySlot && isSynced ? SMStaffWorkingSlot : OBStaffWorkingSlot;

  useEffect(() => {
    if (isEnableOBBySlot) return;
    dispatch(syncWithStaffRegularWorkingHour.setState(false));

    return () => {
      // 非白用户的 sync 状态不止会影响渲染，还会影响数据流逻辑，在切到 book by slot 时，手动把本地 sync 状态设置为 false
      // 用 useEffect 的特性保存初始化时 isSynced 的值，在组件卸载时恢复（切换 availability type 时）
      // 注：deps 里一定不能加上 isSynced
      dispatch(syncWithStaffRegularWorkingHour.setState(isSynced));
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isEnableOBBySlot]);

  return (
    <WeekSlotSchedule
      hideDailyWorkingHour
      className={className}
      value={weekSlotDataForDisplay}
      isDisabled={isDisabled}
      onChange={(v) => {
        dispatch(OBStaffWorkingSlotMapBox.mergeItem(staff.id, v));
      }}
    />
  );
};
