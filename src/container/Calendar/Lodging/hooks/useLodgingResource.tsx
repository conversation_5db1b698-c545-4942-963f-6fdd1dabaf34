import { type ColCellContentArg } from '@fullcalendar/resource-common';
import { useLatestCallback } from '@moego/finance-utils';
import { cn } from '@moego/ui';
import { useSelector } from 'amos';
import React, { useCallback, useMemo, useState } from 'react';
import { lodgingViewExpandAllStore } from '../../../../store/calendarLatest/lodgingCalendar.boxes';
import {
  selectCalendarLodgingUnitList,
  selectLodgingCalendarConfig,
} from '../../../../store/calendarLatest/lodgingCalendar.selectors';
import { lodgingTypesMapBox, lodgingUnitMapBox } from '../../../../store/lodging/lodgingUnit.boxes';
import { type ResourceLabelContentArg } from '../LodgingCalendar.types';
import { RESOURCE_TYPE_FILED } from '../LodgingCalendar/LodgingCalendar.config';
import { LodgingExpandAll } from '../LodgingCalendar/components/LodgingExpandAll';
import {
  type GroupSourceDataMap,
  RELATED_GROUP_LANE_WRAPPER,
  ResourceGroupLaneContent,
} from '../LodgingCalendar/components/ResourceGroupLaneContent';
import { ResourceItemLodging } from '../LodgingCalendar/components/ResourceItemLodging';
import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { GrowthBookFeatureList } from '../../../../utils/growthBook/growthBook.config';

interface RenderGroupProps {
  groupValue: string;
  el: HTMLElement;
}

const COLLAPSE_GROUP_HEIGHT = '60px';
const EXPAND_GROUP_HEIGHT = '36px';

export const useLodgingResource = (tableElementRef: React.MutableRefObject<HTMLTableElement | null>) => {
  const [calendarLodgingUnitList, lodgingUnitMap, lodgingTypesMap, lodgingCalendarConfig, isExpandAll] = useSelector(
    selectCalendarLodgingUnitList,
    lodgingUnitMapBox,
    lodgingTypesMapBox,
    selectLodgingCalendarConfig,
    lodgingViewExpandAllStore,
  );

  const enableHideDefaultArea = useFeatureIsOn(GrowthBookFeatureList.EnableHideDefaultAreaInLodgingView);

  const isEmptyResource = !calendarLodgingUnitList.size;
  const resources = useMemo(
    () =>
      calendarLodgingUnitList
        .map((id, index) => {
          const { name, lodgingTypeId } = lodgingUnitMap.mustGetItem(id);
          const lodgingTypes = lodgingTypesMap.mustGetItem(lodgingTypeId);
          return {
            id,
            title: name,
            orderIndex: index,
            [RESOURCE_TYPE_FILED]: lodgingTypes.name,
            isDefaultArea: lodgingTypes.isDefaultArea,
          };
        })
        .toArray()
        .filter((item) => {
          if (enableHideDefaultArea) {
            return !item.isDefaultArea;
          }
          return true;
        }),
    [calendarLodgingUnitList, lodgingUnitMap, enableHideDefaultArea],
  );

  const resourceGroupLaneClassNames = cn('!moe-bg-white !moe-relative !moe-z-[1]');
  const resourceGroupLabelClassNames = cn(
    '!moe-bg-transparent !moe-border-r-white moe-resource-label-divider moe-relative hover:!moe-bg-neutral-sunken-light',
    [lodgingCalendarConfig.lodgingTypeList.length === 1 && '!moe-border-[transparent]'],
  );

  const [groupSourceDataMap, setGroupSourceDataMap] = useState({} as GroupSourceDataMap);

  const handleSetGroupSourceHoverDataMap = useLatestCallback((value: GroupSourceDataMap) => {
    setGroupSourceDataMap((prev) => {
      return {
        ...prev,
        ...Object.keys(value).reduce((acc, key) => {
          acc[key] = {
            ...prev[key],
            ...value[key],
          };
          return acc;
        }, {} as GroupSourceDataMap),
      };
    });
  });

  const handleGroupClick = useLatestCallback((props: RenderGroupProps) => {
    const { groupValue } = props;
    const { laneElement, element } = groupSourceDataMap[groupValue];

    if (groupSourceDataMap[groupValue]?.expandedValue) {
      element && (element.style.height = COLLAPSE_GROUP_HEIGHT);
      laneElement && (laneElement.style.height = COLLAPSE_GROUP_HEIGHT);
      return;
    }
    element && (element.style.height = EXPAND_GROUP_HEIGHT);
    laneElement && (laneElement.style.height = EXPAND_GROUP_HEIGHT);
  });

  const groupElHeightInit = useLatestCallback((props: RenderGroupProps) => {
    const { el } = props;

    el.style.height = isExpandAll ? EXPAND_GROUP_HEIGHT : COLLAPSE_GROUP_HEIGHT;
  });

  const isAllResourceGroupNotExpanded = useMemo(
    () => Object.values(groupSourceDataMap).every((item) => !item.expandedValue),
    [groupSourceDataMap],
  );

  const renderResourceLabelContent = useCallback(function renderResourceLabelContent(
    resourceLabelContentArg: ResourceLabelContentArg,
  ) {
    const { resource } = resourceLabelContentArg;
    return <ResourceItemLodging title={resource.title} />;
  }, []);

  const renderResourceGroupLabelContent = useLatestCallback(function renderResourceGroupLabelContent({
    groupValue,
  }: { groupValue: string }) {
    const groupSourceHoverData = groupSourceDataMap[groupValue] || {};
    return (
      <div
        className={cn('moe-h-full moe-w-full moe-absolute moe-left-0 moe-cursor-pointer', {
          '!moe-bg-neutral-sunken-light moe-resource-label-divider-hover': groupSourceHoverData.hoverValue,
        })}
      ></div>
    );
  });

  const renderResourceGroupLaneContent = useLatestCallback(function renderResourceGroupLaneContent(
    laneContentArgs: ColCellContentArg,
  ) {
    return (
      <ResourceGroupLaneContent
        groupSourceDataMap={groupSourceDataMap}
        setGroupSourceDataMap={handleSetGroupSourceHoverDataMap}
        laneContentArgs={laneContentArgs}
      />
    );
  });

  const resourceGroupLabelDidMount = useLatestCallback(function resourceGroupLabelDidMount(props) {
    const { el, groupValue } = props;

    groupElHeightInit(props);
    handleSetGroupSourceHoverDataMap({
      [groupValue]: {
        element: el,
      },
    });

    el.onclick = () => handleGroupClick(props);
    el.onmouseenter = () => {
      handleSetGroupSourceHoverDataMap({
        [groupValue]: {
          hoverValue: true,
        },
      });
    };
    el.onmouseleave = (e: MouseEvent) => {
      if (e.relatedTarget) {
        const relatedTarget = e.relatedTarget as HTMLElement;
        // 从 label 移入到 lane 时不触发 mouseleave
        if (relatedTarget.getAttribute('data-slot') === RELATED_GROUP_LANE_WRAPPER) {
          return;
        }
      }
      handleSetGroupSourceHoverDataMap({
        [groupValue]: {
          hoverValue: false,
        },
      });
    };
  });
  const resourceGroupLaneDidMount = useLatestCallback(function resourceGroupLaneDidMount(props) {
    const { el, groupValue } = props;
    const tableElement = el.closest('table');

    groupElHeightInit(props);
    handleSetGroupSourceHoverDataMap({
      [groupValue]: {
        laneElement: el,
      },
    });

    el.onclick = () => handleGroupClick(props);

    if (!tableElementRef.current && tableElement) {
      tableElementRef.current = tableElement;
    }
  });

  const resourceGroupLaneWillUnmount = useLatestCallback(function resourceGroupLaneWillUnmount(props) {
    const { el } = props as { el: HTMLElement };
    const tableElement = el.closest('table');

    // 多个 group 时，它会被卸载多次，所以需要判断卸载时 table container 是否存在，否则会误清除 table container
    if (!tableElement) {
      tableElementRef.current = null;
    }
  });

  const handleExpandAllChange = useLatestCallback(function handleExpandAllChange(isExpandAll: boolean) {
    Object.values(groupSourceDataMap).forEach((item) => {
      if (item.element && item.laneElement) {
        item.element.style.height = isExpandAll ? EXPAND_GROUP_HEIGHT : COLLAPSE_GROUP_HEIGHT;
        item.laneElement.style.height = isExpandAll ? EXPAND_GROUP_HEIGHT : COLLAPSE_GROUP_HEIGHT;
      }
    });
  });

  const resourceAreaHeaderContent = useLatestCallback(function resourceAreaHeaderContent() {
    return <LodgingExpandAll onExpandAllChange={handleExpandAllChange} />;
  });

  return {
    resources,
    isEmptyResource,
    renderResourceLabelContent,
    resourceGroupLaneClassNames,
    resourceGroupLabelClassNames,
    renderResourceGroupLabelContent,
    renderResourceGroupLaneContent,
    resourceGroupLabelDidMount,
    resourceGroupLaneDidMount,
    resourceGroupLaneWillUnmount,
    isAllResourceGroupNotExpanded,
    resourceAreaHeaderContent,
  };
};
