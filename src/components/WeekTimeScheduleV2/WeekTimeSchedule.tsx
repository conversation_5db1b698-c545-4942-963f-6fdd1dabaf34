import classNames from 'classnames';
import React from 'react';
import { useSetState } from 'react-use';
import { dayOfWeeks } from '../../store/business/business.boxes';
import { type FullWeekDay, FullWeekDayList } from '../../store/onlineBooking/models/OnlineBookingPreference';
import { useControllableValue } from '../../utils/hooks/useControlledValue';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { memo } from '../../utils/react';
import { CopyPasteModal } from './CopyPasteModal';
import { WeekTimePeriodRow, type WeekTimePeriodRowProps } from './WeekTimePeriodRow';
import type { TimePeriod, WeekTimeScheduleValue } from './types';

export interface TimesProps {
  value?: TimePeriod[];
  defaultValue?: TimePeriod[];
  onChange?: (v: TimePeriod[]) => void;
  onAdd?: (v: TimePeriod[]) => void;
  maxTime?: number;
}

export interface ExtraCopyProps {
  days: FullWeekDay[];
  day: number;
}

export interface WeekTimeScheduleProps<T extends Partial<WeekTimeScheduleValue> | null = Partial<WeekTimeScheduleValue>>
  extends Pick<WeekTimePeriodRowProps, 'renderEmpty' | 'getDefaultTimePeriod'> {
  className?: string;
  value?: T;
  onChange?: (val: T) => void;
  onCheckChange?: (v: { day: number; checked: boolean }) => void;
  onCopy?: (v: ExtraCopyProps) => void;
  children?: (day: number) => React.ReactNode;
  timeFormat?: string;
}

interface DateState {
  day: number;
  fullWeekDay: FullWeekDay;
  dayKey: Lowercase<FullWeekDay>;
  modalVisible: boolean;
}

export const WeekTimeSchedule = memo(<T extends Partial<WeekTimeScheduleValue>>(props: WeekTimeScheduleProps<T>) => {
  const { className, renderEmpty, getDefaultTimePeriod, children, onCheckChange, onCopy, timeFormat } = props;
  const [value, setValue] = useControllableValue<Partial<WeekTimeScheduleValue>>(props, { defaultValue: {} });
  const [currentDayState, setCurrentDayState] = useSetState<Partial<DateState>>({});

  const handleOpenCopyModal = useLatestCallback(
    (day: number, fullWeekDay: FullWeekDay, dayKey: Lowercase<FullWeekDay>) => {
      setCurrentDayState({
        day,
        fullWeekDay,
        dayKey,
        modalVisible: true,
      });
    },
  );

  const handleCopy = useLatestCallback(async (days: FullWeekDay[]) => {
    const { day, dayKey } = currentDayState as Required<DateState>;
    const times = value[dayKey!] ?? [];
    const lowerDays = days.map((i) => i.toLowerCase() as Lowercase<FullWeekDay>);
    const newValues = lowerDays.reduce(
      (pre, day) => ({
        ...pre,
        [day]: [...times],
      }),
      {} as Partial<WeekTimeScheduleValue>,
    );
    setValue((pre) => ({ ...pre, ...newValues }));
    onCopy?.({ day, days });
  });

  return (
    <div className={classNames('moe-flex moe-flex-col', className)}>
      {dayOfWeeks.sundayFirst.map((day) => {
        const dayKey: Lowercase<FullWeekDay> = FullWeekDayList[day].toLowerCase() as Lowercase<FullWeekDay>;
        const times = value[dayKey] ?? [];

        return (
          <WeekTimePeriodRow
            key={day}
            day={day}
            value={times}
            onChange={(times) => {
              setValue((preObj) => ({ ...preObj, [dayKey]: times }));
            }}
            getDefaultTimePeriod={getDefaultTimePeriod}
            renderEmpty={renderEmpty}
            onCopy={(fullWeekDay) => handleOpenCopyModal(day, fullWeekDay, dayKey)}
            onCheckChange={(v) => onCheckChange?.({ day, checked: v })}
            timeFormat={timeFormat}
          >
            {children?.(day)}
          </WeekTimePeriodRow>
        );
      })}
      <CopyPasteModal
        day={currentDayState.fullWeekDay}
        visible={currentDayState.modalVisible}
        onClose={() => setCurrentDayState({ modalVisible: false })}
        onApply={handleCopy}
      />
    </div>
  );
});
