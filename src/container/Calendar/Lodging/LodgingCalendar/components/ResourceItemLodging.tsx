import { Text } from '@moego/ui';
import React, { memo } from 'react';

export interface ResourceItemLodgingProps {
  title: string;
}

export const ResourceItemLodging = memo<ResourceItemLodgingProps>(({ title }) => {
  return (
    <div>
      <div className="moe-min-h-[42px] moe-translate-y-[-12px] moe-flex moe-flex-col moe-justify-center moe-text-center moe-py-s moe-px-[12px]">
        <Text variant="small" className="moe-text-secondary moe-text-wrap">
          {title}
        </Text>
      </div>
    </div>
  );
});
