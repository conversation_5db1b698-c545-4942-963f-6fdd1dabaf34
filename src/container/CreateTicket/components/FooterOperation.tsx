/* eslint-disable sonarjs/no-nested-conditional */
import { useDispatch, useStore } from 'amos';
import { Popconfirm } from 'antd';
import React, { useCallback } from 'react';
import { useHistory } from 'react-router';
import { Button } from '../../../components/Button/Button';
import { Condition } from '../../../components/Condition';
import { WithPermission } from '../../../components/GuardRoute/WithPermission';
import { AppliedDiscountCodePopover } from '../../../components/Invoice/AppliedDiscountCodePopover';
import { modalApi } from '../../../components/Modal/Modal';
import { toastApi } from '../../../components/Toast/Toast';
import { PATH_GROOMING_CALENDAR } from '../../../router/paths';
import { verifyAmount } from '../../../store/payment/actions/private/payment.actions';
import { selectApptVaccineConfirmText } from '../../../store/pet/petVaccineBinding.selectors';
import { isNormal } from '../../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE } from '../../../utils/DateTimeUtil';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { ReportActionName } from '../../../utils/reportType';
import { reportData } from '../../../utils/tracker';
import {
  type CreateApptReqParams,
  buildPreviewParamsForAdvanceEdit,
  buildSaveApptParamsForAdvanceEdit,
} from '../../Appt/components/RepeatSeries/adapters/ApptParamAdaptersForAdvanceEdit';
import { useRepeatSeriesState } from '../../Appt/components/RepeatSeries/hooks/useRepeatSeriesState';
import { useInvoiceReinvent } from '../../PaymentFlow/hooks/useInvoiceReinvent';
import { useChangeTicket } from '../hooks/useChangeTicket';
import { useEstimatedTotal } from '../hooks/useEstimatedTotal';
import { useSubmitTicket } from '../hooks/useSubmitTicket';
import { useTicketFieldsEdit } from '../hooks/useTicketFieldsEdit';
import { useTicketRestoreState, useTicketStoreCtx } from '../hooks/useTicketForm';
import { FooterFixed } from './FooterOperation.style';
import { apptReporter } from '../../../utils/reportData/reporter/apptReporter';

export function FooterOperation() {
  const { validateFormWithReason, cancelCreateTicket } = useChangeTicket();
  const { postAppointment, postWaiting, getServiceFormParams } = useSubmitTicket();

  const ticketStore = useTicketStoreCtx();
  const [
    {
      isWaitingListTicket,
      isOB,
      ticketId,
      invoiceId,
      copyTicketId,
      date: serviceDate,
      time,
      saveUpdateProps,
      repeatModalProps,
      petsService,
      currentClient,
      isSameTime,
    },
    updateTicketStore,
  ] = ticketStore;
  const currentClientId = currentClient.customerId;
  const estimatedTotal = useEstimatedTotal();
  const store = useStore();
  const dispatch = useDispatch();
  const ticketFieldsEdit = useTicketFieldsEdit();
  const history = useHistory();
  const { isEnableToNewFlow } = useInvoiceReinvent();

  // repeat appt 相关流程
  const { isRepeatPreviewMode, previewRepeat, openPreviewDrawer, isRepeatEditMode } = useRepeatSeriesState();

  const renderMessageWarning = useCallback((msg: string) => toastApi.error(msg), []);
  const isSaveMode = ticketId && !isWaitingListTicket && !isOB;
  const validateForm = (alertWarning: boolean) => {
    // 校验是否选择date time
    const { disabled, reason } = validateFormWithReason();
    if (disabled) {
      alertWarning && renderMessageWarning(reason);
    }
    return !disabled;
  };

  const handleClick = useSerialCallback(async () => {
    if (!ticketId) {
      await handleSubmit();
      return;
    }
    // 需要先 validate form 拿到错误提示，否则在没 time 的情况下，下方 getServiceFormParams 或 verifyAmount 会抛错 / 报错
    const isValid = validateForm(true);
    if (!isValid) {
      return;
    }
    if (!isEnableToNewFlow) {
      const serviceParams = getServiceFormParams();
      const params = {
        groomingId: +ticketId,
        serviceList: serviceParams?.petServices,
      };
      const rsp = await dispatch(verifyAmount(params));

      if (rsp?.refundAmount > 0) {
        return updateTicketStore({
          invoiceRefundProps: { callback: handleSubmit, showRefundWarnings: true, invoiceId: rsp.invoiceId },
        });
      }
    }
    updateTicketStore({ invoiceRefundProps: null });
    await handleSubmit();
  });

  const { saveState } = useTicketRestoreState(ticketStore);
  const handleFinalSubmit = useSerialCallback(async () => {
    if (isRepeatPreviewMode) {
      reportData(ReportActionName.RepeatAdvancedEditPreview);
      const params = getServiceFormParams() as CreateApptReqParams;
      const previewParams = buildPreviewParamsForAdvanceEdit(petsService);
      const saveApptParams = buildSaveApptParamsForAdvanceEdit(params, {
        customerId: currentClientId,
        allPetsStartAtSameTime: isSameTime,
      });
      await previewRepeat({
        previewParams,
        saveApptParams,
        existAppointmentId: ticketId,
        ...((ticketFieldsEdit.has('apptStartDate') || ticketFieldsEdit.has('apptStartTime')) && isRepeatEditMode
          ? {
              updateAppointmentDate: serviceDate.format(DATE_FORMAT_EXCHANGE),
              updateAppointmentStartTime: time?.getMinutes(),
            }
          : undefined),
      });
      openPreviewDrawer('advance');
      saveState();
      history.push(
        PATH_GROOMING_CALENDAR.stated({
          backCalendarView: 'Day',
          ticketId: +(ticketId || 0),
        }),
      );
    } else {
      reportData(ReportActionName.apptAdvancedEditBookNowClick);
      apptReporter.setBookNowFrom('advancedEdit');
      apptReporter.reportCreateApptDuration();
      await postAppointment();
    }
  });

  const handleSubmit = useSerialCallback(async () => {
    if (validateForm(true)) {
      // 若 repeat 规则变动，直接进入 repeat preview，无需再选择 apply 逻辑
      if (!isRepeatPreviewMode && repeatModalProps.repeatId && !copyTicketId && ticketFieldsEdit.isNeedShowApplyModal) {
        updateTicketStore({ saveUpdateProps: { ...saveUpdateProps, visible: true } });
      } else {
        const petIds = petsService.map((i) => i.petId);
        const tip = store.select(selectApptVaccineConfirmText(petIds, serviceDate.format(DATE_FORMAT_EXCHANGE)));
        if (tip) {
          modalApi.confirm({
            title: 'Vaccine expired',
            content: tip,
            okText: 'Confirm',
            onOk: () => handleFinalSubmit(),
          });
        } else {
          await handleFinalSubmit();
        }
      }
    }
  });

  const submitBtnText = isRepeatPreviewMode
    ? isSaveMode
      ? 'Preview and save'
      : 'Preview'
    : isSaveMode
      ? 'Save'
      : 'Book now';

  return (
    <FooterFixed>
      <div className="ticket-footer-fixed">
        <p className="total-money">Estimated total: {estimatedTotal}</p>
        <Condition if={isNormal(invoiceId)}>
          <AppliedDiscountCodePopover invoiceId={+invoiceId!} />
        </Condition>
        <Popconfirm
          placement="top"
          title="Are you sure you want to leave without saving?"
          onConfirm={cancelCreateTicket}
          okText="Yes"
          cancelText="No"
        >
          <Button btnType="white-border" buttonRadius="circle" className="moe-ml-[16px]">
            Cancel
          </Button>
        </Popconfirm>
        {(!isWaitingListTicket || (isWaitingListTicket && ticketId)) && (
          <WithPermission
            permissions={(permissions) => {
              if (isSaveMode) {
                return permissions.has('canAdvancedEditTicket');
              }
              return permissions.has('canAdvancedEditTicket') && permissions.has('createAppointment');
            }}
          >
            <Button
              btnType="primary"
              buttonRadius="circle"
              style={{ marginLeft: 16 }}
              onClick={handleClick}
              loading={handleClick.isBusy()}
            >
              {submitBtnText}
            </Button>
          </WithPermission>
        )}
        {isWaitingListTicket && (
          <Button
            btnType="primary"
            buttonRadius="circle"
            className="footer-waiting-btn"
            onClick={() => {
              if (!currentClientId) {
                renderMessageWarning('Please select client');
                return;
              }
              if (validateForm(true)) {
                postWaiting();
              }
            }}
          >
            {ticketId ? 'Updated to waitlist' : 'Add to waitlist'}
          </Button>
        )}
      </div>
    </FooterFixed>
  );
}
