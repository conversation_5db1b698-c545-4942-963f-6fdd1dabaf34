import { Switch, Text } from '@moego/ui';
import { useDispatch } from 'amos';
import React, { memo } from 'react';
import { OBTestIds } from '../../../../../../../../../../config/testIds/onlineBooking';
import { setAvailabilityDraftSettings } from '../../../../../../../../../../store/onlineBooking/actions/private/availabilityGrooming.actions';
import { type DraftAvailableSlotRecord } from '../../../../../../../../../../store/onlineBooking/settings/availabilityGrooming.boxes';
import { TimePreferenceSettingItem } from './TimePreferenceSettingItem';

export const BySlotShowOneAvailableTime = memo<{ draftSettings: DraftAvailableSlotRecord }>((props) => {
  const { draftSettings } = props;
  const dispatch = useDispatch();

  return (
    <>
      <TimePreferenceSettingItem label="Next available slot" labelClassName="moe-mb-[16px]">
        <Switch
          isSelected={draftSettings.bySlotShowOneAvailableTime}
          onChange={(checked) => {
            dispatch(setAvailabilityDraftSettings({ bySlotShowOneAvailableTime: checked }));
          }}
          data-testid={OBTestIds.SettingNextAvailableSlotSwitch}
        >
          Only show the next available slot
        </Switch>
      </TimePreferenceSettingItem>
      <Text variant="caption" className="moe-text-tertiary">
        This setting only applies when booking an appointment for a single pet via Online Booking. All available slots
        will still be shown when booking for multiple pets.
      </Text>
    </>
  );
});
