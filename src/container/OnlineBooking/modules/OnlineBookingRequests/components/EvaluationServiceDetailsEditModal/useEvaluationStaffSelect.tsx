import React, { useState } from 'react';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { EvaluationStaffPicker } from '../../../../../Appt/components/Evaluation/EvaluationStaffPicker';
import { useEvaluationAutoAssignStaff } from '../../../../../Appt/hooks/useEvaluationAutoAssignStaff';

export interface EvaluationServiceInfo {
  evaluationId: string;
  appointmentDate: string;
  serviceStartTime: number;
  serviceTime: number;
}

export const useEvaluationStaffSelect = (defaultStaffId?: string, params?: EvaluationServiceInfo) => {
  const [staffId, setStaffId] = useState<string | undefined>(defaultStaffId);

  /**
   * 弹窗内自动填充条件
   * 1. 有 evaluationId / evaluationId 发生变化
   * 2. 未指定 staffId，这里有可能会在 OB Request Modal 里面先指定
   */
  useEvaluationAutoAssignStaff({
    evaluationId: params?.evaluationId,
    defaultStaffId: defaultStaffId,
    onStaffReady(staffId) {
      setStaffId(staffId);
    },
  });

  const getStaffId = useLatestCallback(() => staffId);

  const renderStaffSelector = useLatestCallback(() => {
    if (!params) return null;
    const { evaluationId, appointmentDate, serviceStartTime, serviceTime } = params;
    return (
      <div className="moe-w-full">
        <EvaluationStaffPicker
          label="Staff assignment"
          evaluationId={evaluationId}
          appointmentDate={appointmentDate}
          serviceStartTime={serviceStartTime}
          serviceTime={serviceTime}
          placeholder="Assign a staff if needed"
          value={staffId ? +staffId : undefined}
          onChange={(value) => {
            setStaffId(value?.toString());
          }}
        />
      </div>
    );
  });

  return {
    getStaffId,
    renderStaffSelector,
  };
};
