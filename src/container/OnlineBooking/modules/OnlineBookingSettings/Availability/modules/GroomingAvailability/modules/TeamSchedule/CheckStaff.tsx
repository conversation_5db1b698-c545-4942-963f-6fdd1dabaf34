import { Avatar, Checkbox, cn } from '@moego/ui';
import { useSelector } from 'amos';
import React from 'react';
import { OBTestIds } from '../../../../../../../../../config/testIds/onlineBooking';
import { selectStaffRole } from '../../../../../../../../../store/business/role.selectors';
import { staffMapBox } from '../../../../../../../../../store/staff/staff.boxes';
import { StaffBox } from './CheckStaff.style';

export interface CheckStaffProps {
  className?: string;
  staffId: number;
  isSelected: boolean;
  isChecked: boolean;
  onStaffCheck?: (checked: boolean, meta: { staffId: number }) => void;
  onStaffSelect?: (staffId: number) => void;
}

export const CheckStaff = (props: CheckStaffProps) => {
  const { className, staffId, isSelected, isChecked, onStaffSelect, onStaffCheck } = props;
  const [staff, role] = useSelector(staffMapBox.mustGetItem(staffId), selectStaffRole(staffId));

  if (!staff.isShowOnCalendar) {
    return null;
  }

  return (
    <StaffBox
      data-staff-id={staffId}
      className={cn(
        'moe-flex moe-items-center moe-py-[16px] moe-px-[24px] moe-cursor-pointer',
        {
          'moe-bg-brand-subtle': isSelected,
          active: isSelected,
        },
        className,
      )}
      onClick={() => onStaffSelect?.(staffId)}
      data-testid={OBTestIds.SettingAvailabilityStaff}
    >
      <Checkbox
        isSelected={isChecked}
        onChange={(nextChecked) => {
          onStaffCheck?.(nextChecked, { staffId });
        }}
      />
      <Avatar.Staff
        color={staff.getColorCode()}
        name={staff.fullName()}
        size="s"
        src={staff.avatarPath}
        className="moe-shrink-0 moe-ml-s"
      />
      <div className="moe-flex-1 moe-min-w-0 moe-ml-xs">
        <div className="moe-truncate moe-text-regular-short moe-text-primary">{staff.firstName}</div>
        <div className="moe-truncate moe-text-xs moe-text-secondary moe-font-regular">{role?.name}</div>
      </div>
    </StaffBox>
  );
};
