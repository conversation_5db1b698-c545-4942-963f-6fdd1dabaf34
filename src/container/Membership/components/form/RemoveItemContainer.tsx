import { Condition, IconButton, MajorTrashOutlined } from '@moego/ui';
import classNames from 'classnames';
import React from 'react';

interface RemoveItemContainerProps {
  onRemove: () => void;
  isRemoveable: boolean;
  children: React.ReactNode;
  className?: string;
}

export const RemoveItemContainer = ({ onRemove, isRemoveable, children, className }: RemoveItemContainerProps) => {
  return (
    <div className={classNames('moe-flex moe-justify-between moe-w-full moe-mb-[16px]', className)}>
      {children}
      <Condition if={isRemoveable}>
        <IconButton
          className="moe-bg-white moe-mt-1"
          onPress={onRemove}
          variant="primary"
          icon={<MajorTrashOutlined />}
        />
      </Condition>
    </div>
  );
};
