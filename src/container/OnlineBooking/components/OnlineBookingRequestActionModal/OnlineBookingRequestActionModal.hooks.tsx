import { Provider, useStore } from 'amos';
import React from 'react';
import { render, unmountComponentAtNode } from 'react-dom';
import {
  OBAutoMessageActionTypeMap,
  useOBAutoMessage,
} from '../../../settings/Settings/AutoMessageSetting/hooks/useOBAutoMessage';
import { DEFAULT_NOTIFY, useOnlineBookingNotifyClientsModal } from './OnlineBookingNotifyClientsModal';
import { OnlineBookingRequestActionModal } from './OnlineBookingRequestActionModal';
import { type OnOkParams, type OnlineBookingRequestActionModalProps } from './OnlineBookingRequestActionModal.options';

/**
 * 为啥我们需要手动挂载一个组件呢？
 *
 * 背景：首先他的场景很简单，就是一个弹窗，然后获取弹窗的结果，进入后续操作流程，
 *      我们在calendar的ob request也会用到它，但是calendar的设计是
 *      如果request关闭，组件就会被销毁，然后我们需要在calendar 顶层先注册这个组件。
 *      所以我认为这样太繁琐了，而且会让calendar的代码变得很臃肿。
 *
 * 他的优点是，按需使用，无需在全局注册，也无需在上层引用，并且不会丢失状态和流程控制权
 * 缺点是，暂时还没支持完整的动画过程，并且不支持与当前页面实时的数据同步。
 * @deprecated 请用 useObRequestAndNotifyModal 替代，这个为底层使用
 */
export const useShowOBRequestActionModal = () => {
  const store = useStore();

  return <T extends any>(props: OnlineBookingRequestActionModalProps<T>): Promise<T> => {
    return new Promise((resolve: (v: any) => void, reject) => {
      const showModal = props.shouldShowRefund;
      const el = document.createElement('div');
      const close = () => {
        try {
          unmountComponentAtNode(el);
          if (showModal) {
            // 不展示时没有子元素
            document.body.removeChild(el);
          }
        } catch (e) {
          console.log(e);
        }
      };

      document.body.appendChild(el);

      render(
        <Provider store={store}>
          <OnlineBookingRequestActionModal
            {...props}
            showModal={showModal}
            onOk={(data: OnOkParams) => {
              close();
              resolve(data);
            }}
            onCancel={() => {
              close();
              reject();
            }}
          />
        </Provider>,
        el,
      );
    });
  };
};

/**
 * OB Request 处理弹窗和通知客户弹窗分离
 */
export const useObRequestAndNotifyModal = () => {
  const showOBRequestActionModal = useShowOBRequestActionModal();
  const showOBNotifyModal = useOnlineBookingNotifyClientsModal();
  const { getIsOBAutoMessageEnabled } = useOBAutoMessage();

  const showObRequestAndNotifyModal = async (
    props: OnlineBookingRequestActionModalProps & {
      /** 用于 ActionCell 中，在完成接受请求后结束 loading */
      setLoading?: (loading: boolean) => void;
      showNotifyModal?: boolean;
      /** notify 之前的回调函数，如果失败则不出现 notify */
      beforeNotify?: () => Promise<void>;
    },
  ) => {
    const {
      showNotifyModal: internalShowNotifyModal = true,
      beforeNotify,
      hasPetParentAppAccount,
      serviceItemTypes = [],
      type: actionType,
    } = props;
    const isOBAutoMessageEnabled = getIsOBAutoMessageEnabled(OBAutoMessageActionTypeMap[actionType], serviceItemTypes);
    const {
      appointmentId,
      showNotifyModal,
      success: requestSuccess,
    } = await showOBRequestActionModal<OnOkParams>(props);
    let success = requestSuccess;
    const result = { notify: DEFAULT_NOTIFY, appointmentId };

    try {
      if (success) {
        await beforeNotify?.();
      }
    } catch {
      success = false;
    }

    if (props.setLoading) {
      props.setLoading(false);
    }

    if (success && showNotifyModal && internalShowNotifyModal && isOBAutoMessageEnabled) {
      const { notify } = await showOBNotifyModal({
        appointmentId,
        type: props.type,
        hasPetParentAppAccount,
      });
      result.notify = notify;
    }

    return result;
  };

  return showObRequestAndNotifyModal;
};
