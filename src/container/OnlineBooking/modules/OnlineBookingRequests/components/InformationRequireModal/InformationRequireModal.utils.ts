/**
 * Information Require 相关的数据结构处理的转化函数
 * 核心概念：ServiceDetails, IncompleteDetails
 * 本模块核心定位：
 * 1. 基于不完整的 IncompleteDetails 生成表单信息，接受用户的输入
 * 2. 根据用户的表单信息输入，生成后端接口需要的 ServiceDetails 相关参数
 */
import { type IncompleteDetails } from '@moego/api-web/moego/api/online_booking/v1/booking_request_api';
import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import dayjs from 'dayjs';
import { groupBy, map } from 'lodash';
import { isNormal } from '../../../../../../store/utils/identifier';
import {
  type EvaluationDetail,
  type AcceptRequestParams,
  type IncompleteDetailFormItems,
  type InformationRequireInput,
  type LodgingItemsDetail,
  type LodgingItemsDetailInput,
  type StaffTimeDetail,
  type StaffTimeDetailInput,
} from './InformationRequireModal.types';

/**
 * 将后台缺失的数据转换为前端使用的表单项
 *
 * @param incompleteDetails - 原始数据
 * @param useMergedDatePointDetail - 是否使用合并的 grooming detail，目前主要针对 D 带 G / require staff AddOn 的情况
 */
export const incompleteDetailsToFormItems = (
  incompleteDetails: IncompleteDetails,
  useMergedDatePointDetail?: boolean,
): IncompleteDetailFormItems => {
  const lodgingItems: LodgingItemsDetail[] = [];
  const staffTimeItems: StaffTimeDetail[] = [];
  const evaluationItems: EvaluationDetail[] = [];
  const {
    boardingServices,
    boardingAddons,
    daycareServices,
    daycareAddons,
    evaluationServices,
    // grooming 单独处理
    groomingServices,
    groomingAddons,
  } = incompleteDetails;

  boardingServices.forEach(({ service, serviceDetail, missingEvaluation }) => {
    const { id: serviceDetailId, petId, serviceId, startDate, endDate, startTime, endTime } = serviceDetail;
    lodgingItems.push({
      careType: ServiceItemType.BOARDING,
      serviceDetailId,
      petId,
      serviceId,
      startDate,
      endDate,
      startTime,
      endTime,
      service,
      serviceDetail,
    });
    if (missingEvaluation) {
      evaluationItems.push({
        petId,
        serviceDetailId,
        careType: ServiceItemType.BOARDING,
        evaluation: missingEvaluation,
        service,
        serviceDetail,
      });
    }
  });

  boardingAddons.forEach(({ addonDetail, service }) => {
    const { id: serviceDetailId, petId, addOnId, specificDates, dateType } = addonDetail;
    staffTimeItems.push({
      serviceDetailId,
      petId,
      serviceId: addOnId,
      serviceName: service.name,
      specificDates,
      startDate: '',
      careType: ServiceItemType.BOARDING,
      serviceType: ServiceType.ADDON,
      isMergedRecord: false,
      petDetailDateMap: new Map(),
      dateType,
    });
  });

  daycareServices.forEach(({ service, serviceDetail, missingEvaluation }) => {
    const { id: serviceDetailId, petId } = serviceDetail;
    if (missingEvaluation) {
      evaluationItems.push({
        petId,
        serviceDetailId,
        careType: ServiceItemType.DAYCARE,
        service,
        evaluation: missingEvaluation,
        serviceDetail,
      });
    }
  });

  const originDaycareServiceDetailList = daycareAddons.map(({ addonDetail, service }) => {
    const { id: serviceDetailId, petId, addOnId, specificDates } = addonDetail;
    // 特殊情况：在 multiple day daycare request 的场景，requireDedicatedStaff 的 addOn 是 date point 类型，但存储是共用 specificDates，所以这里特殊处理以支持合并
    const { requireDedicatedStaff } = service;
    return {
      serviceDetailId,
      petId,
      serviceId: addOnId,
      serviceName: service.name,
      specificDates: requireDedicatedStaff ? [] : specificDates,
      startDate: requireDedicatedStaff ? specificDates[0] || '' : '',
      careType: ServiceItemType.DAYCARE,
      serviceType: ServiceType.ADDON,
      isMergedRecord: false,
      petDetailDateMap: new Map(),
    };
  });
  if (useMergedDatePointDetail) {
    map(groupBy(originDaycareServiceDetailList, 'serviceId'), (item) => {
      return mergePetDetailToSpecificDates(item);
    }).forEach((item) => {
      staffTimeItems.push(item);
    });
  } else {
    originDaycareServiceDetailList.forEach((item) => {
      staffTimeItems.push(item);
    });
  }

  evaluationServices.forEach(({ serviceDetail, evaluation }) => {
    const { id: serviceDetailId, petId } = serviceDetail;
    evaluationItems.push({
      petId,
      serviceDetailId,
      careType: ServiceItemType.EVALUATION,
      evaluation,
      serviceDetail,
    });
  });

  const origGroomingServiceDetailList = groomingServices.map(({ service, serviceDetail }) => {
    const { id: serviceDetailId, petId, serviceId, startDate, startTime, staffId, dateType } = serviceDetail;
    return {
      serviceDetailId,
      petId,
      serviceId,
      serviceName: service.name,
      specificDates: [],
      startDate: startDate || '',
      careType: ServiceItemType.GROOMING,
      serviceType: ServiceType.SERVICE,
      isMergedRecord: false,
      petDetailDateMap: new Map(),
      // 只有 grooming 场景有默认值
      defaultStartTime: isNormal(startTime) ? startTime : undefined,
      defaultStaffId: isNormal(staffId) ? staffId : undefined,
      dateType,
    };
  });

  if (useMergedDatePointDetail) {
    map(groupBy(origGroomingServiceDetailList, 'serviceId'), (item) => {
      return mergePetDetailToSpecificDates(item);
    }).forEach((item) => {
      staffTimeItems.push(item);
    });
  } else {
    origGroomingServiceDetailList.forEach((item) => {
      staffTimeItems.push(item);
    });
  }

  const origAddOnServiceDetailList = groomingAddons.map(({ service, addonDetail }) => {
    const { id: serviceDetailId, petId, addOnId, startDate, startTime, staffId } = addonDetail;
    return {
      serviceDetailId,
      petId,
      serviceId: addOnId,
      serviceName: service.name,
      specificDates: [],
      startDate,
      careType: ServiceItemType.GROOMING,
      serviceType: ServiceType.ADDON,
      isMergedRecord: false,
      petDetailDateMap: new Map(),
      // 只有 grooming 场景有默认值
      defaultStartTime: isNormal(startTime) ? startTime : undefined,
      defaultStaffId: isNormal(staffId) ? staffId : undefined,
    };
  });

  if (useMergedDatePointDetail) {
    map(groupBy(origAddOnServiceDetailList, 'serviceId'), (item) => {
      return mergePetDetailToSpecificDates(item);
    }).forEach((item) => {
      staffTimeItems.push(item);
    });
  } else {
    origAddOnServiceDetailList.forEach((item) => {
      staffTimeItems.push(item);
    });
  }

  return {
    lodgingItems,
    staffTimeItems,
    evaluationItems,
  };
};

/**
 * 将用户的表单输入，转换为 Accept 提交时的参数
 */
export const buildAcceptParamsFormInput = (
  formItems: IncompleteDetailFormItems,
  userInput: InformationRequireInput,
): AcceptRequestParams => {
  /** build map first */
  const staffTimeDetailMap = new Map<string, StaffTimeDetail>();
  const { staffTimeItems } = formItems;
  staffTimeItems.forEach((item) => {
    staffTimeDetailMap.set(getStaffTimeItemKey(item), item);
  });
  /** build accept input params */
  const p: AcceptRequestParams = {
    boardingServices: [],
    daycareServices: [],
    boardingAddons: [],
    daycareAddons: [],
    groomingServices: [],
    groomingAddons: [],
    evaluationServices: [],
    createEvaluationRequests: [],
  };
  const {
    lodgingInputList: lodgingInput,
    staffTimeInputList: staffTimeInput,
    evaluationInputList,
    createEvaluationInputList,
  } = userInput;
  lodgingInput.forEach((item) => {
    if (item.careType === ServiceItemType.BOARDING) {
      p.boardingServices.push({
        id: item.serviceDetailId,
        lodgingId: item.lodgingUnitId,
      });
    } else if (item.careType === ServiceItemType.DAYCARE) {
      p.daycareServices.push({
        id: item.serviceDetailId,
        lodgingId: item.lodgingUnitId,
      });
    }
  });

  staffTimeInput.forEach((item) => {
    const key = getStaffTimeItemKey(item);
    const currentDetail = staffTimeDetailMap.get(key);
    if (!currentDetail) return;
    let relatedDetails: StaffTimeDetail[] = [currentDetail];
    if (currentDetail.isMergedRecord) {
      relatedDetails = splitPetDetailFromMerged(currentDetail);
    }
    relatedDetails.forEach((detail) => {
      if (detail.careType === ServiceItemType.BOARDING && detail.serviceType === ServiceType.ADDON) {
        p.boardingAddons.push({
          id: detail.serviceDetailId,
          staffId: item.staffId,
          startTime: item.startTime,
        });
      } else if (detail.careType === ServiceItemType.DAYCARE && detail.serviceType === ServiceType.ADDON) {
        p.daycareAddons.push({
          id: detail.serviceDetailId,
          staffId: item.staffId,
          startTime: item.startTime,
        });
      } else if (detail.careType === ServiceItemType.GROOMING && detail.serviceType === ServiceType.SERVICE) {
        p.groomingServices.push({
          id: detail.serviceDetailId,
          staffId: item.staffId,
          startTime: item.startTime,
        });
      } else if (detail.careType === ServiceItemType.GROOMING && detail.serviceType === ServiceType.ADDON) {
        p.groomingAddons.push({
          id: detail.serviceDetailId,
          staffId: item.staffId,
          startTime: item.startTime,
        });
      }
    });
  });
  evaluationInputList.forEach((item) => {
    p.evaluationServices.push(item);
  });
  createEvaluationInputList.forEach((item) => {
    p.createEvaluationRequests.push(item);
  });
  return p;
};

/**
 * 统一处理表单的 key 相关
 */
export const PET_DETAIL_ID_DIVIDER = ',';
export const KEY_FIELD_DIVIDER = '_';

export const getLodgingItemKey = (item: LodgingItemsDetail | LodgingItemsDetailInput) => {
  return [item.serviceDetailId, item.careType].join(KEY_FIELD_DIVIDER);
};

export const extractLodgingItemKey = (itemKey: string) => {
  const [serviceDetailId, careType] = itemKey.split(KEY_FIELD_DIVIDER);
  return { serviceDetailId, careType: +careType as ServiceItemType };
};

export const getStaffTimeItemKey = (item: StaffTimeDetail | StaffTimeDetailInput) => {
  return [item.serviceDetailId, item.careType, item.serviceType].join(KEY_FIELD_DIVIDER);
};

export const extractStaffTimeItemKey = (itemKey: string) => {
  const [serviceDetailId, careType, serviceType] = itemKey.split(KEY_FIELD_DIVIDER);
  return { serviceDetailId, careType: +careType as ServiceItemType, serviceType: +serviceType as ServiceType };
};

export const getEvaluationItemKey = (item: EvaluationDetail) => {
  return [item.serviceDetailId, item.careType].join(KEY_FIELD_DIVIDER);
};

export const extractEvaluationItemKey = (itemKey: string) => {
  const [serviceDetailId, careType] = itemKey.split(KEY_FIELD_DIVIDER);
  return { serviceDetailId, careType: +careType as ServiceItemType };
};

/**
 * 多合一和一分多的可逆实现
 */
export const mergePetDetailToSpecificDates = (petDetails: StaffTimeDetail[]): StaffTimeDetail => {
  const specificDatesSet = new Set<string>();
  const petDetailDateMap = new Map<string, string>();
  petDetails.forEach((petDetail) => {
    specificDatesSet.add(petDetail.startDate);
    petDetailDateMap.set(petDetail.serviceDetailId, petDetail.startDate);
    petDetail.specificDates.forEach((date) => specificDatesSet.add(date));
  });
  // 日期从小到大排序
  const specificDates = [...specificDatesSet].sort((a, b) => dayjs(a).diff(dayjs(b), 'day'));
  return {
    ...petDetails[0],
    serviceDetailId: [...petDetailDateMap.keys()].join(PET_DETAIL_ID_DIVIDER),
    petDetailDateMap,
    specificDates,
    isMergedRecord: true,
  } as StaffTimeDetail;
};

export const splitPetDetailFromMerged = (mergedPetDetail: StaffTimeDetail): StaffTimeDetail[] => {
  return [...mergedPetDetail.petDetailDateMap.entries()].map(([serviceDetailId, startDate]) => {
    return {
      ...mergedPetDetail,
      serviceDetailId,
      startDate,
      specificDates: [],
      isMergedRecord: false,
    };
  });
};

export const getPetMainServiceFormIncompleteFromItems = (incompleteFromItems: IncompleteDetailFormItems) => {
  const { lodgingItems, evaluationItems } = incompleteFromItems;
  const firstItem = lodgingItems[0] || evaluationItems[0];
  if (!firstItem) return null;
  return {
    service: firstItem.service,
    serviceDetail: firstItem.serviceDetail,
  };
};
