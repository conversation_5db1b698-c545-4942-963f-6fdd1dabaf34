import { datadogRum } from '@datadog/browser-rum';
import { MinorChevronDownOutlined } from '@moego/icons-react';
import { cn, Heading } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useLayoutEffect } from 'react';
import { lodgingViewExpandAllStore } from '../../../../../store/calendarLatest/lodgingCalendar.boxes';
import { DataDogActionName } from '../../../../../utils/logger';
import { ReportActionName } from '../../../../../utils/reportType';
import { reportData } from '../../../../../utils/tracker';
import { FirstGuideCollapse } from './FirstGuideCollapse';

export interface LodgingExpandAllProps {
  className?: string;
  onExpandAllChange?: (isExpandAll: boolean) => void;
}

export const LodgingExpandAll = memo<LodgingExpandAllProps>((props) => {
  const { onExpandAllChange } = props;
  const [isExpandAll] = useSelector(lodgingViewExpandAllStore);
  const dispatch = useDispatch();

  useLayoutEffect(() => {
    Promise.resolve().then(() => {
      datadogRum.stopDurationVital(isExpandAll ? DataDogActionName.EXPAND_ALL : DataDogActionName.COLLAPSE_ALL);
    });
  }, [isExpandAll]);

  return (
    <FirstGuideCollapse>
      <Heading
        size="6"
        className="moe-flex moe-items-center moe-cursor-pointer moe-select-none moe-underline moe-pl-[16px] moe-text-secondary moe-absolute moe-w-full moe-h-full moe-top-0"
        onClick={() => {
          const newIsExpandAll = !isExpandAll;
          dispatch(lodgingViewExpandAllStore.setState(newIsExpandAll));
          onExpandAllChange?.(newIsExpandAll);
          datadogRum.startDurationVital(newIsExpandAll ? DataDogActionName.EXPAND_ALL : DataDogActionName.COLLAPSE_ALL);
          reportData(ReportActionName.lodgingViewCollapseAll);
        }}
        ref={(view) => {
          if (view?.parentElement?.previousElementSibling instanceof HTMLElement) {
            view.parentElement.previousElementSibling.classList.add('moe-hidden');
          }
        }}
      >
        <MinorChevronDownOutlined
          className={cn('-moe-rotate-90', {
            'moe-transform moe-rotate-0': isExpandAll,
          })}
        />
        {isExpandAll ? 'Collapse' : 'Expand'}
      </Heading>
    </FirstGuideCollapse>
  );
});
