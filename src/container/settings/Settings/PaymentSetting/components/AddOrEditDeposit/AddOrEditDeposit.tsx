import { isNormal, useLatestCallback } from '@moego/finance-utils';
import { Form, Heading, Input, Spin, useForm, useFormState } from '@moego/ui';
import { useMount } from 'ahooks';
import { useDispatch, useSelector } from 'amos';
import React from 'react';
import { useHistory } from 'react-router';
import { toast<PERSON><PERSON> } from '../../../../../../components/Toast/Toast';
import { FullPageLayout } from '../../../../../../layout/FullPageLayout';
import { PATH_PAYMENT_DEPOSIT_ACTION, PATH_PAYMENT_SETTING } from '../../../../../../router/paths';
import {
  createDepositRule,
  type DepositRuleVM,
  listDepositRules,
  updateDepositRule,
} from '../../../../../../store/payment/actions/private/deposit.actions';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { useUnsavedConfirmGlobalV2 } from '../../../../../../utils/hooks/useUnsavedConfirmGlobalV2';
import { useRouteParams, useRouteQueryV2 } from '../../../../../../utils/RoutePath';
import { FieldClientGroup, FieldClientGroupValidator } from './components/FieldClientGroup';
import { FieldDateRange, FieldDateRangeValidator } from './components/FieldDateRange';
import { FieldDepositConfig, FieldDepositConfigValidator } from './components/FieldDepositConfig';
import { FieldServiceSelect, FieldServiceSelectValidator } from './components/FieldServiceSelect';

export function AddOrEditDeposit() {
  const history = useHistory();
  const dispatch = useDispatch();
  const [business] = useSelector(selectCurrentBusiness());

  const { id: ruleId } = useRouteQueryV2(PATH_PAYMENT_DEPOSIT_ACTION);
  const { action } = useRouteParams(PATH_PAYMENT_DEPOSIT_ACTION);
  const form = useForm<DepositRuleVM>({
    mode: 'onSubmit',
    defaultValues: {
      ruleName: null,
      clientGroup: null,
      serviceType: null,
      dateRange: null,
      depositConfig: null,
    },
  });

  const gotoDepositListPage = useLatestCallback(() => {
    history.push(PATH_PAYMENT_SETTING.build({ panel: 'deposit' }));
  });

  // 由于没有直接的接口来获取单个的 deposit rule 的详情，所以这里使用 listDepositRules 来获取
  const [formLoading, setFormLoading] = React.useState(true);
  const fetchDeposit = useLatestCallback(async () => {
    const list = await dispatch(listDepositRules());
    return list.activeRules.find((item) => item.id === ruleId) || list.inactiveRules.find((item) => item.id === ruleId);
  });

  const isEditMode = action === 'edit' && isNormal(ruleId);
  const loading = isEditMode && formLoading;

  const { isDirty } = useFormState({
    control: form.control,
  });

  useUnsavedConfirmGlobalV2({
    showConfirm: isDirty,
    modalProps: {
      title: 'Unsaved changes',
      content: 'You have unsaved changes. Are you sure you’re ready to leave?',
      onConfirm: () => {
        // 这个 CTA 是留在当前页面，所以抛个 error 中断后续流程
        throw Error('Back to edit');
      },
      onCancel: gotoDepositListPage,
      confirmText: 'Back to edit',
      cancelText: 'Leave anyway',
    },
  });

  useMount(async () => {
    if (isEditMode) {
      setFormLoading(true);
      form.reset(await fetchDeposit());
      setFormLoading(false);
    }
  });

  const handleClose = () => {
    history.push(PATH_PAYMENT_SETTING.build({ panel: 'deposit' }));
  };

  const handleSave = async () => {
    await form.handleSubmit(async (ruleVM: DepositRuleVM) => {
      form.reset(ruleVM, { keepDirty: false });
      if (isEditMode) {
        await dispatch(updateDepositRule(ruleId, ruleVM));
        toastApi.success('Changes saved');
      } else {
        await dispatch(createDepositRule(ruleVM));
        toastApi.success('New deposit rule added');
      }
      gotoDepositListPage();
    })();
  };

  return (
    <FullPageLayout onClose={handleClose} onSave={handleSave}>
      <div className="moe-w-full moe-flex moe-justify-center moe-mb-xl moe-mt-s">
        <div className="moe-w-[900px]">
          <Heading size="2" className="moe-mb-xl">
            {action === 'add' ? 'Add' : 'Edit'} deposit rule
          </Heading>
          <Spin
            isLoading={loading}
            classNames={{
              base: 'moe-w-full moe-h-full',
              container: 'moe-w-full moe-h-full',
              iconContainer: 'moe-top-[20%]',
            }}
          >
            <Form form={form} footer={null}>
              <Form.Item
                name="ruleName"
                label="Rule name"
                rules={{
                  required: 'Rule name is required',
                }}
              >
                <Input isRequired placeholder="Enter rule name" />
              </Form.Item>
              <Form.Item
                name="clientGroup"
                label="Client group"
                rules={{
                  validate: FieldClientGroupValidator,
                }}
              >
                <FieldClientGroup />
              </Form.Item>
              <Form.Item
                name="serviceType"
                label="Service type"
                rules={{
                  validate: FieldServiceSelectValidator,
                }}
              >
                <FieldServiceSelect />
              </Form.Item>
              <Form.Item
                name="dateRange"
                label="Date range"
                rules={{
                  validate: FieldDateRangeValidator,
                }}
              >
                <FieldDateRange />
              </Form.Item>
              <Form.Item
                name="depositConfig"
                rules={{
                  validate: FieldDepositConfigValidator,
                }}
              >
                <FieldDepositConfig business={business} />
              </Form.Item>
            </Form>
          </Spin>
        </div>
      </div>
    </FullPageLayout>
  );
}
