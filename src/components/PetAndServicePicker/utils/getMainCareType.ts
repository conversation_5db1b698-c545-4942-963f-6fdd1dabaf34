import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';

const { BOARDING, DAYCARE, DOG_WALKING, GROOMING, GROUP_CLASS, EVALUATION } = ServiceItemType;

export const getMainCareType = (v: Set<ServiceItemType> | ServiceItemType[]) => {
  const serviceItems = new Set(v);
  if (serviceItems.has(BOARDING)) {
    return BOARDING;
  }
  if (serviceItems.has(DAYCARE)) {
    return DAYCARE;
  }
  if (serviceItems.has(DOG_WALKING)) {
    return DOG_WALKING;
  }
  if (serviceItems.has(GROUP_CLASS)) {
    return GROUP_CLASS;
  }
  if (serviceItems.has(EVALUATION)) {
    return EVALUATION;
  }
  return GROOMING;
};
