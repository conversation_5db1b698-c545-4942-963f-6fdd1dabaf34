import { Checkbox, Controller, Form, Input, Radio, RadioGroup, Text, type UseFormReturn, useWatch } from '@moego/ui';
import { useMount } from 'ahooks';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { Condition } from '../../../../../../../../components/Condition';
import { selectCurrentBusiness } from '../../../../../../../../store/business/business.selectors';

export interface ApptCancelBeforeFinishFormV2Props {
  form: UseFormReturn<ApptCancelBeforeFinishFormValues>;
  refundableAmount: number;
  forfeitableAmount?: number;
  showForfeit?: boolean;
}

export interface ApptCancelBeforeFinishFormValues {
  refundPayment: boolean;
  noShow: {
    markNoShow: boolean;
    noShowFee: number;
  };
  cancelReason: string;
}

const MAX_NO_SHOW_FEE = 500;

export const ApptCancelBeforeFinishFormV2 = memo<ApptCancelBeforeFinishFormV2Props>((props) => {
  const { form, refundableAmount, showForfeit, forfeitableAmount } = props;
  const [business] = useSelector(selectCurrentBusiness);
  const isRefundPayment = useWatch({ control: form.control, name: 'refundPayment' });

  useMount(() => {
    if (showForfeit) {
      form.setValue('noShow.markNoShow', true);
      form.setValue('refundPayment', false);
    } else {
      form.setValue('refundPayment', true);
    }
  });

  const renderAlert = () => {
    if (!refundableAmount) return null;
    const refundText = (
      <>
        <span className="moe-text-danger moe-font-bold">{business.formatAmount(refundableAmount)}</span> paid by the
        client will be refunded
      </>
    );
    const forfeitText = (
      <>
        <span className="moe-font-bold">{business.formatAmount(forfeitableAmount!)}</span> paid by the client will be
        forfeited as no-show fee.
      </>
    );
    return (
      <div className="moe-bg-neutral-sunken-0 moe-rounded-[8px] moe-p-s moe-flex moe-flex-col moe-gap-xxs">
        <Text variant="regular" className="moe-text-primary">
          {isRefundPayment ? refundText : forfeitText}
        </Text>
      </div>
    );
  };

  const renderNoShowOption = () => {
    if (showForfeit) {
      return (
        <Controller
          control={form.control}
          name="noShow.markNoShow"
          render={({ field }) => {
            return (
              <div className="moe-flex moe-flex-col moe-gap-m">
                <Checkbox
                  className="moe-text-primary"
                  isSelected={field.value}
                  onChange={(value) => {
                    field.onChange(value);
                    !value && form.setValue('refundPayment', true);
                  }}
                >
                  Mark as no-show
                </Checkbox>
                <Condition if={field.value}>
                  <Controller
                    control={form.control}
                    name="refundPayment"
                    render={({ field }) => {
                      return (
                        <RadioGroup
                          className="moe-ml-[28px]"
                          value={field.value ? 1 : 0}
                          // eslint-disable-next-line sonarjs/no-nested-functions
                          onChange={(value) => field.onChange(Boolean(value))}
                        >
                          <Radio value={1}>Refund deposit</Radio>
                          <Radio value={0}>Forfeit deposit as no-show fee</Radio>
                        </RadioGroup>
                      );
                    }}
                  />
                </Condition>
              </div>
            );
          }}
        />
      );
    }

    return (
      <Controller
        control={form.control}
        name="noShow.markNoShow"
        render={({ field }) => {
          return (
            <div className="moe-flex moe-flex-col moe-gap-[8px]">
              <Checkbox className="moe-text-primary" isSelected={field.value} onChange={field.onChange}>
                Mark as no-show
              </Checkbox>
              <Condition if={field.value}>
                <div className="moe-ml-[28px]">
                  <div className="moe-bg-neutral-sunken-0 moe-rounded-[8px] moe-p-s moe-flex moe-flex-col moe-gap-xxs">
                    <Form.Item
                      label="No-show fee amount"
                      name="noShow.noShowFee"
                      rules={{
                        required: true,
                      }}
                    >
                      <Input.Number
                        maxValue={MAX_NO_SHOW_FEE}
                        precision={2}
                        className="moe-mt-xxs"
                        description={`Amount should be between 0 and ${MAX_NO_SHOW_FEE}.`}
                        minValue={0}
                        prefix={business.printCurrency()}
                        isRequired
                      ></Input.Number>
                    </Form.Item>
                  </div>
                </div>
              </Condition>
            </div>
          );
        }}
      />
    );
  };

  return (
    <Form form={form} className="moe-flex moe-flex-col moe-gap-m" footer={null}>
      {renderNoShowOption()}
      {renderAlert()}
      <Form.Item name="cancelReason" label="Cancel reason">
        <Input.TextArea placeholder="Input cancel reason" />
      </Form.Item>
    </Form>
  );
});

ApptCancelBeforeFinishFormV2.displayName = 'ApptCancelBeforeFinishFormV2';
