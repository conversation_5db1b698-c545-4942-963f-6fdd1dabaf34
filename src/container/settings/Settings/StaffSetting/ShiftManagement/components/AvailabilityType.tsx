import React, { useEffect } from 'react';
import { Heading, RadioGroup, Radio, Markup, Text, toast } from '@moego/ui';
import type { EnumValues } from '../../../../../../store/utils/createEnum';
import { AvailabilityType } from '../../../../../../store/staffSchedule/staffSchedule.types';
import { useAvailableTypeForSMModal } from '../hooks/useAvailableTypeForSMModal';
import { MinorInfoOutlined } from '@moego/icons-react';
import { SlotCalendarPopover } from '../../../../../Calendar/latest/components/SlotCalendar/SlotCalendarPopover/SlotCalendarPopover';
import { type GroomingCommonBizOnBoarding } from '../../../../../../store/metadata/metadata.types';
import { META_DATA_KEY_LIST } from '../../../../../../store/metadata/metadata.config';
import { useMetaData } from '../../../../../../store/metadata/metadata.hooks';
import { useGroomingAvailability } from '../../../../../Appt/components/SelectServiceDetail/hooks/useGroomingAvailability';
import { OnlineBookingReporter } from '../../../../../../utils/reportData/reporter/onlineBookingReporter';

interface AvailabilityTypeRadioGroupProps {
  className?: string;
  value: EnumValues<typeof AvailabilityType>;
  onChange: (value: EnumValues<typeof AvailabilityType>) => void;
}

export const AvailabilityTypeRadioGroup = (props: AvailabilityTypeRadioGroupProps) => {
  const { value, onChange, className } = props;

  const openAvailableTypeForSMModal = useAvailableTypeForSMModal();
  const [data, setData, loading] = useMetaData<GroomingCommonBizOnBoarding>(
    META_DATA_KEY_LIST.GroomingCommonBizOnBoarding,
  );
  const { isOBBySlot, isLoading: isLoadingAvailability } = useGroomingAvailability();
  const groomingReporter = new OnlineBookingReporter();

  const handleAvailabilityTypeHelpClick = () => {
    openAvailableTypeForSMModal();
  };

  // 当 biz 首次进入，没有开启 OB by slot，且 sm 为 by time 时，才展示
  const isGuideBySlot =
    !data?.hideAvailabilityTypeBySlotTips &&
    value === AvailabilityType.BY_TIME &&
    !isOBBySlot &&
    !loading &&
    !isLoadingAvailability;

  // 当 biz 首次进入，开启 OB by slot，且 sm 为 by time 时，才展示
  const isGuideSMBySlot =
    !data?.hideAvailabilityTypeSMBySlotTips &&
    value === AvailabilityType.BY_TIME &&
    isOBBySlot &&
    !loading &&
    !isLoadingAvailability;

  const handleUpdateMetaData = () => {
    if (isGuideBySlot) {
      setData({ ...data, hideAvailabilityTypeBySlotTips: true });
    } else if (isGuideSMBySlot) {
      setData({ ...data, hideAvailabilityTypeSMBySlotTips: true });
    }
  };

  useEffect(() => {
    if (isGuideSMBySlot || isGuideBySlot) {
      groomingReporter.reportGroomingSMPopover();
    }
  }, [isGuideBySlot, isGuideSMBySlot]);

  return (
    <div className={className}>
      <div className="moe-flex moe-gap-[12px]">
        <Heading size="3" className="moe-text-primary">
          Availability type
        </Heading>
        <div
          className="moe-text-tertiary moe-flex moe-items-center moe-cursor-pointer"
          onClick={handleAvailabilityTypeHelpClick}
        >
          <MinorInfoOutlined className="moe-mr-1" />
          <Markup as="span" variant="small">
            How does it work?
          </Markup>
        </div>
      </div>

      <RadioGroup className="moe-mt-[12px]" orientation="horizontal" value={value} onChange={onChange}>
        <Radio value={AvailabilityType.BY_TIME}>{AvailabilityType.mapLabels[AvailabilityType.BY_TIME]}</Radio>

        <Radio value={AvailabilityType.BY_SLOT}>
          <SlotCalendarPopover
            onLinkClick={() => {
              window.open('https://wiki.moego.pet/book-by-slot/', '_blank', 'noopener,noreferrer');
            }}
            isOpen={isGuideBySlot || isGuideSMBySlot}
            contentProps={{
              showCancelButton: true,
              confirmText: 'Set up now',
              onConfirm: () => {
                groomingReporter.reportGroomingSMPopoverSetUpNow();
                handleUpdateMetaData();

                onChange(AvailabilityType.BY_SLOT);

                toast({
                  type: 'success',
                  title: 'The slot configuration is pre-filled based on Online Booking settings',
                });
              },
              onCancel: () => {
                groomingReporter.reportGroomingSMPopoverNotInterested();
              },
              cancelText: isGuideBySlot ? 'Not interested' : 'Not now',
            }}
            popoverProps={{
              alignOffset: -38,
              onClose: () => {
                groomingReporter.reportGroomingSMPopoverClose();
                handleUpdateMetaData();
              },
            }}
            content={
              isGuideSMBySlot ? (
                <Text variant="small">
                  Your Online Booking is already using Book by Slot. Set up now to turn sync on to seamlessly carry over
                  your existing settings and keep everything in one place.
                </Text>
              ) : null
            }
            title="By Slot is available now!"
          >
            <span>{AvailabilityType.mapLabels[AvailabilityType.BY_SLOT]}</span>
          </SlotCalendarPopover>
        </Radio>
      </RadioGroup>
    </div>
  );
};
