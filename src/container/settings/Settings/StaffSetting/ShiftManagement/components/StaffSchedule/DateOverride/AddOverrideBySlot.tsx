import { MinorPlusOutlined } from '@moego/icons-react';
import { Button } from '@moego/ui';
import { useSelector } from 'amos';
import React from 'react';
import { useSetState } from 'react-use';
import { editingWorkingHourStaffIdBox } from '../../../../../../../../store/staffSchedule/staffSchedule.boxes';
import type { DateOverrideDrawerSlotValue } from '../../../../../../../../store/staffSchedule/staffSchedule.types';
import { useBool } from '../../../../../../../../utils/hooks/useBool';
import { AddOverrideDrawerBySlot } from './AddOverrideDrawerBySlot';

export interface AddOverrideProps {
  businessId: number;
  className?: string;
}

export function AddOverrideBySlot(props: AddOverrideProps) {
  const { businessId, className } = props;
  const addVisible = useBool();
  const [state, setState] = useSetState<DateOverrideDrawerSlotValue>({ dates: [] });
  const [editingStaffId] = useSelector(editingWorkingHourStaffIdBox);

  return (
    <>
      <Button
        variant="tertiary-legacy"
        icon={<MinorPlusOutlined />}
        onPress={() => {
          addVisible.open();
          setState({ dates: [] });
        }}
        className={className}
      >
        Add date override
      </Button>
      <AddOverrideDrawerBySlot
        staffId={editingStaffId}
        visible={addVisible.value}
        onClose={addVisible.close}
        value={state}
        onChange={setState}
        businessId={businessId}
      />
    </>
  );
}
