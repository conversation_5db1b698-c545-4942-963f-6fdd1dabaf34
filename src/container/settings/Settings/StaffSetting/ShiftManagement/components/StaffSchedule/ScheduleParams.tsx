import dayjs, { type Dayjs } from 'dayjs';
import React, { useMemo } from 'react';
import {
  MaxStaffEndDate,
  StaffScheduleType,
  type WeekKeyMapNum,
} from '../../../../../../../store/staffSchedule/staffSchedule.types';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../../utils/DateTimeUtil';
import { useControllableValue } from '../../../../../../../utils/hooks/useControlledValue';
import { SelectScheduleType } from '../SelectScheduleType';

export interface ScheduleParamsValue {
  scheduleType: WeekKeyMapNum;
  startDate: Dayjs;
  endDate: Dayjs;
}

export interface ScheduleParamsProps {
  value?: ScheduleParamsValue;
  onChange?: (v: ScheduleParamsValue) => void;
}

export function ScheduleParams(props: ScheduleParamsProps) {
  const today = useMemo(() => dayjs().startOf('date'), []);
  const [value, setValue] = useControllableValue<ScheduleParamsValue>(props, {
    defaultValue: { scheduleType: StaffScheduleType.Every1Week, startDate: today, endDate: MaxStaffEndDate },
  });

  return (
    <div
      className="!moe-flex !moe-gap-x-[16px]"
      data-start={value.startDate.format(DATE_FORMAT_EXCHANGE)}
      data-end={value.endDate.format(DATE_FORMAT_EXCHANGE)}
    >
      <div className="!moe-mt-[4px] moe-w-[360px]">
        <SelectScheduleType
          value={value.scheduleType}
          onChange={(scheduleType) =>
            setValue((pre) => {
              return {
                ...pre,
                scheduleType,
                endDate: MaxStaffEndDate,
              };
            })
          }
        />
      </div>
    </div>
  );
}
