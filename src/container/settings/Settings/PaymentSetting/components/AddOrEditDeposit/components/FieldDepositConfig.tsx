import { Form, Input, SegmentControl, useFormState } from '@moego/ui';
import React from 'react';
import { useMergeRef } from '../../../../../../../utils/react';
import { type BusinessRecord } from '../../../../../../../store/business/business.boxes';
import { type DepositRuleVM } from '../../../../../../../store/payment/actions/private/deposit.actions';
import { decimal2Number, money2Number, number2Decimal, number2Money } from '../../../../../../../utils/utils';

interface FieldDepositProps {
  business: BusinessRecord;
  value?: DepositRuleVM['depositConfig'];
  onChange?: (value: DepositRuleVM['depositConfig']) => void;
}

type DepositCase = 'depositByAmount' | 'depositByPercentage';

export const FieldDepositConfig = React.forwardRef(function (
  { business, value, onChange }: FieldDepositProps,
  ref: React.Ref<HTMLDivElement>,
) {
  value = Array.isArray(value) ? value[0] : value;

  const amountValue =
    value?.case === 'depositByAmount' ? (value.value !== null ? money2Number(value.value) : null) : null;
  const percentageValue =
    value?.case === 'depositByPercentage' ? (value.value !== null ? decimal2Number(value.value) : null) : null;

  const inputRef = React.useRef<HTMLInputElement>(null);
  const mergedRef = useMergeRef(ref, inputRef);
  const errMsg = useFormState().errors.depositConfig;

  const emitChanges = (caseType: DepositCase, amount: number | null, percentage: number | null) => {
    if (caseType === 'depositByAmount') {
      onChange?.({ case: caseType, value: amount === null ? null! : number2Money(amount) });
    } else {
      onChange?.({ case: caseType, value: percentage === null ? null! : number2Decimal(percentage) });
    }
  };

  // SegmentControl value is string, but we need to set it as DepositCase
  const handleCaseChange = (value: string) => {
    emitChanges(value as DepositCase, amountValue, percentageValue);
    setTimeout(() => inputRef.current?.focus());
  };

  const renderInputNumber = () => {
    const finalValue = Array.isArray(value) ? value[0] : value;

    if (finalValue?.case === 'depositByPercentage') {
      return (
        <Input.Number
          ref={mergedRef}
          value={percentageValue}
          onChange={(inputValue) => {
            emitChanges('depositByPercentage', amountValue, inputValue);
          }}
          precision={2}
          minValue={0}
          maxValue={100}
          suffix="%"
          placeholder="Please enter a percentage"
          description="This percentage is based on the total amount of the service type."
          isInvalid={!!errMsg?.message}
        />
      );
    }
    return (
      <Input.Number
        ref={mergedRef}
        value={amountValue}
        onChange={(inputValue) => {
          emitChanges('depositByAmount', inputValue, percentageValue);
        }}
        precision={2}
        minValue={0}
        maxValue={10000000}
        prefix={business.currencySymbol}
        placeholder="Please enter the amount"
        description="The deposit amount is fixed per service type, regardless of the booking duration or quantity of each service type."
        isInvalid={!!errMsg?.message}
      />
    );
  };

  return (
    <div className="moe-flex moe-flex-col moe-w-[444px] moe-gap-[16px]">
      <Form.Label label="Deposit" isRequired />
      <SegmentControl isBlock value={value?.case || 'depositByAmount'} onChange={handleCaseChange}>
        <SegmentControl.Item value="depositByAmount" label="By amount" />
        <SegmentControl.Item value="depositByPercentage" label="By percentage" />
      </SegmentControl>
      {renderInputNumber()}
      {errMsg ? <Form.HelpText errorMessage={errMsg.message} /> : null}
    </div>
  );
});

export const FieldDepositConfigValidator = (param: DepositRuleVM['depositConfig']) => {
  param = Array.isArray(param) ? param[0] : param;

  if (!param || !param.value) return 'Deposit is required';
  if (!param.case) return 'Deposit type is required';
  if (
    (param.case === 'depositByAmount' && money2Number(param.value) <= 0) ||
    (param.case === 'depositByPercentage' && decimal2Number(param.value) <= 0)
  ) {
    return 'Deposit value must be greater than 0';
  }

  return true;
};
