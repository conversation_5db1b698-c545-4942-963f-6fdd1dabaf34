import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Heading, Text } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import ImageCareContractLogoPng from '../../../../assets/image/care-contract-logo.png';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { type ApptListPrintCardInfo } from '../../../../store/printCard/appointmentList/appointmentList.actions';
import { selectSceneCareTypeAsOptions } from '../../../../store/careType/careType.selectors';
import { Condition } from '../../../Condition';
import { PrintCardContentView } from '../common/PrintCardContentView';
import { TableTitle } from '../common/TableTitle';
import { AppointmentListCardTablePortal } from './AppointmentListCardTablePortal';

export interface AppointmentListSettingsProps {
  data: ApptListPrintCardInfo[];
  date: string;
}

export const AppointmentListCardContent = memo<AppointmentListSettingsProps>(({ data, date }) => {
  const [business, sceneCareTypeOptions] = useSelector(selectCurrentBusiness, selectSceneCareTypeAsOptions);

  const getTitle = (serviceItemType: ServiceItemType) =>
    sceneCareTypeOptions.find((item) => item.value === serviceItemType)?.label || '';

  return (
    <PrintCardContentView>
      {data.map(({ serviceItemType, petDetailsByCareType, lodgingTypes, lodgingUnits }, index) => (
        <div
          key={index}
          className="moe-w-full moe-min-h-[792px] moe-bg-white moe-relative moe-p-m moe-pt-s moe-shadow-elevated print:moe-shadow-none"
        >
          <div className="moe-relative moe-mb-8px-150">
            <img src={ImageCareContractLogoPng} className="moe-absolute moe-right-0 moe-top-0 moe-h-[16px]" />

            <Text variant="small" className="moe-text-center">
              {business.businessName}
            </Text>
            <Heading size="5" className="moe-text-center">
              Appointment list - {date}
            </Heading>
          </div>
          {/* hidden care type when only one */}
          <Condition if={data.length > 1}>
            <TableTitle title={getTitle(serviceItemType)} />
          </Condition>
          <AppointmentListCardTablePortal
            serviceItemType={serviceItemType}
            petDetailsByCareType={petDetailsByCareType}
            lodgingTypes={lodgingTypes}
            lodgingUnits={lodgingUnits}
          />
          <div className="moe-break-after-page" />
        </div>
      ))}
    </PrintCardContentView>
  );
});
