import { useSelector } from 'amos';
import React, { memo } from 'react';
import { type NavLinkProps } from 'react-router-dom';
import { type PermissionKinds } from '../../store/business/role.boxes';
import { selectCurrentPermissions } from '../../store/business/role.selectors';
import { Condition } from '../Condition';
import { judgePermission, type JudgePermissionProps } from '../GuardRoute/WithPermission';
import { NavTabItem as StyledNavTabItem } from './NavTab.style';

export { NavTab, TabItem } from './NavTab.style';

interface NavTabItemProps extends NavLinkProps {
  rolePermissions?: PermissionKinds | PermissionKinds[];
  conditionOperator?: JudgePermissionProps['conditionOperator'];
}

export const NavTabItem = memo<NavTabItemProps>(({ rolePermissions, conditionOperator, ...restProps }) => {
  const [permissions] = useSelector(selectCurrentPermissions);

  const withRolePermissionAuth =
    !rolePermissions ||
    judgePermission({
      permissions: rolePermissions,
      currentUserPermission: permissions.toArray(),
      conditionOperator,
    });

  return (
    <Condition if={withRolePermissionAuth}>
      <StyledNavTabItem {...restProps} />
    </Condition>
  );
});
