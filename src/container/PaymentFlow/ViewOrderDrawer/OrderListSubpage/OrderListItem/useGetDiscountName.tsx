import { type OrderLineDiscountModelV1 } from '@moego/api-web/moego/models/order/v1/order_line_discount_models';
import { useSelector } from 'amos';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { isNormal } from '../../../../../store/utils/identifier';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';

export const useGetDiscountName = () => {
  const [business] = useSelector(selectCurrentBusiness);

  const getDiscountName = useLatestCallback((discount: OrderLineDiscountModelV1) => {
    const { discountCodeId, discountType, discountRate, discountAmount } = discount;
    const name = isNormal(discountCodeId) ? '通过 View 取 name' : null;
    const description =
      discountType === 'percentage' ? `${discountRate.value}% off` : `${business.formatMoney(discountAmount)} off`;

    return [name, description].filter(Boolean).join(' ');
  });

  return getDiscountName;
};
