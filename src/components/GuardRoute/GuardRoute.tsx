/*
 * @since 2020-08-20 11:25:35
 * <AUTHOR> <<EMAIL>>
 */

import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { Redirect, Route, type RouteProps } from 'react-router';
import { Unauthorized } from '../../container/common/Unauthorized/Unauthorized';
import { PATH_ACCOUNT, PATH_HOME, PATH_SIGN_IN } from '../../router/paths';
import { currentAccountIdBox } from '../../store/account/account.boxes';
import { currentBusinessIdBox } from '../../store/business/business.boxes';
import { type PermissionKinds } from '../../store/business/role.boxes';
import { selectCurrentPermissions } from '../../store/business/role.selectors';
import { type PricingPermissionKey } from '../../store/company/company.boxes';
import { selectPricingPermission } from '../../store/company/company.selectors';
import { isAnonymous, isInvalid, isLoading, isLogout } from '../../store/utils/identifier';
import { useRouteQuery } from '../../utils/RoutePath';
import { getRedirectURL } from '../../utils/url';
import { judgePermission, type JudgePermissionProps } from './WithPermission';
import { withTitle } from './withTitle';

export interface AutoRedirectProps {
  path: string;
}

export const AutoRedirect = memo<AutoRedirectProps>(() => {
  const [accountId, businessId] = useSelector(currentAccountIdBox, currentBusinessIdBox);
  const query = useRouteQuery(PATH_SIGN_IN);
  if (isLoading(accountId) || isLoading(businessId)) {
    return null;
  }
  if (isInvalid(accountId) || isAnonymous(accountId)) {
    return <Redirect to={PATH_SIGN_IN.queried(query)} />;
  }
  if (isInvalid(businessId) || isAnonymous(businessId)) {
    return <Redirect to={PATH_ACCOUNT.build()} />;
  }
  if (query && query.redirectUrl) {
    return <Redirect to={PATH_SIGN_IN.queried(query)} />;
  }
  return <Redirect to={PATH_HOME.build()} />;
});

export type AuthType = 'anonymous' | 'account' | 'business';

export interface GuardRouteProps extends RouteProps {
  /**
   * 认证类型
   * - anonymous: 匿名访问
   * - account: B 端已注册
   * - business: B 端已选择商家
   */
  authType: AuthType;
  permissions?: PermissionKinds | PermissionKinds[];
  disabled?: boolean;
  withoutIntercom?: boolean;
  title?: string;
  conditionOperator?: JudgePermissionProps['conditionOperator'];
  /** price功能权限访问控制 */
  pricingPermissions?: PricingPermissionKey[];
}

export const GuardRoute = memo<GuardRouteProps>(
  ({
    authType,
    permissions,
    disabled,
    path,
    title,
    component,
    conditionOperator,
    pricingPermissions,
    ...routeProps
  }) => {
    const [accountId, businessId, permissionInfo, pricingPermissionSet] = useSelector(
      currentAccountIdBox,
      currentBusinessIdBox,
      selectCurrentPermissions,
      selectPricingPermission(),
    );

    const comp = useMemo(() => {
      const Component = title ? withTitle(title, authType, component!) : component;
      return (props: {}) => React.createElement(Component!, { ...props, pending: false });
    }, [title, authType, component]);

    if (disabled) {
      return <Redirect to="/" />;
    }

    if (authType === 'account' || authType === 'business') {
      if (isLoading(accountId)) {
        return null;
      }

      if (isAnonymous(accountId) || isInvalid(accountId) || isLogout(accountId)) {
        if (!['/sign_in'].includes(window.location.pathname)) {
          const url = getRedirectURL();
          window.open(`/sign_in?redirectUrl=${url}`, '_self');
        }
        return null;
      }
    }

    if (authType === 'business') {
      if (isLoading(businessId)) {
        return null;
      }
      if (isAnonymous(businessId) || isInvalid(businessId)) {
        return <Redirect to={PATH_ACCOUNT.build()} />;
      }
      if (permissions !== void 0) {
        // 说明此时权限信息还没拉到，先返回 null
        if (permissionInfo.size === 0) {
          return null;
        }
        if (
          !judgePermission({
            permissions,
            currentUserPermission: permissionInfo.toArray(),
            conditionOperator,
          })
        ) {
          return <Unauthorized />;
        }
      }
    }

    // 任何一个price权限没有命中,拦截
    if (
      !!pricingPermissions &&
      pricingPermissions.length > 0 &&
      pricingPermissions.some((permission) => {
        return !pricingPermissionSet.enable.has(permission);
      })
    ) {
      return <Unauthorized />;
    }

    return <Route path={path} component={comp} {...routeProps} />;
  },
);
