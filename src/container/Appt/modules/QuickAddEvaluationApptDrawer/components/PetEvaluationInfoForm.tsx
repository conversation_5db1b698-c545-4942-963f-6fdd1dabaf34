import { Form, cn, useForm } from '@moego/ui';
import React, { forwardRef, memo, useEffect, useImperativeHandle, useState } from 'react';
import { useBusinessApplicableEvaluation } from '../../../../../store/evaluation/evaluation.hooks';
import {
  type EvaluationDetails,
  getDefaultEvaluationServiceListDetail,
} from '../../../../../store/evaluation/evaluation.types';
import { EvaluationServicePicker } from '../../../components/Evaluation/EvaluationServicePicker';
import { useEvaluationAutoAssignStaff } from '../../../hooks/useEvaluationAutoAssignStaff';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { EvaluationLodgingInfo } from '../modules/QuickAddInfoPanel/EvaluationLodgingInfo';
import { EvaluationStaffInfo } from '../modules/QuickAddInfoPanel/EvaluationStaffInfo';

export interface EvaluationItemFormFields {
  serviceId: string;
  staffId?: string;
  lodgingId: string;
}

interface PetEvaluationInfoFormProps {
  petId: string;
  startDate: string;
  startTime?: number;
}

export interface PetEvaluationInfoFormRef {
  getValues: () => EvaluationItemFormFields;
  isValid: () => boolean;
}

export const PetEvaluationInfoForm = memo(
  forwardRef<PetEvaluationInfoFormRef, PetEvaluationInfoFormProps>((props, ref) => {
    const { petId, startDate, startTime } = props;
    const { evaluationList } = useBusinessApplicableEvaluation();
    const defaultEvaluation = evaluationList.first()?.id;
    const [data, setData] = useState<EvaluationDetails>();
    const form = useForm<EvaluationItemFormFields>();
    const { invalid } = form.getFieldState('serviceId');

    const onStaffReady = useLatestCallback((staffId: string) => {
      form.setValue('staffId', staffId);
    });

    useEvaluationAutoAssignStaff({
      evaluationId: data?.serviceId || '',
      defaultStaffId: form.getValues('staffId'),
      onStaffReady,
    });

    useEffect(() => {
      const service = {
        ...getDefaultEvaluationServiceListDetail(),
        serviceId: defaultEvaluation || '',
      };

      setData(service);

      form.setValue('serviceId', service.serviceId || '');
      form.setValue('staffId', service.staffId || undefined);
      form.setValue('lodgingId', service.lodgingId || '');
    }, [petId, defaultEvaluation, form]);

    useImperativeHandle(ref, () => ({
      getValues: () => form.getValues(),
      isValid: () => !invalid,
    }));

    if (!data) {
      return null;
    }

    return (
      <Form form={form} footer={null} classNames={{ base: 'moe-gap-0' }}>
        <Form.Item name={'serviceId'} label="Evaluation service">
          <EvaluationServicePicker
            classNames={{
              // 如果只有一个 evaluation service，则隐藏选择器
              formItemWrapper: cn(['moe-mt-8px-300', { 'moe-hidden': evaluationList.size <= 1 }]),
            }}
            evaluationList={evaluationList.toArray()}
            value={data.serviceId}
            onChange={(value) => {
              setData({ ...data, serviceId: value });
            }}
          />
        </Form.Item>
        <div className="moe-flex moe-gap-8px-200">
          <div className="moe-flex-1">
            <Form.Item name={'staffId'}>
              <EvaluationStaffInfo
                serviceId={data.serviceId}
                startDate={startDate}
                startTime={startTime}
                serviceTime={data.serviceTime}
              />
            </Form.Item>
          </div>
          <div className="moe-flex-1">
            <Form.Item name={'lodgingId'}>
              <EvaluationLodgingInfo serviceId={data.serviceId} startDate={startDate} petId={petId.toString()} />
            </Form.Item>
          </div>
        </div>
      </Form>
    );
  }),
);
