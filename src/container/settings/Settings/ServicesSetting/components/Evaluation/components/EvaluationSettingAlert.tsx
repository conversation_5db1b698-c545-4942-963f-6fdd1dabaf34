import { Alert, Text, Link } from '@moego/ui';
import React, { memo } from 'react';
import { META_DATA_KEY_LIST } from '../../../../../../../store/metadata/metadata.config';
import { useMetaData } from '../../../../../../../store/metadata/metadata.hooks';
import { type BDCommonOnboardingTips } from '../../../../../../../store/metadata/metadata.types';
import { PATH_SERVICE_SETTING } from '../../../../../../../router/paths';
import { ServicesNav } from '../../../types';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useHistory } from 'react-router';

export const EvaluationSettingAlert = memo(() => {
  const [data, setData, isLoading] = useMetaData<BDCommonOnboardingTips>(META_DATA_KEY_LIST.BDCommonOnboardingTips);
  const history = useHistory();

  const handleClose = () => {
    setData({ ...data, hideEvaluationSettingTips: true });
  };

  const handleJump = () => {
    history.push(
      PATH_SERVICE_SETTING.build({ panel: ServicesNav.Services, childPanel: `${ServiceItemType.BOARDING}` }),
    );
  };

  if (data?.hideEvaluationSettingTips || isLoading) {
    return null;
  }

  return (
    <Alert
      isRounded
      isBordered
      description={
        <Text variant="small">
          To set up the required evaluation for each boarding or daycare service, configure it within the{' '}
          <Link className="moe-font-bold" onClick={handleJump}>
            service settings.
          </Link>
        </Text>
      }
      classNames={{
        base: 'moe-mt-m',
      }}
      onClose={handleClose}
    />
  );
});
