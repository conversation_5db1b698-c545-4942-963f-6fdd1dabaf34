/**
 * @fileoverview 用于在最早的时机初始化 Sentry，并监听 script 加载失败
 */

import { breadcrumbsIntegration, captureException, extraErrorDataIntegration, init } from '@sentry/browser';
import {
  IGNORED_ERROR_NAMES,
  FETCH_ERROR_MESSAGES,
  BLOCKED_URL_KEYWORDS,
  HANDLED_PROMISE_REJECTION_MESSAGE,
} from './utils/errorFiltering';

function getReportEnv() {
  const nodeEnv = process.env.NODE_ENV;
  if (nodeEnv === 'development') {
    return 'development';
  }
  if (nodeEnv === 'production') {
    if (location.hostname.endsWith('t2.moego.dev') || location.hostname.endsWith('t2.moego.pet')) {
      return 't2';
    }
    if (location.hostname.endsWith('s1.moego.pet')) {
      return 's1';
    }
    if (location.hostname === 'go.moego.pet') {
      return 'online';
    }
  }
  return 'unknown';
}

const env = getReportEnv();
// intercom 的引入依赖这个环境变量
window.__env__ = env;

const enableSentry = (!__DEV__ && location.protocol === 'https:') || location.search.indexOf('enable_sentry=1') > 0;
const enableDebug = location.search.indexOf('enable_sentry_debug=1') > 0;
const release = window.MOE_VERSION;

init({
  dsn: 'https://<EMAIL>/4507922008702976',
  enabled: enableSentry,
  debug: enableDebug,
  environment: env,
  release,
  tracesSampleRate: 0,
  integrations: [
    breadcrumbsIntegration({
      console: true,
      dom: { serializeAttribute: ['data-testid', 'data-slot'] },
      fetch: true,
      history: true,
      xhr: true,
    }),
    extraErrorDataIntegration(),
  ],
  ignoreErrors: [...IGNORED_ERROR_NAMES],
  beforeSend(event, _hint) {
    const exception = event.exception?.values?.[0];

    if (
      exception &&
      exception.type === 'TypeError' &&
      exception.value &&
      FETCH_ERROR_MESSAGES.includes(exception.value) &&
      event.breadcrumbs
    ) {
      // 过滤因 ad block 等拦截插件导致的请求报错
      for (let i = event.breadcrumbs.length - 1; i >= 0; i--) {
        const breadcrumb = event.breadcrumbs[i];
        if (breadcrumb.type === 'http' && breadcrumb.data) {
          if (
            // xhr 根据 status_code 0, fetch 根据 level error
            (breadcrumb.data.status_code === 0 || breadcrumb.level === 'error') &&
            BLOCKED_URL_KEYWORDS.some((keyword) => breadcrumb.data!.url.includes(keyword))
          ) {
            return null;
          }
        } else {
          break;
        }
      }
    }

    if (exception && exception.type === 'UnhandledRejection' && exception.value === HANDLED_PROMISE_REJECTION_MESSAGE) {
      // already report in http-client
      return null;
    }
    return event;
  },
});

document.addEventListener(
  'error',
  function (event: ErrorEvent) {
    const target = event.target as HTMLScriptElement;
    if (!target || target.tagName !== 'SCRIPT') return;

    let identifier = target.src;
    try {
      identifier = new URL(identifier).hostname;
    } catch {
      // 忽略 URL 解析错误
    }
    const errorName = 'Failed to load script: ' + target.src;
    captureException(new Error(errorName), {
      tags: { type: 'load_script' },
      fingerprint: ['Failed to load script: ' + identifier],
    });
  },
  true, // useCapture - necessary for resource loading errors
);
