/*
 * @since 2020-12-31 13:01:03
 * <AUTHOR> <<EMAIL>>
 */

import { useDispatch, useSelector } from 'amos';
import { Dropdown, Popconfirm } from 'antd';
import classNames from 'classnames';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import React, { memo, useMemo, useState } from 'react';
import SvgIconDeleteSvg from '../../../assets/svg/icon-delete.svg';
import SvgIconSortSvg from '../../../assets/svg/icon-sort.svg';
import { SvgIcon } from '../../../components/Icon/Icon';
import { TextAnchor } from '../../../components/Style/Style';
import { Table } from '../../../components/Table/Table';
import { type ColumnOptions } from '../../../components/Table/Table.types';
import { useChangeCallback, usePaginationOptions, useRowPropsCallback } from '../../../components/Table/Table.utils';
import { IFTestIds } from '../../../config/testIds/intakeForm';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { getIntakeFormSubmissionList, removeIntakeFormSubmission } from '../../../store/intakeForm/intakeForm.actions';
import {
  type IntakeFormFormRecord,
  type IntakeFormSubmissionRecord,
  IntakeFormSubmissionStatus,
} from '../../../store/intakeForm/intakeForm.boxes';
import {
  selectBusinessIntakeFormSubmissionList,
  selectIntakeFormSubmission,
  selectIntakeFormSubmissionForm,
} from '../../../store/intakeForm/intakeForm.selectors';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { DropdownItem, DropdownList } from '../../Client/ClientList/componentsLegacy/ClientListHeader.style';
import { AccountingUpSellBanner } from '../../Finance/components/AccountingUpsellBanner';
import { IntakeFormSubmissionsView } from './IntakeFormSubmissions.style';
import { IntakeFormSubmissionModal } from './components/IntakeFormSubmissionModal';
import { useGetCustomerWording } from '../../Leads/hooks/useGetCustomerWording';

const { IFSubmissionsItem, IFSubmissionsItemDelete } = IFTestIds;

export interface IntakeFormSubmissionsProps {
  className?: string;
}

export const IntakeFormSubmissions = memo<IntakeFormSubmissionsProps>(({ className }) => {
  const [submissionList, business] = useSelector(selectBusinessIntakeFormSubmissionList, selectCurrentBusiness);
  const getCustomerWording = useGetCustomerWording();
  const dispatch = useDispatch();
  const [visible, setVisible] = useState<number>();
  return (
    <IntakeFormSubmissionsView className={className}>
      <AccountingUpSellBanner from="intake-form" />
      <IntakeFormSubmissionModal
        id={visible}
        onClose={(updated) => {
          setVisible(void 0);
          if (updated) {
            dispatch(getIntakeFormSubmissionList({}));
          }
        }}
      />
      <Table
        className="moe-mt-[10px]"
        rowKeys={submissionList.getList()}
        useRecord={useLatestCallback((id: number) =>
          // 老表格暂时先这样吧
          // eslint-disable-next-line react-hooks/rules-of-hooks
          useSelector(selectIntakeFormSubmission(id), selectIntakeFormSubmissionForm(id)),
        )}
        pagination={usePaginationOptions(submissionList.total, submissionList.pageNum, submissionList.pageSize)}
        loading={submissionList.isLoading()}
        onChange={useChangeCallback<[IntakeFormSubmissionRecord, IntakeFormFormRecord]>((pagination) => {
          dispatch(
            getIntakeFormSubmissionList({
              pageSize: pagination.pageSize,
              pageNum: pagination.current,
            }),
          );
        })}
        getRowProps={useRowPropsCallback<[IntakeFormSubmissionRecord, IntakeFormFormRecord]>(([r]) => ({
          className: r.isCreate ? 'is-added' : r.isRead ? 'is-read' : 'is-new',
          onClick: () => setVisible(r.id),
          'data-testid': IFSubmissionsItem,
        }))}
        columns={useMemo(
          (): ColumnOptions<[IntakeFormSubmissionRecord, IntakeFormFormRecord]>[] => [
            {
              title: 'Form name',
              render: ([r, f]) => <div className={classNames({ read: r.isRead > 0 })}>{f.title}</div>,
            },
            {
              title: 'Submitted at',
              render: ([r]) => business.formatDateTime((r.createTime as number) * T_SECOND),
            },
            {
              title: `${getCustomerWording({ capitalize: true })} name`,
              render: ([r]) => (r.customer.firstName || '') + ' ' + (r.customer.lastName || ''),
            },
            {
              title: (
                <Dropdown
                  overlay={
                    <DropdownList>
                      {IntakeFormSubmissionStatus.values.map((s) => (
                        <DropdownItem
                          key={s}
                          onClick={() => {
                            dispatch(getIntakeFormSubmissionList({ pageNum: 1, clear: true, status: s }));
                          }}
                        >
                          {IntakeFormSubmissionStatus.mapLabels[s]}
                        </DropdownItem>
                      ))}
                    </DropdownList>
                  }
                >
                  <TextAnchor>
                    {IntakeFormSubmissionStatus.mapLabels[submissionList.filter.status]}
                    <SvgIcon src={SvgIconSortSvg} size={18} className="ml-4 mt--2" />
                  </TextAnchor>
                </Dropdown>
              ),
              render: ([r]) => {
                return (
                  <span onClick={(e) => e.stopPropagation()}>
                    {r.isCreate ? <span>Added</span> : null}
                    <Popconfirm
                      title="Are you sure to delete this submission?"
                      okText="Yes"
                      okType="danger"
                      placement="topRight"
                      onConfirm={async () => {
                        await dispatch(removeIntakeFormSubmission(r.id));
                        await dispatch(getIntakeFormSubmissionList({}));
                      }}
                    >
                      <SvgIcon src={SvgIconDeleteSvg} size={18} data-test={IFSubmissionsItemDelete} />
                    </Popconfirm>
                  </span>
                );
              },
            },
          ],
          [business, submissionList.filter.status],
        )}
      />
    </IntakeFormSubmissionsView>
  );
});
