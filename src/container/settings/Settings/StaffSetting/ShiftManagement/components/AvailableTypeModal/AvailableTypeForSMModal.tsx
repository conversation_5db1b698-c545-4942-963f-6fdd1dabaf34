import { But<PERSON>, Heading, Modal, type ModalProps, Text } from '@moego/ui';
import React, { memo } from 'react';
import AvailableTypeForSmSlot from '../../../../../../../assets/image/available-type-for-sm-slot.png';
import { ImgIcon } from '../../../../../../../components/Icon/Icon';
import AvailableTypeForSmTime from '../../../../../../../assets/image/available-type-for-sm-time.png';

export const AvailableTypeForSMModal = memo<ModalProps>((props) => {
  return (
    <Modal
      classNames={{
        body: 'moe-p-l',
      }}
      title="📒  Available type for shift management"
      footer={null}
      isOpen
      {...props}
    >
      <div className="moe-flex moe-gap-m">
        <div className="moe-w-[316px] moe-flex moe-flex-col moe-gap-s moe-flex-1 moe-items-center">
          <div className="moe-flex moe-flex-col moe-gap-[12px] moe-w-full moe-items-center">
            <Heading size="4">Book by time</Heading>

            <ImgIcon src={AvailableTypeForSmTime} width={316} height={264} />
          </div>
          <Text variant="small">
            This mode refers to scheduling appointments sequentially based on service duration, ensuring that no two
            bookings overlap.
          </Text>
        </div>

        <div className="moe-w-[316px] moe-flex moe-flex-col moe-gap-s moe-flex-1 moe-items-center">
          <div className="moe-flex moe-flex-col moe-gap-[12px] moe-w-full moe-items-center">
            <Heading size="4">Book by slot</Heading>

            <ImgIcon src={AvailableTypeForSmSlot} width={316} height={264} />
          </div>
          <Text variant="small">
            This mode is for businesses where customers drop off pets in groups. A slot shows how many pets that can be
            dropped off per staff at a specific time,{' '}
            <span className="moe-text-brand">indicating capacity, not service duration</span>.{' '}
            <Button
              size="s"
              variant="tertiary"
              className="moe-inline-block moe-h-[14px]"
              onPress={() => {
                window.open('https://wiki.moego.pet/book-by-slot/', '_blank', 'noopener,noreferrer');
              }}
            >
              Learn more
            </Button>
          </Text>
        </div>
      </div>
    </Modal>
  );
});

AvailableTypeForSMModal.displayName = 'AvailableTypeForSMModal';
