import { type ListDaySlotInfosResultDaySlotInfo } from '@moego/api-web/moego/api/appointment/v1/calendar_api';
import React, { memo } from 'react';
import { renderCountableNounPlurals } from '../../../../../../utils/utils';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import dayjs from 'dayjs';
import { store } from '../../../../../../provider';

export interface StaffSlotTooltipProps {
  isFullyBooked: boolean;
  notInSlotPetNum: number;
  slotInfoList: ListDaySlotInfosResultDaySlotInfo[];
  header?: React.ReactNode;
}

export const StaffSlotTooltip = memo<StaffSlotTooltipProps>((props) => {
  const { isFullyBooked, notInSlotPetNum, slotInfoList, header } = props;

  const business = store.select(selectCurrentBusiness());

  return (
    <div className="moe-flex moe-flex-col moe-gap-xxs moe-w-[180px]">
      {header}
      {isFullyBooked && <span className="moe-text-warning moe-text-center moe-mb-xxs">Fully booked</span>}
      {notInSlotPetNum > 0 && (
        <div className="moe-flex moe-items-center moe-justify-between moe-w-full">
          <span>Not in any slot</span>
          <span>{renderCountableNounPlurals(notInSlotPetNum, 'pet')}</span>
        </div>
      )}
      {slotInfoList
        .sort((a, b) => a.startTime - b.startTime)
        .map((slotInfo, index) => {
          return (
            <div key={index} className="moe-flex moe-items-center moe-justify-between moe-w-full">
              <span>{business.formatTime(dayjs(slotInfo.date).setMinutes(slotInfo.startTime))}</span>
              <span>{`${slotInfo.usedPetCapacity || 0}/${renderCountableNounPlurals(slotInfo.petCapacity, 'pet')}`}</span>
            </div>
          );
        })}
    </div>
  );
});

StaffSlotTooltip.displayName = 'StaffSlotTooltip';
