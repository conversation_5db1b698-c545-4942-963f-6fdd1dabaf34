import {
  type AlertDetail,
  type PetAlert,
  type ClientAlert,
} from '@moego/api-web/moego/models/appointment/v1/check_in_out_alert_defs';
import { MoeMoney } from '@moego/finance-utils';

/**
 * Check if a single pet has any alerts
 * Based on PetAlertSection line 34-36 logic
 */
export const hasPetAlert = (petAlert?: PetAlert): boolean => {
  if (!petAlert) {
    return false;
  }

  const { vaccineAlert, incidentAlert, petCodeAlert } = petAlert;

  const vaccines = vaccineAlert?.vaccines || [];
  const incidents = incidentAlert?.incidents || [];
  const petCodes = petCodeAlert?.petCodeViews || [];

  return vaccines.length + incidents.length + petCodes.length > 0;
};

/**
 * Check if any pets in the list have alerts
 */
export const hasPetAlerts = (petAlerts?: PetAlert[]): boolean => {
  if (!petAlerts || petAlerts.length === 0) {
    return false;
  }

  return petAlerts.some((petAlert) => hasPetAlert(petAlert));
};

/**
 * Check if client has any alerts
 * Based on ClientAlertSection line 73-80 logic
 */
export const hasClientAlert = (clientAlert?: ClientAlert): boolean => {
  if (!clientAlert) {
    return false;
  }

  const {
    membershipAlert,
    cardOnFileAlert,
    packageAlert,
    noteAlert,
    unsignedAgreementAlert,
    unpaidBalanceAlert,
    clientTagAlert,
    pickupPersonAlert,
  } = clientAlert;

  const subscriptions = membershipAlert?.subscriptions || [];
  const cofStatus = cardOnFileAlert?.cofStatus;
  const packages = packageAlert?.packages || [];
  const alertNote = noteAlert?.alertNote;
  const agreements = unsignedAgreementAlert?.agreements || [];
  const unpaidAmount = unpaidBalanceAlert?.unpaidAmount;
  const clientTags = clientTagAlert?.clientTags || [];
  const pickupPersons = pickupPersonAlert?.contacts || [];

  // Check if unpaid amount is greater than 0
  const hasUnpaidAmount = unpaidAmount && new MoeMoney(unpaidAmount).isZero() === false;

  // Return true if any of these conditions are met
  return (
    subscriptions.length + packages.length + agreements.length + clientTags.length + pickupPersons.length > 0 ||
    !!cofStatus ||
    !!alertNote?.note ||
    !!hasUnpaidAmount
  );
};

/**
 * Check if there are any check-in/out alerts (either client or pet alerts)
 */
export const hasCheckInOutAlert = (alertDetail?: AlertDetail): boolean => {
  if (!alertDetail) {
    return false;
  }

  const { clientAlert, petAlerts } = alertDetail;

  return hasClientAlert(clientAlert) || hasPetAlerts(petAlerts);
};
