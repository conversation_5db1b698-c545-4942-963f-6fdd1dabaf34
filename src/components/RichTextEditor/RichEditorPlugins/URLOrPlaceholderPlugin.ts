import { useDispatch, useSelector } from 'amos';
import { appendQuery } from 'monofile-utilities/lib/query-string';
import { URL_INTAKE_FORM, URL_ONLINE_BOOKING } from '../../../config/host/const';
import { getAgreementList } from '../../../store/agreement/agreement.actions';
import { agreementMapBox } from '../../../store/agreement/agreement.boxes';
import { selectBusinessAgreements } from '../../../store/agreement/agreement.selectors';
import { selectCurrentPermissions } from '../../../store/business/role.selectors';
import { getCompanyAllIntakeFormIdList, getIntakeFormFormList } from '../../../store/intakeForm/intakeForm.actions';
import { intakeFormFormMapBox, intakeFormIdListBox } from '../../../store/intakeForm/intakeForm.boxes';
import { selectBusinessIntakeFormFormList } from '../../../store/intakeForm/intakeForm.selectors';
import { getActiveMembershipList } from '../../../store/membership/membership.actions';
import { selectCompanyActiveMembershipList } from '../../../store/membership/membership.selectors';
import { getCompanyAllOnlineBookingNameList } from '../../../store/onlineBooking/actions/private/onlineBooking.actions';
import {
  getOnlineBookingPreference,
  getOnlineBookingProfile,
} from '../../../store/onlineBooking/actions/private/onlineBookingPreference.actions';
import { onlineBookingNameListBox } from '../../../store/onlineBooking/onlineBooking.boxes';
import { selectOnlineBookingPreference } from '../../../store/onlineBooking/onlineBookingPreference.selectors';
import { isNormal } from '../../../store/utils/identifier';
import { useBizIdReadyEffect } from '../../../utils/hooks/useBizIdReadyEffect';
import { getAgreementVariable, getMembershipVariable } from '../../../utils/messageVariable';
import { useNewAccountStructure } from '../../WithFeature/useNewAccountStructure';
import { type EditorMenuButton } from './types';
import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { GrowthBookFeatureList } from '../../../utils/growthBook/growthBook.config';

export const useGetIntakeFormList = () => {
  const dispatch = useDispatch();
  const { isNewAndHasMultipleLocation } = useNewAccountStructure('all');
  const { locationList } = useNewAccountStructure('accessClient');
  const [intakeFormIdList, intakeFormList, intakeFormFormMap] = useSelector(
    intakeFormIdListBox,
    selectBusinessIntakeFormFormList,
    intakeFormFormMapBox,
  );

  useBizIdReadyEffect(() => {
    dispatch([getIntakeFormFormList(), getCompanyAllIntakeFormIdList()]);
  }, []);

  return () => {
    if (isNewAndHasMultipleLocation) {
      return locationList
        .toArray()
        .map((item) => {
          const id = +item.id;
          const name = item.name;
          const form = intakeFormIdList.find((item) => item.businessId === id);
          if (isNormal(id) && form) {
            return {
              label: `${form.title} (${name})`,
              value: URL_INTAKE_FORM + appendQuery('/go/form', { formId: form.formId }),
            };
          }
          return null;
        })
        .filter(Boolean) as EditorMenuButton[];
    }
    return intakeFormList.toArray().map((id) => {
      const form = intakeFormFormMap.mustGetItem(id);
      return {
        label: form.title,
        value: URL_INTAKE_FORM + form.previewLink(),
      };
    });
  };
};

export const useGetAgreementList = () => {
  const dispatch = useDispatch();
  const { isNewAndHasMultipleLocation } = useNewAccountStructure('all');
  const [agreementList, agreementMap] = useSelector(selectBusinessAgreements(), agreementMapBox);
  useBizIdReadyEffect(() => {
    dispatch(getAgreementList());
  }, []);
  return () => {
    if (isNewAndHasMultipleLocation) {
      return [];
    }
    return agreementList.toArray().map((id) => {
      const agreement = agreementMap.mustGetItem(id);
      return {
        label: agreement.agreementHeader,
        value: getAgreementVariable({ id: agreement.id, title: agreement.agreementHeader }),
      };
    });
  };
};

export const useGetOnlineBookingURL = () => {
  const dispatch = useDispatch();
  // 判断是否是 multi-location 这里需要用 all location 的集合判断
  const { isNewAndHasMultipleLocation } = useNewAccountStructure('all');
  // MC 的 location list 需要跟 client list 的 location list 保持一致
  const { locationList } = useNewAccountStructure('accessClient');
  const [preference, onlineBookingNameList] = useSelector(selectOnlineBookingPreference, onlineBookingNameListBox);
  useBizIdReadyEffect(() => {
    dispatch([getOnlineBookingPreference(), getOnlineBookingProfile(), getCompanyAllOnlineBookingNameList()]);
  }, []);
  return () => {
    if (isNewAndHasMultipleLocation) {
      return locationList
        .toArray()
        .map((item) => {
          const id = +item.id;
          const name = item.name;
          const ob = onlineBookingNameList.find((item) => item.businessId === id);
          if (isNormal(id) && ob) {
            return {
              label: `Online booking URL (${name})`,
              value: `${URL_ONLINE_BOOKING}/ol/${ob.urlDomainName}/landing`,
            };
          }
          return null;
        })
        .filter(Boolean) as EditorMenuButton[];
    }
    if (preference.isEnable > 0) {
      return [
        {
          label: 'Online booking URL',
          value: preference.newOnlineBookingLink(),
        },
      ];
    }
    return [];
  };
};

export const useGetMembershipList = () => {
  const dispatch = useDispatch();
  const [membershipList, permissions] = useSelector(selectCompanyActiveMembershipList, selectCurrentPermissions());
  const enableMembership = useFeatureIsOn(GrowthBookFeatureList.EnableMembership);
  // 这里需要有 accessMembership 的权限主要是 getActiveMembershipList 接口背后需要这个权限否则会报错
  const hasPermission = permissions.has('sellMembership') && permissions.has('accessMembership');

  useBizIdReadyEffect(() => {
    if (enableMembership && hasPermission) {
      dispatch(getActiveMembershipList());
    }
  }, [enableMembership, hasPermission]);

  return () => {
    if (enableMembership) {
      return membershipList.toArray().map((membership) => ({
        label: membership.name,
        value: getMembershipVariable(membership),
      }));
    }

    return [];
  };
};
