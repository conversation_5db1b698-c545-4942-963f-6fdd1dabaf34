/**
 * <AUTHOR>
 * @description pre auth status manager.
 */
import { useDispatch, useSelector, useStore } from 'amos';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useAsync } from 'react-use';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { getTicketPreAuthInfo } from '../../../../store/calendarLatest/actions/private/calendar.actions';
import { getStripeCustomerPaymentMethodList } from '../../../../store/stripe/actions/public/stripe.actions';
import {
  getPreAuthPreviousStatus,
  getPreAuthRecentUsedCard,
  updateTicketPreAuth,
} from '../../../../store/stripe/preAuth.actions';
import { PreAuthRecord, defaultPreAuthDetail } from '../../../../store/stripe/preAuth.boxes';
import { selectPreAuth } from '../../../../store/stripe/preAuth.selectors';
import { stripePaymentMethodMapBox } from '../../../../store/stripe/stripe.boxes';
import { selectStripePaymentMethodList } from '../../../../store/stripe/stripe.selectors';
import { ID_ANONYMOUS, isNormal } from '../../../../store/utils/identifier';
import { useBool } from '../../../../utils/hooks/useBool';
import { apptInfoMapBox } from '../../../Appt/store/appt.boxes';
import { useInvoiceReinvent } from '../../../PaymentFlow/hooks/useInvoiceReinvent';
import { type PreAuthValues, type UsePreAuthSwitchProps } from './PreAuth.types';
import { PRE_AUTH_DEFAULT_VALUES, checkIsPassTime, checkValuesHasChanged } from './PreAuth.utils';
import { type PreAuthSwitchRef } from './PreAuthSwitchBase';
import { preAuthCardHelper } from './preAuthCard.utils';
import { useStripeCofLinkMeta } from './useStripeCofLinkMeta';

export function usePreAuthSwitch({
  ticketId,
  date,
  time,
  isFromOB,
  isRepeat,
  isBookAgain,
  customerId,
  onChange,
  isReschedule,
  updateInPlace,
  presetPreAuthValues,
}: UsePreAuthSwitchProps) {
  const hasTicketId = isNormal(ticketId);
  const isEdit = hasTicketId && !isBookAgain;
  const store = useStore();
  const dispatch = useDispatch();
  const changedRef = useRef(false);
  const preAuthEnable = useBool();
  const loading = useBool(true);
  const { isNewOrderV4Flow } = useInvoiceReinvent();
  // presetPreAuthValues 是外部传进来的值，用于advanced settings等场景，只使用一次，用完就不再使用了。
  const presetPreAuthValuesUsageRef = useRef<'unused' | 'used'>('unused');
  const isAdvanceSettingModify = !!presetPreAuthValues;
  const [initialData, setInitialData] = useState({ ...PRE_AUTH_DEFAULT_VALUES });
  const [paymentMethodId, updatePaymentMethodId] = useState('');
  const [recentCardId, setRecentCardId] = useState('');
  const [business, methodList, methodMap, preAuthInfo, ticket] = useSelector(
    selectCurrentBusiness(),
    selectStripePaymentMethodList(customerId),
    stripePaymentMethodMapBox,
    selectPreAuth(ticketId ?? ID_ANONYMOUS),
    apptInfoMapBox.getItem(String(ticketId || '')),
  );

  // the match card of paymentMethodId, maybe not exists. for example, the card was deleted.
  const currentCard = useMemo(() => {
    return methodMap.getItem(paymentMethodId ?? '') || null; // not mustGetItem.
  }, [paymentMethodId, methodMap]);

  const recentCard = useMemo(() => {
    return methodMap.getItem(recentCardId ?? '') || null;
  }, [recentCardId, methodMap]);

  const preAuthCardNumber = useMemo(() => {
    if (currentCard?.card) {
      return preAuthCardHelper.stringify(currentCard.card);
    }

    return preAuthInfo.preAuthCardNumber;
  }, [currentCard?.card, preAuthInfo.preAuthCardNumber]);

  // check if time is pass.
  const isPassTime = useMemo(() => {
    return date ? checkIsPassTime({ date, time: time || undefined }) : false;
  }, [date, time]);

  const loadForEditOrCopyMode = async () => {
    await dispatch(getStripeCustomerPaymentMethodList(customerId));
    const [detail, recentUsedCard] = await Promise.all([
      dispatch(getTicketPreAuthInfo(ticketId!)),
      dispatch(getPreAuthRecentUsedCard(customerId)),
    ]);

    const info = new PreAuthRecord(detail?.preAuthInfo || {});
    const enable = info.isOpen;
    const pmId = info.preAuthPaymentMethod || '';
    preAuthEnable.as(enable);
    updatePaymentMethodId(pmId);
    setRecentCardId(recentUsedCard?.preAuthPaymentMethod || '');
    setInitialData({
      preAuthEnable: enable,
      preAuthPaymentMethod: pmId,
    });
  };

  const loadForCreateMode = async () => {
    await dispatch(getStripeCustomerPaymentMethodList(customerId));
    const [isOpen, recentUsedCard] = await Promise.all([
      dispatch(getPreAuthPreviousStatus(customerId)),
      dispatch(getPreAuthRecentUsedCard(customerId)),
    ]);
    const map = store.select(stripePaymentMethodMapBox);
    const cardExists = Boolean(
      recentUsedCard && recentUsedCard.preAuthPaymentMethod && map.hasItem(recentUsedCard.preAuthPaymentMethod),
    );
    const enable = isOpen && cardExists; // strict extends when previous is open and recent card exists.
    const pmId = enable ? recentUsedCard.preAuthPaymentMethod : '';
    setRecentCardId(recentUsedCard?.preAuthPaymentMethod || '');
    setInitialData({
      preAuthEnable: enable,
      preAuthPaymentMethod: pmId,
    });
  };

  // public method for getting values.
  const getValues: PreAuthSwitchRef['getValues'] = () => {
    const enable = preAuthEnable.value;
    return {
      preAuthEnable: enable,
      preAuthPaymentMethod: enable ? paymentMethodId : '',
      preAuthCardNumber: enable ? preAuthCardNumber : '',
    };
  };

  const isSwitchVisible = !(isFromOB && !preAuthInfo.isOpen);
  // emit change in only one place
  const checkChanged = () => {
    if (loading.value || !isSwitchVisible) {
      return;
    }

    if (changedRef.current) {
      onChange?.(getValues(), true);
      return;
    }

    // check if changed, include preset changed or manually changed.
    const compareState = isEdit ? initialData : PRE_AUTH_DEFAULT_VALUES;
    const isChanged = checkValuesHasChanged(
      {
        preAuthEnable: preAuthEnable.value,
        preAuthPaymentMethod: paymentMethodId,
      },
      compareState,
    );

    if (isChanged) {
      changedRef.current = true;
      onChange?.(getValues(), true);
    } else {
      onChange?.(getValues(), false);
    }
  };

  useStripeCofLinkMeta(customerId, 'initial');
  useAsync(async () => {
    if (!isNormal(customerId)) {
      return;
    }

    try {
      loading.open();
      if (hasTicketId) {
        await loadForEditOrCopyMode();
      } else {
        await loadForCreateMode();
      }
    } finally {
      loading.close();
    }
  }, [customerId, hasTicketId]);

  // Auto-switch for pass time
  // preset data once while time is not pass.
  useEffect(() => {
    if (loading.value || isEdit) {
      return;
    }

    if (isAdvanceSettingModify && presetPreAuthValuesUsageRef.current === 'unused') {
      presetPreAuthValuesUsageRef.current = 'used';
      preAuthEnable.as(presetPreAuthValues!.preAuthEnable);
      updatePaymentMethodId(presetPreAuthValues!.preAuthPaymentMethod);
    } else if (isPassTime) {
      preAuthEnable.close();
      updatePaymentMethodId('');
    } else {
      preAuthEnable.as(initialData.preAuthEnable);
      updatePaymentMethodId(initialData.preAuthPaymentMethod);
    }
  }, [loading.value, isAdvanceSettingModify, isEdit, isPassTime, initialData]);

  useEffect(() => {
    if (loading.value) {
      return;
    }

    checkChanged();
  }, [loading.value, paymentMethodId, preAuthEnable.value]);

  const isPaidStarted = useMemo(() => {
    return Boolean(ticket?.apptPayStatus);
  }, [ticket]);

  const isPayDepositInNewOrderFlow = useMemo(() => {
    const ticketDepositAmount = ticket?.paymentSummary.collectedDepositAmount.valueOf() ?? 0;
    return isNewOrderV4Flow && ticketDepositAmount > 0;
  }, [ticket?.paymentSummary.collectedDepositAmount.valueOf(), isNewOrderV4Flow]);

  // status values
  const isChecked = preAuthEnable.value;
  const switchDisabledType =
    (loading.value && 'loading') ||
    (isPassTime && 'passTime') ||
    (isFromOB && 'ob') ||
    (isPaidStarted && 'paidStarted') ||
    (isEdit && preAuthInfo.isToBeCaptureOrCaptured && 'preAuthSucceed') ||
    (isEdit && preAuthInfo.inBSPD && preAuthInfo.isAfterReadyBeforeCaptured && 'checkedInBSPD') ||
    '';
  const isSwitchDisabled = Boolean(switchDisabledType) || isPayDepositInNewOrderFlow;
  const isWaitingForCard = isChecked && !paymentMethodId && !preAuthInfo.preAuthFailedMessage && !methodList.size;
  const isCardDeleted = !!paymentMethodId && !currentCard;
  const isCardPickerDisabled =
    isSwitchDisabled || Boolean(((methodList.size === 1 && currentCard) || !methodList.size) && !isCardDeleted);

  const updateValues = async (values: Partial<PreAuthValues>) => {
    if (ticketId && updateInPlace) {
      await dispatch(
        updateTicketPreAuth({
          ticketId,
          ...values,
        }),
      );
      setInitialData((prev) => ({ ...prev, ...values }));
    }
    if (values.preAuthPaymentMethod) {
      updatePaymentMethodId(values.preAuthPaymentMethod);
    }
    if (typeof values.preAuthEnable === 'boolean') {
      preAuthEnable.as(values.preAuthEnable);
    }
  };

  return {
    ticketId,
    isRepeat,
    business,
    customerId,
    loading: loading.value,
    isEdit,
    isPassTime,
    paymentMethodId,
    currentCard,
    recentCard,
    isChecked,
    isSwitchDisabled,
    isCardDeleted,
    isCardPickerDisabled,
    isWaitingForCard,
    isReschedule,
    switchDisabledType,
    preAuthInfo: isEdit ? preAuthInfo : defaultPreAuthDetail,
    switchControl: preAuthEnable,
    updatePaymentMethodId,
    methodList,
    getValues,
    updateValues,
  };
}

export type PreAuthSwitchState = ReturnType<typeof usePreAuthSwitch>;
