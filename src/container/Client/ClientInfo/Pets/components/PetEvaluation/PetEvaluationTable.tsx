import { AlertDialog } from '@moego/ui';
import React, { type FC, useMemo } from 'react';
import { updatePetEvaluation, type PetEvaluation } from '../../../../../../store/evaluation/evaluation.actions';
import { Select, Table } from 'antd';
import { useGetClosestScroller } from '../../../../../../layout/components/ScrollerProvider';
import { EvaluationStatus } from '../../../../../../store/evaluation/evaluation.types';
import { type EnumValues } from '../../../../../../store/utils/createEnum';
import { useDispatch } from 'amos';
import { PetEvaluationHistoryModelActionType } from '@moego/api-web/moego/models/business_customer/v1/business_pet_evaluation_models';
import { isNormal } from '../../../../../../store/utils/identifier';
import { type ColumnsType } from 'antd/lib/table';

export interface PetEvaluationTableProps {
  afterChangePetEvaluationStatus: () => void;
  petEvaluationList?: PetEvaluation[];
  isReadOnly?: boolean;
}

export const PetEvaluationTable: FC<PetEvaluationTableProps> = (props) => {
  const { afterChangePetEvaluationStatus, petEvaluationList = [], isReadOnly = false } = props;
  const getMainScroller = useGetClosestScroller();
  const dispatch = useDispatch();

  const handleChangePetEvaluationStatus = async (
    petEvaluation: PetEvaluation,
    status: EnumValues<typeof EvaluationStatus>,
  ) => {
    const { evaluationId, petId } = petEvaluation;

    const nextStatus = isNormal(status) ? status : EvaluationStatus.NoResult;

    const alertContent = isNormal(status)
      ? `Are you sure you want to update the evaluation result to: ${EvaluationStatus.mapLabels[nextStatus]}?`
      : `Are you sure you want to clear the evaluation result?`;

    await new Promise((resolve, reject) => {
      AlertDialog.open({
        title: 'Change evaluation results',
        content: alertContent,
        onConfirm: async () => {
          await dispatch(
            updatePetEvaluation({
              petId,
              evaluationId,
              evaluationStatus: nextStatus,
              actionType: PetEvaluationHistoryModelActionType.UPDATE_BY_STAFF,
            }),
          );
          afterChangePetEvaluationStatus();
          resolve(true);
        },
        onCancel: reject,
        onClose: reject,
      });
    });
  };

  const columns = useMemo<ColumnsType<PetEvaluation>>(
    () => [
      {
        title: 'Evaluation service',
        dataIndex: 'evaluationName',
        key: 'evaluationName',
        width: 813,
      },
      {
        title: 'Results',
        dataIndex: 'evaluationStatus',
        key: 'evaluationStatus',
        width: 577,
        render: (value, record) => {
          return (
            <Select
              // value:null is for displaying empty text when the value is NoResult
              value={value !== EvaluationStatus.NoResult ? value : null}
              getPopupContainer={getMainScroller}
              allowClear
              disabled={isReadOnly}
              className="moe-w-[300px]"
              onChange={(value) => handleChangePetEvaluationStatus(record, value || EvaluationStatus.NoResult)}
            >
              {EvaluationStatus.values
                .filter((item) => item !== EvaluationStatus.NoResult)
                .map((item) => (
                  <Select.Option key={item} value={item}>
                    {EvaluationStatus.mapLabels[item]}
                  </Select.Option>
                ))}
            </Select>
          );
        },
      },
    ],
    [],
  );

  return (
    <Table columns={columns} dataSource={petEvaluationList} rowKey={(row) => row.evaluationId} pagination={false} />
  );
};
