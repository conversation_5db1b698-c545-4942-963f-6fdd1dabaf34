import { DatePicker, MinorFileOutlined, MinorShowOutlined, Text } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { useState } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { Switch } from '../../../../../../../components/SwitchCase';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../../utils/DateTimeUtil';
import { type PetDetailType } from '../../../type';

export interface DiffVaccineItemProps<T = PetDetailType['vaccineList'][number]> {
  vaccine: T;
  readonly?: boolean;
  checked?: boolean;
  isError?: boolean;
  onChange?: (v: T) => void;
}

export const DiffVaccineItem = (props: DiffVaccineItemProps) => {
  const { vaccine, readonly, checked, isError, onChange } = props;
  const [business] = useSelector(selectCurrentBusiness());
  const [expirationDate, setExpirationDate] = useState(vaccine.expirationDate);

  return (
    <div className="moe-flex moe-flex-col moe-gap-[4px]">
      <div className="moe-flex moe-items-start moe-gap-xs moe-flex-1">
        <Switch shortCircuit>
          <Switch.Case if={readonly}>
            <Text variant="small">Expiration date: {expirationDate ? business.formatDate(expirationDate) : 'NA'}</Text>
          </Switch.Case>
          <Switch.Case else>
            <div onClick={(e) => e.stopPropagation()} className="moe-flex-1">
              <DatePicker
                // 如果该item被勾选，则 expired date 字段需要必填
                {...(checked &&
                  isError &&
                  !expirationDate && { errorType: 'required', errorMessage: 'Expiration date is required' })}
                isRequired={checked}
                label="Expiration date"
                className="moe-flex-1"
                isClearable={false}
                format={business.dateFormat}
                value={expirationDate ? dayjs(expirationDate) : undefined}
                onChange={(v) => {
                  const newVal = v ? v.format(DATE_FORMAT_EXCHANGE) : '';
                  setExpirationDate(newVal);
                  onChange?.({ ...vaccine, expirationDate: newVal });
                }}
              />
            </div>
          </Switch.Case>
        </Switch>
      </div>
      <Condition if={vaccine.documentUrls?.length}>
        <div className="moe-flex moe-flex-col">
          {vaccine.documentUrls?.map((url, index) => {
            const name = `Document ${index + 1}`;

            return (
              <div className="moe-flex moe-items-center moe-gap-xs" key={url}>
                <MinorFileOutlined className="moe-w-[20px] moe-h-[20px]" />
                <Text variant="small">{name}</Text>
                <div
                  className="moe-p-[6px] moe-flex moe-justify-center moe-items-center"
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(url, '_blank', 'noopener noreferrer');
                  }}
                >
                  <MinorShowOutlined className="moe-w-[20px] moe-h-[20px]" />
                </div>
              </div>
            );
          })}
        </div>
      </Condition>
    </div>
  );
};
