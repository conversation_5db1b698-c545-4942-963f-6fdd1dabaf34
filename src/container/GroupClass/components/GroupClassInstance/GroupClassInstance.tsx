import { GroupClassInstanceStatus } from '@moego/api-web/moego/models/offering/v1/group_class_models';
import React, { type FC } from 'react';
import { GroupClassInstanceAction, GroupClassInstanceActions } from './GroupClassInstanceActions';
import { GroupClassInstanceInfo } from './GroupClassInstanceInfo/GroupClassInstanceInfo';

export interface GroupClassInstanceProps {
  groupClassInstanceId: string;
  status: GroupClassInstanceStatus;
}

export const GroupClassInstance: FC<GroupClassInstanceProps> = (props) => {
  const { groupClassInstanceId, status } = props;
  const action =
    status === GroupClassInstanceStatus.UPCOMING
      ? [
          GroupClassInstanceAction.EnrollPets,
          GroupClassInstanceAction.Edit,
          GroupClassInstanceAction.Delete,
          GroupClassInstanceAction.MassEmail,
        ]
      : [GroupClassInstanceAction.MassEmail];

  return (
    <div className="moe-flex-1 moe-flex moe-justify-between moe-items-center moe-py-m moe-pr-s">
      <GroupClassInstanceInfo groupClassInstanceId={groupClassInstanceId} />
      <GroupClassInstanceActions groupClassInstanceId={groupClassInstanceId} actionList={action} />
    </div>
  );
};
