import { Typography, cn } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React from 'react';
import { FullWeekDayList } from '../../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { currentWorkingHourWeekBox } from '../../../../../../../store/staffSchedule/staffSchedule.boxes';

export function RowWeek() {
  const [currentWeek] = useSelector(currentWorkingHourWeekBox);

  return (
    <div className="moe-flex-1 moe-flex moe-border-b moe-border-divider">
      <div className="moe-min-w-[200px]"></div>
      <div className="moe-flex-1 moe-grid moe-grid-cols-7 moe-gap-x-s">
        {FullWeekDayList.map((week, index, arr) => {
          const isLast = index === arr.length - 1;
          const date = currentWeek.add(index, 'day');
          const isToday = date?.isSame(dayjs(), 'day');
          const className = cn(isToday ? 'moe-text-brand' : '');
          return (
            <div
              key={week}
              className={cn('moe-pb-8px-150 moe-text-center moe-min-w-0', {
                'moe-mr-xxs': isLast,
              })}
            >
              {date && (
                <div>
                  <Typography.Text variant="caption" className={className}>
                    {date.format('ddd')}
                  </Typography.Text>
                  <Typography.Heading size="3" className={className}>
                    {date.format('D')}
                  </Typography.Heading>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
