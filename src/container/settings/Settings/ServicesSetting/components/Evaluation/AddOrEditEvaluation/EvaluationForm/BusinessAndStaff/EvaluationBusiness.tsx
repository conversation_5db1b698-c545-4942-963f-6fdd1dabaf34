import { Form, useWatch } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { WithMultiLocation } from '../../../../../../../../../components/WithFeature/WithMultiLocation';
import { selectAllLocationIdList } from '../../../../../../../../../store/business/location.selectors';
import { MultiLocationSelector } from '../../../../../../components/Selector/MultiLocationSelector';
import { useServiceStaffPermissions } from '../../../../AddOrEditComponents/hooks/useServiceStaffPermissions';
import { useEvaluationFormContext } from '../EvaluationFormContext';

export const EvaluationBusiness = memo(() => {
  const [allLocationIdList] = useSelector(selectAllLocationIdList);
  const { form, isEdit } = useEvaluationFormContext();
  const { isDisabledForRemains } = useServiceStaffPermissions(isEdit);

  const availableForAllBusiness = useWatch({
    control: form?.control,
    name: 'availableForAllBusiness',
  });

  return (
    <WithMultiLocation scene="all">
      <Form.Item<string[], string[]>
        name="availableBusinessIds"
        label="Business"
        rules={{
          validate: (value) => {
            if (!availableForAllBusiness) {
              if (value?.length === 0) {
                return 'Business is required';
              }
            }
            return true;
          },
        }}
        transformer={{
          input(locationOverrideList) {
            if (availableForAllBusiness) {
              return allLocationIdList.toJSON().map((item) => `${item}`);
            } else {
              return locationOverrideList || [];
            }
          },
          output(locationIdList) {
            if (locationIdList.length < allLocationIdList.size) {
              form?.setValue('availableForAllBusiness', false, { shouldDirty: true });
            } else {
              form?.setValue('availableForAllBusiness', true, { shouldDirty: true });
            }
            return locationIdList;
          },
        }}
      >
        <MultiLocationSelector
          isDisabled={() => !!isDisabledForRemains}
          scene="all"
          menuListClassName="moe-max-h-[300px]"
          isRequired
          showSelectAll={true}
          footer={null}
        />
      </Form.Item>
    </WithMultiLocation>
  );
});
