import { Heading, Switch } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { Row } from 'antd';
import React, { memo } from 'react';
import { WithPricingEnableUpgrade } from '../../../../../../components/Pricing/WithPricingComponents';
import { toastApi } from '../../../../../../components/Toast/Toast';
import { selectPricingPermission } from '../../../../../../store/company/company.selectors';
import { updateOnlineBookingPreference } from '../../../../../../store/onlineBooking/actions/private/onlineBookingPreference.actions';
import { ServiceFilterKind } from '../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { selectOnlineBookingPreference } from '../../../../../../store/onlineBooking/onlineBookingPreference.selectors';
import { useSerialUpdate } from '../../../../../../utils/hooks/hooks';

export const ServicePreference = memo(() => {
  const [preference, pricingPermission] = useSelector(selectOnlineBookingPreference, selectPricingPermission());
  const dispatch = useDispatch();
  const update = useSerialUpdate(dispatch, updateOnlineBookingPreference);

  return (
    <div>
      <Heading size="3" className="moe-mb-l">
        Service preference
      </Heading>
      <Row align="middle">
        <WithPricingEnableUpgrade permission="obByBreed" overrideEvent="onChange">
          <Switch
            isSelected={
              preference.serviceFilter === ServiceFilterKind.Open && pricingPermission.enable.has('obByBreed')
            }
            onChange={async (checked) => {
              await update('serviceFilter', { serviceFilter: +checked });
              toastApi.success('Service preference updated.');
            }}
            isDisabled={update.loading.has('serviceFilter')}
          >
            Only show applicable service and add-ons on booking page
          </Switch>
        </WithPricingEnableUpgrade>
      </Row>
      <div className="moe-text-xs moe-text-tertiary moe-mt-s">
        Customers can only book services and add-ons that match their pet&apos;s weight, type, breed, and coat type, as
        well as add-ons specific to the selected service. Please go to Setting {'>'} Services {'>'} Edit to set up.
      </div>
    </div>
  );
});
