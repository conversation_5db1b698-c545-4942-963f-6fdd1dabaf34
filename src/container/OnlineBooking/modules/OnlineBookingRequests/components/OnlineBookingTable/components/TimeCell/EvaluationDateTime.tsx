import React, { type FC } from 'react';
import { type CareTypeServiceDateTimeProps } from './timeCell.types';
import { Text } from '@moego/ui';
import dayjs from 'dayjs';
import { useSelector } from 'amos';
import { selectCurrentBusiness } from '../../../../../../../../store/business/business.selectors';
import { T_MINUTE } from 'monofile-utilities/lib/consts';
import { Condition } from '../../../../../../../../components/Condition';
import { selectCompanyCareTypeNameMap } from '../../../../../../../../store/careType/careType.selectors';

/**
 * it should be like: <Evaluation>: 01/26, 9:00 am - 10:00 am
 */
export const EvaluationDateTime: FC<CareTypeServiceDateTimeProps> = (props) => {
  const { showCareTypeName, rowData } = props;
  const { services } = rowData;

  const [business, companyCareTypeNameMap] = useSelector(selectCurrentBusiness, selectCompanyCareTypeNameMap);

  const firstEvaluationServiceDetail = services?.find((service) => service.services.some((s) => s.evaluation));
  const {
    startDate,
    startTime = 0,
    endTime = 0,
  } = firstEvaluationServiceDetail?.services.find((s) => s.evaluation)?.evaluation?.service || {};

  const appointmentDateDay = dayjs(startDate);

  return (
    <div className="moe-text-primary">
      <Condition if={showCareTypeName}>
        <Text as="span" variant="regular-short" className="moe-font-bold">
          {companyCareTypeNameMap.Evaluation}:&nbsp;
        </Text>
      </Condition>
      <Text as="span" variant="regular-short">
        {appointmentDateDay.format(business.dateFormatMD)},&nbsp;{business.formatFixedTime(startTime * T_MINUTE)}
        &nbsp;-&nbsp;{business.formatFixedTime(endTime * T_MINUTE)}
      </Text>
    </div>
  );
};
