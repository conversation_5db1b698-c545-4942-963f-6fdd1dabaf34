/*
 * @since 2020-10-14 18:54:46
 * <AUTHOR> <<EMAIL>>
 */

import { Modal, Tooltip } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { upperFirst } from 'lodash';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import React, { memo, useEffect } from 'react';
import { PartialPrintView } from '../../../components/Print/PartialPrintView';
import { usePartialPrint } from '../../../components/Print/usePartialPrint';
import { openGlobalModal } from '../../../components/globals/GlobalModals.store';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { selectCurrentPermissions } from '../../../store/business/role.selectors';
import { type PaymentRecord, defaultPayment } from '../../../store/payment/payment.boxes';
import { isNormal } from '../../../store/utils/identifier';
import { useBool } from '../../../utils/hooks/useBool';
import { RetailSaleInvoiceModal } from '../../ProductRetail/components/RetailSaleInvoiceModal';
import { PaymentReceiptView } from './PaymentReceipt.style';
import { useViewOrder } from '../../PaymentFlow/ViewOrderDrawer/useViewOrder';
import { useInvoiceReinvent } from '../../PaymentFlow/hooks/useInvoiceReinvent';
import { OrderSourceType } from '@moego/api-web/moego/models/order/v1/order_enums';

export interface PaymentReceiptProps {
  className?: string;
  item: PaymentRecord | undefined;
}

export const PaymentReceipt = memo<PaymentReceiptProps>(({ className, item = defaultPayment }) => {
  const [business, permissions] = useSelector(selectCurrentBusiness, selectCurrentPermissions);
  const dispatch = useDispatch();
  const invoiceVisible = useBool();
  const [printAreaRef] = usePartialPrint({
    listenKeyboardPrint: isNormal(item.id),
  });
  const canProcessPayment = permissions.has('canProcessPayment');
  const { isNewOrderV4Flow } = useInvoiceReinvent();
  const { openViewOrderDrawer } = useViewOrder();

  // 按Esc关闭modal时，只会触发PaymentReceiptModal的onClose, 所以外层的PaymentReceiptModal关闭时，invoiceModal也需close
  useEffect(() => {
    if (!isNormal(item.id)) {
      invoiceVisible.close();
    }
  }, [item]);

  const openInvoiceModal = () => {
    if (item.module !== 'retail') {
      if (isNewOrderV4Flow) {
        openViewOrderDrawer({
          sourceId: item.groomingId.toString(),
          sourceType: OrderSourceType.APPOINTMENT,
          businessId: item.businessId.toString(),
          module: 'grooming',
        });
        return;
      }

      dispatch(openGlobalModal({ takePayment: { invoiceId: item.invoiceId, module: 'grooming', isViewOnly: true } }));
    } else {
      invoiceVisible.open();
    }
  };

  const [desc, status] = item.printMethod();
  const method = upperFirst(desc) + (item.isPrepay ? ` (${status})` : '');

  const isMembership = item.module === 'membership';

  return (
    <PartialPrintView>
      <PaymentReceiptView ref={printAreaRef} className={className}>
        {invoiceVisible.value && isNormal(item.id) && item.module === 'retail' ? (
          <RetailSaleInvoiceModal invoiceId={item.invoiceId} onClose={invoiceVisible.close} />
        ) : null}
        <div className="business-name">{business.businessName}</div>
        <div className="title">PAYMENT RECEIPT</div>
        <div className="amount">{business.formatAmount(item.amount)}</div>
        <div className="fields">
          <div className="field">
            <span className="label">Date</span>
            <span className="value">{business.formatDateTime((item.createTime as number) * T_SECOND)}</span>
          </div>
          <div className="field">
            <span className="label">Paid by</span>
            <span className="value">{item.paidBy}</span>
          </div>
          <div className="field">
            <span className="label">Description</span>
            <span className="value">{item.description}</span>
          </div>
          <div className="field">
            <span className="label">Payment method</span>
            <span className="value">{method}</span>
          </div>
        </div>
        <div className="footer">
          <div className="intro">HAVE A PAWSOME DAY!</div>
          <div className={`invoice${canProcessPayment ? '' : ' disabled'}`}>
            <Tooltip
              isDisabled={canProcessPayment}
              side="top"
              content="Please request “Can process payment” permission from the owner"
              delay={0}
              closeDelay={0}
            >
              <span
                onClick={() => {
                  canProcessPayment && !isMembership && openInvoiceModal();
                }}
              >
                Invoice #{item?.invoiceId}
              </span>
            </Tooltip>
          </div>
        </div>
      </PaymentReceiptView>
    </PartialPrintView>
  );
});

export interface PaymentReceiptModalProps extends PaymentReceiptProps {
  onClose: () => void;
}

export const PaymentReceiptModal = memo<PaymentReceiptModalProps>(({ onClose, ...props }) => {
  return (
    <>
      <Modal
        classNames={{
          container: 'moe-w-[685px]',
        }}
        footer={null}
        isOpen={!!props.item}
        zIndex={1}
        onClose={onClose}
        shouldCloseOnInteractOutside={() => false}
      >
        <PaymentReceipt {...props} />
      </Modal>
    </>
  );
});
