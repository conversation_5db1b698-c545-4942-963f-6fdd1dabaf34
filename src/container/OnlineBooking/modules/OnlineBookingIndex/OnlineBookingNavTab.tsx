import { <PERSON><PERSON>rown } from '@moego/icons-react';
import { <PERSON><PERSON>, Tabs } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { type ReactNode, memo, useEffect, useMemo, useState } from 'react';
import { useHistory, useLocation } from 'react-router';
import { usePermissionCheck } from '../../../../components/GuardRoute/WithPermission';
import { usePricingEnableUpgrade } from '../../../../components/Pricing/pricing.hooks';
import { PATH_ONLINE_BOOKING_ABANDON_LIST, PATH_ONLINE_BOOKING_REQUESTS } from '../../../../router/paths';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import { selectOnlineBookingAbandonSummary } from '../../../../store/onlineBooking/abandonBooking.selectors';
import { getOnlineBookingAbandonList } from '../../../../store/onlineBooking/actions/private/abandonBooking.actions';
import { getBookingRequestList } from '../../../../store/onlineBooking/actions/private/onlineBooking.actions';
import { OnlineBookingRequestListType } from '../../../../store/onlineBooking/models/OnlineBookingRequest';
import { selectBusinessOnlineBookingRequests } from '../../../../store/onlineBooking/onlineBooking.selectors';
import { getPetTypeList } from '../../../../store/pet/petType.actions';
import { createEnum } from '../../../../store/utils/createEnum';
import { isNormal } from '../../../../store/utils/identifier';
import { isUndefinedOrNullOrEmptyString } from '../../../../utils/common';
import { useFilterParams } from '../OnlineBookingAbandonList/AbandonFilters/AbandonFilters.hooks';

export interface OnlineBookingNavTabProps {
  className?: string;
  children?: ReactNode | ReactNode[];
}

const NavTabsMap = createEnum<string, number, string>({
  BookingRequest: [0, PATH_ONLINE_BOOKING_REQUESTS.build()],
  AbandonList: [2, PATH_ONLINE_BOOKING_ABANDON_LIST.build()],
});

const pathNavMap = {
  [PATH_ONLINE_BOOKING_REQUESTS.path]: NavTabsMap.BookingRequest,
  [PATH_ONLINE_BOOKING_ABANDON_LIST.path]: NavTabsMap.AbandonList,
};

export const OnlineBookingNavTab = memo<OnlineBookingNavTabProps>(({ children }) => {
  const dispatch = useDispatch();
  const [business, requestList, { abandonedClientAbandonedCount }, currentPermissions] = useSelector(
    selectCurrentBusiness,
    selectBusinessOnlineBookingRequests(OnlineBookingRequestListType.Requests),
    selectOnlineBookingAbandonSummary(),
    selectCurrentPermissions(),
  );
  const filterParam = useFilterParams();
  const { access } = usePricingEnableUpgrade('abandonedBookings');
  const history = useHistory();
  const { pathname } = useLocation();
  const canViewOnlineBooking = usePermissionCheck({ permissions: 'viewOnlineBooking' });
  const canAccessAbandonBookings = usePermissionCheck({ permissions: 'canAccessAbandonBookings' });
  const [currentTab, setCurrentTab] = useState(NavTabsMap.BookingRequest);

  useEffect(() => {
    if (isNormal(business.id)) {
      if (currentPermissions.has('viewOnlineBooking')) {
        dispatch(getBookingRequestList({ type: OnlineBookingRequestListType.Requests, pageNum: 1 }));
      }
      if (currentPermissions.has('canAccessAbandonBookings')) {
        dispatch(getOnlineBookingAbandonList({ filter: filterParam, pageNum: 1 }));
      }

      dispatch(getPetTypeList({ useNewASApi: false }));
    }
  }, [business.id]);

  useEffect(() => {
    if (!isUndefinedOrNullOrEmptyString(pathNavMap[pathname])) {
      setCurrentTab(pathNavMap[pathname]);
    }
  }, [pathname]);

  const NavTabsList = useMemo(() => {
    const list = [];

    if (canViewOnlineBooking) {
      list.push(
        <Tabs.Item
          label={
            <div className="!moe-flex !moe-items-center">
              Booking requests
              {!!requestList.total && <Badge.Count count={requestList.total} />}
            </div>
          }
          key={NavTabsMap.BookingRequest}
        />,
      );
    }

    if (canAccessAbandonBookings) {
      list.push(
        <Tabs.Item
          label={
            <div className="!moe-flex !moe-items-center">
              Abandoned bookings
              {!access && <MajorCrown />}
              {access && !!abandonedClientAbandonedCount && <Badge.Count count={abandonedClientAbandonedCount} />}
            </div>
          }
          key={NavTabsMap.AbandonList}
        />,
      );
    }

    return list;
  }, [canViewOnlineBooking, canAccessAbandonBookings, requestList, access, abandonedClientAbandonedCount]);

  return (
    <div className="moe-relative">
      <Tabs
        onChange={(key) => {
          const path = NavTabsMap.mapLabels[Number(key)];
          if (Number(key) === currentTab) return;

          setCurrentTab(Number(key));
          path && history.push(path);
        }}
        selectedKey={currentTab.toString()}
      >
        {NavTabsList}
      </Tabs>
      {children}
    </div>
  );
});
