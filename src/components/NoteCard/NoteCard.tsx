/*
 * @since 2020-09-02 09:30:19
 * <AUTHOR> <<EMAIL>>
 */

import { MajorEditOutlined, MajorPinOutlined, MajorTrashOutlined, MinorPinFilled } from '@moego/icons-react';
import { type Action, useDispatch, useSelector } from 'amos';
import { Form, Popconfirm } from 'antd';
import { type FormInstance } from 'antd/es/form';
import TextArea from 'antd/es/input/TextArea';
import { capitalize } from 'lodash';
import React, { memo, useEffect, useRef, useState } from 'react';
import { type ClientDetailAnchorPoint, type PetDetailAnchorPoint } from '../../router/paths';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { computeNoteInfo } from '../../store/customer/customer.selectors';
import { selectCurrentStaff } from '../../store/staff/staff.selectors';
import { formInput } from '../../utils/form';
import { useSerialCallback } from '../../utils/hooks/useSerialCallback';
import { alertApi } from '../Alert/AlertApi';
import { Button } from '../Button/Button';
import { AddButton } from '../Button/Button.style';
import { Modal } from '../Modal/Modal';
import { AlignRight } from '../Style/Style';
import { toastApi } from '../Toast/Toast';
import { NoteCardView } from './NoteCard.style';
import { ObserverView } from '../ObserverView';

export interface CommonNoteRecord {
  readonly accountId: number;
  readonly accountName: string;
  readonly lastAccountId: number;
  readonly lastAccountName: string;
  readonly createTime: number;
  readonly updateTime: number;
  readonly note: string;
  getId(): number;
  isPinned?: boolean;
}

export type NoteCardScenario = 'Client' | 'Pet';

export interface NoteCardProps {
  noteList: CommonNoteRecord[];
  createAdd: (note: string) => Action;
  createUpdate: (noteId: number, note: string) => Action;
  createDelete: (noteId: number) => Action;
  createPin?: (noteId: number) => Action;
  createUnpin?: (noteId: number) => Action;
  allowPin?: boolean;
  scenario: NoteCardScenario;
  className?: string;
  anchorPoint?: ClientDetailAnchorPoint | PetDetailAnchorPoint;
}

export const NoteCard = memo<NoteCardProps>(
  ({
    noteList,
    scenario,
    createDelete,
    createAdd,
    createUpdate,
    createPin,
    createUnpin,
    className,
    anchorPoint,
    allowPin = false,
  }) => {
    const [business, staff] = useSelector(selectCurrentBusiness, selectCurrentStaff);
    const [active, setActive] = useState<CommonNoteRecord | null>();
    const dispatch = useDispatch();
    const handleDelete = (note: CommonNoteRecord) => () => {
      dispatch(createDelete(note.getId())).then(() => {
        alertApi.warn(scenario + ' note deleted successfully!');
      });
    };
    return (
      <NoteCardView
        className={className}
        headerClassName="moe-flex moe-justify-between moe-items-center"
        title={
          <ObserverView viewActionName="note_card_title_view" clickActionName="note_card_title_click">
            <div data-anchor-point={anchorPoint}>{`${scenario} note (${noteList.length})`}</div>
          </ObserverView>
        }
        extra={<AddButton onClick={() => setActive(null)}>+ {capitalize(scenario)} note</AddButton>}
      >
        <NoteModal
          onClose={() => setActive(void 0)}
          note={active}
          createAdd={createAdd}
          createUpdate={createUpdate}
          scenario={scenario}
        />
        {noteList.map((note) => {
          const isPinned = note.isPinned;
          return (
            <div className="note-item" key={note.getId()}>
              <div className="moe-flex moe-flex-col moe-gap-y-[8px]">
                <div className="note">{note.note}</div>
                <div className="actions">
                  <div className="times">
                    <div className="create">
                      {computeNoteInfo(
                        'Created',
                        note.createTime,
                        staff.id === note.accountId ? 'you' : note.accountName,
                        business,
                      ).join(' ')}
                    </div>
                    {note.updateTime !== note.createTime ? (
                      <div className="update">
                        {computeNoteInfo(
                          'Last edit',
                          note.updateTime,
                          staff.id === note.lastAccountId ? 'you' : note.lastAccountName,
                          business,
                        ).join(' ')}
                      </div>
                    ) : null}
                  </div>
                </div>
              </div>

              <div className="ops moe-whitespace-nowrap">
                {allowPin &&
                  (isPinned ? (
                    <MinorPinFilled
                      className="!moe-w-[24px] !moe-h-[24px] moe-p-[2px] !moe-text-[24px] moe-cursor-pointer moe-text-gray-400"
                      onClick={() => dispatch(createUnpin!(note.getId()))}
                    />
                  ) : (
                    <MajorPinOutlined
                      className="!moe-w-[24px] !moe-h-[24px] moe-p-[2px] !moe-text-[24px] moe-cursor-pointer moe-text-gray-900"
                      onClick={() => dispatch(createPin!(note.getId()))}
                    />
                  ))}
                <MajorEditOutlined
                  className="!moe-w-[24px] !moe-h-[24px] moe-p-[2px]  moe-ml-xs moe-cursor-pointer moe-text-gray-900"
                  onClick={() => setActive(note)}
                />
                <Popconfirm
                  title="Are you sure to delete this note?"
                  onConfirm={handleDelete(note)}
                  okText="Yes"
                  okType="danger"
                  cancelText="No"
                  placement="topRight"
                >
                  <MajorTrashOutlined className="!moe-w-[24px] !moe-h-[24px] moe-p-[2px] moe-ml-xs moe-cursor-pointer moe-text-gray-900" />
                </Popconfirm>
              </div>
            </div>
          );
        })}
      </NoteCardView>
    );
  },
);

export interface NoteModalProps {
  onClose: () => void;
  note: CommonNoteRecord | null | undefined;
  createAdd: (note: string) => Action<any>;
  createUpdate: (noteId: number, note: string) => Action<any>;
  scenario: NoteCardScenario;
}

export const noteFormInput = formInput<CommonNoteRecord>().copy('note');

export const NoteModal = memo<NoteModalProps>(({ note, onClose, createUpdate, createAdd, scenario }) => {
  const form = useRef<FormInstance>(null);
  const dispatch = useDispatch();
  useEffect(() => {
    noteFormInput.attach(form, note);
  }, [note]);
  const handleSubmit = useSerialCallback(async () => {
    const input = await noteFormInput.validate(form);
    if (!note) {
      await dispatch(createAdd(input.note));
    } else {
      await dispatch(createUpdate(note.getId(), input.note));
    }
    toastApi.success(scenario + ' note saved successfully.');
    onClose();
  });
  return (
    <Modal
      title={note === null ? `Add ${scenario.toLowerCase()} note` : `Edit ${scenario.toLowerCase()} note`}
      visible={note !== void 0}
      onClose={onClose}
      width="808px"
    >
      <Form ref={form} labelAlign="left">
        <Form.Item
          label={scenario + ' note'}
          name="note"
          rules={[{ required: true, message: 'Please input note content', transform: (v) => v.trim() }]}
        >
          <TextArea autoSize={{ minRows: 3, maxRows: 6 }} style={{ resize: 'none' }} maxLength={3000} />
        </Form.Item>
      </Form>
      <AlignRight>
        <Button onClick={handleSubmit} loading={handleSubmit.isBusy()} btnType="primary" buttonRadius="circle">
          Save
        </Button>
      </AlignRight>
    </Modal>
  );
});
