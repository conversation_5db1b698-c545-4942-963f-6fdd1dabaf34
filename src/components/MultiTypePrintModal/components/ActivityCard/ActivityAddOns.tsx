import { omit } from 'lodash';
import React, { useMemo } from 'react';
import { type ActivityPrintCardInfo } from '../../../../store/printCard/activityCard/activityCard.actions';
import { useActivityCardCompareFn } from '../../hooks/useActivityCardCompareFn';
import { type ActivityTableDataItem } from './Activity.type';
import { ActivityTable } from './ActivityTable';

const convertAddOn = ({
  associatedServiceName,
  startTime,
  quantityPerDay,
  staffName,
  ...rest
}: ActivityPrintCardInfo['addOns'][number]): ActivityTableDataItem[] => {
  const returnValue = {
    ...omit(rest, ['addOnName']),
    serviceName: associatedServiceName,
    time: staffName ? startTime : undefined,
    instruction: [],
    staffName,
  };
  if (!staffName && quantityPerDay > 1) {
    return Array.from({ length: quantityPerDay }).map((_, index) => ({
      ...returnValue,
      instruction: [`${index + 1}/${quantityPerDay}`],
    }));
  }

  return [returnValue];
};

export const ActivityAddOns = (props: {
  data: ActivityPrintCardInfo['addOns'];
  lodgingTypes: ActivityPrintCardInfo['lodgingTypes'];
  lodgingUnits: ActivityPrintCardInfo['lodgingUnits'];
  className?: string;
}) => {
  const { data, className, lodgingTypes, lodgingUnits } = props;
  const activityCardCompareFn = useActivityCardCompareFn();

  const groupedByAddOnName = useMemo(() => {
    return data.reduce(
      (acc, addon) => {
        acc[addon.addOnName] ||= [];
        const convertedAddon = convertAddOn(addon);
        acc[addon.addOnName].push(...convertedAddon);
        return acc;
      },
      {} as Record<string, ActivityTableDataItem[]>,
    );
  }, [data]);

  return (
    <>
      {Object.entries(groupedByAddOnName).map(([addOnName, addOns]) => {
        const tableData = addOns
          .map((item) => ({
            ...item,
            lodgingUnitSort: lodgingUnits.find((unit) => unit.id === item.lodgingUnitId)?.sort,
            lodgingTypeSort: lodgingTypes.find((type) => type.id === item.lodgingTypeId)?.sort,
          }))
          .sort(activityCardCompareFn);
        return (
          <ActivityTable
            key={addOnName}
            className={className}
            title={addOnName}
            data={tableData}
            noDataDesc="No add-ons"
            instructionAlias="Qty"
            isAddon
          />
        );
      })}
    </>
  );
};
