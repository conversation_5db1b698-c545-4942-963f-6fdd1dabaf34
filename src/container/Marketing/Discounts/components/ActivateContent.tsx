import dayjs from 'dayjs';
import React from 'react';
import { ExpireDateSelect, type ExpireDateSelectValue } from './ExpireDateSelect';

export const ActivateContent = ({
  dateFormat,
  value,
  onChange,
}: { dateFormat: string; value: ExpireDateSelectValue; onChange: (value: ExpireDateSelectValue) => void }) => {
  return (
    <div>
      <div className="moe-text-regular-short moe-leading-[20px]">
        Since this discount code has expired, please change the expiry date to make it active again.
      </div>
      <div className="moe-mt-[24px]">
        <ExpireDateSelect
          dateFormat={dateFormat}
          value={value}
          onChange={onChange}
          label="Expiration Date"
          disabledDate={(date) => date?.isBefore(dayjs().subtract(1, 'day')) || false}
        />
      </div>
    </div>
  );
};
