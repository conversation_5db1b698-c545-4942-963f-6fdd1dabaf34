import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Drawer, Tabs } from '@moego/ui';
import { useSelector } from 'amos';
import React, { type Key, memo, useState } from 'react';
import { Switch } from '../../../../../components/SwitchCase';
import { selectBDFeatureEnable } from '../../../../../store/company/company.selectors';
import { BoardingOrDaycareWorkFlow } from './BoardingOrDaycareWorkFlow';
import { GroomingWorkFlow } from './GroomingWorkFlow';
import { selectSceneCareTypeAsOptions } from '../../../../../store/careType/careType.selectors';
import { Scene } from '../../../../../store/service/scene.enum';

export interface AutomationDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AutomationDrawer = memo(function AutomationDrawer(props: AutomationDrawerProps) {
  const { isOpen, onClose } = props;
  const [isBDEnable, careTypeOptions] = useSelector(
    selectBDFeatureEnable,
    selectSceneCareTypeAsOptions(Scene.OBAutomation),
  );
  const [currentTab, setCurrentTab] = useState(`${ServiceItemType.BOARDING}`);
  return (
    <Drawer isOpen={isOpen} onClose={onClose} title="Automation" className="moe-w-[480px]" footer={null}>
      <Switch>
        <Switch.Case if={isBDEnable}>
          <Tabs
            defaultSelectedKey={currentTab}
            onChange={(k: Key) => setCurrentTab(k as string)}
            classNames={{
              base: 'moe-mb-xs',
            }}
          >
            {careTypeOptions.map((item) => (
              <Tabs.Item key={item.value} label={item.label}></Tabs.Item>
            ))}
          </Tabs>
          <Switch>
            <Switch.Case if={currentTab !== `${ServiceItemType.GROOMING}`}>
              <BoardingOrDaycareWorkFlow serviceItemType={+currentTab} />
            </Switch.Case>
            <Switch.Case else>
              <GroomingWorkFlow />
            </Switch.Case>
          </Switch>
        </Switch.Case>
        <Switch.Case else>
          <GroomingWorkFlow />
        </Switch.Case>
      </Switch>
    </Drawer>
  );
});
