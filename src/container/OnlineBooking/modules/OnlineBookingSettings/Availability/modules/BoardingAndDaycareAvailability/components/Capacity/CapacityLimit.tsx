import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import {
  type CapacityOverrideDef,
  type LodgingAvailabilityDef,
} from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_defs';
import { Checkbox, Markup, LegacySelect as Select, Text } from '@moego/ui';
import { useDispatch } from 'amos';
import React, { memo } from 'react';
import { Condition } from '../../../../../../../../../components/Condition';
import { toastApi } from '../../../../../../../../../components/Toast/Toast';
import { OBTestIds } from '../../../../../../../../../config/testIds/onlineBooking';
import { deleteCapacityOverride } from '../../../../../../../../../store/onlineBooking/actions/private/availabilityBD.actions';
import { CapacityLimitType } from '../../../../../../../../../store/onlineBooking/settings/availabilityBD.boxes';
import { useBool } from '../../../../../../../../../utils/hooks/useBool';
import { type LatestCallback } from '../../../../../../../../../utils/hooks/useLatestCallback';
import { useBDOpacityOverrideByDate } from '../../hooks/useBDOpacityOverrideByDate';
import { useCapacityOverrideByDateModal } from '../../hooks/useCapacityOverrideByDateModal';
import { useDeleteOverrideModal } from '../../hooks/useDeleteOverrideModal';
import { OverrideByDateContainer } from '../OverrideByDate/OverrideByDateContainer';
import { CapacityOverrideByDateItem } from './components/CapacityOverrideByDateItem';

export interface CapacityLimitProps {
  lodgingAvailable: LodgingAvailabilityDef;
  updateLodgingAvailable: LatestCallback<(value: Partial<LodgingAvailabilityDef>) => void>;
  serviceItemType: ServiceItemType;
}

const capacityLimitOptions = CapacityLimitType.values.map((item) => ({
  label: CapacityLimitType.mapLabels[item],
  value: String(item),
}));

const UpdateText = 'Capacity override updated.';

export const CapacityLimit = memo<CapacityLimitProps>((props) => {
  const dispatch = useDispatch();
  const { lodgingAvailable, updateLodgingAvailable, serviceItemType } = props;
  const overrideByDateModal = useCapacityOverrideByDateModal({ serviceItemType });
  const deleteOverrideModal = useDeleteOverrideModal();
  const { capacityOverrideByDate, updateCapacityOverrideByDate } = useBDOpacityOverrideByDate(serviceItemType);
  const overrideConfigStatus = useBool(true);
  const overrideByDateList: CapacityOverrideDef[] = capacityOverrideByDate.filter(
    (item) => item.isActive === overrideConfigStatus.value,
  );

  const handleAdd = async () => {
    const res = await overrideByDateModal();
    if (res) {
      toastApi.success(UpdateText);
    }
  };

  const handleDelete = async (item: CapacityOverrideDef) => {
    if (!item.id) {
      return;
    }

    const isDelete = await deleteOverrideModal({
      onConfirm: async () => {
        await dispatch(deleteCapacityOverride({ id: item.id! }));
        updateCapacityOverrideByDate(capacityOverrideByDate.filter(({ id }) => id !== item.id));
      },
    });

    if (isDelete) {
      toastApi.success(UpdateText);
    }
  };

  const handleEdit = async (item: CapacityOverrideDef) => {
    const res = await overrideByDateModal(item);
    if (res) {
      toastApi.success(UpdateText);
    }
  };

  return (
    <div className="moe-mt-[16px] moe-flex moe-flex-col moe-gap-s moe-pl-[28px]">
      <div className="moe-flex moe-flex-col moe-gap-xxs">
        <Select
          isSearchable={false}
          value={String(lodgingAvailable.capacityLimit)}
          onChange={(value) => {
            updateLodgingAvailable({ capacityLimit: Number(value) });
          }}
          options={capacityLimitOptions}
        ></Select>
        <Text variant="small" className="moe-text-tertiary">
          The capacity will be calculated based on scheduled appointment + pending requests. Lodging capacity setting
          and service eligible lodgings can be managed at{' '}
          <Markup as="span" variant="small" className="moe-text-tertiary">
            Settings - Lodgings & Settings - Services
          </Markup>
          .
        </Text>
      </div>
      <Condition if={serviceItemType === ServiceItemType.DAYCARE}>
        <OverrideByDateContainer
          renderList={() => {
            return overrideByDateList?.map((item) => {
              return (
                <CapacityOverrideByDateItem
                  key={item.id}
                  item={item}
                  serviceItemType={serviceItemType}
                  onDelete={() => handleDelete(item)}
                  onEdit={() => handleEdit(item)}
                  isActive={overrideConfigStatus.value}
                  deleteBtnTestId={OBTestIds.SettingCapacityOverrideByDateDelete}
                  editBtnTestId={OBTestIds.SettingCapacityOverrideByDateEdit}
                />
              );
            });
          }}
          addBtnTestId={OBTestIds.SettingCapacityOverrideByDateAdd}
          handleAdd={handleAdd}
          isAllEmpty={!capacityOverrideByDate?.length}
          isCurrentTypeEmpty={!overrideByDateList?.length}
          onActiveChange={overrideConfigStatus.as}
          isActive={overrideConfigStatus.value}
        />
      </Condition>

      <Checkbox
        isSelected={lodgingAvailable.allowWaitlistSignups}
        onChange={(value) => {
          updateLodgingAvailable({ allowWaitlistSignups: value });
        }}
      >
        Allow waitlist sign-ups
      </Checkbox>
    </div>
  );
});

CapacityLimit.displayName = 'CapacityLimit';
