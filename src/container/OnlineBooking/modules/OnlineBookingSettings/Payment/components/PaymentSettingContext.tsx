import { useDispatch, useSelector } from 'amos';
import { noop } from 'lodash';
import React, {
  type PropsWithChildren,
  type RefObject,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { toast<PERSON>pi } from '../../../../../../components/Toast/Toast';
import { type OpenApiModels } from '../../../../../../openApi/schema';
import { PATH_CREDIT_CARD_SETTING, PATH_SIGN_IN } from '../../../../../../router/paths';
import {
  removeCertainClientsPayment,
  updateOnlineBookingPaymentSetting,
} from '../../../../../../store/onlineBooking/actions/private/onlineBookingPreference.actions';
import { NoShowProtectType } from '../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { selectOnlineBookingPreference } from '../../../../../../store/onlineBooking/onlineBookingPreference.selectors';
import { isNormal } from '../../../../../../store/utils/identifier';
import { abortNavigation } from '../../../../../../utils/abortNavigation';
import { useSerialUpdate } from '../../../../../../utils/hooks/hooks';
import { useBool } from '../../../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useModalToggle } from '../../../../../../utils/hooks/useModalToggle';
import { useRouteConfirmLeave } from '../../../../../../utils/hooks/useRouteConfirmLeave';
import { PolicyConfirmModal, type PolicyConfirmModalProps } from './PolicyConfirmModal';

export type DepositSubmitInput = Pick<
  OpenApiModels['PUT/grooming/bookOnline/payment/group/setting']['Req'],
  // 最后的 prepayTipEnable 在 deposit 场景下并没有用到
  // 但为了避免 narrow 掉联合类型 SubmitInput 的字段，还是加上了
  'prepayPolicy' | 'paymentType' | 'prepayTipEnable'
>;

export type DefaultSubmitInput = Omit<
  OpenApiModels['PUT/grooming/bookOnline/payment/group/setting']['Req'],
  'paymentGroupSetting'
>;
export type CertainClientsSubmitInput = NonNullable<
  OpenApiModels['PUT/grooming/bookOnline/payment/group/setting']['Req']['paymentGroupSetting']
>;
export type SubmitInput = DepositSubmitInput | DefaultSubmitInput | CertainClientsSubmitInput;

export interface PaymentSettingFormRef {
  validate: () => Promise<SubmitInput>;
}

type FormType = 'deposit' | 'default' | 'certain_clients';

interface FormState {
  isExpanded: boolean;
  isDirty: boolean;
  onStartEditing: () => void;
  onDirty: () => void;
  onFinishEditing: () => void;
  formRef: RefObject<PaymentSettingFormRef>;
  submit: () => Promise<void>;
}

const defaultFormState: FormState = {
  isExpanded: false,
  isDirty: false,
  onStartEditing: noop,
  onDirty: noop,
  onFinishEditing: noop,
  formRef: { current: null },
  submit: async () => {},
};

const useFormState = (onSubmit: (input: SubmitInput) => Promise<void>) => {
  const formRef = useRef<PaymentSettingFormRef>(null);
  const { value: isExpanded, as: setExpanded } = useBool();
  const { value: isDirty, as: setDirty } = useBool();
  const onFinishEditing = () => {
    setDirty(false);
    setExpanded(false);
  };
  return useMemo(
    () => ({
      isExpanded,
      isDirty,
      onStartEditing: () => {
        setExpanded(true);
      },
      onDirty: () => {
        setDirty(true);
      },
      onFinishEditing,
      formRef,
      submit: async () => {
        const formInput = await formRef.current?.validate();
        if (!formInput) {
          return;
        }
        await onSubmit(formInput);
        onFinishEditing();
      },
    }),
    [isExpanded, isDirty],
  );
};

interface PaymentSettingState {
  depositFormState: FormState;
  defaultFormState: FormState;
  certainClientsFormState: FormState;
  removeCertainClientsSetting: () => Promise<void>;
}

/**
 * Context that provides:
 * - UI states and callbacks;
 * - Form ref for expanded setting;
 * - submit function.
 */
export const PaymentSettingContext = createContext<PaymentSettingState>({
  depositFormState: defaultFormState,
  defaultFormState: defaultFormState,
  certainClientsFormState: defaultFormState,
  removeCertainClientsSetting: async () => {},
});

export const PaymentSettingProvider = ({ children }: PropsWithChildren<{}>) => {
  const dispatch = useDispatch();
  const [{ id, paymentSetting }] = useSelector(selectOnlineBookingPreference());

  const updatePaymentSetting = useSerialUpdate(dispatch, updateOnlineBookingPaymentSetting);
  const submit = useLatestCallback(async (type: FormType, formInput: SubmitInput) => {
    const setting = type === 'default' || type === 'deposit' ? paymentSetting : paymentSetting.certainGroupSetting;
    const paymentTypeChanged = setting?.paymentType !== formInput.paymentType;
    const isPrepayment = formInput.paymentType === NoShowProtectType.Prepayment;
    if (paymentTypeChanged && isPrepayment) {
      setPolicyConfirmModalType('prepayment');
      await policyConfirmModal.open();
    }
    await updatePaymentSetting(
      'enableNoShowFee',
      type === 'certain_clients'
        ? { paymentGroupSetting: formInput as CertainClientsSubmitInput }
        : // 更新 deposit cancellation policy 的时候，formInput 只有 prepayPolicy，没有 paymentType。
          // 但是接口必须接收一个 paymentType，所以这里传入原来的 paymentType，属于是将 bug 当 feature 用了。
          { paymentType: setting?.paymentType, ...formInput },
    );
    toastApi.success('Payment for online booking updated successfully.');
  });

  const depositFormState = useFormState((input: SubmitInput) => submit('deposit', input));
  const defaultFormState = useFormState((input: SubmitInput) => submit('default', input));
  const certainClientsFormState = useFormState((input: SubmitInput) => submit('certain_clients', input));

  const policyConfirmModal = useModalToggle();
  const [policyConfirmModalType, setPolicyConfirmModalType] = useState<PolicyConfirmModalProps['type']>('prepayment');

  const removeCertainClientsSetting = useCallback(async () => {
    await dispatch(removeCertainClientsPayment());
  }, []);

  // Triggered only on first entrance
  const isLoaded = isNormal(id);
  const loadedOneShotRef = useRef(false);
  useEffect(() => {
    if (loadedOneShotRef.current) {
      return;
    }
    if (isLoaded) {
      loadedOneShotRef.current = true;
      if (!paymentSetting.certainGroupSetting) {
        defaultFormState.onStartEditing();
      }
    }
  }, [isLoaded]);

  const routeConfirmLeaveTrigger = useBool();
  useRouteConfirmLeave({
    showConfirm: defaultFormState.isDirty || certainClientsFormState.isDirty,
    config: useMemo(
      () => ({
        onOk: async () => {
          // useRouteConfirmLeave 只能 block 一次，这里通过 routeConfirmLeaveTrigger 来立刻触发下一次 block
          routeConfirmLeaveTrigger.toggle();
          abortNavigation('Continue editing');
        },
        okText: 'Continue editing',
      }),
      [],
    ),
    predictSkip: (nextLocation) =>
      !!(PATH_SIGN_IN.match(nextLocation.pathname) || PATH_CREDIT_CARD_SETTING.match(nextLocation.pathname)),
    updateTrigger: routeConfirmLeaveTrigger.value,
  });

  const value: PaymentSettingState = useMemo(
    () => ({ depositFormState, defaultFormState, certainClientsFormState, removeCertainClientsSetting }),
    [depositFormState, defaultFormState, certainClientsFormState, removeCertainClientsSetting],
  );
  return (
    <PaymentSettingContext.Provider value={value}>
      {children}
      <PolicyConfirmModal
        visible={policyConfirmModal.isOpen}
        onOk={() => policyConfirmModal.submit(undefined)}
        onClose={policyConfirmModal.cancel}
        type={policyConfirmModalType}
      />
    </PaymentSettingContext.Provider>
  );
};

export const usePaymentSettingFormState = (type: FormType) => {
  const { depositFormState, defaultFormState, certainClientsFormState } = useContext(PaymentSettingContext);
  return type === 'default' ? defaultFormState : type === 'deposit' ? depositFormState : certainClientsFormState;
};
