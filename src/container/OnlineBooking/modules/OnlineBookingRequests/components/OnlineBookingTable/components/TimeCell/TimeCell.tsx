import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type CellContext } from '@moego/ui/dist/esm/components/Table/Table.types';
import React from 'react';
import { type OnlineBookingRequestModel } from '../../../../../../../../store/onlineBooking/models/OnlineBookingRequest';
import { getMainCareType } from '../../../../../../../../components/PetAndServicePicker/utils/getMainCareType';
import { CareTypeDateTimeRouter } from './CareTypeDateTimeRouter';

export function TimeCell(props: CellContext<OnlineBookingRequestModel, unknown>) {
  const { row } = props;
  const { appointmentDate, services, serviceItemTypes } = row.original;

  // ob request main care type
  const bookingRequestMainServiceItemType = getMainCareType(serviceItemTypes ?? []);
  // main care type of each pet
  const petMainServiceItemTypeList = services?.map((serviceDetail) => {
    return getMainCareType(serviceDetail.services.map((s) => s.serviceItemType));
  });
  const hasEvaluation = serviceItemTypes?.includes(ServiceItemType.EVALUATION);
  const hasEvaluationWithOtherServices = hasEvaluation && (serviceItemTypes?.length ?? 0) > 1;

  /*
   * Show time cell content according to the following rules:
   * 1. if booking request has evaluation and other services, show evaluation + other services, like:
   *    - Evaluation: 01/25, 9:00 am - 05:00 pm
   *    - Boarding: 01/25 10:00 am - 01/28 06:00 pm
   * 2. otherwise, show booking request main care type by CareTypeDateTimeRouter
   */

  if (!appointmentDate) {
    return null;
  }

  if (hasEvaluationWithOtherServices) {
    return (
      <div className="moe-flex moe-flex-col moe-gap-y-xxs">
        {petMainServiceItemTypeList?.map((careType) => (
          <CareTypeDateTimeRouter key={careType} serviceItemType={careType} rowData={row.original} showCareTypeName />
        ))}
      </div>
    );
  }
  return (
    <CareTypeDateTimeRouter
      rowData={row.original}
      showCareTypeName={false}
      serviceItemType={bookingRequestMainServiceItemType}
    />
  );
}
