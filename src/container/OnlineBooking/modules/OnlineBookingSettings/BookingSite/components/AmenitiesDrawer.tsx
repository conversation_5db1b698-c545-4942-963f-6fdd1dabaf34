import { Button, Heading, Radio, RadioGroup } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { Divider } from 'antd';
import React, { memo, useCallback, useEffect, useState } from 'react';
import { UpgradePopover } from '../../../../../../components/Pricing/UpgradePopover';
import { toast<PERSON><PERSON> } from '../../../../../../components/Toast/Toast';
import { updateOnlineBookingLatestPreference } from '../../../../../../store/onlineBooking/actions/private/onlineBookingPreference.actions';
import { selectOnlineBookingLatestPreference } from '../../../../../../store/onlineBooking/onlineBookingPreference.selectors';
import { StyledDrawer, type StyledDrawerProps } from '../../../../../settings/components/StyledDrawer';
import { AmenitiesConfig, type AmenitiesConfigItem } from '../Overview.config';
import { ConfigCheck } from './Components.style';
import { useFeatureValue } from '@growthbook/growthbook-react';
import { GrowthBookFeatureList } from '../../../../../../utils/growthBook/growthBook.config';
import { merge } from 'lodash/fp';

export const AmenitiesDrawer = memo<Pick<StyledDrawerProps, 'visible' | 'onClose'>>(({ visible, onClose }) => {
  const [preference] = useSelector(selectOnlineBookingLatestPreference);
  const [features, setFeatures] = useState(preference.amenities.features);
  const [payment, setPayment] = useState(preference.amenities.payment);
  const { features: extraFeatures = [], payment: extraPayment = [] } = useFeatureValue<{
    features: AmenitiesConfigItem[];
    payment: AmenitiesConfigItem[];
  }>(GrowthBookFeatureList.ExtraAmenities, { features: [], payment: [] });

  const dispatch = useDispatch();
  const handleSave = useCallback(async () => {
    await dispatch(updateOnlineBookingLatestPreference({ amenities: { features, payment } }));
    toastApi.success('Amenities updated.');
    onClose();
  }, [preference, features, payment]);

  useEffect(() => {
    if (!visible) {
      setFeatures(
        merge(
          preference.amenities.features,
          extraFeatures.reduce(
            (acc, curr) => {
              acc[curr.label] = preference.amenities.features[curr.label] ?? false;
              return acc;
            },
            {} as Record<string, boolean>,
          ),
        ),
      );
      setPayment(
        merge(
          preference.amenities.payment,
          extraPayment.reduce(
            (acc, curr) => {
              acc[curr.label] = preference.amenities.payment[curr.label] ?? false;
              return acc;
            },
            {} as Record<string, boolean>,
          ),
        ),
      );
    }
  }, [visible, preference, extraFeatures.join(','), extraPayment.join(',')]);

  return (
    <StyledDrawer
      width={360}
      visible={visible}
      title={<Heading size="5">Amenities</Heading>}
      onClose={onClose}
      footer={
        <div className="moe-flex moe-justify-center moe-gap-x-[8px]">
          <Button variant="secondary" onPress={onClose}>
            Cancel
          </Button>
          <UpgradePopover permission="amenities" overrideEvent="onPress">
            <Button variant="primary" onPress={handleSave}>
              Confirm
            </Button>
          </UpgradePopover>
        </div>
      }
    >
      {AmenitiesConfig.values.map((categoryName, index, array) => {
        const AmenitiesItemList = AmenitiesConfig.mapLabels[categoryName].concat(
          categoryName === 'Features' ? extraFeatures : extraPayment,
        );
        const values = (categoryName === 'Features' ? features : payment) ?? {};
        const set = (k: string, v: boolean) => {
          const newValues = { ...values, [k]: v };
          if (k === 'cashOnly' && v) {
            newValues.acceptCreditCards = false;
            newValues.androidPay = false;
            newValues.applePay = false;
          } else if (k === 'acceptCreditCards' && v) {
            newValues.cashOnly = false;
          } else if (k === 'androidPay' || k === 'applePay') {
            newValues.acceptCreditCards = true;
            newValues.cashOnly = false;
          }
          return (categoryName === 'Features' ? setFeatures : setPayment)(newValues);
        };

        const radioValue = categoryName === 'Payment' ? (values['cashOnly'] ? 'cashOnly' : 'acceptCreditCards') : '';

        return (
          <div key={categoryName} className="moe-font-manrope">
            <div className="!moe-mb-[16px] !moe-text-[16px] !moe-font-bold">{categoryName}</div>
            <div className="!moe-flex !moe-flex-col !moe-gap-y-[12px]">
              <RadioGroup onChange={(e) => set(e, true)} value={radioValue}>
                {AmenitiesItemList.map(({ name, label }) => {
                  const mobilePay = name === 'applePay' || name === 'androidPay';
                  const showRadio = name === 'cashOnly' || name === 'acceptCreditCards';
                  const checked = values[name ?? label];
                  if (showRadio) {
                    return (
                      <Radio key={name} value={name} isSelected={checked}>
                        {label}
                      </Radio>
                    );
                  }
                  return (
                    <ConfigCheck
                      className={mobilePay ? '!moe-ml-[24px]' : ''}
                      key={name ?? label}
                      checked={checked}
                      label={label}
                      onChange={(checked) => set(name ?? label, checked)}
                    />
                  );
                })}
              </RadioGroup>
            </div>
            {index !== array.length - 1 && <Divider className="!moe-my-[24px]" />}
          </div>
        );
      })}
    </StyledDrawer>
  );
});
