import { Moe<PERSON><PERSON> } from '@moego/finance-utils';
import {
  FNK_OrderItemType,
  type IGeneralOrderDetailV2,
  type IRefundOrderDetail,
  RealmType,
  assertModel,
} from '@moego/finance-web-kit';
import { Heading, Text, cn } from '@moego/ui';
import { useSelector } from 'amos';
import React from 'react';
import { type FinanceKit } from '../../../../../service/finance-kit';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';

export const ServiceSubtotal = (props: {
  orderDetail: IGeneralOrderDetailV2<typeof FinanceKit.model> | IRefundOrderDetail<typeof FinanceKit.model>;
  className?: string;
}) => {
  const { className, orderDetail } = props;
  const { subtotalByItemType, order } = orderDetail;
  const [business] = useSelector(selectCurrentBusiness);
  const isRefund = assertModel(order, RealmType.RefundOrder);

  const serviceSubtotalAmount = subtotalByItemType[FNK_OrderItemType.Service] ?? MoeMoney.empty();
  const serviceChargeSubtotalAmount = subtotalByItemType[FNK_OrderItemType.ServiceCharge] ?? MoeMoney.empty();
  const noShowSubtotalAmount = subtotalByItemType[FNK_OrderItemType.NoShow] ?? MoeMoney.empty();

  const preDiscountSubtotalByItemType = isRefund
    ? subtotalByItemType
    : (orderDetail as IGeneralOrderDetailV2<typeof FinanceKit.model>).preDiscountSubtotalByItemType;
  const preDiscountServiceSubtotalAmount =
    preDiscountSubtotalByItemType?.[FNK_OrderItemType.Service] ?? MoeMoney.empty();
  const preDiscountServiceChargeSubtotalAmount =
    preDiscountSubtotalByItemType?.[FNK_OrderItemType.ServiceCharge] ?? MoeMoney.empty();
  const preDiscountNoShowSubtotalAmount = preDiscountSubtotalByItemType?.[FNK_OrderItemType.NoShow] ?? MoeMoney.empty();

  const subtotalAmount = serviceSubtotalAmount.plus(serviceChargeSubtotalAmount).plus(noShowSubtotalAmount);
  const preDiscountSubtotalAmount = preDiscountServiceSubtotalAmount
    .plus(preDiscountServiceChargeSubtotalAmount)
    .plus(preDiscountNoShowSubtotalAmount);
  const havePromotion = preDiscountSubtotalAmount.valueOf() !== subtotalAmount.valueOf();

  const showSubtotalAmount = isRefund ? -subtotalAmount.valueOf() : subtotalAmount.valueOf();
  const showPreDiscountSubtotalAmount = isRefund
    ? -preDiscountSubtotalAmount.valueOf()
    : preDiscountSubtotalAmount.valueOf();

  return (
    <div className={cn('moe-flex moe-items-start moe-justify-between', className)}>
      <Heading size="5">Service subtotal</Heading>
      <div className="moe-flex moe-flex-col moe-items-end">
        <Heading size="5">{business.formatAmount(showSubtotalAmount)}</Heading>
        {havePromotion ? (
          <Text variant="small" className="moe-text-tertiary moe-line-through">
            {business.formatAmount(showPreDiscountSubtotalAmount)}
          </Text>
        ) : null}
      </div>
    </div>
  );
};
