import { useSelector } from 'amos';
import { useDispatch } from 'amos';
import dayjs from 'dayjs';
import { useCallback } from 'react';
import { toast<PERSON><PERSON> } from '../../../../../components/Toast/Toast';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { openNewEvaluationDrawer } from '../../../../../store/evaluation/evaluation.actions';
import { EvaluationQuickAddFrom } from '../../../../../store/evaluation/evaluation.boxes';
import { useInitBusinessApplicableEvaluation } from '../../../../../store/evaluation/evaluation.hooks';
import {
  EvaluationQuickAddStep,
  getDefaultEvaluationServiceInfo,
} from '../../../../../store/evaluation/evaluation.types';
import { type OnlineBookingAbandonRecordType } from '../../../../../store/onlineBooking/abandonBooking.boxes';
import { DATE_FORMAT_EXCHANGE } from '../../../../../utils/DateTimeUtil';
import { EvaluationNotFoundError } from '../../../../Appt/modules/QuickAddEvaluationApptDrawer/utils/errors';
import { useEvaluationServiceDetailsEditModal } from '../../OnlineBookingRequests/hooks/useEvaluationServiceDetailsEditModal';
import { useGetCreateTicketRouteState } from './useGetCreateTicketRouteState';
import { isNormal } from '../../../../../store/utils/identifier';

export const useEvaluationAbandonToSchedule = () => {
  const [business] = useSelector(selectCurrentBusiness);
  const dispatch = useDispatch();
  const getCreateTicketRouteState = useGetCreateTicketRouteState();

  const { isMultipleEvaluationMode, currentEvaluation, evaluationList } = useInitBusinessApplicableEvaluation(true);
  const evaluationServiceDetailsEditModal = useEvaluationServiceDetailsEditModal();

  return useCallback(
    async (abandonRecord: OnlineBookingAbandonRecordType, clientId?: number, petIdList?: number[]) => {
      const { date, time, presetInfo } = await getCreateTicketRouteState(abandonRecord);
      const petId = petIdList?.[0] || presetInfo?.petAndServices?.[0]?.pet;
      const defaultEvaluationServiceInfo = getDefaultEvaluationServiceInfo();

      if (!currentEvaluation) {
        toastApi.error('Evaluation is inactive based on your settings.');
        throw new EvaluationNotFoundError();
      }

      let finalEvaluation = currentEvaluation.toJSON();
      if (isMultipleEvaluationMode) {
        const res = await evaluationServiceDetailsEditModal({
          petId: petId.toString(),
          isEvaluationPickerEnable: true,
          isServiceTimeEnable: false,
          isStaffSelectEnable: false,
        });
        const newEvaluationId = res?.evaluationId;
        if (!newEvaluationId) {
          return;
        }
        const newEvaluation = evaluationList.find((evaluation) => evaluation.id === newEvaluationId);
        if (newEvaluation) {
          finalEvaluation = newEvaluation.toJSON();
        }
      }

      // todo(ikun): 需要支持 muti pet evaluation
      const {
        id: serviceId = '',
        price: servicePrice = 0,
        duration: serviceTime = 0,
        colorCode: serviceColorCode = '',
      } = finalEvaluation;

      const petIds = petIdList ?? abandonRecord.pets.map((pet) => pet.petId).filter(isNormal);

      dispatch(
        openNewEvaluationDrawer(
          business.id,
          {
            customerId: clientId || presetInfo?.client?.value,
            petIds,
            evaluationInfo: {
              ...defaultEvaluationServiceInfo,
              startTime: dayjs(time).getMinutes(),
              startDate: dayjs(date).format(DATE_FORMAT_EXCHANGE),
              serviceColorCode,
              petServiceList:
                petIds?.map((petId) => ({
                  petId: petId.toString(),
                  serviceId,
                  servicePrice,
                  serviceTime,
                })) || [],
            },
            stepInfo: {
              type: EvaluationQuickAddStep.AfterSelectPetAndEvaluation,
            },
          },
          EvaluationQuickAddFrom.OBAbandon,
        ),
      );
    },
    [getCreateTicketRouteState],
  );
};
