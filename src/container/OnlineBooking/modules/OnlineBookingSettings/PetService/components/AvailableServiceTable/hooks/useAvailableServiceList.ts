import { useSelector } from 'amos';
import { selectCategoryServices } from '../../../../../../../../store/service/service.selectors';
import { type ServiceItemType, type ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { serviceMapBox } from '../../../../../../../../store/service/service.boxes';
import { useMemo } from 'react';
import { selectCareTypeCategoryServiceList } from '../../../../../../../../store/onlineBooking/onlineBookingServices.selectors';
import { isNormal } from '@moego/finance-utils';

export interface UseAvailableServiceListParams {
  serviceType: ServiceType;
  serviceItemType: ServiceItemType;
  categoryId: number;
  careTypeId?: string;
}

export const useAvailableServiceList = ({
  serviceType,
  serviceItemType,
  categoryId,
  careTypeId,
}: UseAvailableServiceListParams) => {
  const [serviceIdList, careTypeServiceIdList, serviceMap] = useSelector(
    selectCategoryServices([serviceType, serviceItemType], categoryId),
    selectCareTypeCategoryServiceList(careTypeId ?? '', categoryId),
    serviceMapBox,
  );

  const idList = isNormal(careTypeId) ? careTypeServiceIdList : serviceIdList;

  const serviceList = useMemo(() => {
    return idList.toArray().map((serviceId) => serviceMap.mustGetItem(serviceId));
  }, [idList, serviceMap]);

  return serviceList;
};
