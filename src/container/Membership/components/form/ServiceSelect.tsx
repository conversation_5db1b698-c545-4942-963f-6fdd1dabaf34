import {
  Checkbox,
  Form,
  type GroupBase,
  type Option,
  LegacySelect as Select,
  Switch,
  Tabs,
  Text,
  Tooltip,
  cn,
  type useForm,
  useWatch,
} from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { currentAccountIdBox } from '../../../../store/account/account.boxes';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { getAllCompanyBasicServiceInfoList } from '../../../../store/service/actions/public/service.actions';
import { AllServicesItemType } from '../../../../store/service/category.boxes';
import { selectAllCategoriesOfAllServices } from '../../../../store/service/category.selectors';
import { ServiceActive, companyServiceMapBox } from '../../../../store/service/service.boxes';

import { type FormItemProps } from '@moego/ui/dist/esm/components/Form/FormItem';
import { useAsyncEffect } from 'ahooks';
import { get } from 'lodash';
import { PATH_MEMBERSHIP } from '../../../../router/paths';
import { type MembershipModel, companyMembershipMapBox } from '../../../../store/membership/membership.boxes';
import { useRouteQueryV2 } from '../../../../utils/RoutePath';
import { useServicesTitleMap } from '../../../../utils/hooks/useServicesTitleMap';

export function ServiceSelect({
  showTabs = false,
  form,
  formItemName,
  formItemSelectAllName,
  acceptAllServices = false,
  multiple = false,
  disabled = false,
  serviceType = [AllServicesItemType.grooming],
  rules,
  selectAllLabel = 'All services (including new services)',
  disabledServiceIds = [],
}: {
  showTabs?: boolean;
  form: ReturnType<typeof useForm<MembershipModel>>;
  formItemName: any;
  formItemSelectAllName?: any;
  acceptAllServices?: boolean;
  disabled?: boolean;
  multiple?: boolean;
  serviceType: (typeof AllServicesItemType)[keyof typeof AllServicesItemType][];
  rules?: FormItemProps<any, any>['rules'];
  selectAllLabel?: string;
  disabledServiceIds?: number[];
}) {
  const dispatch = useDispatch();

  const [currentTab, setCurrentTab] = useState<(typeof AllServicesItemType)[keyof typeof AllServicesItemType]>(
    serviceType?.[0] ?? AllServicesItemType.grooming,
  );
  const [business, accountId, serviceMap] = useSelector(
    selectCurrentBusiness,
    currentAccountIdBox,
    companyServiceMapBox,
  );
  const [allServices] = useSelector(selectAllCategoriesOfAllServices(accountId, false, serviceType));
  const hasInit = useRef<boolean>(false);
  const { id } = useRouteQueryV2(PATH_MEMBERSHIP);
  const [companyMembershipMap] = useSelector(companyMembershipMapBox);
  const serviceLoaded = useRef<boolean>(false);
  const [formItemValue] = useWatch({
    control: form.control,
    name: [formItemName],
  });
  const [formItemSelectAllNameValue] = useWatch({
    control: form.control,
    name: [formItemSelectAllName],
  });

  const servicesTitleMap = useServicesTitleMap();

  const serviceOptions = useMemo(() => {
    if (currentTab) {
      return allServices[currentTab]
        .map<GroupBase<Option>>((service) => {
          return {
            label: service.label,
            options: service.options.map<Option>((option) => {
              const isSelected = multiple
                ? (formItemValue?.includes?.(option.value + '') ?? false)
                : formItemValue == option.value;
              return {
                label: option.label,
                value: option.value + '',
                price: serviceMap.mustGetItem(option.value as number).price,
                isDisabled: disabledServiceIds?.includes(option.value) && !isSelected,
                isSelected,
              };
            }),
          };
        })
        .filter((service) => service.options.length > 0);
    }
    return [];
  }, [allServices, currentTab, disabledServiceIds, formItemValue, multiple, serviceMap]);

  const onSelectAll = (checked: boolean) => {
    if (checked) {
      const checkAllValue =
        serviceType
          .flatMap((key) => allServices[key])
          .flatMap((item) => item.options.map((option) => option.value + '')) ?? [];

      form.setValue(formItemName, checkAllValue);
    } else {
      form.setValue(formItemName, []);
    }
  };

  useEffect(() => {
    if (!multiple) {
      return;
    }
    const isAllServiceSelect = serviceType.reduce((pre, key) => {
      return (
        pre &&
        allServices[key]?.reduce((acc, item) => {
          // eslint-disable-next-line sonarjs/no-nested-functions
          return acc && item.options.every((option) => formItemValue?.includes?.(option.value + ''));
        }, true)
      );
    }, true);
    if (formItemSelectAllNameValue === isAllServiceSelect) {
      return;
    }
    form.setValue(formItemSelectAllName, isAllServiceSelect);
  }, [multiple, formItemValue, allServices]);

  useAsyncEffect(async () => {
    if (serviceType.every((st) => allServices[st].flatMap((item) => item.options).length > 0)) {
      serviceLoaded.current = true;
      return;
    }
    await dispatch(
      getAllCompanyBasicServiceInfoList({
        inactive: !!ServiceActive.Active,
        withAddon: true,
      }),
    );
    serviceLoaded.current = true;
  }, []);

  const serviceName = useMemo(() => {
    return serviceType
      .flatMap((key) => allServices[key])
      .flatMap((item) => item.options)
      .filter((option) => (multiple ? formItemValue?.includes?.(option.value + '') : formItemValue == option.value))
      .map((option) => option.label)
      .join(', ');
  }, [allServices, serviceType, formItemValue, multiple]);

  const findName = (id: string | number | null) => {
    if (!id) return '';
    return serviceType
      .flatMap((key) => allServices[key])
      .flatMap((item) => item.options)
      .find((option) => option.value == id)?.label;
  };

  useEffect(() => {
    if (!serviceLoaded.current) return;
    if (!allServices || Object.keys(allServices).length === 0) return;
    if (id) {
      if (hasInit.current) return;
      hasInit.current = true;
      const membership = companyMembershipMap.mustGetItem(id).toJSON();
      if (get(membership, formItemSelectAllName)) {
        onSelectAll(true);
      }
    }
  }, [allServices, id]);

  return (
    <Tooltip
      key={'service-select' + formItemName}
      content={serviceName}
      side="top"
      isDisabled={serviceName.length === 0}
    >
      <div>
        <Form.Item name={formItemName} rules={rules}>
          <Select
            className="moe-w-[414px]"
            isMultiple={multiple}
            multipleMode="value"
            options={serviceOptions}
            isRequired
            isDisabled={disabled}
            placeholder="Select service"
            renderItem={(option) => {
              const { data } = option as unknown as {
                data: { value: any; label: string; price: number; isDisabled: boolean; isSelected: boolean };
              };
              return (
                <div key={data.value} className="moe-flex moe-items-center moe-gap-xs moe-justify-between moe-w-full">
                  <div
                    className={cn(['moe-flex moe-items-center moe-gap-xs', { 'moe-text-disabled': data.isDisabled }])}
                  >
                    {multiple && <Checkbox.Icon isSelected={data.isSelected} isDisabled={data.isDisabled} />}

                    {data.label}
                  </div>
                  <Text variant="small" className={cn(['moe-flex-shrink-0', { 'moe-text-disabled': data.isDisabled }])}>
                    {business.formatAmount(data.price as number)}
                  </Text>
                </div>
              );
            }}
            renderMenu={(menu) => {
              return (
                <div className="moe-w-full moe-flex moe-flex-col moe-h-[336px]">
                  {showTabs && (
                    <Tabs
                      selectedKey={currentTab}
                      className="moe-relative moe-shrink-0"
                      onChange={(key) => {
                        setCurrentTab(key as string);
                      }}
                      classNames={{
                        base: ['moe-w-full moe-px-spacing-xs moe-pt-spacing-xs'],
                        tabList: ['moe-mx-xs moe-w-[calc(100%_-_16px)] moe-overflow-x-auto'],
                        panel: ['moe-h-0 moe-pt-0'],
                      }}
                    >
                      {serviceType.map((key) => {
                        return <Tabs.Item label={servicesTitleMap[key]} key={key}></Tabs.Item>;
                      })}
                    </Tabs>
                  )}
                  <div className="moe-relative moe-flex-1 moe-overscroll-y-auto moe-min-h-0">{menu}</div>
                  {acceptAllServices && (
                    <div className="moe-relative moe-h-[52px] moe-flex-shrink-0 moe-p-[16px] moe-border-t-divider moe-border-t-[1px]">
                      <Form.Item name={formItemSelectAllName} />
                      <Switch
                        isSelected={formItemSelectAllNameValue}
                        isDisabled={
                          !(
                            (formItemValue?.length || 0) >= (disabledServiceIds?.length || 0) ||
                            formItemSelectAllNameValue
                          )
                        }
                        onChange={onSelectAll}
                      >
                        {selectAllLabel}
                      </Switch>
                    </div>
                  )}
                </div>
              );
            }}
            showSelectAll
            renderMultipleValues={(values) => {
              if (formItemSelectAllNameValue) {
                return selectAllLabel;
              }
              return values
                .map((value) => {
                  return findName(value.value);
                })
                .join(', ');
            }}
            formatOptionLabel={(option) => {
              // 用户切换 tab 后，上一个 tab 的 options 会被清理。
              // 如果 select 中的 formItemValue 是上一个 tab 中的数据，这里需要手动显示上一个 tab 中的 label。
              return findName(option.value);
            }}
          />
        </Form.Item>
      </div>
    </Tooltip>
  );
}
