import { type PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import { type EvaluationBriefView } from '@moego/api-web/moego/models/offering/v1/evaluation_models';
import { type ServiceItemType, type ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type ServiceBriefView } from '@moego/api-web/moego/models/offering/v1/service_models';
import { type BoardingServiceDetailModel } from '@moego/api-web/moego/models/online_booking/v1/boarding_service_detail_models';
import { type DaycareServiceDetailModel } from '@moego/api-web/moego/models/online_booking/v1/daycare_service_detail_models';
import { type EvaluationTestDetailModel } from '@moego/api-web/moego/models/online_booking/v1/evaluation_test_detail_models';
import {
  type AcceptBookingRequestV2RequestCreateEvaluationRequest,
  type AcceptBookingRequestV2Request,
} from '@moego/api-web/moego/service/online_booking/v1/booking_request_service';
import { type Dayjs } from 'dayjs';

export interface LodgingItemsDetail {
  /** primary keys */
  careType: ServiceItemType;
  serviceDetailId: string;
  /** basic info */
  petId: string;
  serviceId: string;
  startDate: string;
  endDate: string;
  startTime: number;
  endTime: number;
  service: ServiceBriefView;
  serviceDetail: BoardingServiceDetailModel;
}

export interface StaffTimeDetail {
  /** primary keys */
  careType: ServiceItemType;
  serviceType: ServiceType;
  serviceDetailId: string;
  /** basic info */
  petId: string;
  serviceId: string;
  serviceName: string;
  /**
   * OB Request 中：
   * B/D 的非 require staff 的 AddOn 日期会合并在一起，用 specificDates；
   * G / require staff AddOn 有独立的日期，用 startDate；
   *
   * 特殊情况：
   * - 多天 Daycare 的 add-on / additional service，在展示上需要单个 detail + specificDates 多天，数据结构是每天一个 startDate 的 detail，涉及一个合并、拆分的逻辑，通过 isMergedRecord 来区分
   * - boarding 的 addon 可能既没有 specific dates 也没 start date，只有 date type
   */
  startDate: string;
  specificDates: string[];
  isMergedRecord: boolean;
  petDetailDateMap: Map<string, string>;
  dateType?: PetDetailDateType;
  /** default date time */
  defaultStartTime?: number;
  defaultStaffId?: string;
}

export interface EvaluationDetail {
  careType: ServiceItemType;
  serviceDetailId: string;
  petId: string;
  service?: ServiceBriefView;
  serviceDetail: EvaluationTestDetailModel | BoardingServiceDetailModel | DaycareServiceDetailModel;
  evaluation?: EvaluationBriefView;
}

export interface IncompleteDetailFormItems {
  lodgingItems: LodgingItemsDetail[];
  staffTimeItems: StaffTimeDetail[];
  evaluationItems: EvaluationDetail[];
}

export interface LodgingItemsDetailInput {
  serviceDetailId: string;
  careType: ServiceItemType;
  lodgingUnitId: string;
}

export interface StaffTimeDetailInput {
  /** primary keys */
  careType: ServiceItemType;
  serviceType: ServiceType;
  serviceDetailId: string;
  /** user input */
  staffId: string;
  startTime: number;
}

export interface EvaluationDetailInput {
  id: string;
  staffId?: string;
  evaluationId: string;
}

export interface InformationRequireInput {
  lodgingInputList: LodgingItemsDetailInput[];
  staffTimeInputList: StaffTimeDetailInput[];
  evaluationInputList: EvaluationDetailInput[];
  createEvaluationInputList: AcceptBookingRequestV2RequestCreateEvaluationRequest[];
}

export interface AcceptRequestParams extends Omit<AcceptBookingRequestV2Request, 'id'> {}

export type InformationRequireFormRecord = Record<
  string,
  string | number | Dayjs | AcceptBookingRequestV2RequestCreateEvaluationRequest | undefined
>;
