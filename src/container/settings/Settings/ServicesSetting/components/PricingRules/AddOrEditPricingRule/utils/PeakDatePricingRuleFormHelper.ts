import { ConditionType, EffectType, RuleType } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { BasePricingRuleFormHelper } from './BasePricingRuleFormHelper';
import { LocalPricingRule } from '../PricingRuleSettingContext';
import { Operator } from '@moego/api-web/moego/utils/v2/condition_messages';
import { Condition } from '@moego/api-web/moego/models/offering/v2/pricing_rule_defs';

export class PeakDatePricingRuleFormHelper extends BasePricingRuleFormHelper {
  protected ruleType = RuleType.PEAK_DATE;

  public static createDateRangeCondition(): Condition {
    return {
      type: ConditionType.DATE_RANGE,
      operator: Operator.GE,
      value: { dateRange: { startDate: '', endDate: '' } },
    };
  }

  public static createRepeatDateCondition(): Condition {
    return {
      type: ConditionType.REPEAT_DATES,
      operator: Operator.GE,
      value: {
        repeatDates: {
          dateRange: { startDate: '', endDate: '' },
          week: { intervalNum: 1, dayOfWeeks: [] },
        },
      },
    };
  }

  public static createConditionByType(conditionType: ConditionType): Condition {
    switch (conditionType) {
      case ConditionType.DATE_RANGE:
        return PeakDatePricingRuleFormHelper.createDateRangeCondition();
      case ConditionType.REPEAT_DATES:
        return PeakDatePricingRuleFormHelper.createRepeatDateCondition();
      default:
        throw new Error(`Unsupported condition type: ${conditionType}`);
    }
  }

  /**
   * Peak date 现在有两种 condition
   * 第一种是 specific date range
   * 第二种是 repeat date range
   * 默认是 specific date range 且只有 specific date range 允许创建多个
   *
   * @return {*}  {Condition}
   * @memberof PeakDatePricingRuleFormHelper
   */
  public createCondition(): Condition {
    return PeakDatePricingRuleFormHelper.createDateRangeCondition();
  }

  public createEffect() {
    return {
      type: EffectType.FIXED_INCREASE,
      value: null,
    };
  }

  protected initAddFormCustomizedValue(): Partial<LocalPricingRule> {
    return {
      isChargePerLodging: false,
    };
  }

  protected initEditFormCustomizedValue(): Partial<LocalPricingRule> {
    return {};
  }
}
