import { useWatch } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { selectAllLodgingTypes } from '../../../../../../../../../store/lodging/lodgingType.selectors';
import { useLatestCallback } from '../../../../../../../../../utils/hooks/useLatestCallback';
import { useServiceStaffPermissions } from '../../../../AddOrEditComponents/hooks/useServiceStaffPermissions';
import { SelectLodging } from '../../../../SelectLodging';
import { useEvaluationFormContext } from '../EvaluationFormContext';

export const EligibleLodgings = memo(() => {
  const { form, isEdit } = useEvaluationFormContext();
  const { isDisabledForRemains } = useServiceStaffPermissions(isEdit);
  const [lodgingTypeList = []] = useSelector(selectAllLodgingTypes);
  const [customizedLodgingIds, lodgingFilter] = useWatch({
    control: form?.control,
    name: ['customizedLodgingIds', 'lodgingFilter'],
  });

  const isSelectedAll = isDisabledForRemains || !lodgingFilter;

  const availableLodgingOption = lodgingTypeList.map((item) => {
    const { name, id } = item || {};
    return {
      label: name,
      value: id,
      isDisabled: isSelectedAll,
    };
  });

  const handleSelectAllChange = useLatestCallback((value: boolean) => {
    form?.setValue('lodgingFilter', !value, {
      shouldDirty: true,
    });
    !value &&
      form?.setValue('customizedLodgingIds', [], {
        shouldDirty: true,
      });
  });

  const handleChange = (value: string[] | undefined) => {
    form?.setValue('customizedLodgingIds', value || [], { shouldDirty: true });
    form?.setValue('lodgingFilter', true, { shouldDirty: true });
  };

  return (
    <SelectLodging
      isDisabled={isDisabledForRemains}
      lodgingsName="customizedLodgingIds"
      onSelectChange={handleChange}
      onSelectAllChange={handleSelectAllChange}
      customizedLodgings={customizedLodgingIds}
      lodgingFilter={lodgingFilter}
      availableLodgingOption={availableLodgingOption}
    />
  );
});
