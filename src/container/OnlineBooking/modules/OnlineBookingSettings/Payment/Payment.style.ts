import styled from 'styled-components';
import { NavPrimaryButton } from '../../../../../layout/components/Navbar/NavButton.style';
import { UpdateSettingBtn } from '../components/Update.style';

export const PaymentContainer = styled.div`
  .radio-section {
    font-size: 14px;
    line-height: 20px;
    padding: 12px 16px;
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    cursor: pointer;
    &:not(:first-child) {
      margin-top: 24px;
    }
    &:hover {
      border-color: var(--moe-color-border-brand);
    }
    &.active {
      border-color: var(--moe-color-border-brand);
      cursor: auto;
    }
    .radio-row {
      font-size: 16px;
      line-height: 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left {
        display: flex;
        align-items: center;
      }
      .radio {
        width: 16px;
        height: 16px;
        background-color: #fff;
        border-color: #d9d9d9;
        display: block;
        border: 1px solid #d9d9d9;
        border-radius: 50%;
        margin-right: 8px;
        &-text {
          font-weight: 700;
        }
      }
    }

    .ant-radio-wrapper {
      font-weight: 700;
      font-size: 16px;
      line-height: 24px;
      color: #333333;
      margin-right: 4px;
    }
    .radio-row {
      display: flex;
    }
    .radio-desc {
      font-weight: 500;
      color: #999;
      margin-top: 4px;
      margin-left: 24px;
      a {
        color: #f96b18;
      }
    }

    /* Disabled Style */
    &.disabled {
      cursor: not-allowed;
      .radio-desc {
        color: #ccc;
        &-content {
          color: #ccc;
        }
      }
    }
    .ant-radio-checked.ant-radio-disabled {
      .ant-radio-inner {
        border-color: #ffa46b !important;
        background-color: #fff;

        &::after {
          content: '';
          background-color: #ffa46b;
        }
      }
    }
    .ant-radio-disabled + span {
      color: #ccc;
    }
  }

  .tag-new {
    padding: 2px 8px;
    border-radius: 37.5px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    background-color: #fff7f0;
    font-size: 12px;
    line-height: 16px;
    color: #f96b18;
  }

  .hidden {
    display: none;
  }

  .form-section {
    margin-left: 24px;
  }

  ${UpdateSettingBtn} {
    padding: 6px 0;
    width: 96px;
    display: inline-block;
    text-align: center;
  }
`;

export const CancelButton = styled(NavPrimaryButton)`
  background: white;
  border: 1px solid #dee1e5;
  color: #333333;
  padding: 5px 0;
  width: 96px;
  display: inline-block;
  text-align: center;

  &:hover,
  &:active {
    background-color: #f2f3f6;
    border-color: #dee1e5;
  }
`;

// 很多个 tag 但似乎都不一致
export const Tag = styled.span<{ filled?: boolean }>`
  font-size: 14px;
  line-height: 16px;
  display: inline-block;
  padding: 2px 8px;
  border-radius: 10px;
  color: ${({ filled = false }) => (filled ? '#fff' : '#f96b18')};
  background-color: ${({ filled = false }) => (filled ? '#f96b18' : '#fef0e8')};
`;
