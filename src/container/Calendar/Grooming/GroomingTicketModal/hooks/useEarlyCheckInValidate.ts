import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { apptInfoMapBox } from '../../../../Appt/store/appt.boxes';
import dayjs from 'dayjs';
import { useStore } from 'amos';
import { useNotMatchCheckInModal } from '../components/ModalNotMatchCheckInModal';
import { abortFlow } from '../../../../../utils/abortFlow';
import { reportData } from '../../../../../utils/tracker';
import { ReportActionName } from '../../../../../utils/reportType';

enum EarlyCheckInStatus {
  CONTINUE_TO_CHECK_IN,
  CANCEL_TO_CHECK_IN,
}

export const useEarlyCheckInValidate = () => {
  const store = useStore();
  const openNotMatchCheckInModal = useNotMatchCheckInModal();
  return useLatestCallback(async (ticketId: number) => {
    const appointment = store.select(apptInfoMapBox.mustGetItem(String(ticketId))).appointment;
    const { appointmentDate } = appointment;
    const isToday = dayjs(appointmentDate).isSame(dayjs(), 'day');

    if (!isToday) {
      const res = await openNotMatchCheckInModal({
        onCancel: async () => {
          reportData(ReportActionName.earlyCheckInCancelClick);
          // cancel 的 CTA 是取消 check in
          openNotMatchCheckInModal.close(EarlyCheckInStatus.CANCEL_TO_CHECK_IN);
        },
        onConfirm: async () => {
          reportData(ReportActionName.earlyCheckInConfirmClick);
          // continue 是放在 confirm button 上的
          openNotMatchCheckInModal.close(EarlyCheckInStatus.CONTINUE_TO_CHECK_IN);
        },
      });
      // 只有点击 CONTINUE_TO_CHECK_IN 才会继续 check in，否则都不 checkin
      if (res !== EarlyCheckInStatus.CONTINUE_TO_CHECK_IN) {
        // 取消 check in
        abortFlow('Check in date mismatch!');
        return;
      }
    }
  });
};
