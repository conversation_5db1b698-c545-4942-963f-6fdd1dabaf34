import { action } from 'amos';
import { TimeSlotServiceClient } from '../../../../../middleware/clients';
import {
  type ListAllTimeSlotsParams,
  type ListTimeSlotsParams,
} from '@moego/api-web/moego/api/smart_scheduler/v1/time_slot_api';
import { store } from '../../../../../provider';
import { petMapBox } from '../../../../../store/pet/pet.boxes';
import { type PetParam } from '@moego/api-web/moego/models/smart_scheduler/v1/time_slot_models';
import { selectApptPetAddon, selectApptPetService } from '../../appt.selectors';
import { isNormal } from '@moego/finance-utils';

export const getListAvailableTimeSlots = action(async (dispatch, select, params: ListTimeSlotsParams) => {
  const res = await TimeSlotServiceClient.listAvailableTimeSlots(params);
  return res;
});

export const getListAllTimeSlots = action(async (dispatch, select, params: ListAllTimeSlotsParams) => {
  const res = await TimeSlotServiceClient.listAllTimeSlots(params);
  return res;
});

export const transformPetParams = (
  ids: string[],
  staffId: string,
  petId: string,
  appointmentId: string,
): PetParam[] => {
  const petStore = store.select(petMapBox);
  const pet = petStore.mustGetItem(Number(petId));

  return ids.map((id, index) => {
    const service = store.select(selectApptPetService(appointmentId, id));
    const addon = store.select(selectApptPetAddon(appointmentId, id));
    const serviceId = service.serviceId;
    const addonId = addon.addOnId;

    return {
      petIndex: String(index + 1),
      petType: pet.petTypeId,
      breed: pet.breed,
      serviceIds: [isNormal(serviceId) ? serviceId : addonId],
      weight: pet.weight,
      staffId,
    };
  });
};
