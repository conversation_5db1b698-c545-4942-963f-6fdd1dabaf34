import { isNil } from 'lodash';
import React, { useState, type ReactNode } from 'react';
import { dayOfWeeks } from '../../../../../../../../store/business/business.boxes';
import {
  type FullWeekDay,
  FullWeekDayList,
} from '../../../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { type WeekSlotValue } from '../../../../../../../../store/staffSchedule/staffSchedule.types';
import { WeekList, type WeekListProps } from '../../WeekList';
import cn from 'classnames';

export interface ScheduleWeekListProps {
  className?: string;
  classNames?: {
    weekList?: string;
    content?: string;
  };
  value: WeekSlotValue;
  isDisabled?: boolean;
  onSelectChange?: (selectedDayKey: number) => void;
  onCheckedKeysChange?: (checkedKeys: number[]) => void;
  onCheck?: (checked: boolean | undefined, key: number | undefined) => void;
  children?: ({
    checkedDayKeys,
    selectedDayKey,
  }: {
    checkedDayKeys: number[];
    selectedDayKey: number;
  }) => ReactNode;
}

export const ScheduleWeekList = (props: ScheduleWeekListProps) => {
  const { onCheckedKeysChange, className, classNames, onSelectChange, children, onCheck, value, isDisabled } = props;
  const [selectedDayKey, setSelectedDayKey] = useState(dayOfWeeks.sundayFirst[0]);

  const checkedDayKeys = dayOfWeeks.sundayFirst
    .map((dayIndex) => {
      const dayKey = FullWeekDayList[dayIndex].toLowerCase() as Lowercase<FullWeekDay>;
      const dayData = value[dayKey];

      return dayData?.isAvailable ? dayIndex : null;
    })
    .filter((v) => !isNil(v)) as number[];

  const handleSelectChange = (dayKey: number) => {
    setSelectedDayKey(dayKey);
    onSelectChange?.(dayKey);
  };

  const handleCheckedChange: WeekListProps['onCheckedKeysChange'] = (keys, meta) => {
    onCheckedKeysChange?.(keys);
    onCheck?.(meta.checked, meta.key);
  };

  return (
    <div className={cn('moe-flex moe-gap-s min-h-[448px]', className)}>
      <WeekList
        isDisabled={isDisabled}
        className={classNames?.weekList}
        selectedKey={selectedDayKey}
        onSelectChange={handleSelectChange}
        checkedKeys={checkedDayKeys}
        onCheckedKeysChange={handleCheckedChange}
      />
      {/* 竖线 */}
      <div className="moe-flex moe-gap-s moe-flex-1">
        <div className="moe-h-full moe-w-[1px] moe-bg-[var(--moe-color-border-divider)]"></div>
        <div className="moe-flex-1">{children?.({ checkedDayKeys, selectedDayKey })}</div>
      </div>
    </div>
  );
};
