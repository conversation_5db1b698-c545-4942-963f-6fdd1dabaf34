import { datadogRum } from '@datadog/browser-rum';
import { MinorChevronDownOutlined, MinorChevronLeftOutlined, MinorChevronRightOutlined } from '@moego/icons-react';
import { Button, DatePicker, Heading, IconButton } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs, { type Dayjs } from 'dayjs';
import { range } from 'lodash';
import React, { memo, useEffect } from 'react';
import { businessMapBox } from '../../../../store/business/business.boxes';
import {
  selectLodgingCalendarConfig,
  selectLodgingCalendarCurrentBusinessId,
} from '../../../../store/calendarLatest/lodgingCalendar.selectors';
import { DataDogActionName } from '../../../../utils/logger';
import { batchUpdates } from '../../../../utils/unstable/batchUpdates';
import { useChangeLodgingCalendarDate } from '../hooks/LodgingFullCalendarApi';

export interface LodgingCalendarDateProps {
  onChange?: (date: Dayjs) => void;
}

export const DatePickerPresets = range(1, 15).map((i) => {
  return {
    label: i === 1 ? '1 week out' : `${i} weeks out`,
    value: dayjs().add(i, 'week'),
  };
});

export const LodgingCalendarDate = memo<LodgingCalendarDateProps>((props) => {
  const { onChange } = props;
  const [businessMap, businessId, lodgingCalendarConfig] = useSelector(
    businessMapBox,
    selectLodgingCalendarCurrentBusinessId,
    selectLodgingCalendarConfig,
  );
  const { weekRange, calendarDate } = lodgingCalendarConfig;
  const business = businessMap.mustGetItem(Number(businessId));

  const changeLodgingCalendarDate = useChangeLodgingCalendarDate();

  const handleChangeCalendarDate = (date: Dayjs) => {
    batchUpdates(() => {
      datadogRum.startDurationVital(DataDogActionName.CHANGE_CALENDAR_DATE);
      changeLodgingCalendarDate(date.toDate());
      onChange?.(date);
    });
  };

  const handlePrev = () => {
    handleChangeCalendarDate(calendarDate?.subtract(weekRange, 'week') ?? null);
  };

  const handleNext = () => {
    handleChangeCalendarDate(calendarDate?.add(weekRange, 'week') ?? null);
  };

  useEffect(() => {
    // init calendar date use config date
    changeLodgingCalendarDate(calendarDate.toDate());
  }, [calendarDate.toDate().getTime()]);

  return (
    <div className="moe-flex moe-items-center">
      <div className="moe-flex moe-items-center">
        <IconButton size="s" icon={<MinorChevronLeftOutlined />} onPress={handlePrev} />
        <DatePicker
          mode={weekRange > 1 ? 'fortnight' : 'week'}
          value={calendarDate}
          presets={DatePickerPresets}
          onChange={(startDate) => handleChangeCalendarDate(startDate!)}
        >
          {() => {
            const { startDate, endDate } = lodgingCalendarConfig.getStartEndDate();
            const startDateFormat = startDate?.format(business.dateFormatMD);
            const endDateFormat = endDate?.format(business.dateFormat);

            return (
              <div className="moe-flex moe-items-center moe-px-xs moe-cursor-pointer">
                <Heading size="4" className="moe-text-primary">
                  {startDateFormat} - {endDateFormat}
                </Heading>
                <MinorChevronDownOutlined className="moe-ml-xxs" />
              </div>
            );
          }}
        </DatePicker>
        <IconButton size="s" icon={<MinorChevronRightOutlined />} onPress={handleNext} />
      </div>
      <Button variant="secondary" size="s" className="moe-ml-s" onPress={() => handleChangeCalendarDate(dayjs())}>
        Today
      </Button>
    </div>
  );
});
