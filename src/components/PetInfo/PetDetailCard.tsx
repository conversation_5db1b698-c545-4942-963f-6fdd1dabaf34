import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import React, { memo } from 'react';
import { ID_ANONYMOUS, isNormal } from '../../store/utils/identifier';
import { PetInfo, type PetInfoProps } from './PetInfo';
import { PetInfoRender } from './PetInfoRender';

export interface PetDetailCardProps extends Omit<PetInfoProps, 'petId'> {
  petId?: number | string;
  appointmentDate?: string;
  appointmentEndDate?: string;
  appointmentId?: number;
  serviceItemTypes?: ServiceItemType[];
  preventAutoReload?: boolean;
}

export const PetDetailCard = memo<PetDetailCardProps>(function PetDetailCard(props) {
  const {
    petId,
    appointmentDate,
    appointmentEndDate,
    appointmentId,
    serviceItemTypes,
    preventAutoReload = false,
    ...petProps
  } = props;
  const targetPetId = +(petId || ID_ANONYMOUS);
  if (!isNormal(targetPetId)) {
    return null;
  }

  return (
    <PetInfoRender petId={targetPetId} preventAutoReload={preventAutoReload}>
      {({ go2PetDetail }: { go2PetDetail: (petId: string) => void }) => {
        return (
          <PetInfo
            {...petProps}
            petId={targetPetId}
            go2PetDetail={go2PetDetail}
            startDate={appointmentDate}
            endDate={appointmentEndDate}
            appointmentId={appointmentId}
            serviceItemTypes={serviceItemTypes}
          />
        );
      }}
    </PetInfoRender>
  );
});
