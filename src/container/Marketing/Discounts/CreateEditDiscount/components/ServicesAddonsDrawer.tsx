import { useDispatch, useSelector } from 'amos';
import { Spin, Tabs } from 'antd';
import React, { memo, useEffect, useMemo } from 'react';
import { useMount, useSetState, useThrottleFn } from 'react-use';
import SvgIconMagnifierSvg from '../../../../../assets/svg/icon-magnifier.svg';
import { StyledInputSearch } from '../../../../../components/AntdStyle/StyledInputSearch';
import { StyledCheckbox } from '../../../../../components/Checkbox/Checkbox.style';
import { Condition } from '../../../../../components/Condition';
import { SvgIcon } from '../../../../../components/Icon/Icon';
import { getCompanyBasicServiceInfoList } from '../../../../../store/service/actions/public/service.actions';
import { ServiceType, serviceCategoryMapBox } from '../../../../../store/service/category.boxes';
import { ServiceActive } from '../../../../../store/service/service.boxes';
import { ID_INVALID, ID_LOADING } from '../../../../../store/utils/identifier';
import { group } from '../../../../../store/utils/utils';
import { useCancelableCallback } from '../../../../../utils/hooks/useCancelableCallback';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { useDiscountServiceTabs } from '../hooks/useDiscountServiceTabs';
import { InfiniteScrollContainer } from './InfiniteScrollContainer';
import { ApplicableCategory } from './PetAndServices/ApplicableCategory';
import { StyledDiscountDrawer } from './styledComponents';

export interface SaveServiceAddonParams {
  serviceIdList: number[];
  addonIdList: number[];
  isSelectAll: boolean;
}

export interface ServicesAddonsDrawerProps extends SaveServiceAddonParams {
  visible: boolean;
  onClose: () => void;
  onSave: (params: SaveServiceAddonParams) => void;
}

interface ServicesAddonsDrawerState {
  activeTab: number;
  selectedServiceAddonIdList: [number[], number[]];
  isSelectAll: boolean;
  keyword: string;
  pageNum: number;
  searchResult: {
    serviceId: number;
    categoryId?: number;
    [key: string]: any;
  }[];
  total: number;
}

export const ServicesAddonsDrawer = memo<ServicesAddonsDrawerProps>(
  ({ visible, isSelectAll, serviceIdList, addonIdList, onClose, onSave }) => {
    const dispatch = useDispatch();
    const [serviceCategoryMap] = useSelector(serviceCategoryMapBox);
    const { serviceTabs, defaultActiveTab } = useDiscountServiceTabs();
    const [state, setState] = useSetState<ServicesAddonsDrawerState>({
      activeTab: defaultActiveTab,
      selectedServiceAddonIdList: [serviceIdList, addonIdList],
      isSelectAll,
      keyword: '',
      pageNum: 1,
      searchResult: [],
      total: 0,
    });

    useMount(() => {
      serviceTabs.values.map((tab) => {
        const { serviceType, serviceItemType } = serviceTabs.mapLabels[tab];
        dispatch(
          getCompanyBasicServiceInfoList({
            serviceType,
            serviceItemType,
            onlyAvailable: false,
            selectedServiceIds: [],
          }),
        );
      });
    });

    useEffect(() => {
      if (visible) {
        setState({
          selectedServiceAddonIdList: [serviceIdList.map((id) => Number(id)), addonIdList.map((id) => Number(id))],
          isSelectAll,
        });
      }
    }, [visible, isSelectAll, serviceIdList, addonIdList]);

    const handleToggleService = useLatestCallback((serviceId: number, serviceType: number) => {
      let [nextSelectedServiceIdList, nextSelectedAddonIdList] = state.selectedServiceAddonIdList.slice();
      if (serviceType === ServiceType.Service) {
        nextSelectedServiceIdList = nextSelectedServiceIdList.toggle(serviceId);
      }
      if (serviceType === ServiceType.Addon) {
        nextSelectedAddonIdList = nextSelectedAddonIdList.toggle(serviceId);
      }

      setState({
        isSelectAll: false,
        selectedServiceAddonIdList: [nextSelectedServiceIdList, nextSelectedAddonIdList],
      });
    });

    const searchServiceAddons = useCancelableCallback(
      async (signal, keyword: string, tab: number, pageNum: number = 1) => {
        const { serviceType, serviceItemType } = serviceTabs.mapLabels[tab];
        const params = {
          inactive: !!ServiceActive.Active,
          keyword,
          serviceType,
          serviceItemType,
          pagination: {
            pageNum,
            pageSize: 20,
            // unused fields
            total: 0,
          },
        };
        const { serviceList, total } = await dispatch(getCompanyBasicServiceInfoList(params, signal));
        setState({
          pageNum,
          searchResult: pageNum > 1 ? state.searchResult.concat(serviceList) : serviceList,
          total,
        });
      },
    );

    const handleChangeSelectAll = (checked: boolean) => {
      const loadedServiceAddonIdList = state.searchResult.map((service) => service.serviceId);
      const nextSelectedServiceAddonIdList: [number[], number[]] =
        state.activeTab === ServiceType.Service
          ? [loadedServiceAddonIdList, state.selectedServiceAddonIdList[1]]
          : [state.selectedServiceAddonIdList[0], loadedServiceAddonIdList];
      setState({
        isSelectAll: checked,
        selectedServiceAddonIdList: checked ? nextSelectedServiceAddonIdList : [[], []],
      });
    };

    const handleSave = () => {
      onSave({
        serviceIdList: state.selectedServiceAddonIdList[0],
        addonIdList: state.selectedServiceAddonIdList[1],
        isSelectAll: state.isSelectAll,
      });
      onClose();
    };

    useThrottleFn(
      (keyword: string, type: number) => {
        searchServiceAddons(keyword, type);
      },
      500,
      [state.keyword, state.activeTab],
    );

    const categoryServicesList = useMemo(() => {
      return group(
        state.searchResult,
        (service) => service.categoryId,
        (service) => service.serviceId,
      );
    }, [state.searchResult]);

    return (
      <StyledDiscountDrawer
        visible={visible}
        onClose={onClose}
        title="Select services & Add-ons"
        bodyStyle={{ display: 'flex', flexDirection: 'column', paddingBottom: 0 }}
        saveBtnText="Save"
        onApply={handleSave}
      >
        <StyledCheckbox checked={state.isSelectAll} onChange={(e) => handleChangeSelectAll(e.target.checked)}>
          Select all
        </StyledCheckbox>
        <Tabs
          activeKey={String(state.activeTab)}
          onChange={(activeKey) => setState({ activeTab: Number(activeKey) })}
          size="small"
        >
          {serviceTabs.values.map((tab) => {
            const { title, serviceType } = serviceTabs.mapLabels[tab];
            return (
              <Tabs.TabPane key={tab} tab={title}>
                <StyledInputSearch
                  placeholder="Search service..."
                  prefix={<SvgIcon src={SvgIconMagnifierSvg} />}
                  allowClear
                  value={state.keyword}
                  onChange={(e) => setState({ keyword: e.target.value })}
                />
                <Condition if={tab === state.activeTab}>
                  <Spin spinning={searchServiceAddons.isBusy()} wrapperClassName="moe-flex-1 moe-overflow-hidden">
                    <InfiniteScrollContainer
                      total={state.total}
                      dataLength={state.searchResult.length}
                      wrapperClassName="moe-pb-[20px]"
                      containerId={`infinite-scroll-container-${tab}`}
                      next={() => searchServiceAddons(state.keyword, state.activeTab, state.pageNum + 1)}
                    >
                      {categoryServicesList.map(([categoryId = ID_LOADING, serviceList]) => {
                        const { name } = serviceCategoryMap.mustGetItem(categoryId);
                        return (
                          <ApplicableCategory
                            key={categoryId}
                            className="moe-mt-[16px]"
                            petId={ID_INVALID}
                            title={name || 'uncategorized'.toUpperCase()}
                            services={serviceList}
                            isSelectAll={state.isSelectAll}
                            isDisableAll={state.isSelectAll}
                            selectedServiceIds={state.selectedServiceAddonIdList.flat()}
                            onToggleService={(serviceId) => handleToggleService(serviceId, serviceType)}
                            isCompanyLevel={true}
                          />
                        );
                      })}
                    </InfiniteScrollContainer>
                  </Spin>
                </Condition>
              </Tabs.TabPane>
            );
          })}
        </Tabs>
      </StyledDiscountDrawer>
    );
  },
);
