import React, { memo, useEffect, useRef } from 'react';
import { LodgingAppointmentCard, type LodgingAppointmentCardProps } from './LodgingAppointmentCard';
import { LodgingAppointmentCardMask } from './LodgingAppointmentCardMask';
import { useCardVisible, useCardVisibleObserver, useHiddenResource } from '../../../../../components/CardVisible';

export interface LodgingAppointmentEventProps extends LodgingAppointmentCardProps {}

interface LazyAppointmentEventCardProps extends LodgingAppointmentEventProps {
  visible: boolean;
}
export const LazyAppointmentEventCard = memo(({ visible, ...props }: LazyAppointmentEventCardProps) => {
  if (!visible) {
    return null;
  }
  return (
    <>
      <LodgingAppointmentCardMask />
      <LodgingAppointmentCard {...props} />
    </>
  );
});

export const LodgingAppointmentEvent: React.FC<LodgingAppointmentEventProps> = memo((props) => {
  const visible = useCardVisible(props.cardId);
  const observerRef = useCardVisibleObserver();
  const { hiddenResource } = useHiddenResource();
  const divRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const divCurrent = divRef.current;
    const observerCurrent = observerRef.current;
    if (divCurrent && observerCurrent) {
      observerCurrent.observe(divCurrent);
    }
    return () => {
      if (divCurrent && observerCurrent) {
        observerCurrent.unobserve(divCurrent);
      }
    };
  }, [observerRef]);

  return (
    <div className="moe-min-h-[40px]" ref={divRef} data-card-id={props.cardId}>
      <LazyAppointmentEventCard visible={visible && !hiddenResource} {...props} />
    </div>
  );
});
LodgingAppointmentEvent.displayName = 'LodgingAppointmentEvent';
