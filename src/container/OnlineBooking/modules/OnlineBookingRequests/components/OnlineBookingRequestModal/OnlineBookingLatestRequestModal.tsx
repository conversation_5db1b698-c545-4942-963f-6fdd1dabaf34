import { type AcceptBookingRequestV2Request } from '@moego/api-web/moego/api/online_booking/v1/booking_request_api';
import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { memo, useEffect, useMemo, useRef } from 'react';
import { useHistory, useLocation } from 'react-router';
import { Button } from '../../../../../../components/Button/Button';
import { Condition } from '../../../../../../components/Condition';
import { WithPermission } from '../../../../../../components/GuardRoute/WithPermission';
import { getDefaultService } from '../../../../../../components/ServiceApplicablePicker/utils/getDefaultService';
import { Switch } from '../../../../../../components/SwitchCase';
import { AutoAssignTips } from '../../../../../../components/Tips/AutoAssignTips';
import { PATH_TICKET_EDIT } from '../../../../../../router/paths';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { isValidAutoAssign } from '../../../../../../store/createTicket/ticket.utils';
import {
  autoAssignOBRequestLodging,
  getBookingRequest,
  getBookingRequestList,
  getOnlineBookingLatestRequestDetail,
} from '../../../../../../store/onlineBooking/actions/private/onlineBooking.actions';
import { OnlineBookingRequestListType } from '../../../../../../store/onlineBooking/models/OnlineBookingRequest';
import { onlineBookingLatestRequestMapBox } from '../../../../../../store/onlineBooking/onlineBooking.boxes';
import { createEnum, type EnumValues } from '../../../../../../store/utils/createEnum';
import { ID_ANONYMOUS, isNormal } from '../../../../../../store/utils/identifier';
import { apptToWaitList } from '../../../../../../store/waitList/actions/public/waitList.actions';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../utils/DateTimeUtil';
import { useBool } from '../../../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { BDScheduleTips } from '../../../../../Appt/components/SelectServiceDetail/components/DaycareScheduleTips/DaycareScheduleTips';
import { useCheckConflictAlert } from '../../../../../Appt/hooks/useCheckConflictAlert';
import { type CreateWaitListState } from '../../../../../CreateWaitList/CreateWaitList.props';
import { useCreateWaitList } from '../../../../../CreateWaitList/hooks/useCreateWaitList';
import { BookingInfo } from '../../../../components/BookingRequestModal/BookingInfo';
import { ClientInfo } from '../../../../components/BookingRequestModal/ClientInfo';
import { PetInfo } from '../../../../components/BookingRequestModal/PetInfo';
import { isValidAddress } from '../../../../components/ClientLocation/ClientLocation';
import { ReviewUpdateTips } from '../../../../components/OnlineBookingInfoUpdateDrawer/components/ReviewUpdateTips';
import { useObRequestAndNotifyModal } from '../../../../components/OnlineBookingRequestActionModal/OnlineBookingRequestActionModal.hooks';
import { usePreloadRequestDetailExtra } from '../../../../hooks/usePreload';
import { useInformationRequire } from '../../hooks/useInformationRequire';
import { useOnlineBookingUpdate } from '../../hooks/useOnlineBookingUpdate';
import { type LodgingItemsDetailInput } from '../InformationRequireModal/InformationRequireModal.types';
import { incompleteDetailsToFormItems } from '../InformationRequireModal/InformationRequireModal.utils';
import { resolvedApiParams, resolvedCreateWaitListModalParams } from './OnlineBookingLatestRequestModal.utils';
import {
  FooterView,
  OnlineBookingRequestContentView,
  OnlineBookingRequestModalView,
} from './OnlineBookingRequestModal.style';
import { useBookingClientInfoList } from './hooks/useBookingClientInfoList';
import { useBookingInfoList } from './hooks/useBookingInfoList';
import { useBookingPetInfoList } from './hooks/useBookingPetInfoList';
import { useDraftEvaluationPetToStaff } from './hooks/useDraftEvaluationPetToStaff';
import { useDraftPetToEvaluation } from './hooks/useDraftPetToEvaluation';
import { useDraftPetToLodging } from './hooks/useDraftPetToLodging';
import { selectCompanyCareTypeNameMap } from '../../../../../../store/careType/careType.selectors';
import { useOpenApptDetailDrawer } from '../../../../../../utils/hooks/useOpenApptDetailDrawer';
import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { GrowthBookFeatureList } from '../../../../../../utils/growthBook/growthBook.config';

export const Scene = createEnum({
  BDRequest: [0, 'boarding_daycare_request'],
});

export interface OnlineBookingLatestRequestModalProps {
  scene?: EnumValues<typeof Scene>;
  className?: string;
  id?: number;
  onClose: (updated: boolean) => void;
}

export const OnlineBookingLatestRequestModal = memo<OnlineBookingLatestRequestModalProps>(
  ({ className, id, onClose, scene }) => {
    const { pathname: currentPath } = useLocation();
    const dispatch = useDispatch();
    const history = useHistory();
    const [request, business, companyCareTypeMap] = useSelector(
      onlineBookingLatestRequestMapBox.mustGetItem(id ?? ID_ANONYMOUS),
      selectCurrentBusiness,
      selectCompanyCareTypeNameMap,
    );
    const showObRequestAndNotifyModal = useObRequestAndNotifyModal();
    const informationRequire = useInformationRequire();

    const { draftPetToLodging, updateDraftPetToLodging, resetDraftPetToLodging } = useDraftPetToLodging();
    const { draftPetToEvaluation, updateDraftPetToEvaluation, resetDraftPetToEvaluation } = useDraftPetToEvaluation();
    const { draftEvaluationPetToStaff, updateDraftEvaluationPetToStaff, resetDraftEvaluationPetToStaff } =
      useDraftEvaluationPetToStaff();
    const checkConflictAlert = useCheckConflictAlert();
    const { openApptDetailDrawer } = useOpenApptDetailDrawer();
    const enableOpenApptAfterAcceptingRequest = useFeatureIsOn(
      GrowthBookFeatureList.EnableOpenApptAfterAcceptingRequest,
    );

    const onlyGrooming = !request?.serviceItemTypes?.some((type) =>
      [ServiceItemType.BOARDING, ServiceItemType.DAYCARE, ServiceItemType.EVALUATION].includes(type),
    );
    const updated = useRef(false);
    const showMoreCustomer = useBool();
    const isRequest = !request.isWaitingList;
    const bookingInfoList = useBookingInfoList(id);
    const clientInfoList = useBookingClientInfoList(id);
    const petInfoList = useBookingPetInfoList(id, {
      draftPetToLodging,
      onUpdateDraftToLodging: updateDraftPetToLodging,
      draftPetToEvaluation,
      onUpdateDraftToEvaluation: updateDraftPetToEvaluation,
      draftEvaluationPetToStaff,
      onUpdateEvaluationPetToStaff: updateDraftEvaluationPetToStaff,
    });
    const isBoardingDaycareRequest = isRequest && !onlyGrooming;
    const { requestId, incompleteDetails, serviceItemTypes, mainServiceItemType } = request;
    usePreloadRequestDetailExtra();
    /**
     * 这个 scene 会决定 request 的数据源，有两个迷惑点，需要梳理下
     * 1. appointmentId 是新接口的返回，apptId 是旧接口的返回
     * 2. BD 场景下 appointmentId 又是 0，这里啥时候会依赖需要确认下，看能否直接干掉
     * more: MER-3921
     */
    const appointmentId = scene === Scene.BDRequest ? request.appointmentId : request.apptId;

    const isAutoAssign = isValidAutoAssign(request.autoAssign);
    const ifNeedDiff = request.hasRequestUpdate;
    const isDisabled = ifNeedDiff;
    const openCreateWaitListDrawer = useCreateWaitList();

    const isBoarding = serviceItemTypes?.includes(ServiceItemType.BOARDING);
    const isDaycare = serviceItemTypes?.includes(ServiceItemType.DAYCARE);
    const isMainDaycare = isBoardingDaycareRequest && !isBoarding;

    /**
     * 展示 daycare，evaluation 当天的 capacity 情况。
     *
     * daycare 支持多天，所以多天不需要展示 capacity 信息。
     */
    const scheduleTipsStartDate = dayjs(request.appointmentDate).format(DATE_FORMAT_EXCHANGE);
    const showDaycareScheduleTips = isDaycare && request.specificDates?.length === 1;
    const showBDScheduleTips = showDaycareScheduleTips;
    const serviceItemTypeForScheduleTips = serviceItemTypes?.[0] as
      | ServiceItemType.DAYCARE
      | ServiceItemType.EVALUATION;

    const titleNode = useMemo(() => {
      const mainServiceItemTypeName = companyCareTypeMap.get(mainServiceItemType);
      // it should include all care types but not just evaluation, but currently only support evaluation + BD
      const otherServiceItemTypes = serviceItemTypes?.filter(
        (type) => type !== mainServiceItemType && type === ServiceItemType.EVALUATION,
      );
      const otherServiceItemTypeNames = otherServiceItemTypes?.length
        ? `/${otherServiceItemTypes.map((type) => companyCareTypeMap.get(type).toLowerCase())?.join('/')}`
        : '';
      const title = `${mainServiceItemTypeName}${otherServiceItemTypeNames} request`;

      if (isRequest) {
        return title;
      } else {
        return 'Online booking waitlist';
      }
    }, [isRequest, serviceItemTypes]);

    const fetchData = useSerialCallback(async () => {
      if (scene === Scene.BDRequest && isNormal(id)) {
        await dispatch(getBookingRequest(id));
      } else if (isNormal(id)) {
        await dispatch(getOnlineBookingLatestRequestDetail(id));
      }
      updated.current = false;
      if (id === void 0) {
        showMoreCustomer.close();
      }
    });

    useEffect(() => {
      fetchData();
      resetDraftPetToLodging();
      resetDraftPetToEvaluation();
      resetDraftEvaluationPetToStaff();
    }, [id, scene]);

    const openCreateWaitList = useSerialCallback(async () => {
      const {
        customer,
        pets,
        services,
        staff,
        appointmentDate,
        appointmentStartTime,
        additionalNote,
        autoAssign,
        serviceItemTypes,
      } = request;

      const onSave = async (result: CreateWaitListState) => {
        if (!appointmentId) return;

        const type = 'move';
        await showObRequestAndNotifyModal({
          groomingId: appointmentId,
          type,
          shouldShowRefund: false,
          skipSubmit: true,
          requestId,
          beforeNotify: async () => {
            await dispatch(apptToWaitList(resolvedApiParams(appointmentId, result)));
          },
          hasPetParentAppAccount: !!customer?.hasPetParentAppAccount,
          serviceItemTypes,
        });
        onClose(true);
      };

      openCreateWaitListDrawer({
        ...resolvedCreateWaitListModalParams({
          clientId: customer.customerId,
          staff,
          appointmentDate,
          additionalNote,
          autoAssign,
          appointmentStartTime,
        }),
        petAndServices: pets.map((pet) => ({
          petId: pet.petId,
          serviceList: services
            .filter((s) => Number(s.petId) === pet.petId)
            .map((s) =>
              getDefaultService({
                ...s,
                serviceId: s.serviceId ? +s.serviceId : undefined,
                staffId: s.staffId ? +s.staffId : undefined,
                servicePrice: s.price,
                serviceTime: s.duration,
              }),
            ),
        })),
        isPetServiceDisabled: true,
        mask: true,
        onSave,
      });

      onClose(false);
    });

    const getMainServiceDetail = useLatestCallback((petId: string) => {
      const currentPet = request.pets.find((pet) => pet.petId === +petId);
      if (!currentPet) return null;
      const mainServiceId = currentPet.serviceId;
      const origServiceList = request.services.filter(
        (s) => s.serviceType === ServiceType.SERVICE && Number(s.petId) === +petId,
      );
      return origServiceList.find((s) => s.serviceId && +s.serviceId === mainServiceId);
    });

    /**
     * 如果用户已在外层选过，没必要在弹窗选择一次，直接使用 draftPetToLodging 中的数据
     */
    const getDraftLodgingAssignInfo = useLatestCallback(() => {
      // 在外层生成对应的 form items，供预填逻辑使用
      const incompleteFormItems = incompleteDetailsToFormItems(incompleteDetails, isMainDaycare);
      const { lodgingItems, staffTimeItems, evaluationItems } = incompleteFormItems;
      const draftAssignedPetIdList = Object.keys(draftPetToLodging).filter(
        (petId) => !!draftPetToLodging[petId].lodgingUnitId,
      );
      const hasPetsNotAssignLodgingBeforeAccept =
        lodgingItems.length && lodgingItems.some((require) => !draftAssignedPetIdList.includes(require.petId));
      const needManualAssignLodgingAndStaffTime = hasPetsNotAssignLodgingBeforeAccept || staffTimeItems.length;

      // convert draftPetToLodging to LodgingItemsDetailInput
      const draftLodgingInputList: LodgingItemsDetailInput[] = [];
      draftAssignedPetIdList.forEach((petId) => {
        const careType = isMainDaycare ? ServiceItemType.DAYCARE : ServiceItemType.BOARDING;
        const lodgingUnitId = draftPetToLodging[petId].lodgingUnitId;
        // 一个假设是 assign lodging 的 service 是 main service
        const serviceDetailId = getMainServiceDetail(petId)?.serviceDetailId;
        if (!serviceDetailId) return;
        draftLodgingInputList.push({
          serviceDetailId,
          careType,
          lodgingUnitId,
        });
      });

      return {
        /**
         * 需要手动调整的情况：
         * 1. 存在点击 accept 前，没有 assign lodging 的 pet
         * 2. 有 staffTimeItems
         */
        shouldShowInformationRequireModal: needManualAssignLodgingAndStaffTime || evaluationItems.length,
        draftLodgingInputList,
        incompleteFormItems,
      };
    });

    const onRefreshTableList = useLatestCallback(async () => {
      await dispatch(getBookingRequestList({ type: OnlineBookingRequestListType.Requests }));
    });

    const handleSchedule = useSerialCallback(async () => {
      if (isBoardingDaycareRequest && requestId) {
        /** build accept input params */
        const acceptParams: AcceptBookingRequestV2Request = {
          id: `${requestId}`,
          boardingServices: [],
          daycareServices: [],
          boardingAddons: [],
          daycareAddons: [],
          groomingServices: [],
          groomingAddons: [],
          evaluationServices: [],
          createEvaluationRequests: [],
        };

        /** 根据必填的 incomplete form items 构建 accept params */
        const { shouldShowInformationRequireModal, draftLodgingInputList } = getDraftLodgingAssignInfo();
        if (shouldShowInformationRequireModal) {
          /** 当前只有 boarding 涉及到 auto assign 逻辑 */
          const autoAssignResult = await dispatch(autoAssignOBRequestLodging(`${requestId}`));
          const autoAssignBoardingServiceInput = autoAssignResult.boardingServices || [];
          // 用户选择的 lodging 优先级高于自动分配的 lodging
          // 所以过滤掉自动分配里面已经选择的 serviceDetailId，以用户的为核心做草稿
          const defaultLodgingInputList = autoAssignBoardingServiceInput
            .filter(
              (input) => input.lodgingId && !draftLodgingInputList.find((item) => item.serviceDetailId === input.id),
            )
            .map(({ lodgingId: lodgingUnitId, id: serviceDetailId }) => ({
              careType: ServiceItemType.BOARDING,
              serviceDetailId,
              lodgingUnitId: lodgingUnitId!,
            }))
            .concat(draftLodgingInputList);
          const resultParams = await informationRequire({
            requestId: requestId.toString(),
            incompleteDetails,
            defaultLodgingInputList,
            useMergedGroomingDetail: isMainDaycare,
          });
          if (!resultParams) return;
          Object.assign(acceptParams, resultParams);
        }

        /** 在 information require 未选择 lodging 的情况下，需要提交用户在弹窗的 draft 输入（Daycare 等非 require lodging 的情况会涉及到） */
        const isAcceptParamsLodgingEmpty =
          [...acceptParams.boardingServices, ...acceptParams.daycareServices].filter((item) => item?.lodgingId)
            .length === 0;
        if (isAcceptParamsLodgingEmpty && draftLodgingInputList.length) {
          draftLodgingInputList.forEach((item) => {
            if (item.careType === ServiceItemType.BOARDING) {
              acceptParams.boardingServices.push({
                id: item.serviceDetailId,
                lodgingId: item.lodgingUnitId,
              });
            } else if (item.careType === ServiceItemType.DAYCARE) {
              acceptParams.daycareServices.push({
                id: item.serviceDetailId,
                lodgingId: item.lodgingUnitId,
              });
            }
          });
        }

        const { pets, customer, appointmentDate, appointmentEndDate } = request;
        const lodgingUnitIds = [...acceptParams.boardingServices, ...acceptParams.daycareServices]
          .map((service) => service.lodgingId)
          .filter(Boolean) as string[];

        await checkConflictAlert({
          appointmentId: String(appointmentId),
          startDateStr: appointmentDate,
          endDateStr: isMainDaycare ? appointmentDate : appointmentEndDate,
          petIds: pets.map((pet) => String(pet.petId)),
          lodgingUnitIds,
          clientId: String(customer.customerId),
          cancelText: 'Cancel',
        });

        const { appointmentId: appointmentIdAfterSubmission } = await showObRequestAndNotifyModal({
          requestId,
          groomingId: request.appointmentId!,
          type: 'accept',
          serviceItemTypes,
          acceptParams,
          hasPetParentAppAccount: !!request.customer?.hasPetParentAppAccount,
        });
        // 白名单内 & 是 boarding / daycare 情况下，希望能够自动打开 appt detail drawer
        if (enableOpenApptAfterAcceptingRequest && (isBoarding || isDaycare)) {
          await onRefreshTableList();
          openApptDetailDrawer({ ticketId: appointmentIdAfterSubmission });
        } else {
          onRefreshTableList();
        }
      } else {
        const { pets, customer, appointmentDate, appointmentEndDate } = request;
        await checkConflictAlert({
          appointmentId: String(appointmentId),
          startDateStr: appointmentDate,
          endDateStr: appointmentEndDate,
          petIds: pets.map((pet) => String(pet.petId)),
          clientId: String(customer.customerId),
          cancelText: 'Cancel',
        });
        history.push(PATH_TICKET_EDIT.queried({ redirect: currentPath }, { ticketId: appointmentId! }));
      }
      onClose?.(false);
    });

    const openInfoUpdateModal = useOnlineBookingUpdate();

    return (
      <OnlineBookingRequestModalView
        className={className}
        visible={id !== undefined}
        onClose={() => onClose(updated.current)}
        title={titleNode}
        width="580px"
        height="calc(100% - 248px)"
        loading={fetchData.isBusy()}
      >
        <Switch>
          <Switch.Case if>
            {/* sticky area */}
            <div className="moe-sticky moe-top-0 moe-bg-white moe-z-[99]">
              <Condition if={isAutoAssign}>
                <AutoAssignTips autoAssign={request.autoAssign} />
              </Condition>
              <Condition if={ifNeedDiff}>
                <ReviewUpdateTips
                  onClick={() => {
                    openInfoUpdateModal({
                      ticketId: appointmentId,
                      customerId: request.customer.customerId,
                      showUpdateAndScheduleBtn: onlyGrooming,
                      onSchedule: handleSchedule,
                      onClose(v) {
                        if (v) {
                          fetchData();
                          onRefreshTableList();
                        }
                      },
                    });
                  }}
                />
              </Condition>
              <Condition if={showBDScheduleTips}>
                <BDScheduleTips
                  date={scheduleTipsStartDate}
                  serviceItemType={serviceItemTypeForScheduleTips}
                  isRounded={false}
                />
              </Condition>
            </div>
            <OnlineBookingRequestContentView>
              <BookingInfo infoList={bookingInfoList} />
              <ClientInfo
                infoList={clientInfoList}
                isCollapsed={!(business.isMobileGrooming() && isValidAddress(request.address))}
                recordId={id}
              />
              <PetInfo petInfoList={petInfoList} />
            </OnlineBookingRequestContentView>
            <WithPermission permissions="viewOnlineBooking">
              <FooterView>
                <Button
                  btnType="danger"
                  disabled={isDisabled}
                  fill={false}
                  buttonRadius="circle"
                  onClick={async () => {
                    await showObRequestAndNotifyModal({
                      groomingId: appointmentId!,
                      type: isRequest ? 'decline' : 'delete',
                      shouldShowRefund: request.prepay.prepaidAmount > request.prepay.refundAmount,
                      serviceItemTypes,
                      requestId,
                      hasPetParentAppAccount: !!request.customer?.hasPetParentAppAccount,
                    });
                    updated.current = true;
                    onClose(updated.current);
                  }}
                >
                  {isRequest ? 'Decline' : 'Delete'}
                </Button>
                <Condition if={isRequest && onlyGrooming}>
                  <Button
                    btnType="primary"
                    disabled={isDisabled}
                    fill={false}
                    buttonRadius="circle"
                    onClick={openCreateWaitList}
                    loading={openCreateWaitList.isBusy()}
                  >
                    {/* @text-lint ignore */}
                    To waitlist
                  </Button>
                </Condition>

                <Button
                  buttonRadius="circle"
                  disabled={isDisabled}
                  loading={handleSchedule.isBusy()}
                  passiveDisabled={false}
                  btnType="primary"
                  onClick={handleSchedule}
                >
                  {isBoardingDaycareRequest ? 'Accept' : 'Schedule'}
                </Button>
              </FooterView>
            </WithPermission>
          </Switch.Case>
        </Switch>
      </OnlineBookingRequestModalView>
    );
  },
);
