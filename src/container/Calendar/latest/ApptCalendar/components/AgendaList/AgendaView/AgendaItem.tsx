import { AmountUtil } from '@moego/finance-utils';
import { cn } from '@moego/ui';
import { useSelector } from 'amos';
import classNames from 'classnames';
import React, { memo } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import { calendarViewConfigBox } from '../../../../../../../store/calendarLatest/calendar.boxes';
import { apptEventCardMapBox } from '../../../../../../../store/calendarLatest/card.boxes';
import { type MoeEventInfo } from '../../../../../../../store/calendarLatest/card.types';
import { printFullName } from '../../../../../../../store/customer/customer.boxes';
import { useInvoiceReinvent } from '../../../../../../PaymentFlow/hooks/useInvoiceReinvent';
import { OrderPaymentStatus } from '../../../../../../PaymentFlow/shared/OrderPaymentStatus';
import { WithInvoiceReinvent } from '../../../../../../PaymentFlow/shared/WithInvoiceReinvent';
import { PreAuthStatusIcon } from '../../../../../Grooming/PreAuthForAppt/PreAuthStatus/PreAuthStatusIcon';
import { StaffInfoRender } from '../../../../components/StaffInfoRender';
import { useCalendarCustomConfig } from '../../../hooks/useCalendarCustomConfig';
import { useEventCardState } from '../../../hooks/useEventCardState';
import { useWidthStyle } from '../../../hooks/useWidth';
import { ApptAlerts } from '../../ApptEvent/ApptAlerts';
import { ApptEventClick } from '../../ApptEvent/ApptEventClick';
import { useApptEventsDefaultColors } from '../../ApptEvent/hooks/useApptEventsThemeColor';
import { ApptPayStatus } from '../../ApptPayStatus';
import { ApptStatusBadge } from '../../ApptStatusBadge';
import { AutoAssignStatus } from '../../AutoAssignStatus';
import { AGENDA_STATUS_BAR_NARROW_WIDTH, AGENDA_STATUS_BAR_WIDE_WIDTH } from './Agenda.util';
import { AgendaAddress } from './AgendaAddress';
import { AgendaPetAndServices } from './AgendaPetAndServices';
import { AgendaStaff } from './AgendaStaff';
import { AgendaStatusLabel } from './AgendaStatusLabel';
import { useBlockTypeWidth } from './hooks/useAgendaItemWidth';

export interface AgendaItemProps {
  isLast: boolean;
  event: MoeEventInfo;
}

export const AgendaItem = memo(({ isLast, event }: AgendaItemProps) => {
  const [apptEventMap, business, { leftNavCollapsed }] = useSelector(
    apptEventCardMapBox,
    selectCurrentBusiness(),
    calendarViewConfigBox,
  );
  const apptEvent = apptEventMap.mustGetItem(event.cardId);
  const { appointmentStatus, customerInfo, staffId } = apptEvent;
  const config = useCalendarCustomConfig();
  const { isBlockCardType, ticketComments, isPreAuthShown } = useEventCardState(event.cardId);
  const { isNewOrderV4Flow } = useInvoiceReinvent();

  const statusStyle = useWidthStyle(leftNavCollapsed ? AGENDA_STATUS_BAR_WIDE_WIDTH : AGENDA_STATUS_BAR_NARROW_WIDTH);

  const blockCardWidth = useBlockTypeWidth();
  const blockCardStyle = useWidthStyle(blockCardWidth);
  const { alertsClass } = useApptEventsDefaultColors();

  return (
    <ApptEventClick ticketId={apptEvent.appointmentId}>
      {({ openTicketDetail }) => (
        <div
          className={classNames(
            'moe-flex moe-items-center moe-p-[16px] moe-justify-between hover:moe-bg-[#FFF7F0] moe-cursor-pointer',
            {
              'moe-border-0 moe-border-b-[1px] moe-border-b-[#CCC] moe-border-dashed': !isLast,
            },
          )}
          onClick={openTicketDetail}
          data-card-id={event.cardId}
        >
          <div className="moe-flex moe-items-center moe-min-w-0 moe-flex-grow moe-flex-shrink">
            <ApptStatusBadge cardId={event.cardId} size={12} className="moe-mb-[1px]" />
            <div className="moe-text-[#666] moe-text-[14px] moe-font-[500] moe-leading-[18px] moe-ml-[8px] moe-flex-shrink-0">
              <span>{business.formatTime(event.start)} - </span>
              <span>{business.formatTime(event.end)}</span>
            </div>
            <Condition if={!isBlockCardType}>
              <Condition if={config.showClientName}>
                <div className="moe-text-[14px] moe-font-[500] moe-text-[#333] moe-leading-[18px] moe-ml-[20px] moe-shrink-0 moe-w-[120px] moe-overflow-hidden moe-min-w-0 moe-truncate">
                  {printFullName(customerInfo.firstName, customerInfo.lastName)}
                </div>
              </Condition>
              <div className="moe-text-[14px] moe-font-[500] moe-leading-[18px] moe-ml-[20px] moe-text-[#333] moe-flex moe-flex-col moe-gap-y-[4px] moe-shrink-0 moe-overflow-hidden moe-truncate">
                {apptEvent.pets?.map((pet, index) => {
                  return <AgendaPetAndServices cardId={apptEvent.cardId} pet={pet} key={index} />;
                })}
              </div>
              <AgendaAddress cardId={apptEvent.cardId} />
            </Condition>
            <Condition if={isBlockCardType}>
              <div className="moe-text-[14px] moe-font-[500] moe-text-[#333] moe-leading-[18px] moe-ml-[20px] moe-shrink-0 moe-w-[120px] moe-overflow-hidden moe-min-w-0 moe-truncate">
                Block time
              </div>
              <div
                className="moe-text-[14px] moe-font-[500] moe-text-[#333] moe-leading-[18px] moe-ml-[20px] moe-shrink-0 moe-overflow-hidden moe-min-w-0 moe-truncate"
                style={blockCardStyle}
              >
                {ticketComments}
              </div>
              <StaffInfoRender staffId={staffId}>
                {({ staff }) => <AgendaStaff isMultiStaff={false} staffNameText={staff.firstName} />}
              </StaffInfoRender>
            </Condition>
          </div>
          <Condition if={!isBlockCardType}>
            <div
              className="moe-flex moe-gap-x-[8px] moe-flex-shrink-0 moe-justify-end moe-items-center"
              style={statusStyle}
            >
              <AutoAssignStatus />
              <ApptAlerts showVaccination className={cn('moe-gap-x-[4px] moe-flex-shrink-0', alertsClass)} />

              <WithInvoiceReinvent
                fallback={
                  <ApptPayStatus
                    className="moe-flex-shrink-1"
                    labelClassName="moe-truncate"
                    payStatus={apptEvent.paymentStatusToDisplay}
                  />
                }
              >
                {!isNewOrderV4Flow && (
                  <OrderPaymentStatus
                    status={apptEvent.orderInfo.paymentStatus}
                    isOverPaid={AmountUtil.checkIsCompatibleNegative(apptEvent.orderInfo.outstandingBalance)}
                  />
                )}
              </WithInvoiceReinvent>
              <Condition if={isPreAuthShown}>
                <PreAuthStatusIcon
                  className="!moe-grow-0"
                  size={20}
                  customerId={customerInfo.customerId}
                  ticketId={apptEvent.appointmentId}
                  placement="bottomRight"
                />
              </Condition>
              <AgendaStatusLabel cardId={event.cardId} status={appointmentStatus} />
            </div>
          </Condition>
        </div>
      )}
    </ApptEventClick>
  );
});
