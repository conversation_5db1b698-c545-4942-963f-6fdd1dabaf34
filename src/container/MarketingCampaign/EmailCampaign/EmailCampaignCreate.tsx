import { useDispatch, useSelector } from 'amos';
import { Spin } from 'antd';
import classNames from 'classnames';
import { type Dayjs } from 'dayjs';
import { isNumber } from 'lodash';
import { type DeepPartial } from 'monofile-utilities/lib/types';
import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import { useBeforeUnload, useSetState, useUnmount } from 'react-use';
import { Condition } from '../../../components/Condition';
import {
  addInlineStyleForContent,
  changeTemplateLogoWithBusinessSetting,
} from '../../../components/RichTextEditor/RichEditorPlugins/utils';
import { applyPlaceholder } from '../../../components/RichTextEditor/utils/applyPlaceholder';
import { PATH_MARKETING_CAMPAIGN_PANEL } from '../../../router/paths';
import { useBrandedAppConfig } from '../../../store/branded/branded.hooks';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { selectEmailBilling } from '../../../store/company/company.selectors';
import { removeAllClientFilter } from '../../../store/customer/clientFilters.action';
import { ClientFilterListSourceMap } from '../../../store/customer/clientFilters.boxes';
import {
  type EmailDetail,
  type TestEmailDetail,
  getEmailTemplateDetail,
  purchaseEmailCredit,
  saveEmailAsDraft,
  sendEmail,
  sendTestEmail,
  setEmailPresevedFilters,
  setEmailTemplateFilters,
} from '../../../store/marketingCampaign/emailCampaign.action';
import { CreateEmailStep } from '../../../store/marketingCampaign/emailCampaign.util';
import { useRouteQuery } from '../../../utils/RoutePath';
import { useBool } from '../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { useClientFilterListParams } from '../../Client/ClientList/componentsLegacy/ClientFilters/ClientFilters.hooks';
import { EmailEditorPlaceholderKey } from '../const';
import { emailLeaveConfirm } from './EmailActions/EmailLeaveConfirm';
import { StyledSpinView } from './EmailCampaignCreate.style';
import { EmailCampaignBread } from './components/EmailCampaignBread';
import { EmailCampaignCreateSteps } from './components/EmailCampaignCreateSteps';
import { EmailCampaignEditor, type EmailCampaignEditorRef } from './components/EmailCampaignEditor';
import {
  EmailCampaignSelectRecipient,
  type EmailCampaignSelectRecipientRef,
} from './components/EmailCampaignSelectRecipient';
import { EmailCampaignSendEmail } from './components/EmailCampaignSendEmail';
import { useCreateOrSaveAsNewEmail } from './hooks/useCreateNewEmail';
import { useEditContent } from './hooks/useEditContent';
import { useEmailClientListKeyword } from './hooks/useEmailClientListFilters';
import { usePermissionCheckAndJump } from './hooks/usePermissionCheckAndJump';
import { selectChosenCustomersWithoutFilter } from '../../../store/customer/chosenCustomers.selectors';
import { EmailCampaignSelectSpecificCustomer } from './components/EmailCampaignSelectSpecificCustomer';
import { EmailCampaignSelectTemplate } from './components/EmailCampaignSelectTemplate';

export const EmailCampaignCreate = memo(() => {
  const recipientLoading = useBool(false);
  const editorRef = useRef<EmailCampaignEditorRef>(null);
  const recipientRef = useRef<EmailCampaignSelectRecipientRef>(null);
  const dispatch = useDispatch();
  const query = useRouteQuery(PATH_MARKETING_CAMPAIGN_PANEL);
  const queryedStep = parseInt(query?.step ?? '0');
  const step = isNaN(queryedStep) ? 0 : queryedStep > 1 ? 1 : queryedStep;
  const [currentStep, setCurrentStep] = useState(step); // just can be 0 or 1
  const hasSent = useBool(false);
  const isNewEmail = useCreateOrSaveAsNewEmail();
  const isSpecificCustomer = !!query.specificCustomer;
  const [business, emailBilling, chosenCustomersWithoutFilter] = useSelector(
    selectCurrentBusiness,
    selectEmailBilling,
    selectChosenCustomersWithoutFilter(query.specificCustomer),
  );
  const specificCustomerList = useMemo(() => {
    return chosenCustomersWithoutFilter.toArray().filter((customer) => !!customer.email);
  }, [chosenCustomersWithoutFilter]);
  const { data: brandedConfig, petParentDownloadLink } = useBrandedAppConfig();
  const filterListParams = useClientFilterListParams(ClientFilterListSourceMap.MassEmail);
  const [emailState, setEmailState] = useSetState({
    emailSubject: '',
    emailContent: '',
  });
  const { emailSubject, emailContent } = emailState;
  const [templateId, setTemplateId] = useState('0');
  const editorIsDirty = useBool(false);
  const customerListKeyword = useEmailClientListKeyword();

  const [attachment, setAttachment] = useSetState({
    name: '',
    url: '',
    type: '',
    size: 0,
  });

  const { loading } = useEditContent({
    onChange: (res) => {
      setEmailState({ emailSubject: res.subject, emailContent: res.content });
      dispatch(setEmailPresevedFilters(res.clientFilter));
    },
  });

  const handleNextStep = useSerialCallback(async (nextStep: number) => {
    if (currentStep === CreateEmailStep.EditCampaign) {
      if (!editorRef.current?.checkContentIsValid()) {
        return;
      }
    }
    if (currentStep === CreateEmailStep.SelectRecipients && !isSpecificCustomer) {
      const { validate, autoReloadTimes } = recipientRef.current?.checkRecipientAndCredit() ?? {};
      if (!validate) {
        return;
      }
      if (isNumber(autoReloadTimes)) {
        await dispatch(
          purchaseEmailCredit({
            autoReload: emailBilling.autoReload,
            credits: autoReloadTimes,
          }),
        );
      }
    }

    handleSelecStep(nextStep, true);
  });

  const handleSelecStep = (selectedStep: number, canGoNext = false) => {
    if (selectedStep === currentStep) return;
    setCurrentStep(selectedStep);
  };

  useEffect(() => {
    handleSelecStep(step, true);
  }, [step]);

  const handleSendTestEmail = useSerialCallback(async (params: { subject: string; recipient: string }) => {
    const { subject, recipient } = params;
    const sendObj: TestEmailDetail = {
      content: addInlineStyleForContent(emailContent),
      subject,
      attachments: [],
      recipientEmail: recipient,
    };

    if (attachment.url && attachment.name && attachment.type) {
      sendObj.attachments!.push({
        name: attachment.name,
        type: attachment.type,
        url: attachment.url,
      });
    }

    await dispatch(sendTestEmail(sendObj));
  });

  const handleSendEmail = useSerialCallback(async (sendAt?: Dayjs) => {
    const sendToSpecificCustomers = !!query.specificCustomer;
    const draftId = isNewEmail ? undefined : query.id;
    const sendObj: DeepPartial<EmailDetail> = {
      content: addInlineStyleForContent(emailContent),
      subject: emailSubject,
      attachmentUrls: [],
      sendAt: sendAt ? sendAt.toISOString() : undefined,
    };
    if (attachment.url && attachment.name && attachment.type) {
      sendObj.attachmentUrls!.push({
        name: attachment.name,
        type: attachment.type,
        url: attachment.url,
      });
    }
    await dispatch(
      sendEmail(sendObj, draftId, {
        filters: filterListParams,
        specificCustomerList: sendToSpecificCustomers
          ? specificCustomerList.map((customer) => String(customer.customerId))
          : undefined,
        queries: {
          keyword: customerListKeyword,
        },
      }),
    );
    hasSent.open();
  });

  const handleClickTemplate = useSerialCallback(async (templateId: string) => {
    if (templateId !== '0') {
      try {
        const res = await dispatch(getEmailTemplateDetail(templateId));
        const _emailContent = changeTemplateLogoWithBusinessSetting(res.content, business.avatarPath || '');
        const applyTask = applyPlaceholder(_emailContent);

        if (brandedConfig) {
          applyTask
            .apply(EmailEditorPlaceholderKey.AppDownloadLink, petParentDownloadLink)
            .apply(EmailEditorPlaceholderKey.BrandedAppIcon, brandedConfig.appIconUrl)
            .apply(EmailEditorPlaceholderKey.BrandedAppName, brandedConfig.appName);
        }
        const emailContent = applyTask.done();
        const emailSubject = res.subject;
        const filter = res.clientFilter;
        setEmailState({
          emailSubject,
          emailContent,
        });
        dispatch(setEmailTemplateFilters(filter, templateId));
      } catch (e) {
        console.error(e);
      }
    } else {
      setEmailState({
        emailContent: '',
        emailSubject: '',
      });
      dispatch(setEmailTemplateFilters('', templateId));
    }
    if (!query.hasFilter) {
      dispatch(removeAllClientFilter(ClientFilterListSourceMap.MassEmail));
    }
    setTemplateId(templateId);
    editorIsDirty.close();
    handleNextStep(CreateEmailStep.EditCampaign);
  });

  const isDirty = useMemo(() => {
    if (hasSent.value) {
      return false;
    }
    return editorIsDirty.value;
  }, [hasSent.value, editorIsDirty.value]);

  useBeforeUnload(isDirty, 'You have unsaved changes, are you sure?');

  const handleSaveAsDraft = useSerialCallback(async (draftId?: string) => {
    const sendObj: Partial<EmailDetail> = {
      content: addInlineStyleForContent(emailContent),
      subject: emailSubject,
    };
    await dispatch(
      saveEmailAsDraft(sendObj, draftId, {
        filters: filterListParams,
        queries: {
          keyword: customerListKeyword,
        },
      }),
    );
  });

  const handleConfirmLeave = useSerialCallback(async () => {
    await handleSaveAsDraft(isNewEmail ? undefined : query.id);
  });

  const handleClickStep = useLatestCallback(async (step: number) => {
    if (step >= currentStep) return;
    if (step === CreateEmailStep.SelectTemplate && isDirty) {
      const canGonextStep = await emailLeaveConfirm({
        onConfirm: handleConfirmLeave,
      });
      if (!canGonextStep) {
        return;
      }
    }
    handleSelecStep(step);
  });

  useUnmount(() => {
    dispatch(setEmailPresevedFilters(''));
  });

  useEffect(() => {
    query.templateId && handleClickTemplate(query.templateId);
  }, [query.templateId]);

  usePermissionCheckAndJump('create');

  const isSelectRecipientsOrEditCampaign = useMemo(() => {
    return currentStep === CreateEmailStep.SelectRecipients || currentStep === CreateEmailStep.EditCampaign;
  }, [currentStep]);

  return (
    <div className="moe-p-[20px]">
      <EmailCampaignBread isDirty={isDirty} handleSaveAsDraft={handleSaveAsDraft} />
      <div className="moe-rounded-[8px] moe-pb-[20px] moe-flex moe-flex-col moe-justify-between">
        <EmailCampaignCreateSteps
          loading={handleNextStep.isBusy()}
          currentStep={currentStep}
          handleClickStep={handleClickStep}
          handleNextStep={handleNextStep}
          disabled={recipientLoading.value}
        />
        <div
          className={classNames(
            'moe-rounded-[8px] moe-flex-1 moe-min-w-[1080px]',
            isSelectRecipientsOrEditCampaign ? 'moe-flex' : 'moe-bg-[#FFF] moe-py-[40px]',
          )}
        >
          <Condition if={currentStep === CreateEmailStep.SelectTemplate}>
            <EmailCampaignSelectTemplate onClickTemplate={handleClickTemplate} />
          </Condition>
          <Condition if={currentStep === CreateEmailStep.EditCampaign}>
            <StyledSpinView>
              <Spin spinning={loading}>
                <EmailCampaignEditor
                  ref={editorRef}
                  subject={emailState.emailSubject}
                  content={emailState.emailContent}
                  onContentChange={(emailContent) => setEmailState({ emailContent })}
                  onSubjectChange={(emailSubject) => setEmailState({ emailSubject })}
                  onAttachmentChange={(res) => setAttachment(res)}
                  attachment={attachment}
                  templateId={templateId}
                  onDirty={editorIsDirty.open}
                  // if there is an email id, we need to set the content after editor init
                  setContentAfterInit={!!query.id}
                />
              </Spin>
            </StyledSpinView>
          </Condition>
          <Condition if={currentStep === CreateEmailStep.SelectRecipients}>
            {isSpecificCustomer ? (
              <EmailCampaignSelectSpecificCustomer
                templateId={templateId}
                recipientLoading={recipientLoading.value}
                setRecipientLoading={recipientLoading.as}
              />
            ) : (
              <EmailCampaignSelectRecipient
                ref={recipientRef}
                templateId={templateId}
                recipientLoading={recipientLoading.value}
                setRecipientLoading={recipientLoading.as}
              />
            )}
          </Condition>
          <Condition if={currentStep === CreateEmailStep.SendEmail}>
            <EmailCampaignSendEmail
              sendTestEmail={handleSendTestEmail}
              sendEmail={handleSendEmail}
              subject={emailSubject}
              content={emailContent}
            />
          </Condition>
        </div>
      </div>
    </div>
  );
});
