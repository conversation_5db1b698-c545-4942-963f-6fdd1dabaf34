// add history item for lead create

import { type HistoryLogCreate } from '@moego/api-web-v2/backend/proto/customer/v1/customer';
import React, { memo, useMemo } from 'react';
import { type HistoryItemParams } from './HistoryItem';
import { HistoryItemWrapper } from './HistoryItemWrapper';

export const LeadCreate = memo<HistoryItemParams<HistoryLogCreate>>(function LeadCreate(props) {
  const { dateTime, sourceName, isLast } = props;
  const label = useMemo(() => {
    let label = `Lead was created`;
    if (sourceName) {
      label += ` by ${sourceName}`;
    }
    return label;
  }, [sourceName]);

  return (
    <HistoryItemWrapper label={label} dateTime={dateTime} isLast={isLast}>
      {null}
    </HistoryItemWrapper>
  );
});
