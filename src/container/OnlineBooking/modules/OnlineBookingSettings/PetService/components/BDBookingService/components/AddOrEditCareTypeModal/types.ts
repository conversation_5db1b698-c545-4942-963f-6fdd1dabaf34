import { type FormFieldSharedProps, type UseFormReturn } from '@moego/ui';
import { type OnlineBookingCareTypeConfig } from '../../../../../../../../../store/onlineBooking/onlineBookingServices.boxes';
import { type ApplicableServices } from '@moego/api-web/moego/models/online_booking/v1/ob_customize_care_type_defs';

export interface FormValues extends Omit<OnlineBookingCareTypeConfig, 'id' | 'serviceType' | 'applicableServices'> {
  applicableServices: ApplicableServices;
}

export interface CareTypeIconImageProps extends FormFieldSharedProps {
  value?: string;
  onChange?: (value: string) => void;
  id?: string;
  form: UseFormReturn<FormValues>;
}

export interface PreviewCareTypeImage {
  id: string;
  name: string;
  description?: string;
  image?: string;
}

export interface PreviewCareTypeIcon {
  id: string;
  name: string;
  icon?: string;
}
