import { useSelector } from 'amos';
import { Select, Tabs } from 'antd';
import { type SelectProps } from 'antd/es/select';
import classNames from 'classnames';
import React, { memo, useMemo } from 'react';
import { TagServiceDuration } from '../../../components/ServiceApplicablePicker/components/TagService/TagServiceDuration';
import { TagServicePrice } from '../../../components/ServiceApplicablePicker/components/TagService/TagServicePrice';
import { type ServicePriceDurationInfo } from '../../../components/ServiceApplicablePicker/hooks/useServiceInfo';
import { useGetClosestScroller } from '../../../layout/components/ScrollerProvider';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { type PetServiceWithCategory } from '../../../store/createTicket/createTicket.types';
import { ServiceType } from '../../../store/service/category.boxes';
import { serviceMapBox } from '../../../store/service/service.boxes';
import { ID_ANONYMOUS, isNormal } from '../../../store/utils/identifier';
import { useBool } from '../../../utils/hooks/useBool';
import { useControllableValue } from '../../../utils/hooks/useControlledValue';
import { ToggleApplicable } from '../../Calendar/Grooming/QuickAppointmentModal/ToggleApplicable';
import { useApplicablePetServices } from '../hooks/useApplicablePetServices';
import { useTicketStoreCtx } from '../hooks/useTicketForm';
import { PickServiceBox } from './PickService.style';

export interface PickServiceProps extends Omit<SelectProps<number>, 'options' | 'onChange'> {
  className?: string;
  petId: number;
  clientId: number;
  value?: number;
  serviceLocalId?: number;
  onChange?: (value: number, service: ServicePriceDurationInfo) => void;
  /** option中不存在的service id，则展示fallbackServiceName */
  fallbackServiceName?: string;
  disabledServices?: number[];
  loadingOptions?: boolean;
  options?: PetServiceWithCategory[];
  bottomRight?: React.ReactNode;
  serviceType?: number;
  onChangeServiceType?: (serviceType: number) => void;
}

export const PickService = memo<PickServiceProps>(function PickService(props) {
  const {
    className,
    petId,
    clientId,
    value,
    serviceLocalId,
    fallbackServiceName,
    onChange,
    disabledServices,
    loadingOptions,
    options,
    bottomRight,
    ...otherSelectProps
  } = props;
  const hasDefinedOptions = 'options' in props;
  const disabledServiceIds = useMemo(
    () => (Array.isArray(disabledServices) ? disabledServices : []),
    [disabledServices],
  );
  const [{ petsService }] = useTicketStoreCtx();
  const [serviceMap, business] = useSelector(serviceMapBox, selectCurrentBusiness);
  const validService = isNormal(value);
  const currentServiceId = validService ? value : ID_ANONYMOUS;
  const currentServiceName = validService ? serviceMap.mustGetItem(value).name || fallbackServiceName : '';
  const isApplicableOpen = useBool(true);
  const [serviceType, setServiceType] = useControllableValue<number>(props, {
    valuePropName: 'serviceType',
    trigger: 'onChangeServiceType',
    defaultValue: ServiceType.Service,
  });

  const serviceStaffId =
    petsService.find((i) => i.petId === petId)?.services?.find((i) => i.servicLocalId === serviceLocalId)?.staffId ??
    ID_ANONYMOUS;

  const { applicableServices, applicableAddons, loading, lastActiveAddonList, lastActiveServiceList, getServiceInfo } =
    useApplicablePetServices({
      petId,
      clientId,
      serviceStaffId,
      applicable: isApplicableOpen.value,
      loadingOptions,
      // 内部根据!!options判断是会否受控，所以如果外部传了options，就必定传个值
      options: hasDefinedOptions ? (options ?? []) : undefined,
    });
  const getPopupContainer = useGetClosestScroller();
  const isAddons = serviceType === ServiceType.Addon;
  const list = isAddons ? applicableAddons : applicableServices;
  const lastActiveList = isAddons ? lastActiveAddonList : lastActiveServiceList;
  const renderOption = (serviceId: number, prefixKey = '') => {
    const savedInfo = getServiceInfo(serviceId);
    const { servicePrice, serviceTime, priceOverrideType, durationOverrideType } = savedInfo;
    const serviceName = serviceMap.mustGetItem(serviceId).name;
    const formattedPrice = business.formatAmount(servicePrice);

    return (
      <Select.Option
        key={`${prefixKey}_${serviceId}`}
        value={serviceId}
        label={serviceName}
        disabled={disabledServiceIds.includes(serviceId)}
      >
        <div className="moe-flex moe-items-center moe-justify-between">
          <div className="moe-flex-1 moe-min-w-0 moe-truncate">{serviceName}</div>
          <div className="moe-flex moe-items-center moe-gap-x-[20px]">
            <TagServicePrice
              hiddenIcon
              className="moe-flex-shrink-0"
              price={formattedPrice}
              overrideType={priceOverrideType}
            />
            <TagServiceDuration
              hiddenIcon
              className="moe-flex-shrink-0 moe-min-w-[60px]"
              duration={serviceTime}
              overrideType={durationOverrideType}
            />
          </div>
        </div>
      </Select.Option>
    );
  };

  return (
    <Select<number>
      {...otherSelectProps}
      value={validService ? currentServiceId : undefined}
      showSearch
      className={classNames(className)}
      placeholder="Search with service name"
      loading={loading}
      getPopupContainer={getPopupContainer}
      dropdownRender={(menu) => {
        return (
          <PickServiceBox>
            <Tabs
              size="small"
              activeKey={String(serviceType)}
              onChange={(key) => {
                setServiceType(Number(key));
              }}
            >
              <Tabs.TabPane tab="Services" key={ServiceType.Service}>
                <div>{menu}</div>
              </Tabs.TabPane>
              <Tabs.TabPane tab="Add-ons" key={ServiceType.Addon}>
                <div>{menu}</div>
              </Tabs.TabPane>
            </Tabs>
            <div
              className="moe-py-[8px] moe-px-[20px] moe-flex moe-justify-between"
              style={{ borderTop: '1px solid #E6E6E6' }}
            >
              <ToggleApplicable
                checked={isApplicableOpen.value}
                onChange={isApplicableOpen.as}
                serviceType={serviceType}
              />
              {bottomRight}
            </div>
          </PickServiceBox>
        );
      }}
      optionFilterProp="label"
      optionLabelProp="label"
      onChange={(nextServiceId) => {
        const serviceInfo = getServiceInfo(nextServiceId);

        onChange?.(nextServiceId, serviceInfo);
      }}
    >
      <Select.Option
        className="!moe-hidden"
        key={`placeholder_${currentServiceId}`}
        value={currentServiceId}
        label={currentServiceName}
      >
        {currentServiceName}
      </Select.Option>
      {lastActiveList.length > 0 && (
        <Select.OptGroup label="LAST APPOINTMENT">
          {lastActiveList.map((item) => renderOption(item.serviceId, 'last_'))}
        </Select.OptGroup>
      )}
      {list.map((category) => {
        const { categoryId, categoryName, services } = category;
        const serviceIds = services.map((i) => i.serviceId);

        return (
          <Select.OptGroup key={categoryId} label={categoryName || 'UNCATEGORIZED'}>
            {serviceIds.map((serviceId) => renderOption(serviceId))}
          </Select.OptGroup>
        );
      })}
    </Select>
  );
});
