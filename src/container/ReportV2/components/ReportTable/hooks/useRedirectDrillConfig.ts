import { type DrillConfig } from '@moego/reporting';
import { useCallback } from 'react';
import { PATH_REPORT_INSIGHTS_REPORTS } from '../../../../../router/paths';
import { openWindow } from '../../../../../utils/utils';
import { type Dayjs } from 'dayjs';

interface RedirectDrillConfigParams {
  drillConfig: DrillConfig;
  businessId: string | undefined;
  dateRanges: [Dayjs, Dayjs];
}

interface UseRedirectDrillConfigParams {
  active?: string;
}

export const useRedirectDrillConfig = ({ active }: UseRedirectDrillConfigParams = {}) => {
  const redirect = useCallback(
    (config: RedirectDrillConfigParams) => {
      const { drillConfig, dateRanges, businessId } = config;
      if (!drillConfig || !drillConfig.targetId) return;
      const { filters, targetId, dimensions } = drillConfig;
      const url = PATH_REPORT_INSIGHTS_REPORTS.queried({
        diagramId: targetId,
        filters: filters ? JSON.stringify(filters) : undefined,
        dateRange: JSON.stringify({
          start: dateRanges[0].startOf('day'),
          end: dateRanges[1].endOf('day'),
        }),
        dimensions: dimensions && dimensions.length > 0 ? JSON.stringify(dimensions) : undefined,
        active,
        businessId,
      });
      openWindow(url);
    },
    [active],
  );
  return redirect;
};
