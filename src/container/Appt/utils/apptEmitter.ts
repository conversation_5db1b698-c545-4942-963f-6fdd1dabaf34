import EventEmitter from 'eventemitter3';
import { useEffect } from 'react';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';

export enum ApptEventEnum {
  APPT_REFRESHED = 'apptRefreshed',
}
export const apptEmitter = new EventEmitter<{
  [ApptEventEnum.APPT_REFRESHED]: [];
}>();

export function useApptEvent(event: ApptEventEnum, callback: () => void) {
  const memoizedCallback = useLatestCallback(callback);
  useEffect(() => {
    apptEmitter.on(event, memoizedCallback);
    return () => {
      apptEmitter.off(event, memoizedCallback);
    };
  }, [event, memoizedCallback]);
}
