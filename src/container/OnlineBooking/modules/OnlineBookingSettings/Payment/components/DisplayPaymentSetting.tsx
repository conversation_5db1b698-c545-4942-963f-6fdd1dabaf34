import { useSelector } from 'amos';
import React, { type ReactNode, useMemo } from 'react';
import { Typography } from '@moego/ui';
import { ErrorHint } from '../../../../../../components/ColoredHint/ColoredHint';
import { Condition } from '../../../../../../components/Condition';
import { type CustomerSmartListFilterParams } from '../../../../../../store/customer/customer.boxes';
import {
  PaymentSettingsAcceptClientType,
  isExistingClientAccepted,
  isNewClientAccepted,
} from '../../../../../../store/onlineBooking/onlineBooking.boxes';
import { selectOnlineBookPaymentAcceptCustomerType } from '../../../../../../store/onlineBooking/onlineBooking.selectors';
import { selectOnlineBookingPreference } from '../../../../../../store/onlineBooking/onlineBookingPreference.selectors';
import { jsonParse } from '../../../../../../utils/utils';
import { paymentPropertyList } from '../../../../../Client/ClientList/componentsLegacy/ClientFilters/AllFilterList';
import { useDisplayBEFilters } from '../../../../../Client/ClientList/componentsLegacy/ClientFilters/useDisplayBEFilters';
import { DisplayPaymentType } from './DisplayPaymentType';
import { DisplayPaymentTypeDetail } from './DisplayPaymentTypeDetail';
import { getClientGroupErrorMsg } from './PaymentType.util';

interface ClientGroupItem {
  label: string;
  values: ReactNode[];
}

type PaymentPropertyKey = (typeof paymentPropertyList)[number];
// Used to sort display filters
const propertyIndexMap = paymentPropertyList.reduce(
  (previousValue, currentValue, currentIndex) => {
    previousValue[currentValue] = currentIndex;
    return previousValue;
  },
  {} as Record<PaymentPropertyKey, number>,
);

/**
 * The payment setting summary to display after saved.
 */
export const DisplayPaymentSetting = () => {
  const getDisplayBEFilters = useDisplayBEFilters();
  const [preference, acceptClient] = useSelector(
    selectOnlineBookingPreference(),
    selectOnlineBookPaymentAcceptCustomerType(),
  );
  const { certainGroupSetting } = preference.paymentSetting;
  const { ACCEPT_CLIENT_TYPE_UNSPECIFIED, ACCEPT_CLIENT_TYPE_BOTH } = PaymentSettingsAcceptClientType;
  const isNew = isNewClientAccepted(certainGroupSetting?.acceptClient ?? ACCEPT_CLIENT_TYPE_UNSPECIFIED);
  const isExisting = isExistingClientAccepted(certainGroupSetting?.acceptClient ?? ACCEPT_CLIENT_TYPE_UNSPECIFIED);
  const acceptFilters = useMemo(() => {
    if (!isExisting) {
      return undefined;
    }
    const { filters } = jsonParse<{
      filters?: CustomerSmartListFilterParams[];
    }>(preference.certainGroupSettingAcceptRule, {
      filters: undefined,
    });
    // Sort by the order in filter drawer
    return filters?.sort(
      ({ property: pa }, { property: pb }) =>
        (propertyIndexMap[pa as PaymentPropertyKey] ?? 0) - (propertyIndexMap[pb as PaymentPropertyKey] ?? 0),
    );
  }, [isExisting, preference.certainGroupSettingAcceptRule]);

  const clientGroupItems = useMemo(() => {
    const items: ClientGroupItem[] = [];
    if (!acceptFilters) {
      return items;
    }
    getDisplayBEFilters(acceptFilters).forEach(({ title, values }) => {
      items.push({
        label: title,
        values: values,
      });
    });
    return items;
  }, [acceptFilters, getDisplayBEFilters]);

  const renderClientGroupItems = () => {
    return (
      <div className="moe-ml-[22px] moe-flex moe-flex-col moe-gap-[10px]">
        {clientGroupItems.map((item) => (
          <div key={item.label}>
            <span className="moe-text-[14px] moe-leading-[18px] moe-font-medium moe-text-tertiary moe-mr-[4px]">
              {item.label}:
            </span>
            {item.values.map((v, i) => (
              <>
                {i !== 0 && (
                  <span className="moe-text-[14px] moe-leading-[18px] moe-font-medium moe-text-tertiary moe-mx-[4px]">
                    or
                  </span>
                )}
                <span className="moe-text-[12px] moe-leading-[16px] moe-font-medium moe-text-[#4B83EE] moe-bg-[#EDF3FD] moe-rounded-[37.5px] moe-px-[8px] moe-py-[4px]">
                  {v}
                </span>
              </>
            ))}
          </div>
        ))}
      </div>
    );
  };

  if (!certainGroupSetting) {
    return null;
  }

  return (
    <div>
      <div className="moe-pb-[24px]">
        <Condition if={certainGroupSetting.acceptClient !== ACCEPT_CLIENT_TYPE_UNSPECIFIED}>
          <div className="moe-text-regular-short moe-font-bold moe-text-tertiary moe-mb-[16px]">Client group:</div>
          <div className="moe-flex moe-flex-col moe-gap-[8px]">
            <Condition if={isNew}>
              <div className="moe-p-[16px] moe-rounded-[8px] moe-bg-neutral-sunken-0 moe-text-[16px] moe-leading-[18px] moe-font-medium">
                <div className="moe-flex moe-items-center">
                  <div className="moe-h-[5px] moe-w-[5px] moe-mx-[8.5px] moe-bg-[#333] moe-rounded-[50%]" />
                  <Typography.Heading size={5} className="moe-mb-[8px] moe-text-secondary">
                    New visitors
                  </Typography.Heading>
                </div>
                <div className="moe-ml-[22px] moe-text-tertiary">New visitors not yet in your client list.</div>
              </div>
            </Condition>
            <Condition if={isExisting}>
              <div className="moe-p-[16px] moe-rounded-[8px] moe-bg-neutral-sunken-0 moe-text-[16px] moe-leading-[18px] moe-font-medium">
                <div className="moe-flex moe-items-center">
                  <div className="moe-h-[5px] moe-w-[5px] moe-mx-[8.5px] moe-bg-[#333] moe-rounded-[50%]" />
                  <Typography.Heading size={5} className="moe-mb-[8px] moe-text-secondary">
                    Existing clients
                  </Typography.Heading>
                </div>
                <Condition if={acceptFilters}>{renderClientGroupItems()}</Condition>
                <Condition if={!acceptFilters}>
                  <div className="moe-ml-[22px] moe-text-tertiary">All existing clients in your client list.</div>
                </Condition>
              </div>
            </Condition>
          </div>
          <Condition if={acceptClient !== ACCEPT_CLIENT_TYPE_BOTH && acceptClient !== certainGroupSetting.acceptClient}>
            <ErrorHint
              text={getClientGroupErrorMsg(certainGroupSetting.acceptClient, acceptClient)}
              className="moe-mt-[8px]"
            />
          </Condition>
        </Condition>
      </div>
      <DisplayPaymentType showTitle />
      <Condition if={certainGroupSetting}>
        <DisplayPaymentTypeDetail paymentSetting={certainGroupSetting} />
      </Condition>
    </div>
  );
};
