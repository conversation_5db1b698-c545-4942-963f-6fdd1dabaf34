import React, { memo, useMemo } from 'react';
import { ClearlyFilterValues, type SpecificFilterProps } from '../FilterProperty.options';
import { CheckboxFilter } from './common/CheckboxFilter';
import { FilterWrapper } from './common/FilterWrapper';
import { useSelector } from 'amos';
import { leadLifeCycleMapBox } from '../../../../../../store/leads/leadLifeCycle.boxes';
import { selectLeadLifeCycleIdList } from '../../../../../../store/leads/leadLifeCycle.selectors';
import { getMultiSelectLabel } from '../FilterProperty.utils';
import { useFilterProperty } from '../../hooks/useFilterProperty';

export const LeadLifeCycle = memo<SpecificFilterProps>(({ type, property }) => {
  const { filter, handleChange, handleRemove } = useFilterProperty(property);
  const [lifeCycleList, lifeCycleMap] = useSelector(selectLeadLifeCycleIdList(), leadLifeCycleMapBox);
  const lifeCycleOptionList = useMemo(() => {
    return lifeCycleList.toArray().map((id) => ({ label: lifeCycleMap.mustGetItem(id).name, value: id }));
  }, [lifeCycleList, lifeCycleMap]);
  const dropdownValue = getMultiSelectLabel({ options: lifeCycleOptionList, values: filter.values });

  return (
    <FilterWrapper type={type} property={property} dropdownValue={dropdownValue} onClear={handleRemove}>
      <CheckboxFilter
        options={lifeCycleOptionList}
        value={ClearlyFilterValues(lifeCycleOptionList, filter.values)}
        onChange={handleChange}
      />
    </FilterWrapper>
  );
});
