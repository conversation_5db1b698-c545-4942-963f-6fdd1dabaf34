import { useDispatch } from 'amos';
import { useLatest } from 'react-use';
import {
  type CalendarApptCardParams,
  getMonthlyViewList,
  getCalendarSlotInfo,
} from '../../../../../store/calendarLatest/actions/private/calendar.actions';
import { calendarCurrentViewBox, calendarLoadingEventsBox } from '../../../../../store/calendarLatest/calendar.boxes';
import { ViewType } from '../../../../../store/calendarLatest/calendar.types';
import { getApptCalendarCards } from '../../../../../store/calendarLatest/card.actions';
import { selectBDFeatureEnable } from '../../../../../store/company/company.selectors';
import { useDebounceCallback } from '../../../../../utils/hooks/useDebounceCallback';
import { useLazySelectors } from '../../../../../utils/unstable/createSelectAccessor';
import { useSelectedRangeDates } from '../../hooks/useSelectedRangeDates';
import { store } from '../../../../../provider';
import { currentBusinessIdBox } from '../../../../../store/business/business.boxes';

interface Params extends Partial<CalendarApptCardParams> {
  /**
   * 是否展示loading的UI
   * 不需要展示的场景，比如抽屉的一些状态更新，展示UI会太频繁
   */
  loading?: boolean;
}

type RefreshCallback = (extraParams?: Params) => Promise<void>;

/** 获取calendar中appt卡片 */
export function useGetCalendarEvents() {
  const dispatch = useDispatch();
  const { getRangeDates } = useSelectedRangeDates();
  const [currentView, boardingDaycareFeatureEnable] = useLazySelectors(calendarCurrentViewBox, selectBDFeatureEnable);

  const getAppts = useLatest<RefreshCallback>(async (extraParams?: Params) => {
    const isMonthlyView = currentView() === ViewType.MONTH;
    const { loading, ...extraReqParams } = extraParams ?? { loading: true };
    const noNeedLoading = loading === false;
    const params: CalendarApptCardParams = {
      isWaitingList: false,
      filterNoStaff: boardingDaycareFeatureEnable() ? false : true,
      filterNoStartTime: true,
      ...getRangeDates(),
      ...extraReqParams,
    };
    try {
      if (!noNeedLoading) {
        dispatch(calendarLoadingEventsBox.setState(true));
      }
      isMonthlyView ? await dispatch(getMonthlyViewList(params)) : await dispatch(getApptCalendarCards(params));
      dispatch(
        getCalendarSlotInfo({
          businessId: store.select(currentBusinessIdBox).toString(),
          startDate: params.startDate!,
          endDate: params.endDate!,
        }),
      );
    } finally {
      dispatch(calendarLoadingEventsBox.setState(false));
    }
  });

  /** 重新加载数据 */
  const reloadAppts = useDebounceCallback<RefreshCallback>((...args) => getAppts.current(...args), 100);

  return {
    reloadAppts,
  };
}
