import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useSelector } from 'amos';
import { useEffect } from 'react';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { getTaxList } from '../../../store/business/tax.actions';
import { getServiceCategoryList } from '../../../store/service/actions/public/category.actions';
import { AddonItemType } from '../../../store/service/category.boxes';
import { isNormal } from '../../../store/utils/identifier';

export function useTaxAndService() {
  const dispatch = useDispatch();
  const [business] = useSelector(selectCurrentBusiness);
  const { id: bizId } = business;

  useEffect(() => {
    if (isNormal(bizId)) {
      dispatch([
        getTaxList(),
        getServiceCategoryList(ServiceType.SERVICE, ServiceItemType.GROOMING),
        getServiceCategoryList(ServiceType.ADDON, AddonItemType.serviceAddon),
      ]);
    }
  }, [bizId]);
}
