import React, { type FC } from 'react';
import { type CareTypeServiceDateTimeProps } from './timeCell.types';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { BoardingDateTime } from './BoardingDateTime';
import { EvaluationDateTime } from './EvaluationDateTime';
import { DaycareDateTime } from './DaycareDateTime';
import { DefaultDateTime } from './DefaultDateTime';
import { GroupClassDateTime } from './GroupClassDateTime';

export interface CareTypeDateTimeRouterProps extends CareTypeServiceDateTimeProps {
  serviceItemType: ServiceItemType;
}

export const CareTypeDateTimeRouter: FC<CareTypeDateTimeRouterProps> = (props) => {
  const { serviceItemType, showCareTypeName, rowData } = props;

  switch (serviceItemType) {
    case ServiceItemType.BOARDING:
      return <BoardingDateTime showCareTypeName={showCareTypeName} rowData={rowData} />;
    case ServiceItemType.DAYCARE:
      return <DaycareDateTime showCareTypeName={showCareTypeName} rowData={rowData} />;
    case ServiceItemType.EVALUATION:
      return <EvaluationDateTime showCareTypeName={showCareTypeName} rowData={rowData} />;
    case ServiceItemType.GROUP_CLASS:
      return <GroupClassDateTime rowData={rowData} />;
    default:
      return <DefaultDateTime rowData={rowData} />;
  }
};
