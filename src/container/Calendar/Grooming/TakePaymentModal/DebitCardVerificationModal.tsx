import { useDispatch } from 'amos';
import React, { memo } from 'react';
import { ConfirmModal } from '../../../../components/Modal/ConfirmModal';
import { toastApi } from '../../../../components/Toast/Toast';
import { type InvoiceRecord } from '../../../../store/payment/payment.selectors';
import { cancelACHPayment, getACHPaymentStatus } from '../../../../store/stripe/ach.actions';
import { useBool } from '../../../../utils/hooks/useBool';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';

export interface ACHVerificationParams {
  paymentId: number;
  verifyType?: string;
  redirectUrl: string;
}

interface DebitCardVerificationProps {
  visible: boolean;
  onClose: () => void;
  invoice: InvoiceRecord;
  achVerificationParams: ACHVerificationParams;
  onFinished: () => void;
}

export const ALTER_TEXT = {
  success: 'ACH initiated! Payment typically takes 1-2 business days to process.',
  fail: 'Fail to submit payment. Please try again.',
};

const MODAL_TEXT =
  'We’ve sent a micro deposit to the customer bank account, please enter either a single micro-deposit with a 6-digit descriptor code or two micro-deposit amounts for verification.';

const CONFIRM_TEXT = {
  step1: 'Verify now',
  step2: 'Finish verification',
};

const CONFIRM_TITLE = 'Verify bank account';

const PAYMENT_STATUS = {
  success: 'succeed',
  pending: 'pending',
};

export const DebitCardVerificationModal = memo<DebitCardVerificationProps>((props) => {
  const { visible, onFinished, onClose, achVerificationParams } = props;
  const dispatch = useDispatch();
  const confirmJumpModalVisible = useBool(true);
  const verificationModalVisible = useBool(false);
  const { paymentId, redirectUrl } = achVerificationParams || {};

  const handleOK = useSerialCallback(async () => {
    const { status } = (await dispatch(getACHPaymentStatus({ paymentId }))) || {};
    if (status === PAYMENT_STATUS.success) {
      toastApi.success(ALTER_TEXT.success);
    } else {
      toastApi.error(ALTER_TEXT.fail);
    }

    onFinished();
    resetModalStatus();
  });

  const resetModalStatus = () => {
    verificationModalVisible.close();
    confirmJumpModalVisible.open();
    onClose();
  };

  const handleCancel = () => {
    dispatch(cancelACHPayment({ paymentId }));
    resetModalStatus();
  };

  const handleConfirmJump = () => {
    window.open(redirectUrl);
    confirmJumpModalVisible.close();
    verificationModalVisible.open();
  };

  if (!visible) {
    return null;
  }

  return (
    <>
      <ConfirmModal
        title={CONFIRM_TITLE}
        visible={confirmJumpModalVisible.value}
        onConfirm={handleConfirmJump}
        onClose={handleCancel}
        content={<p>{MODAL_TEXT}</p>}
        confirmBtnText={CONFIRM_TEXT.step1}
        maskClosable={false}
      />
      <ConfirmModal
        title={CONFIRM_TITLE}
        visible={verificationModalVisible.value}
        onConfirm={handleOK}
        onClose={() => {}}
        loading={handleOK.isBusy()}
        confirmBtnText={CONFIRM_TEXT.step2}
        showClose={false}
        maskClosable={false}
        content={<p>{MODAL_TEXT}</p>}
      />
    </>
  );
});
