import { Button } from '@moego/ui';
import React, { memo } from 'react';
import { useEvaluationDescriptionPreview } from '../../../components/EvaluationDescriptionPreview';

export interface DescriptionPreviewProps {
  description: string;
}

export const DescriptionPreview = memo(({ description }: DescriptionPreviewProps) => {
  const openPreview = useEvaluationDescriptionPreview();
  const handlePreview = () => {
    openPreview({
      isOpen: true,
      description,
    });
  };

  return (
    <Button
      variant="tertiary"
      className="moe-h-[20px] moe-text-tertiary"
      classNames={{ text: 'moe-text-markup-small' }}
      onPress={handlePreview}
    >
      Preview
    </Button>
  );
});
