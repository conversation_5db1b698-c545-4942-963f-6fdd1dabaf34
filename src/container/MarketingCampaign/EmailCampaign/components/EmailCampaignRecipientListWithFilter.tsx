import { useSelector } from 'amos';
import classNames from 'classnames';
import { type List } from 'immutable';
import React, { memo } from 'react';
import SvgIconArrowSvg from '../../../../assets/svg/icon-arrow.svg';
import SvgIconEditNewSvg from '../../../../assets/svg/icon-edit-new.svg';
import { Condition } from '../../../../components/Condition';
import { SvgIcon } from '../../../../components/Icon/Icon';
import { clientFiltersMapBox } from '../../../../store/customer/clientFilters.boxes';
import { useBool } from '../../../../utils/hooks/useBool';
import { FilterPropertyComponentMap } from '../../../Client/ClientList/componentsLegacy/ClientFilters/AllFilterList';
import { FilterPropertyConfigMap } from '../../../Client/ClientList/componentsLegacy/ClientFilters/ClientFilters.config';
import { useFilterListDisplayCount } from '../../../Client/ClientList/componentsLegacy/ClientFilters/ClientFilters.hooks';
import { ReadonlyFilter } from '../../../Client/ClientList/componentsLegacy/ClientFilters/components/common/ReadonlyFilter';
import { ClientSelectedFiltersStyle } from '../../../Client/ClientList/componentsLegacy/ClientSelectedFilters.style';
import { RecipientTable } from './RecipientTable';

interface EmailCampaignRecipientListWithFilterProps {
  customerListTotal: number;
  clientFiltersList: List<string>;
  showFilters: boolean;
  hasExtraFilter: boolean;
  toggleFilters: () => void;
}

export const EmailCampaignRecipientListWithFilter = memo(
  ({
    customerListTotal,
    clientFiltersList,
    showFilters,
    toggleFilters,
    hasExtraFilter,
  }: EmailCampaignRecipientListWithFilterProps) => {
    const [clientFiltersMap] = useSelector(clientFiltersMapBox);
    const filterListDisplayCount = useFilterListDisplayCount();
    const showList = useBool(false);
    return (
      <ClientSelectedFiltersStyle className="!moe-border-solid !moe-border-[1px] !moe-border-[#E6E6E6] !moe-rounded-[8px] !moe-px-[16px] !moe-py-[22px] !moe-mt-[32px]">
        <div className="!moe-flex !moe-justify-between !moe-mb-[26px]">
          <div>
            {customerListTotal} clients selected based on {filterListDisplayCount} filtering criteria:
          </div>
          <div
            onClick={toggleFilters}
            className="!moe-text-brand !moe-text-[14px] !moe-flex !moe-items-center !moe-text-bold !moe-cursor-pointer"
          >
            <SvgIcon src={SvgIconEditNewSvg} size={12.5} className="!moe-mr-[4px]"></SvgIcon>
            {showFilters ? 'Hide filters' : 'Edit filters'}
          </div>
        </div>
        <div className="!moe-flex !moe-flex-wrap !moe-gap-x-[8px] !moe-gap-y-[12px]">
          <Condition if={hasExtraFilter}>
            <ReadonlyFilter title="Customers" value="chosen" />
          </Condition>
          <ReadonlyFilter title="Email" value="Subscribed" />
          {clientFiltersList
            .sort((item) => {
              if (item === FilterPropertyConfigMap.EmailCnt) {
                return -1;
              }
              return 1;
            })
            .map((propertyKey) => {
              const { property } = clientFiltersMap.mustGetItem(propertyKey);
              const filterConfig = FilterPropertyComponentMap.mapLabels[property] ?? null;
              const FilterComponent = filterConfig?.component;
              return FilterComponent ? <FilterComponent property={property} type="dropdown" key={property} /> : null;
            })}
        </div>
        <div className="!moe-mt-[27px]">
          <div
            className="!moe-text-[#333] !moe-text-[14px] !moe-mb-[8px] !moe-flex !moe-items-center !moe-font-bold !moe-cursor-pointer"
            onClick={showList.toggle}
          >
            View list
            <SvgIcon
              src={SvgIconArrowSvg}
              size={12}
              className={classNames(
                '!moe-ml-[4px] !moe-transition-all !moe-duration-300',
                showList.value && '!moe-transform !moe-rotate-180',
              )}
            />
          </div>
          <Condition if={showList.value}>
            <div className="!moe-mx-[-17px] !moe-mb-[-23px]">
              <RecipientTable />
            </div>
          </Condition>
        </div>
      </ClientSelectedFiltersStyle>
    );
  },
);
