import { DatePicker, Form, transformer, Input, Text, cn, useWatch } from '@moego/ui';
import React, { memo } from 'react';
import { usePricingRuleSettingContext } from '../../PricingRuleSettingContext';
import { useSelector } from 'amos';
import { selectCurrentBusiness } from '../../../../../../../../../store/business/business.selectors';
import { emptyToUndefinedWrapper } from '../../../../../../../../../components/form/transformers';
import { FormItemLabel } from '@moego/ui/dist/esm/components/Form/FormItemLabel';
import { WithFormError } from '../../../../../../../../../components/WithFormError/WithFormError';
import { MultipleRuleTips } from './MultipleRuleTips';
import { WeekDayEnum } from '../../../../../../../../../utils/DateTimeUtil';

const FormPrefix = 'ruleConfiguration.conditionGroups.0.conditions.0.value';
const emptyAndDateStringToDayjsTransformer = emptyToUndefinedWrapper(transformer.dateStringToDayjs);
const repeatOptions = WeekDayEnum.values
  .map((item) => ({
    ...WeekDayEnum.mapLabels[item],
    value: item,
  }))
  .sort((a, b) => a.sort - b.sort);

export const RepeatDate = memo(() => {
  const { form } = usePricingRuleSettingContext();
  const [business] = useSelector(selectCurrentBusiness);
  const [dayOfWeeks = []] = useWatch({
    control: form?.control,
    name: [`${FormPrefix}.repeatDates.week.dayOfWeeks`],
  });
  if (!form) return null;
  return (
    <div className="moe-p-8px-200 moe-bg-neutral-sunken-0 moe-rounded-8px-200 moe-flex moe-flex-col moe-gap-y-[24px]">
      <div className="moe-flex moe-flex-row moe-gap-x-[16px]">
        <Form.Item
          label="Start date"
          name={`${FormPrefix}.repeatDates.dateRange.startDate`}
          transformer={emptyAndDateStringToDayjsTransformer}
          rules={{ required: true }}
        >
          <DatePicker isClearable isRequired className="moe-flex-1" format={business.dateFormat} />
        </Form.Item>
        <Form.Item
          label="End date"
          name={`${FormPrefix}.repeatDates.dateRange.endDate`}
          transformer={emptyAndDateStringToDayjsTransformer}
          rules={{ required: true }}
        >
          <DatePicker isClearable isRequired className="moe-flex-1" format={business.dateFormat} />
        </Form.Item>
      </div>
      <div>
        <FormItemLabel isRequired>Occurrence</FormItemLabel>
        <div className="moe-flex moe-flex-row moe-items-center moe-gap-x-s">
          <Text variant="regular-short">Repeat every</Text>
          <Form.Item name={`${FormPrefix}.repeatDates.week.intervalNum`}>
            <Input.Number step={1} minValue={1} suffix="week(s)" className="moe-w-[320px]" />
          </Form.Item>
        </div>
      </div>
      <div>
        <FormItemLabel isRequired>Repeat on</FormItemLabel>
        <Form.Item
          name={`${FormPrefix}.repeatDates.week.dayOfWeeks`}
          rules={{
            validate: (value) => {
              if (!value || value.length === 0) {
                return 'Please select at least one day';
              }
              return true;
            },
          }}
        >
          <WithFormError>
            <div className="moe-flex moe-flex-row moe-gap-x-[16px] moe-mt-[16px]">
              {repeatOptions.map((item) => {
                const isActive = dayOfWeeks.includes(item.value);
                return (
                  <div
                    key={item.value}
                    className={cn(
                      'moe-w-[32px] moe-h-[32px] moe-rounded-[32px] moe-flex moe-items-center moe-justify-center moe-cursor-pointer ',
                      isActive ? 'moe-text-white moe-bg-brand-bold' : 'moe-text-primary moe-bg-white',
                    )}
                    onClick={() => {
                      if (isActive) {
                        form.setValue(
                          `${FormPrefix}.repeatDates.week.dayOfWeeks`,
                          dayOfWeeks.filter((day) => day !== item.value),
                        );
                      } else {
                        form.setValue(`${FormPrefix}.repeatDates.week.dayOfWeeks`, [...dayOfWeeks, item.value]);
                      }
                    }}
                  >
                    {item.label}
                  </div>
                );
              })}
            </div>
          </WithFormError>
        </Form.Item>
      </div>
      <MultipleRuleTips />
    </div>
  );
});
