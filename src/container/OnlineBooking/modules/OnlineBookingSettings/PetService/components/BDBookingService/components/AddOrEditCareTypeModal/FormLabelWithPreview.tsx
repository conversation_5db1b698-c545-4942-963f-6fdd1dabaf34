import { type FormFieldSharedProps, Text, Link } from '@moego/ui';
import { FormItemLabel } from '@moego/ui/dist/esm/components/Form/FormItemLabel';
import React, { memo } from 'react';

export interface FormLabelWithPreviewProps extends FormFieldSharedProps {
  onPreview: () => void;
  description: string;
}

export const FormLabelWithPreview = memo<FormLabelWithPreviewProps>((props) => {
  const { label, isRequired, description, onPreview } = props;

  return (
    <>
      <FormItemLabel isRequired={isRequired} label={label} />
      <div className="moe-flex moe-items-center">
        <Text variant="small" className=" moe-text-tertiary">
          {description}
        </Text>
        <Link variant="small" className="moe-ml-[2px] moe-text-tertiary moe-text-markup-small" onClick={onPreview}>
          Preview
        </Link>
      </div>
    </>
  );
});
