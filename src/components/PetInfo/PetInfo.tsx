import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Heading, cn } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useRef } from 'react';
import SvgMinorEditOutlinedSvg from '../../assets/svg/minor-edit-outlined.svg';
import SvgMinorTrashOutlinedSvg from '../../assets/svg/minor-trash-outlined.svg';
import { ApptTestIds } from '../../config/testIds/apptDrawer';
import { selectCurrentPermissions } from '../../store/business/role.selectors';
import { petMapBox } from '../../store/pet/pet.boxes';
import { getPetAvatarType } from '../../utils/BusinessUtil';
import { useSerialCallback } from '../../utils/hooks/useSerialCallback';
import { CompressedAvatar } from '../CompressedAvatar/CompressedAvatar';
import { Condition } from '../Condition';
import { CircleIcon } from '../Icon/CircleIcon';
import { SvgIcon } from '../Icon/Icon';
import { PetActiveInfo } from './PetActiveInfo';
import { PetAvatarPreview } from './PetAvatarPreview';
import { PetCodes } from './PetCodes';
import { useClientPet } from './hooks/useClientPets';
import { usePetDescription } from './hooks/usePetDescription';

export interface PetInfoProps {
  petId: number;
  className?: string;
  showVaccine?: boolean;
  showIncident?: boolean;
  showNotes?: boolean;
  startDate?: string;
  endDate?: string;
  petCodeTagsMax?: number;
  /** 相当于快捷preset，删除和编辑按钮不展示，但是不禁用跳转 */
  disabled?: boolean;
  /** 如果为空，不显示按钮 */
  onEdit?: (petId: string) => void;
  /** 如果为空，不显示按钮 */
  onDelete?: (petId: string) => void | Promise<void>;
  /** 点击路由到pet详情 */
  go2PetDetail?: (petId: string) => void;
  /** 点击路由到pet notes页面 */
  go2PetNotes?: (petId: string) => void;

  appointmentId?: number;
  serviceItemTypes?: ServiceItemType[];
}

export const PetInfo = memo<PetInfoProps>(function PetInfo(props) {
  const refDiv = useRef<HTMLDivElement>(null);
  const {
    petId,
    className,
    go2PetDetail,
    go2PetNotes,
    onEdit,
    onDelete,
    showVaccine,
    showIncident,
    showNotes,
    startDate,
    endDate,
    petCodeTagsMax,
    disabled,
    appointmentId,
    serviceItemTypes,
  } = props;
  const [pet, permissions] = useSelector(petMapBox.mustGetItem(petId), selectCurrentPermissions());
  const petDescription = usePetDescription(petId);
  const canViewClientProfile = permissions.has('viewIndividualClientProfile');
  const { petHasDeleted } = useClientPet(petId);

  const handleDelete = useSerialCallback(async (e) => {
    e.stopPropagation();
    await onDelete?.(petId.toString());
  });

  const handleEdit = useSerialCallback((e) => {
    e.stopPropagation();
    onEdit?.(petId.toString());
  });

  const handleToPetDetail = useSerialCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (petHasDeleted || !canViewClientProfile) {
      return;
    }
    go2PetDetail?.(petId.toString());
  });

  return (
    <div className={cn('pet-info-box moe-flex moe-items-center moe-gap-x-[16px] moe-select-none', className)}>
      <div onClick={handleToPetDetail}>
        <PetAvatarPreview petAvatar={pet.avatarPath}>
          <CompressedAvatar.Pet src={pet.avatarPath} type={getPetAvatarType(pet.petTypeId)} size="m" color="neutral" />
        </PetAvatarPreview>
      </div>
      <div className="moe-flex-1 moe-min-w-0 moe-flex moe-flex-col moe-gap-y-[4px]">
        <div className="moe-flex moe-items-center moe-gap-x-[8px] moe-gap-y-8px-50 moe-flex-wrap">
          <Heading
            size="5"
            className={cn(
              'moe-max-w-[208px] moe-truncate',
              petHasDeleted || !canViewClientProfile
                ? 'moe-text-disabled'
                : 'hover:!moe-text-brand moe-cursor-pointer moe-text-primary',
            )}
            onClick={handleToPetDetail}
          >
            {pet.petName}
          </Heading>
          <PetActiveInfo petId={petId} />
          <PetCodes
            showVaccine={showVaccine}
            showNotes={showNotes}
            startDate={startDate}
            endDate={endDate}
            showIncident={showIncident}
            petId={petId}
            go2PetNotes={go2PetNotes}
            onClick={(e) => {
              e.stopPropagation();
            }}
            petCodeTagsMax={petCodeTagsMax}
            appointmentId={appointmentId}
            serviceItemTypes={serviceItemTypes}
          />
        </div>
        <div className="moe-max-w-[368px] moe-text-[14px] moe-text-tertiary moe-leading-[18px]">{petDescription}</div>
      </div>
      <Condition if={!disabled && (onEdit || onDelete)}>
        <div
          className={cn(
            'moe-invisible moe-self-start group-hover:moe-visible moe-flex moe-flex-nowrap moe-gap-x-[16px]',
          )}
        >
          <div className="moe-w-0 moe-h-0" ref={refDiv} />
          <Condition if={onDelete}>
            <CircleIcon content="Delete" container={refDiv.current} loading={handleDelete.isBusy()}>
              <SvgIcon
                src={SvgMinorTrashOutlinedSvg}
                size={20}
                color="#202020"
                onClick={handleDelete}
                data-testid={ApptTestIds.ApptPetServiceDelete}
              />
            </CircleIcon>
          </Condition>
          <Condition if={onEdit}>
            <CircleIcon content="Edit" container={refDiv.current} loading={handleEdit.isBusy()}>
              <SvgIcon
                src={SvgMinorEditOutlinedSvg}
                size={20}
                color="#202020"
                onClick={handleEdit}
                data-testid={ApptTestIds.ApptPetServiceEdit}
              />
            </CircleIcon>
          </Condition>
        </div>
      </Condition>
    </div>
  );
});
