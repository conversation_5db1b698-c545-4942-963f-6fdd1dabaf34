import { type ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useMemo } from 'react';
import { useSelector } from 'amos';
import { selectAllCategoriesOfAllServices } from '../../../../../../../../store/service/category.selectors';
import { currentBusinessIdBox } from '../../../../../../../../store/business/business.boxes';
import { serviceMapBox } from '../../../../../../../../store/service/service.boxes';
import { selectBusinessCareTypeConfigList } from '../../../../../../../../store/onlineBooking/onlineBookingServices.selectors';
import { type ApplicableServices } from '@moego/api-web/moego/models/online_booking/v1/ob_customize_care_type_defs';

export interface ApplicableServicesAddonsParams {
  value?: ApplicableServices;
  careTypeId: string;
  serviceType: ServiceType;
  serviceItemType: ServiceItemType;
  onChange?: (value: ApplicableServices) => void;
}

export const useApplicableServicesAddons = (params: ApplicableServicesAddonsParams) => {
  const { value, careTypeId, serviceType, serviceItemType, onChange } = params;
  const { selectedServices = [], isAllServiceApplicable } = value || {};
  const type = `${serviceType}-${serviceItemType}`;
  const [businessId] = useSelector(currentBusinessIdBox);
  const [categoryServiceList, serviceMap, bookingCareTypeConfigList] = useSelector(
    selectAllCategoriesOfAllServices(businessId, true, [type]),
    serviceMapBox,
    selectBusinessCareTypeConfigList,
  );

  const isAddon = serviceType === ServiceType.ADDON;

  const options = categoryServiceList[type].map((item) => {
    const conflictingServices = bookingCareTypeConfigList
      .filter((item) => {
        return item.serviceItemType === serviceItemType && item.serviceType === serviceType && item.id !== careTypeId;
      })
      .flatMap((item) => item.applicableServices?.selectedServices ?? []);
    return {
      label: item.label,
      options: item.options.map((option) => ({
        label: option.label,
        value: String(option.value),
        isDisabled: conflictingServices.includes(String(option.value)),
      })),
    };
  });

  const selectorValue = useMemo(() => {
    if (isAllServiceApplicable) {
      return options.map((item) => item.options.map((option) => String(option.value))).flat();
    }
    return selectedServices;
  }, [options, isAllServiceApplicable, selectedServices]);

  const formattedValues = isAllServiceApplicable
    ? 'All selected'
    : selectedServices.length === 1
      ? serviceMap.mustGetItem(Number(selectedServices[0])).name
      : `${selectedServices.length} ${isAddon ? 'add-ons' : 'services'}`;

  const handleChange = (selected: string[]) => {
    onChange?.({ selectedServices: selected as string[], isAllServiceApplicable: false });
  };

  return {
    options,
    selectorValue,
    formattedValues,
    handleChange,
  };
};
