import { useDispatch, useSelector, useStore } from 'amos';
import { selectMainServiceInAppt } from '../../../../../store/selectors/appt';
import { serviceMapBox } from '../../../../../../../store/service/service.boxes';
import { getDefaultService } from '../../../../../../../components/ServiceApplicablePicker/utils/getDefaultService';
import { type PetServiceListWithMainService, type ApptPetIdsServiceList } from '../../../../../store/appt.types';
import {
  type PetIdsServiceList,
  type ServiceEntry,
} from '../../../../../../../components/ServiceApplicablePicker/types/serviceEntry';
import { useCallback } from 'react';
import { getApplicablePetService } from '../../../../../../../components/ServiceApplicablePicker/hooks/useApplicablePetServices';
import { currentBusinessIdBox } from '../../../../../../../store/business/business.boxes';
import { ServiceType } from '../../../../../../../store/service/category.boxes';
import { getSavedPriceList } from '../../../../../../../store/pet/petSavedPrice.actions';
import { useImperativeServicePriceDurationInfo } from '../../../../../../../components/ServiceApplicablePicker/hooks/useServiceInfo';
import { getPetDetailDateTypeFromDateType } from '../../../../../../../components/DateType/DateType.utils';

/**
 * 获取 Additional Service 列表
 */
export const useGetAdditionalServiceList = () => {
  const store = useStore();
  const dispatch = useDispatch();
  const [businessId] = useSelector(currentBusinessIdBox);

  /**
   * 需要从中补充个性化的信息
   */
  const getApplicablePetServiceListByPetIds = useCallback(
    async (currentPetIds: string[]) => {
      await Promise.all([
        getApplicablePetService(
          {
            onlyAvailable: false,
            petIds: currentPetIds,
            businessId: String(businessId),
            serviceType: ServiceType.Service,
            selectedServiceIds: [],
          },
          dispatch,
        ),
        getApplicablePetService(
          {
            onlyAvailable: false,
            petIds: currentPetIds,
            businessId: String(businessId),
            serviceType: ServiceType.Addon,
            selectedServiceIds: [],
          },
          dispatch,
        ),
        dispatch(currentPetIds.map((id) => getSavedPriceList(Number(id)))),
      ]);
    },
    [businessId, dispatch],
  );

  const getServicePriceDurationInfo = useImperativeServicePriceDurationInfo();

  return async (params: {
    petIds?: number[];
    // petId 和选择的 service 列表
    petIdsServiceList: ApptPetIdsServiceList;
    // 总的 appointmentId
    appointmentId: string;
  }) => {
    const { petIds, appointmentId, petIdsServiceList } = params;
    const mainService = store.select(selectMainServiceInAppt(appointmentId));
    const petServiceListWithMainService = petIdsServiceList
      .map((petItem) => {
        const { serviceList } = petItem;
        const petMainService = serviceList.find(
          ({ serviceItemType, serviceType }) =>
            serviceType === ServiceType.Service && serviceItemType === mainService.serviceItemType,
        );
        if (!petMainService) {
          return undefined;
        }
        const mainServiceRecord = store.select(serviceMapBox.getItem(+petMainService.serviceId));
        if (!mainServiceRecord?.additionalServiceRule) {
          return undefined;
        }
        return {
          petId: petItem.petId,
          serviceList,
          mainServiceInfo: mainServiceRecord,
          mainServiceDetail: petMainService,
        };
      })
      .filter(Boolean) as PetServiceListWithMainService[];

    const hasAdditionalRule = petServiceListWithMainService.some(
      (petItem) => petItem.mainServiceInfo?.additionalServiceRule?.enable,
    );
    if (hasAdditionalRule && petIds) {
      // 这里核心是为 getServicePriceDurationInfo 准备相关的 service 数据，填充 saved price / saved duration 的考虑
      await getApplicablePetServiceListByPetIds(petIds.map(String));
    }

    const petAdditionalServiceList = petServiceListWithMainService
      .map((petItem) => {
        const { mainServiceInfo, mainServiceDetail } = petItem;
        if (!mainServiceInfo.additionalServiceRule) return undefined;
        const { enable, applyRules } = mainServiceInfo.additionalServiceRule;
        // 只考虑 enable，先不考虑 applyRules
        if (!enable) {
          return undefined;
        }
        const additionalServiceList = applyRules
          .map((rule) => {
            const { serviceId, quantityPerDay, dateType } = rule;
            const service = store.select(serviceMapBox.getItem(+serviceId));
            if (!service) {
              return undefined;
            }
            const { name: serviceName, requireDedicatedStaff, serviceItemType } = service;
            return {
              ...getDefaultService({
                ...service.toJSON(),
                serviceType: service.type,
                // 如果是 addOn，需要走 main service 的 serviceItemType 来判断（这里有点混淆概念，待进一步澄清）
                serviceItemType: service.type === ServiceType.Addon ? mainServiceInfo.serviceItemType : serviceItemType,
                // 如果是 addOn，需要指定 associateId
                ...(service.type === ServiceType.Addon ? { associatedId: mainServiceDetail.id } : undefined),
                ...getServicePriceDurationInfo({ petId: +petItem.petId, serviceId: +serviceId }),
                serviceName,
                requireDedicatedStaff,
                quantityPerDay,
                dateType: getPetDetailDateTypeFromDateType(dateType),
              }),
              isAdditionalService: true,
            };
          })
          .filter(Boolean) as ServiceEntry[];

        return {
          petId: petItem.petId,
          serviceList: additionalServiceList,
        };
      })
      .filter(Boolean) as PetIdsServiceList;

    return {
      petServiceListWithMainService,
      petAdditionalServiceList,
    };
  };
};
