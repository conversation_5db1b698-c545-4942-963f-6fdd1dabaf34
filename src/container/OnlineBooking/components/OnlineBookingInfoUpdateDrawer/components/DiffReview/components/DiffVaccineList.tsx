import React from 'react';
import { type PetDetailType, type ReturnNodeAndValue } from '../../../type';
import { DiffVaccine } from './DiffVaccine';

export interface DiffVaccineListProps {
  newVal: PetDetailType;
  oldVal: PetDetailType;
  needUpdateVaccineIds?: number[];
  onChange?: (v: boolean, o: ReturnNodeAndValue, n: ReturnNodeAndValue) => void;
}

export const DiffVaccineList = (props: DiffVaccineListProps) => {
  const { newVal, oldVal, needUpdateVaccineIds, onChange } = props;
  const { vaccineList: newVaccineList } = newVal;
  const { vaccineList: oldVaccineList } = oldVal;

  const maxList = oldVaccineList.length >= newVaccineList.length ? oldVaccineList : newVaccineList;

  return (
    <>
      {maxList.map(({ vaccineBindingId }) => {
        const oldVaccine = oldVaccineList.find((item) => item.vaccineBindingId === vaccineBindingId);
        const newVaccine = newVaccineList.find((item) => item.vaccineBindingId === vaccineBindingId);

        return (
          <DiffVaccine
            key={vaccineBindingId}
            vaccineBindingId={vaccineBindingId}
            title={oldVaccine?.vaccineName || newVaccine?.vaccineName || '(Deleted)'}
            isError={needUpdateVaccineIds?.includes(vaccineBindingId)}
            oldVaccine={oldVaccine}
            newVaccine={newVaccine}
            onChange={(v, o, n) => {
              onChange?.(v, { value: { vaccineList: [o] }, node: null }, { value: { vaccineList: [n] }, node: null });
            }}
          />
        );
      })}
    </>
  );
};
