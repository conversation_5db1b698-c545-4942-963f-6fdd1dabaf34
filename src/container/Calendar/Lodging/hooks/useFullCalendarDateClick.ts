import { type DateClickArg } from '@fullcalendar/interaction';
import { useDispatch } from 'amos';
import dayjs from 'dayjs';
import { useCallback } from 'react';
import { apptDetailDrawerBox, calendarQuickAddApptVisible } from '../../../../store/calendarLatest/calendar.boxes';
import { getIsDefaultArea } from '../../../../store/calendarLatest/calendar_data.utils';
import { LodgingCalendarPlaceholderCardTypeMap } from '../../../../store/calendarLatest/lodgingCalendar.boxes';
import { DATE_FORMAT_EXCHANGE } from '../../../../utils/DateTimeUtil';
import { updateApptAllServiceDateWithOffset } from '../../../Appt/store/appt.actions';
import { selectMainServiceInAppt } from '../../../Appt/store/appt.selectors';
import { CreateApptId } from '../../../Appt/store/appt.types';
import { useLodgingPlaceHolderCard } from './useLodgingPlaceholderCard';
import { useLazySelectors } from '../../../../utils/unstable/createSelectAccessor';
import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { GrowthBookFeatureList } from '../../../../utils/growthBook/growthBook.config';

export const useFullCalendarDateClick = () => {
  const [quickAddApptVisible, apptDetailDrawer, mainService] = useLazySelectors(
    calendarQuickAddApptVisible,
    apptDetailDrawerBox,
    selectMainServiceInAppt,
  );
  const dispatch = useDispatch();
  const { addPlaceholderCard } = useLodgingPlaceHolderCard();
  const hideAddCardInLodgingView = useFeatureIsOn(GrowthBookFeatureList.EnableHideAddCardInLodgingView);

  return useCallback(
    (dateClickArg: DateClickArg) => {
      const { date, resource } = dateClickArg;
      const { id } = resource || {};
      const startDate = dayjs(date).hour(12);

      // when appointment detail drawer is open, do nothing
      if (apptDetailDrawer().visible) {
        return;
      }

      const renderPlaceholderAddCard = () => {
        if (hideAddCardInLodgingView) {
          return;
        }
        const isDefaultArea = getIsDefaultArea(id || '');
        if (isDefaultArea) {
          // 默认区域不支持 quick add
          return;
        }

        addPlaceholderCard(
          {
            resourceId: id!,
            start: startDate.toDate(),
            end: dayjs(startDate).add(1, 'day').toDate(),
          },
          LodgingCalendarPlaceholderCardTypeMap.Add,
        );
      };

      const updatePlaceholderNewAppointmentCard = () => {
        const { startDate: currentStartDate } = mainService();
        const nextStartDate = dayjs(dayjs(date).format(DATE_FORMAT_EXCHANGE));
        const startDateOffset = nextStartDate.diff(currentStartDate, 'day');

        dispatch(updateApptAllServiceDateWithOffset(CreateApptId, startDateOffset, id));
      };

      quickAddApptVisible() ? updatePlaceholderNewAppointmentCard() : renderPlaceholderAddCard();
    },
    [addPlaceholderCard, hideAddCardInLodgingView],
  );
};
