import { type EventDropArg } from '@fullcalendar/common';
import { useDispatch, useStore } from 'amos';
import dayjs from 'dayjs';
import { calendarQuickAddApptFields } from '../../../../../../store/calendarLatest/calendar.boxes';
import { type MoeEventInfo } from '../../../../../../store/calendarLatest/card.types';
import { isNormal } from '../../../../../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../utils/DateTimeUtil';
import { type SetGroomingOnlyStaffAndStartTimeParams } from '../../../../../Appt/store/appt.actions';
import {
  ApptServiceRecord,
  apptAddOnMapBox,
  apptInfoMapBox,
  apptServiceMapBox,
} from '../../../../../Appt/store/appt.boxes';
import { selectApptStartAndEndTime } from '../../../../../Appt/store/appt.selectors';
import { CreateApptId } from '../../../../../Appt/store/appt.types';
import { resolveEventNewSchedule } from '../useDateClickBD';
import { useEventChangeNewAppt } from './useEventChangeNewAppt';
import { useEventDropPreviewCard } from './useEventDropPreviewCard';

export interface EventDropNewCardProps extends SetGroomingOnlyStaffAndStartTimeParams {
  cardId: string;
}

// 一旦是 新卡片的拖动，就会 match 到此逻辑
// 新卡片可能是 preview 产生的新卡片
// 也可能是 create or exist bd appt，add service 时产生的新卡片
// 此时拖动新卡片，也会快捷同步数据
export const useEventDropNewCard = () => {
  const store = useStore();
  const dispatch = useDispatch();
  const changeNewApptEvent = useEventChangeNewAppt();
  const dropPreviewCard = useEventDropPreviewCard();

  return async (eventDropInfo: EventDropArg) => {
    const {
      newResource,
      event: { extendedProps, start },
    } = eventDropInfo;
    const newStart = dayjs(start);
    const newStaffId: number | undefined = newResource?.extendedProps?.staffId;
    const { ownId, staffId: originStaffId } = extendedProps as MoeEventInfo;
    const appointmentId = isNormal(extendedProps.appointmentId) ? String(extendedProps.appointmentId) : CreateApptId;

    const newSchedule = {
      startDate: newStart.format(DATE_FORMAT_EXCHANGE),
      startTime: newStart.getMinutes(),
      // placeholder 的情况 , 这里兜底原来的 staffId，是为了简化逻辑，同时保证正确性
      staffId: newStaffId || originStaffId,
    };

    const { clientId, allPetsStartAtSameTime } = store.select(calendarQuickAddApptFields);
    const { isMatched, task } = await dropPreviewCard(eventDropInfo, {
      appointmentId,
      allPetsStartAtSameTime,
      customerId: String(clientId),
    });
    if (isMatched) {
      await task();
    } else {
      const apptStartEndTime = store.select(selectApptStartAndEndTime(appointmentId));
      const { startDateTime, endDateTime } = apptStartEndTime;
      const isPlaceholderOwnerId = ownId === ApptServiceRecord.createOwnId(CreateApptId, CreateApptId);
      if (ownId && !isPlaceholderOwnerId && startDateTime && endDateTime) {
        // bd + grooming 的情况
        if (newStart.isBefore(dayjs(startDateTime), 'day') || newStart.isAfter(dayjs(endDateTime), 'day')) {
          // 禁止拖拽出主 service 日期范围
          return eventDropInfo.revert();
        }

        const service = store.select(apptServiceMapBox.mustGetItem(ownId));
        if (isNormal(service.serviceId)) {
          const newVal = resolveEventNewSchedule(newStart, newSchedule.staffId, service, apptStartEndTime);
          dispatch(apptServiceMapBox.mergeItem(ownId, newVal));
        } else {
          const addon = store.select(apptAddOnMapBox.mustGetItem(ownId));
          const newVal = resolveEventNewSchedule(newStart, newSchedule.staffId, addon, apptStartEndTime);
          dispatch(apptAddOnMapBox.mergeItem(ownId, newVal));
        }
      } else {
        changeNewApptEvent({ appointmentDate: newStart, staffId: newSchedule.staffId });
      }
    }
  };
};

/**
 * 此方法暂时未使用到
 * 这个是一个优化逻辑，后续可以考虑参考来实现
 * 例如在 edit service 的时候同时拖拽，更好的交互是不立即更新，而是 update data 到 drawer 中，由 drawer 二级弹窗内的 save 按钮来触发
 */
export const useEventDropExistAppt = () => {
  const store = useStore();
  const dropPreviewCard = useEventDropPreviewCard();

  return async (eventDropInfo: EventDropArg, appointmentId: string) => {
    const {
      appointment: { startAtSameTime },
      customer: {
        customerProfile: { id },
      },
    } = store.select(apptInfoMapBox.mustGetItem(appointmentId));
    const { isMatched, task } = await dropPreviewCard(eventDropInfo, {
      appointmentId,
      allPetsStartAtSameTime: startAtSameTime,
      customerId: id,
    });
    if (isMatched) {
      await task();
    }
  };
};
