import { useSelector } from 'amos';
import { Checkbox, Tooltip } from 'antd';
import React from 'react';
import { Button } from '../../../components/Button/Button';
import { ColorPicker } from '../../../components/ColorPicker/ColorPicker';
import { WithPricingEnableUpgrade } from '../../../components/Pricing/WithPricingComponents';
import { Full } from '../../../components/Style/Style';
import { CTTestIds } from '../../../config/testIds/createTicket';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { syncServiceTime } from '../hooks/ticketTimeSync';
import { useChangeTicket } from '../hooks/useChangeTicket';
import { useMultiPetsStartSameTime } from '../hooks/useMultiPetsStartSameTime';
import { useSmartSchedule } from '../hooks/useSmartSchedule';
import { useTicketComputed } from '../hooks/useTicketComputed';
import { useTicketFieldsEdit } from '../hooks/useTicketFieldsEdit';
import { useTicketStoreCtx } from '../hooks/useTicketForm';
import { TimePicker, DatePicker } from '@moego/ui';

export interface ApptDatesProps {}

const { SSBtn } = CTTestIds;

export function ApptDates(props: ApptDatesProps) {
  const { checkIfEligibleForSmartScheduling } = useChangeTicket();
  const { smartScheduleVisible } = useTicketComputed();
  const { handleSmartScheduling } = useSmartSchedule();
  const [ticketStore, updateTicketStore] = useTicketStoreCtx();
  const [business] = useSelector(selectCurrentBusiness);
  const eligibleForSmartScheduling = checkIfEligibleForSmartScheduling();
  const { colorCode, repeatModalProps, isSameTime, petsService } = ticketStore;
  const { switchAtTheSameTime } = useMultiPetsStartSameTime();
  const ticketFieldsEdit = useTicketFieldsEdit();

  const onChangeColorCode = (colorCode: string) => {
    updateTicketStore({
      colorCode,
      ticketChanged: true,
    });
    ticketFieldsEdit.add('colorCode');
  };

  return (
    <div className="ticket-date-color-container">
      <div className="!moe-mr-[8px] !moe-whitespace-nowrap">Date and time</div>
      <DatePicker
        isClearable={false}
        format={business.dateFormat}
        value={ticketStore.date}
        onChange={(d) => {
          updateTicketStore({
            date: d!,
            ticketChanged: true,
            repeatModalProps: { ...repeatModalProps, startOnDate: d! },
          });
          ticketFieldsEdit.add('apptStartDate');
        }}
      />
      <TimePicker
        className="ml-12"
        isClearable={false}
        value={ticketStore.time}
        format={business.timeFormat()}
        minuteStep={5}
        onChange={(time) => {
          const newPetsService = syncServiceTime(petsService, { isSameTime, time });
          updateTicketStore({
            time,
            ticketChanged: true,
            petsService: newPetsService,
          });
          ticketFieldsEdit.add('apptStartTime');
        }}
      />
      {petsService.length >= 2 && (
        <Checkbox
          className="start-same-time-checkbox !moe-whitespace-nowrap"
          checked={isSameTime}
          onChange={(e) => switchAtTheSameTime(e.target.checked)}
        >
          Multiple pets start at the same time
        </Checkbox>
      )}
      <Full />
      <div className="ticket-color-wrapper">
        {smartScheduleVisible && (
          <WithPricingEnableUpgrade permission="ss">
            {(onCapture) => (
              <Tooltip title={eligibleForSmartScheduling.reason} placement="top">
                <Button
                  buttonRadius="circle"
                  btnType="primary"
                  className="smart-scheduling-button"
                  disabled={eligibleForSmartScheduling.disabled}
                  onClick={onCapture || handleSmartScheduling}
                  data-testid={SSBtn}
                >
                  Smart scheduling
                </Button>
              </Tooltip>
            )}
          </WithPricingEnableUpgrade>
        )}

        <div className="flex-row align-items-center">
          <span className="color-wrapper-text !moe-mr-[8px] !moe-text-sm !moe-text-[#333] !moe-whitespace-nowrap">
            Color
          </span>
          <ColorPicker
            width="24px"
            height="24px"
            value={colorCode}
            onChange={onChangeColorCode}
            popPosition="bottom-left"
          />
        </div>
      </div>
    </div>
  );
}
