import { ServiceBriefView } from '@moego/api-web/moego/models/offering/v1/service_models';
import { BoardingServiceDetailModel } from '@moego/api-web/moego/models/online_booking/v1/boarding_service_detail_models';
import { DaycareServiceDetailModel } from '@moego/api-web/moego/models/online_booking/v1/daycare_service_detail_models';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import { useSelector } from 'amos';
import { computedDiffNights } from '../../../../../../../utils/utils';
import { T_MINUTE } from 'monofile-utilities/lib/consts';
import { getDiffDateTextByUnit } from '../../../../../../Appt/utils/getDiffDateTextByUnit';
import { hasDaycareSpecificDates } from '../Evaluation/Evaluation.utils';

export const useServiceDateTimeText = (
  service: ServiceBriefView,
  serviceDetail: BoardingServiceDetailModel | DaycareServiceDetailModel,
) => {
  const [business] = useSelector(selectCurrentBusiness);

  const getBoardingDateTimeText = (detail: BoardingServiceDetailModel) => {
    const subNights = computedDiffNights(detail.startDate, detail.endDate);
    if (subNights) {
      const start = business.formatFixedDateTime(detail.startDate, detail.startTime * T_MINUTE);
      const end = business.formatFixedDateTime(detail.endDate, detail.endTime * T_MINUTE);
      return `${start} - ${end} (${getDiffDateTextByUnit(subNights, service.priceUnit)})`;
    }
    return business.formatFixedDateTime(detail.startDate, detail.startTime * T_MINUTE);
  };

  const getDaycareDateTimeText = (detail: DaycareServiceDetailModel) => {
    const dates = detail.specificDates.map((date) => business.formatDate(date));
    return `${dates.join(', ')} ${business.formatTime(detail.startTime)} - ${business.formatTime(detail.endTime)}`;
  };

  return hasDaycareSpecificDates(serviceDetail)
    ? getDaycareDateTimeText(serviceDetail)
    : getBoardingDateTimeText(serviceDetail);
};
