import React, { type SetStateAction, memo, useState } from 'react';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { type CustomerPetsUpdateParams, type NewAddressItem } from '../../../type';
import { getNewAddressItemRenderNode } from '../DiffReview.utils';
import { DiffItem } from './DiffItem';

export interface DiffNewAddressProps {
  initialValue: NewAddressItem[];
  setVisible: (v: SetStateAction<boolean>) => void;
  onChange?: (v: CustomerPetsUpdateParams) => void;
}

export const DiffNewAddress = memo<DiffNewAddressProps>(({ initialValue, setVisible, onChange }) => {
  const [addressMap, setAddressMap] = useState<{
    [key in number]: boolean;
  }>({});

  const handleNewAddressChange = useLatestCallback((_isNew: boolean, _oldNode, newNode) => {
    setVisible(true);
    const addressItem = newNode?.value as NewAddressItem;
    const addressId = addressItem.customerAddressId;
    setAddressMap((prevVal) => {
      const newVal = {
        ...prevVal,
        [addressId]: !addressMap?.[addressId], // toggle selected state
      };
      onChange?.({
        newAddresses: initialValue?.filter(({ customerAddressId }) => !!newVal?.[customerAddressId]),
      });
      return newVal;
    });
  });

  return (
    <>
      {initialValue
        ?.filter(({ customerAddressId }) => !!customerAddressId)
        ?.map((address) => {
          return (
            <DiffItem
              key={address.customerAddressId}
              title={'New Address'}
              newNode={getNewAddressItemRenderNode(address, true)}
              oldNode={getNewAddressItemRenderNode(address, false)}
              onChange={handleNewAddressChange}
              ignoreOldNode={true}
            />
          );
        })}
    </>
  );
});
