import { MinorRefreshOutlined, MinorSettingOutlined } from '@moego/icons-react';
import { Button as Moe<PERSON><PERSON><PERSON><PERSON>, cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { Badge, Popover } from 'antd';
import React, { memo, useCallback, useMemo, useRef } from 'react';
import { useHistory } from 'react-router-dom';
import SvgIconWarningCircleSvg from '../../../../../assets/svg/icon-warning-circle.svg';
import { Button } from '../../../../../components/Button/Button';
import { SvgIcon } from '../../../../../components/Icon/Icon';
import { PATH_ONLINE_BOOKING_NEW_SETTINGS } from '../../../../../router/paths';
import { updateMetadata } from '../../../../../store/metadata/metadata.actions';
import { META_DATA_KEY_LIST } from '../../../../../store/metadata/metadata.config';
import { useMetaDataOnce } from '../../../../../store/metadata/metadata.hooks';
import { updateOnlineBookingPreference } from '../../../../../store/onlineBooking/actions/private/onlineBookingPreference.actions';
import { selectOnlineBookingPreference } from '../../../../../store/onlineBooking/onlineBookingPreference.selectors';
import { type EnumKeys } from '../../../../../store/utils/createEnum';
import { c_warning } from '../../../../../style/_variables';
import { useBool } from '../../../../../utils/hooks/useBool';
import { type GroomingAutomationOptions } from '../AutomationDrawer/Automation.options';
import { AutomationDrawer } from '../AutomationDrawer/AutomationDrawer';
import { AutomationUpdatePopoverStyle, NavAction } from './OnlineBookingActionBar.style';
import { WaitlistEntry } from '../../../../Overview/components/WaitlistEntry';

export const OnlineBookingActionBar = memo(() => {
  const dispatch = useDispatch();
  const history = useHistory();

  const [preference] = useSelector(selectOnlineBookingPreference);
  const { data: automationTips, loading: automationTipsLoading } = useMetaDataOnce(
    META_DATA_KEY_LIST.OnlineBookingAutomationTips,
  );
  const workFlowDrawerVisible = useBool();

  const showAutomationUpdatePopover = useMemo(
    () => preference.autoAccept && !Number(automationTips) && !automationTipsLoading,
    [preference, automationTips, automationTipsLoading],
  );

  const handleAutomationSetting = useCallback(async (newVal: EnumKeys<typeof GroomingAutomationOptions>) => {
    await dispatch([
      updateMetadata(META_DATA_KEY_LIST.OnlineBookingAutomationTips, 1),
      updateOnlineBookingPreference({
        requestSubmittedAutoType: newVal,
      }),
    ]);
  }, []);

  const rootRef = useRef<HTMLDivElement>(null);

  const automationEntry = (
    <MoegoButton
      variant="secondary"
      onPress={() => {
        if (showAutomationUpdatePopover) {
          return;
        }
        workFlowDrawerVisible.open();
      }}
      align="start"
      icon={<MinorRefreshOutlined />}
    >
      Automation
    </MoegoButton>
  );

  return (
    <>
      <div ref={rootRef} className={cn(['moe-flex moe-flex-row moe-items-center moe-gap-[16px]'])}>
        <WaitlistEntry overrideFilters={{ isFromOb: true }} />
        {showAutomationUpdatePopover ? (
          <>
            <div className="moe-w-[100vw] moe-h-[100vw] moe-fixed moe-left-0 moe-top-0 moe-bg-[rgba(0,0,0,0.4)] moe-z-[10]" />
            <Popover
              visible
              placement="bottomRight"
              className="!moe-z-[20] !moe-bg-white"
              overlayClassName="automation-update-popover"
              content={
                <section>
                  <div className="moe-flex moe-items-center">
                    <SvgIcon src={SvgIconWarningCircleSvg} color={c_warning} size={20} />
                    <span className="moe-text-[16px] moe-font-bold moe-leading-[20px] moe-text-[#333] moe-ml-[8px]">
                      Action needed
                    </span>
                  </div>
                  <p className="moe-mt-[16px] moe-text-[14px] moe-font-medium moe-leading-[18px] moe-text-[#333]">
                    {`Clients can now send profile updates through Online Booking. Since you're auto-accepting booking
                    requests, would you like to enable auto-acceptance for profile changes as well?`}
                  </p>
                  <Button
                    buttonRadius="circle"
                    size="sm"
                    hoverAnimation={false}
                    className="!moe-mt-[28px] !moe-w-full !moe-bg-[#faad14] !moe-border-[#faad14] !moe-font-bold"
                    onClick={() => handleAutomationSetting('auto_accept_request')}
                  >
                    Auto-accept profile changes
                  </Button>
                  <Button
                    btnType="link"
                    hoverAnimation={false}
                    className="!moe-mt-[14px] !moe-w-full !moe-no-underline !moe-text-[#faad14] !moe-justify-center !moe-text-[12px] !moe-font-normal !moe-leading-[16px]"
                    onClick={() => handleAutomationSetting('no_automation')}
                  >
                    Manually review requests with profile updates
                  </Button>
                </section>
              }
            >
              <div className="moe-rounded-[8px] moe-p-[12px]">
                <Badge dot>{automationEntry}</Badge>
              </div>
            </Popover>
          </>
        ) : (
          automationEntry
        )}
        <NavAction>
          <MoegoButton
            variant="secondary"
            onPress={() => {
              history.push(PATH_ONLINE_BOOKING_NEW_SETTINGS.build());
            }}
            align="start"
            icon={<MinorSettingOutlined />}
          >
            Settings
          </MoegoButton>
        </NavAction>
      </div>
      <AutomationUpdatePopoverStyle />
      <AutomationDrawer isOpen={workFlowDrawerVisible.value} onClose={workFlowDrawerVisible.close} />
    </>
  );
});
