import { Button } from '@moego/ui';
import React, { useState } from 'react';
import { Card } from '../../../Report/components/Card';

/**
 * simon 练习项目
 * @returns
 */
export const Simon: React.FC = () => {
  const [value, setValue] = useState(0);

  return (
    <Card
      title={<span>Simon 👋👋👋</span>}
      className="moe-flex moe-justify-center moe-items-center moe-flex-col moe-gap-y-4"
    >
      <div>Hello Counter!</div>
      <div className="moe-flex moe-justify-center moe-items-center moe-text-3xl">{value}</div>
      <div className="moe-flex moe-justify-center moe-items-center moe-gap-x-4">
        <Button
          isDisabled={value <= 0}
          onPress={() => {
            setValue(value - 1);
          }}
        >
          -
        </Button>
        <Button
          onPress={() => {
            setValue(value + 1);
          }}
        >
          +
        </Button>
      </div>
    </Card>
  );
};
