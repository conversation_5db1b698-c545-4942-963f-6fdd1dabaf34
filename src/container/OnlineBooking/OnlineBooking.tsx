/*
 * @since 2020-12-10 15:10:47
 * <AUTHOR> <<EMAIL>>
 */

import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect } from 'react';
import { Redirect, Switch, useRouteMatch } from 'react-router';
import { SwitchBusinessDropdown } from '../../components/Business/SwitchBusinessDropdown';
import { GuardRoute } from '../../components/GuardRoute/GuardRoute';
import { WithPermission } from '../../components/GuardRoute/WithPermission';
import { type LocationControlSceneKeys } from '../../components/WithFeature/useNewAccountStructure';
import { LayoutContainer } from '../../layout/LayoutContainer';
import { PageTitle } from '../../layout/components/PageTitle';
import {
  PATH_ONLINE_BOOKING_ABANDON_LIST,
  PATH_ONLINE_BOOKING_NEW_SETTINGS,
  PATH_ONLINE_BOOKING_REQUESTS,
  PATH_ONLINE_BOOKING_SETTINGS,
} from '../../router/paths';
import { currentBusinessIdBox } from '../../store/business/business.boxes';
import { useInitBusinessApplicableEvaluation } from '../../store/evaluation/evaluation.hooks';
import { getOnlineBookingPreference } from '../../store/onlineBooking/actions/private/onlineBookingPreference.actions';
import { selectOnlineBookingLatestPreference } from '../../store/onlineBooking/onlineBookingPreference.selectors';
import { getPetHairLengthList } from '../../store/pet/petHairLength.actions';
import { OnlineBookingAbandonList } from './modules/OnlineBookingAbandonList/OnlineBookingAbandonList';
import { OnlineBookingActionBar } from './modules/OnlineBookingIndex/OnlineBookingActionBar/OnlineBookingActionBar';
import { OnlineBookingNavTab } from './modules/OnlineBookingIndex/OnlineBookingNavTab';
import {
  OnlineBookingNewUser,
  OnlineBookingNewUserSetup,
} from './modules/OnlineBookingIndex/OnlineBookingNewUser/OnlineBookingNewUser';
import { OnlineBookingRequests } from './modules/OnlineBookingRequests/OnlineBookingRequests';
import { OnlineBookingSettingsEntry } from './modules/OnlineBookingSettings/OnlineBookingSettingsEntry';

export interface OnlineBookingProps {
  className?: string;
}

const routesMap: Record<
  string,
  {
    path: string;
    scene: LocationControlSceneKeys;
  }
> = {
  bookingRequest: {
    path: PATH_ONLINE_BOOKING_REQUESTS.path,
    // scene: 'accessBookingRequestAndWaitingList',
    scene: 'working',
  },
  abandonList: {
    path: PATH_ONLINE_BOOKING_ABANDON_LIST.path,
    // scene: 'accessAbandonBookingRequest',
    scene: 'working',
  },
  bookingSetting: {
    path: PATH_ONLINE_BOOKING_SETTINGS.path,
    // scene: 'onlineBookingSettings',
    scene: 'working',
  },
};

export const OnlineBooking = memo<OnlineBookingProps>(() => {
  const dispatch = useDispatch();
  const isNewBookSetting = useRouteMatch(PATH_ONLINE_BOOKING_NEW_SETTINGS.path);
  const [{ isNew }] = useSelector(selectOnlineBookingLatestPreference());
  const [businessId] = useSelector(currentBusinessIdBox);
  useEffect(() => {
    dispatch(getOnlineBookingPreference());
    dispatch(getPetHairLengthList());
  }, [businessId]);
  useInitBusinessApplicableEvaluation(true);

  if (isNewBookSetting) {
    return <OnlineBookingSettingsEntry />;
  }

  return (
    <LayoutContainer
      className="online-booking-layout moe-bg-white moe-pb-0"
      scroll
      header={
        <PageTitle
          className="moe-mb-[30px]"
          title={
            <div className="moe-flex moe-items-center">
              <div className="moe-h2 moe-font-manrope">Online booking</div>
              <SwitchBusinessDropdown scene={'working'} />
            </div>
          }
          right={
            <WithPermission permissions="canAccessOBAutomationAndConfiguration">
              <OnlineBookingActionBar />
            </WithPermission>
          }
        />
      }
      key={businessId}
    >
      <WithPermission permissions="canAccessOBAutomationAndConfiguration">
        {isNew && <OnlineBookingNewUserSetup />}
      </WithPermission>

      <OnlineBookingNavTab className="!moe-relative"></OnlineBookingNavTab>
      <Switch>
        <GuardRoute
          authType="business"
          path={routesMap.bookingRequest.path}
          component={isNew ? OnlineBookingNewUser : OnlineBookingRequests}
          permissions="viewOnlineBooking"
        />
        <GuardRoute
          authType="business"
          permissions="canAccessAbandonBookings"
          path={routesMap.abandonList.path}
          component={isNew ? OnlineBookingNewUser : OnlineBookingAbandonList}
        />
        <Redirect to={PATH_ONLINE_BOOKING_REQUESTS.build()} />
      </Switch>
    </LayoutContainer>
  );
});
