import React from 'react';
import { type DepositRule } from '@moego/bff-openapi/clients/client.order';
import { money2Number, decimal2Number } from '../../../../../../utils/utils';
import { type BusinessRecord } from '../../../../../../store/business/business.boxes';

interface DepositDisplayProps {
  depositConfig: DepositRule['depositConfig'] | null;
  business: BusinessRecord;
}

export const DepositDisplay = ({ depositConfig, business }: DepositDisplayProps) => {
  if (!depositConfig) {
    return <></>;
  }

  const config = Array.isArray(depositConfig) ? depositConfig : [depositConfig];
  const text = config
    .map((item) =>
      item.value
        ? item.case === 'depositByAmount'
          ? business.formatAmount(money2Number(item.value))
          : `${decimal2Number(item.value)}%`
        : '/',
    )
    .join(' ');

  return <>{text}</>;
};
