import { Heading, Text } from '@moego/ui';
import { useSelector } from 'amos';
import React, { useMemo } from 'react';
import ImageCareContractLogoPng from '../../../../assets/image/care-contract-logo.png';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { type ActivityPrintCardInfo } from '../../../../store/printCard/activityCard/activityCard.actions';
import { activityPrintCardSettingsBox } from '../../../../store/printCard/activityCard/activityCard.boxes';
import { Condition } from '../../../Condition';
import { ActivityAddOns } from './ActivityAddOns';
import { ActivityFeeding } from './ActivityFeeding';
import { ActivityMedication } from './ActivityMedication';

export const ActivityCardContent = (props: { data: ActivityPrintCardInfo[]; date: string }) => {
  const { data, date } = props;
  return (
    <div className="moe-flex moe-w-full moe-flex-col moe-gap-y-m">
      {data.map((card, index) => (
        <ActivityCard key={index} data={card} date={date} />
      ))}
    </div>
  );
};

const ActivityCard = (props: { data: ActivityPrintCardInfo; date: string }) => {
  const [settings] = useSelector(activityPrintCardSettingsBox);
  const {
    data: { feedingInstructions, medicationInstructions, addOns, lodgingTypes, lodgingUnits },
    date,
  } = props;
  const filteredFeedingInstructions = useMemo(
    () => feedingInstructions.filter((item) => settings.type.feedingItems.includes(String(item.time))),
    [feedingInstructions, settings.type.feedingItems],
  );
  const filteredAddOnsInstructions = useMemo(
    () => addOns.filter((item) => settings.type.addOnItems.includes(String(item.serviceId))),
    [addOns, settings.type.addOnItems],
  );

  const filteredMedicationInstructions = useMemo(() => {
    return medicationInstructions.filter((item) => settings.type.medicationItems?.includes(String(item.time)));
  }, [medicationInstructions, settings.type.medicationItems]);

  return (
    <div className="moe-w-full moe-min-h-[792px] moe-bg-white moe-relative moe-p-m moe-pt-s moe-shadow-elevated print:moe-shadow-none">
      <Header date={date} />
      <Condition if={settings.type.feeding}>
        <ActivityFeeding data={filteredFeedingInstructions} lodgingTypes={lodgingTypes} lodgingUnits={lodgingUnits} />
      </Condition>
      <Condition if={settings.type.medication}>
        <ActivityMedication
          data={filteredMedicationInstructions}
          lodgingTypes={lodgingTypes}
          lodgingUnits={lodgingUnits}
          className="moe-mt-m"
        />
      </Condition>
      <Condition if={settings.type.addOns}>
        <ActivityAddOns
          data={filteredAddOnsInstructions}
          lodgingTypes={lodgingTypes}
          lodgingUnits={lodgingUnits}
          className="moe-mt-m"
        />
      </Condition>
    </div>
  );
};

const Header = (props: { date: string }) => {
  const [business, settings] = useSelector(selectCurrentBusiness, activityPrintCardSettingsBox);
  const { date } = props;
  const title = [
    settings.type.feeding ? 'Feeding' : '',
    settings.type.medication ? 'Medication' : '',
    settings.type.addOns ? 'Add-ons' : '',
  ]
    .filter(Boolean)
    .join(', ');

  return (
    <div className="moe-relative moe-mb-8px-150">
      <img src={ImageCareContractLogoPng} className="moe-absolute moe-right-0 moe-top-0 moe-h-[16px]" />
      <Text variant="small" className="moe-text-center">
        {business.businessName}
      </Text>
      <Heading size="5" className="moe-text-center">
        Daily activity check-off list
      </Heading>
      <div className="moe-text-center moe-text-[12px] moe-font-normal">
        <span>{date}</span>
        <Condition if={title}>
          <span className="moe-ml-s">{title}</span>
        </Condition>
      </div>
    </div>
  );
};
