import { useSelector } from 'amos';
import { Select } from 'antd';
import classNames from 'classnames';
import { type Dayjs } from 'dayjs';
import React, { memo, useEffect, useState } from 'react';
import { http } from '../../../../../middleware/api';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { createEnum } from '../../../../../store/utils/createEnum';
import { isNormal } from '../../../../../store/utils/identifier';
import { DATE_FORMAT_EXCHANGE } from '../../../../../utils/DateTimeUtil';
import { useCancelableCallback } from '../../../../../utils/hooks/useCancelableCallback';
import { CalendarDataSummaryView } from './CalendarDataSummary.style';
import { GroomingReportActionName } from '../../../../../utils/reportData/grooming/grooming';
import { reportData as reportDataGrooming } from '../../../../../utils/tracker';

const ReportOptions = createEnum({
  day: [0, 'Day'],
  week: [1, 'Week'],
  month: [2, 'Month'],
  year: [3, 'Year'],
});

const { Option } = Select;

interface IReportData {
  appts: number;
  pets: number;
  finishedAppts: number;
  earnedRev: number;
  expectedRev: number;
}

interface IMemoReportData {
  /**
   * startDate-endDate
   */
  [propName: string]: IReportData;
}

export interface CalendarDataSummaryProps {
  currentDate: Dayjs;
  reportClassName?: string;
}

export const CalendarDataSummary = memo<CalendarDataSummaryProps>(({ currentDate, reportClassName }) => {
  const [business] = useSelector(selectCurrentBusiness);
  const [range, setRange] = useState(ReportOptions.week);
  const [reportData, setReportData] = useState({
    appts: 0,
    pets: 0,
    finishedAppts: 0,
    earnedRev: 0,
    expectedRev: 0,
  } as IReportData);
  const [memoData, setMemoData] = useState({} as IMemoReportData);

  useEffect(() => {
    if (isNormal(business.id)) {
      fetchData();
    }
  }, [currentDate, range, business.id]);

  const fetchData = useCancelableCallback(async (signal) => {
    try {
      const startDate = currentDate.startOf(ReportOptions.keys[range]).format(DATE_FORMAT_EXCHANGE);
      const endDate = currentDate.endOf(ReportOptions.keys[range]).format(DATE_FORMAT_EXCHANGE);
      const memoKey = `${business.id}-${startDate}-${endDate}`;
      if (typeof memoData[memoKey]?.appts === 'number') {
        setReportData({ ...memoData[memoKey] });
        return;
      }
      const res = await http.open(
        'GET/business/report/mobile/summary',
        {
          startDate,
          endDate,
          businessIds: [business.id],
        },
        { signal },
      );
      if (res.code === 200) {
        const { data } = res;
        const report = {
          appts: data.totalAppts,
          pets: data.totalPets,
          finishedAppts: data.appts?.finished ?? 0,
          earnedRev: data.earnedRevenue,
          expectedRev: data.expectedRevenue,
        };
        setMemoData({
          ...memoData,
          [memoKey]: report,
        });
        setReportData(report);
      }
    } catch (error) {
      console.error(error);
    }
  });

  return (
    <CalendarDataSummaryView>
      <div className="summary-header">
        <span className="title">Calendar report</span>
        <Select
          defaultValue={range}
          style={{ width: 90, flex: 'none' }}
          bordered={false}
          onChange={(e) => {
            setRange(e);
            reportDataGrooming(GroomingReportActionName.CalendarDataSummaryRangeChange, {
              range: ReportOptions.mapLabels[e],
            });
          }}
        >
          {ReportOptions.values.map((v, index) => (
            <Option value={v} key={index}>
              {ReportOptions.mapLabels[v]}
            </Option>
          ))}
        </Select>
      </div>
      <div className={classNames('report-list', reportClassName)}>
        <div className="report-row">
          <label>Total appts:</label>
          <span>{reportData.appts}</span>
        </div>
        <div className="report-row">
          <label>Total pets:</label>
          <span>{reportData.pets}</span>
        </div>
        <div className="report-row">
          <label>Finished appts:</label>
          <span>{reportData.finishedAppts}</span>
        </div>
        <div className="report-row">
          <label>Earned Rev:</label>
          <span>{`${business.currencySymbol}${(reportData.earnedRev ?? 0).toFixed(2)}`}</span>
        </div>
        <div className="report-row">
          <label>Expected Rev:</label>
          <span>{`${business.currencySymbol}${(reportData.expectedRev ?? 0).toFixed(2)}`}</span>
        </div>
      </div>
    </CalendarDataSummaryView>
  );
});
