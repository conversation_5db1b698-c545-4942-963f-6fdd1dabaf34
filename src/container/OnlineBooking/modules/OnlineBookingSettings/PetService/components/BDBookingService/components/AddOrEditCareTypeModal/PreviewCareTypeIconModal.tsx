import React, { type FC } from 'react';
import { Modal, cn } from '@moego/ui';
import ImageSmartTipPreviewMobilePng from '../../../../../../../../../assets/image/smart-tip-preview-mobile.png';
import { useModal } from '../../../../../../../../../components/Modal/useModal';
import { CareTypeIconOptions } from './AddOrEditCareTypeModal.config';
import { MinorChevronDownOutlined, MinorChevronRightOutlined } from '@moego/icons-react';
import { type PreviewCareTypeIcon } from './types';
import { useSelector } from 'amos';
import { selectOnlineBookingLatestPreference } from '../../../../../../../../../store/onlineBooking/onlineBookingPreference.selectors';

export interface PreviewCareTypeIconModalProps {
  onClose: () => void;
  previewCareTypeIconList: PreviewCareTypeIcon[];
}

const PreviewCareTypeIconModal: FC<PreviewCareTypeIconModalProps> = (props) => {
  const { onClose, previewCareTypeIconList } = props;
  const [preference] = useSelector(selectOnlineBookingLatestPreference);

  return (
    <Modal
      isOpen
      title="Preview"
      size="m"
      onClose={onClose}
      classNames={{
        body: 'moe-p-0',
      }}
      showCancelButton={false}
      confirmText="Close"
    >
      <div className="moe-h-[460px] moe-flex moe-justify-center moe-p-m moe-bg-neutral-sunken-light moe-overflow-hidden moe-font-[ProximaNova]">
        <div
          className="moe-w-[375px] moe-h-[667px] moe-py-[80px] moe-px-l moe moe-bg-cover moe-bg-no-repeat"
          style={{
            backgroundImage: `url(${ImageSmartTipPreviewMobilePng})`,
          }}
        >
          <div className="moe-h-[360px] moe-overflow-y-auto">
            <p className="moe-text-[24px] moe-text-primary moe-font-bold">Our offerings</p>
            <div className="moe-flex moe-gap-[12px] moe-flex-wrap moe-mt-s">
              {previewCareTypeIconList.map(({ id, name, icon }, index) =>
                icon ? (
                  <div
                    key={id}
                    className={cn(
                      'moe-flex moe-justify-between moe-items-center moe-gap-x-xs moe-px-s moe-py-8px-100 moe-text-primary moe-border moe-border-divider moe-rounded-full',
                      {
                        'moe-text-white': !index,
                      },
                    )}
                    style={!index ? { backgroundColor: preference.themeColor } : undefined}
                  >
                    {CareTypeIconOptions.mapLabels[icon].icon}
                    <p className="moe-text-[16px] moe-font-normal">{name}</p>
                  </div>
                ) : null,
              )}
            </div>
            <div className="moe-flex moe-justify-between moe-items-center moe-px-s moe-py-xs moe-mt-s moe-border moe-border-divider moe-rounded-m">
              <p className="moe-text-[18px] moe-text-primary moe-font-bold">Full service</p>
              <MinorChevronDownOutlined className="!moe-text-[24px]" />
            </div>
            <div className="moe-flex moe-justify-between moe-items-center moe-px-s moe-py-xs moe-mt-s moe-border moe-border-divider moe-rounded-m">
              <p className="moe-text-[18px] moe-text-primary moe-font-bold">Grooming only</p>
              <MinorChevronRightOutlined className="!moe-text-[24px]" />
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export const usePreviewCareTypeIconModal = () => useModal(PreviewCareTypeIconModal);
