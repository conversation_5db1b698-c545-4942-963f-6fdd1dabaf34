/*
 * @Author: <PERSON>
 * @Date: 2022-06-20 10:32:53
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2022-06-20 15:09:57
 */

import { ExclamationCircleOutlined } from '@ant-design/icons';
import { type Reader, ReaderType } from '@moego/finance-terminal';
import { useDispatch, useSelector } from 'amos';
import { Empty } from 'antd';
import React, { memo, useEffect } from 'react';
import ImagePaymentSmartReaderPng from '../../../assets/image/payment-smart-reader.png';
import { Button } from '../../../components/Button/Button';
import { type ModalRequiredProps, modalApi } from '../../../components/Modal/Modal';
import { toastApi } from '../../../components/Toast/Toast';
import { deleteStripeSmartReaderById } from '../../../store/stripe/stripeTerminal.actions';
import { c_text_secondary_color } from '../../../style/_variables';
import { useBool } from '../../../utils/hooks/useBool';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { getPurchaseHardwareLink } from '../../CardProcessing/CardProcessing.utils';
import { useTerminalReaders } from '../../PaymentFlow/RightColumnV2/hooks/smart-reader/useTerminalReader';
import { StripeAddSmartReaderModal } from './StripeAddSmartReaderModal';
import { StripeSmartReaderListModalView } from './StripeSmartReaderListModal.style';
import { selectCurrentStaff } from '../../../store/staff/staff.selectors';

export const URL_STRIPE_TERMINAL_SHOP = 'https://dashboard.stripe.com/terminal/shop';

export interface StripeSmartReaderListModalProps extends ModalRequiredProps {
  className?: string;
  locationId: string;
}

export const StripeSmartReaderListModal = memo<StripeSmartReaderListModalProps>(
  ({ className, visible, locationId, onClose }) => {
    const [staff] = useSelector(selectCurrentStaff());
    const { discovered: readerList, isDiscovering, discoverReaders } = useTerminalReaders(ReaderType.Smart);

    const addTerminalVisible = useBool();
    const dispatch = useDispatch();

    useEffect(() => {
      if (visible) {
        discoverReaders();
      }
    }, [visible]);

    const handleDeleteReader = useSerialCallback(async (reader: Reader) => {
      return new Promise((resolve, reject) => {
        modalApi.confirm({
          type: 'success',
          icon: <ExclamationCircleOutlined style={{ color: '#d0021b' }} />,
          okType: 'danger',
          content: (
            <div>
              <p>The smart reader will be deleted</p>
              <p>SN: {reader.SN}</p>
              <p>name: {reader.name}</p>
            </div>
          ),
          okText: 'Confirm',
          onOk: async () => {
            try {
              await dispatch(deleteStripeSmartReaderById(reader.id));
              toastApi.success('delete success');
              resolve('success');
            } catch (error) {
              reject();
            }
          },
        });
      });
    });

    return (
      <StripeSmartReaderListModalView
        width="600px"
        className={className}
        title="Smart readers"
        visible={visible}
        onClose={onClose}
        loading={isDiscovering}
      >
        {addTerminalVisible.value && (
          <StripeAddSmartReaderModal locationId={locationId} onClose={addTerminalVisible.close} />
        )}
        <div className="list">
          {readerList.map((reader) => {
            return (
              <div className="item" key={reader.id}>
                <img src={ImagePaymentSmartReaderPng} />
                <div className="info">
                  <div className="name">{reader.name}</div>
                  <div className="sn">{reader.SN}</div>
                </div>
                <div
                  className="code"
                  onClick={() => {
                    if (handleDeleteReader.isBusy()) return;

                    handleDeleteReader(reader);
                  }}
                >
                  Delete
                </div>
              </div>
            );
          })}
          {!readerList.length && <Empty style={{ color: c_text_secondary_color }} />}
        </div>

        <div className="actions">
          {!readerList.length && (
            <div className="actions-link-wrap">
              <Button
                className="actions-link"
                btnType="link"
                target="_blank"
                href={getPurchaseHardwareLink(staff.isEnterpriseRelatedStaff())}
              >
                Purchase hardware
              </Button>
            </div>
          )}

          <Button onClick={addTerminalVisible.open} buttonRadius="circle" btnType="primary">
            Pair a reader
          </Button>
        </div>
      </StripeSmartReaderListModalView>
    );
  },
);
