/**
 * FullCalendar API hooks for LodgingCalendar
 * 1. change viewType (and set eventConstraint)
 * 2. change date (and set eventConstraint)
 */
import { useCallback } from 'react';
import { type EnumValues } from '../../../../store/utils/createEnum';
import { ReportActionName } from '../../../../utils/reportType';
import { reportData } from '../../../../utils/tracker';
import { useGetFullCalendarApi } from '../../latest/AwesomeCalendarProvider';
import { type LodgingCalendarViewsMap } from '../LodgingCalendar/LodgingCalendar.config';

export const useSetLodgingCalendarEventConstraint = () => {
  const getFullCalendarApi = useGetFullCalendarApi();

  return useCallback(() => {
    const calendarApi = getFullCalendarApi();
    const { currentStart, currentEnd } = calendarApi?.view || {};

    if (!calendarApi || !currentStart || !currentEnd) return;
    calendarApi.setOption('eventConstraint', {
      start: currentStart,
      end: currentEnd,
    });
  }, []);
};

export const useSwitchLodgingCalendarView = () => {
  const getFullCalendarApi = useGetFullCalendarApi();

  return useCallback(
    (view: EnumValues<typeof LodgingCalendarViewsMap>) => {
      const calendarApi = getFullCalendarApi();
      if (calendarApi) {
        calendarApi.removeAllEvents();
        calendarApi.changeView(view);
      }
    },
    [getFullCalendarApi],
  );
};

export const useChangeLodgingCalendarDate = () => {
  const getFullCalendarApi = useGetFullCalendarApi();

  return useCallback(
    (date: Date) => {
      const calendarApi = getFullCalendarApi();
      if (calendarApi) {
        calendarApi.removeAllEvents();
        calendarApi.gotoDate(date);
      }
      reportData(ReportActionName.lodgingViewChangeDate);
    },
    [getFullCalendarApi],
  );
};
