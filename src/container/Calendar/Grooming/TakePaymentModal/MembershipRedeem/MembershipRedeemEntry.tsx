import { type LatestCallback, useSerialCallback } from '@moego/finance-utils';
import { cn } from '@moego/ui';
import { type Action, useSelector } from 'amos';
import React, { memo } from 'react';
import { Switch } from '../../../../../components/SwitchCase';
import { selectGroomingTicketInvoice } from '../../../../../store/grooming/grooming.selectors';
import { selectActiveMembershipSubscriptionList } from '../../../../../store/membership/membership.selectors';
import { removeInvoiceAllMembership } from '../../../../../store/membership/membershipApply.actions';
import { Add, Edit } from '../InvoiceInfo/components/AddAndEdit';
import { useInvoiceMembershipHelpers } from './hooks/useInvoiceMembershipHelpers';

export interface MembershipRedeemEntryProps {
  invoiceId: number;
  customerId: string;

  onClick?: () => void;
  dispatchActionWithLoading: LatestCallback<(action: Action) => Promise<void>>;
  className?: string;
}

export const MembershipRedeemEntry = memo(function MembershipRedeemEntry(props: MembershipRedeemEntryProps) {
  const { invoiceId, onClick, dispatchActionWithLoading, className, customerId } = props;
  const [invoice, customerMembershipList] = useSelector(
    selectGroomingTicketInvoice(invoiceId),
    selectActiveMembershipSubscriptionList(customerId),
  );

  // 用户可能membership 已过期，但遗留可用次数，所以需要同时检查customerMembershipList和invoice.membershipList
  const isCustomerHasMembershipSubscription = customerMembershipList?.length > 0 || invoice.membershipList.length > 0;
  const { isMembershipEnabled } = useInvoiceMembershipHelpers();

  const handleRemoveAll = useSerialCallback(async () => {
    await dispatchActionWithLoading(removeInvoiceAllMembership(invoiceId));
  });

  if (!isMembershipEnabled || !isCustomerHasMembershipSubscription) {
    return null;
  }

  const hasMembership = invoice.membershipList?.length > 0;
  const isEditDisabled = invoice.isCompleted;
  const isShowAdd = !hasMembership && !invoice.isCompleted;
  return (
    <div className={cn('moe-pl-[20px] moe-pr-[36px]', className)}>
      <Switch shortCircuit>
        <Switch.Case if={hasMembership}>
          <Edit
            title="Membership"
            value="Applied"
            titleClassName="moe-text-[#666]"
            onEdit={isEditDisabled ? void 0 : onClick}
            onRemove={isEditDisabled ? void 0 : handleRemoveAll}
          />
        </Switch.Case>
        <Switch.Case if={isShowAdd}>
          <Add title={'Apply membership'} onClick={onClick} />
        </Switch.Case>
      </Switch>
    </div>
  );
});
