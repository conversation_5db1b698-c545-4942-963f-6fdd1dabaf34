import { PeriodType } from '@moego/api-web/moego/models/membership/v1/membership_defs';
import { DiscountUnit, TargetType } from '@moego/api-web/moego/models/membership/v1/redeem_models';
import { MinorInfoOutlined } from '@moego/icons-react';
import {
  Alert,
  Checkbox,
  CheckboxGroup,
  Form,
  Heading,
  Input,
  Radio,
  RadioGroup,
  LegacySelect as Select,
  Switch,
  Tooltip,
  Typography,
  type useForm,
  useWatch,
} from '@moego/ui';
import { useMemoizedFn } from 'ahooks';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect, useMemo } from 'react';
import { useMount } from 'react-use';
import { Condition } from '../../../../components/Condition';
import { QuestionTooltip } from '../../../../components/Popup/QuestionTooltip';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import { getTaxList } from '../../../../store/business/tax.actions';
import { selectBDFeatureEnable, selectPricingPermission } from '../../../../store/company/company.selectors';
import { type MembershipModel, ServiceExpiredTypeMap } from '../../../../store/membership/membership.boxes';
import { AllServicesItemType } from '../../../../store/service/category.boxes';
import { defaultLimitedValue } from '../../consts';
import { useMembershipFormIsEditMode } from '../../hooks/useMembershipFormMode';
import { BillingCycle, MembershipDiscountType } from '../../types';
import { ServiceSelect } from './ServiceSelect';
import { AddItemContainer } from './AddItemContainer';
import { RemoveItemContainer } from './RemoveItemContainer';
import { BenifitFormItem } from './BenefitsFormItem';
import { useEnableFeature } from '../../../../store/metadata/featureEnable.hooks';
import { META_DATA_KEY_LIST } from '../../../../store/metadata/metadata.config';
import { generateUUID } from '@moego/pagespy';

export interface BenefitsFormProps {
  form: ReturnType<typeof useForm<MembershipModel>>;
}

interface BenefitsCardProps extends BenefitsFormProps {
  formName: 'enableDiscountBenefits' | 'enableQuantityBenefits';
  formLabel: string;
  formTip?: string;
  formDescription?: string;
  disabled?: boolean;
  children?: React.ReactNode;
}
const BenefitsCard = memo((props: BenefitsCardProps) => {
  const { form, formName, formLabel, disabled, formTip, formDescription, children } = props;
  const [benefitsItemField] = useWatch({
    control: form.control,
    name: [formName],
  });
  return (
    <div className="moe-w-full moe-p-[24px] moe-rounded-[24px] moe-border-divider moe-border">
      <div className="moe-w-full moe-flex moe-items-center">
        <Form.Item disabled={disabled} name={formName} valuePropName="isSelected">
          <Switch isDisabled={disabled}>
            <Typography.Heading size={5}>{formLabel}</Typography.Heading>
          </Switch>
        </Form.Item>
        <Condition if={formTip}>
          <Tooltip side="top" content={formTip}>
            <MinorInfoOutlined className="moe-cursor-pointer moe-ml-xxs" />
          </Tooltip>
        </Condition>
      </div>
      <div className="moe-text-tertiary moe-text-base moe-font-normal moe-mt-[8px]">{formDescription}</div>
      <Condition if={benefitsItemField && children}>
        <div className="moe-mt-m moe-pt-m moe-border-t-divider moe-border-t-[1px]">{children}</div>
      </Condition>
    </div>
  );
});

export const BenefitsForm = memo((props: BenefitsFormProps) => {
  const [_, isDisabled] = useMembershipFormIsEditMode();
  const { form } = props;
  const dispatch = useDispatch();
  const [enableBD, permissions, pricingPermission] = useSelector(
    selectBDFeatureEnable,
    selectCurrentPermissions,
    selectPricingPermission(),
  );
  const { enable: enablePackage } = useEnableFeature(META_DATA_KEY_LIST.PackageEnabled);

  const showPackage = permissions.has('viewPackageSetting') && pricingPermission.enable.has('package') && enablePackage;
  const [
    enableDiscountBenefits,
    enableQuantityBenefits,
    includedDiscountTypes,
    includeServices,
    includeServiceExpired,
    serviceDiscounts,
    addOnDiscounts,
    productDiscounts,
    packageDiscounts,
  ] = useWatch({
    control: form.control,
    name: [
      'enableDiscountBenefits',
      'enableQuantityBenefits',
      'includedDiscountTypes',
      'includeServices',
      'includeServiceExpired',
      'serviceDiscounts',
      'addOnDiscounts',
      'productDiscounts',
      'packageDiscounts',
    ],
  });
  useEffect(() => {
    if (enableQuantityBenefits && (includeServices?.length ?? 0) === 0) {
      form.setValue('includeServices', [
        {
          serviceId: null,
          count: 1,
          uniqueId: generateUUID(),
          unlimited: false,
        },
      ]);
    }
  }, [enableQuantityBenefits]);

  useEffect(() => {
    if (enableDiscountBenefits) {
      for (const [targetType, discountTypeData, membershipDiscountType] of [
        [TargetType.SERVICE, serviceDiscounts, MembershipDiscountType.ServiceDiscount],
        [TargetType.ADDON, addOnDiscounts, MembershipDiscountType.AddOnDiscount],
        [TargetType.PRODUCT, productDiscounts, MembershipDiscountType.ProductDiscount],
        [TargetType.PACKAGE, packageDiscounts, MembershipDiscountType.PackageDiscount],
      ] as const) {
        if (includedDiscountTypes?.includes(targetType) && (discountTypeData?.length ?? 0) === 0)
          form.setValue(membershipDiscountType, [
            {
              uniqueId: generateUUID(),
              ids: [],
              count: undefined,
              unit: DiscountUnit.PERCENT,
              isAll: false,
            },
          ]);
      }
    }
  }, [enableDiscountBenefits, includedDiscountTypes]);

  const addPerksService = useMemoizedFn(() => {
    form.setValue(
      'includeServices',
      (form.getValues().includeServices ?? []).concat({
        serviceId: null,
        count: defaultLimitedValue,
        uniqueId: generateUUID(),
        unlimited: false,
      }),
    );
  });
  const removePerksService = useMemoizedFn((index) => {
    const tempItems = form.getValues().includeServices ?? [];
    if (index < tempItems.length) {
      tempItems.splice(index, 1);
    }
    form.setValue('includeServices', tempItems);
  });

  useMount(() => {
    dispatch(getTaxList());
  });

  const addDiscountService = useMemoizedFn((type: MembershipDiscountType) => {
    form.setValue(
      type,
      (form.getValues()[type] ?? []).concat({
        uniqueId: generateUUID(),
        ids: [],
        count: undefined,
        unit: DiscountUnit.PERCENT,
        isAll: false,
      }),
    );
  });
  const removeDiscountService = useMemoizedFn((type: MembershipDiscountType, index) => {
    const tempItems = form.getValues()[type] ?? [];
    if (index < tempItems.length) {
      tempItems.splice(index, 1);
    }
    form.setValue(type, tempItems);
  });

  const serviceDiscountOptions = useMemo(() => {
    const tempOptions = [
      {
        label: 'Service discount',
        value: TargetType.SERVICE,
      },
      {
        label: 'Add-on discount',
        value: TargetType.ADDON,
      },
    ];
    if (permissions.has('viewProductSetting') && pricingPermission.enable.has('retail')) {
      tempOptions.push({
        label: 'Product discount',
        value: TargetType.PRODUCT,
      });
    }
    if (showPackage) {
      tempOptions.push({
        label: 'Package discount',
        value: TargetType.PACKAGE,
      });
    }
    return tempOptions;
  }, [permissions, pricingPermission.enable, showPackage]);

  const disabledServiceIds: number[] =
    includeServices?.map((item) => (item.serviceId ? +item.serviceId : 0)).filter((item) => !!item) ?? [];

  const disabledDiscountServiceIds: number[] =
    serviceDiscounts?.flatMap((item) => (item.ids ? item.ids : [])).map(Number) ?? [];
  const disacbledDiscountAddonIds: number[] =
    addOnDiscounts?.flatMap((item) => (item.ids ? item.ids : [])).map(Number) ?? [];
  const disacbledDiscountProductIds: number[] =
    productDiscounts?.flatMap((item) => (item.ids ? item.ids : [])).map(Number) ?? [];
  const disacbledDiscountPackageIds: number[] =
    packageDiscounts?.flatMap((item) => (item.ids ? item.ids : [])).map(Number) ?? [];

  const discountCrossInclude = useMemo(() => {
    if (!enableDiscountBenefits || !enableQuantityBenefits) return [];
    const filterService = includedDiscountTypes?.includes(TargetType.SERVICE)
      ? (serviceDiscounts?.flatMap((item) => item.ids) ?? [])
      : [];
    const filterAddons = includedDiscountTypes?.includes(TargetType.ADDON)
      ? (addOnDiscounts?.flatMap((item) => item.ids) ?? [])
      : [];
    return filterService.concat(filterAddons).filter(
      (item) =>
        includeServices
          ?.filter((item) => item.serviceId)
          ?.map((item) => item.serviceId)
          ?.includes(item) ?? false,
    );
  }, [
    enableDiscountBenefits,
    enableQuantityBenefits,
    includedDiscountTypes,
    serviceDiscounts,
    addOnDiscounts,
    includeServices,
  ]);

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-m moe-items-start" id="benefit-form-id">
      <Heading size="3">Benefits</Heading>
      <BenefitsCard
        key="enableDiscountBenefits"
        disabled={isDisabled}
        form={form}
        formName="enableDiscountBenefits"
        formLabel="Include discount"
        formDescription="Apply discount for all or selected services, add-ons and products."
      >
        <Form.Item name="includedDiscountTypes">
          <CheckboxGroup isDisabled={isDisabled} className="moe-gap-0">
            {serviceDiscountOptions.map((option) => (
              <div key={option.value}>
                <Checkbox className="moe-mb-2" value={option.value}>
                  {option.label}
                </Checkbox>
                <Condition if={includedDiscountTypes?.includes(option.value)}>
                  <Condition if={option.value === TargetType.SERVICE}>
                    {serviceDiscounts?.map((serviceDiscount, index) => (
                      <React.Fragment key={`service-discount-${serviceDiscount.uniqueId}`}>
                        <RemoveItemContainer
                          onRemove={() => removeDiscountService(MembershipDiscountType.ServiceDiscount, index)}
                          isRemoveable={serviceDiscounts.length > 1 && !isDisabled}
                          className={index === 0 ? 'moe-mt-4' : ''}
                        >
                          <BenifitFormItem
                            type={MembershipDiscountType.ServiceDiscount}
                            item={serviceDiscount}
                            form={form}
                            index={index}
                            isDisabled={isDisabled}
                            serviceType={
                              enableBD
                                ? [
                                    AllServicesItemType.boarding,
                                    AllServicesItemType.daycare,
                                    AllServicesItemType.grooming,
                                  ]
                                : [AllServicesItemType.grooming]
                            }
                            selectAllLabel="All services (including new services)"
                            disabledIds={disabledDiscountServiceIds}
                          />
                        </RemoveItemContainer>
                        {serviceDiscount?.ids?.some((serDisId) => discountCrossInclude.includes(serDisId)) && (
                          <Alert
                            isCloseable={false}
                            color="warning"
                            classNames={{
                              innerContent: 'moe-text-warning',
                            }}
                            className="-moe-mt-4 moe-mb-4 moe-bg-white moe-p-0"
                          >
                            Selected service(s) already exist in another benefit item.
                          </Alert>
                        )}
                      </React.Fragment>
                    ))}
                    <AddItemContainer
                      isDisabled={isDisabled}
                      onAdd={() => addDiscountService(MembershipDiscountType.ServiceDiscount)}
                      label="Add service"
                    />
                  </Condition>
                  <Condition if={option.value === TargetType.ADDON}>
                    {addOnDiscounts?.map((addOnDiscount, index) => (
                      <React.Fragment key={`add-on-discount-${addOnDiscount.uniqueId}`}>
                        <RemoveItemContainer
                          onRemove={() => removeDiscountService(MembershipDiscountType.AddOnDiscount, index)}
                          isRemoveable={addOnDiscounts.length > 1 && !isDisabled}
                          className={index === 0 ? 'moe-mt-4' : ''}
                        >
                          <BenifitFormItem
                            type={MembershipDiscountType.AddOnDiscount}
                            item={addOnDiscount}
                            form={form}
                            index={index}
                            isDisabled={isDisabled}
                            serviceType={[AllServicesItemType.serviceAddon]}
                            selectAllLabel="All add-ons (including new services)"
                            disabledIds={disacbledDiscountAddonIds}
                          />
                        </RemoveItemContainer>
                        {addOnDiscount?.ids?.some((serDisId) => discountCrossInclude.includes(serDisId)) && (
                          <Alert
                            isCloseable={false}
                            color="warning"
                            classNames={{ innerContent: 'moe-text-warning' }}
                            className="-moe-mt-4 moe-mb-4 moe-bg-white moe-p-0"
                          >
                            Selected add-on(s) already exist in another benefit item.
                          </Alert>
                        )}
                      </React.Fragment>
                    ))}
                    <AddItemContainer
                      isDisabled={isDisabled}
                      onAdd={() => addDiscountService(MembershipDiscountType.AddOnDiscount)}
                      label="Add add-on"
                    />
                  </Condition>
                  <Condition
                    if={
                      permissions.has('viewProductSetting') &&
                      pricingPermission.enable.has('retail') &&
                      option.value === TargetType.PRODUCT
                    }
                  >
                    {productDiscounts?.map((productDiscount, index) => (
                      <React.Fragment key={`product-discount-${productDiscount.uniqueId}`}>
                        <RemoveItemContainer
                          onRemove={() => removeDiscountService(MembershipDiscountType.ProductDiscount, index)}
                          isRemoveable={productDiscounts.length > 1 && !isDisabled}
                          className={index === 0 ? 'moe-mt-4' : ''}
                        >
                          <BenifitFormItem
                            type={MembershipDiscountType.ProductDiscount}
                            item={productDiscount}
                            form={form}
                            index={index}
                            isDisabled={isDisabled}
                            serviceType={[]}
                            disabledIds={disacbledDiscountProductIds}
                          />
                        </RemoveItemContainer>
                      </React.Fragment>
                    ))}
                    <AddItemContainer
                      isDisabled={isDisabled}
                      onAdd={() => addDiscountService(MembershipDiscountType.ProductDiscount)}
                      label="Add product"
                    />
                  </Condition>
                  <Condition if={showPackage && option.value === TargetType.PACKAGE}>
                    {packageDiscounts?.map((packageDiscount, index) => (
                      <React.Fragment key={`package-discount-${packageDiscount.uniqueId}`}>
                        <RemoveItemContainer
                          onRemove={() => removeDiscountService(MembershipDiscountType.PackageDiscount, index)}
                          isRemoveable={packageDiscounts.length > 1 && !isDisabled}
                          className={index === 0 ? 'moe-mt-4' : ''}
                        >
                          <BenifitFormItem
                            type={MembershipDiscountType.PackageDiscount}
                            item={packageDiscount}
                            form={form}
                            index={index}
                            isDisabled={isDisabled}
                            serviceType={[]}
                            disabledIds={disacbledDiscountPackageIds}
                          />
                        </RemoveItemContainer>
                      </React.Fragment>
                    ))}
                    <AddItemContainer
                      isDisabled={isDisabled}
                      onAdd={() => addDiscountService(MembershipDiscountType.PackageDiscount)}
                      label="Add package"
                    />
                  </Condition>
                </Condition>
              </div>
            ))}
          </CheckboxGroup>
        </Form.Item>
      </BenefitsCard>
      <BenefitsCard
        key="enableQuantityBenefits"
        form={form}
        disabled={isDisabled}
        formName="enableQuantityBenefits"
        formLabel="Include services & add-ons"
        formDescription="Include certain quantity or unlimited services or add-ons."
      >
        {includeServices?.map((includeServicesItem, index) => (
          <React.Fragment key={`include-services-${includeServicesItem.uniqueId}`}>
            <RemoveItemContainer
              onRemove={() => removePerksService(index)}
              isRemoveable={includeServices.length > 1 && !isDisabled}
            >
              <div className="moe-w-[438px] moe-min-w-[438px] moe-shrink-0 moe-pr-4">
                <ServiceSelect
                  showTabs
                  form={form}
                  disabled={isDisabled}
                  disabledServiceIds={disabledServiceIds}
                  formItemName={`includeServices.${index}.serviceId`}
                  rules={{
                    required: 'Service is required',
                  }}
                  serviceType={
                    enableBD
                      ? [
                          AllServicesItemType.boarding,
                          AllServicesItemType.daycare,
                          AllServicesItemType.grooming,
                          AllServicesItemType.serviceAddon,
                        ]
                      : [AllServicesItemType.grooming, AllServicesItemType.serviceAddon]
                  }
                />
              </div>
              <div className="moe-flex-1 moe-flex moe-gap-4">
                <Condition if={!includeServicesItem?.unlimited}>
                  <Form.Item
                    name={`includeServices.${index}.count`}
                    rules={{
                      required: 'Count is required',
                    }}
                  >
                    <Input.Number
                      className="moe-w-[124px]"
                      step={1}
                      minValue={1}
                      isDisabled={isDisabled || includeServicesItem?.unlimited}
                      isRequired={!includeServicesItem?.unlimited}
                    />
                  </Form.Item>
                </Condition>

                <Form.Item name={`includeServices.${index}.unlimited`} valuePropName="isSelected">
                  <Checkbox className="moe-mt-3" isDisabled={isDisabled}>
                    Unlimited
                  </Checkbox>
                </Form.Item>
              </div>
            </RemoveItemContainer>
            {includeServicesItem.serviceId && discountCrossInclude.includes(includeServicesItem.serviceId) && (
              <Alert
                isCloseable={false}
                color="warning"
                classNames={{
                  innerContent: 'moe-text-warning',
                }}
                className="-moe-mt-4 moe-mb-4 moe-bg-white moe-p-0"
              >
                Selected service/add-on(s) already exist in another benefit item.
              </Alert>
            )}
          </React.Fragment>
        ))}
        <AddItemContainer isDisabled={isDisabled} onAdd={addPerksService} label="Add service/add-on" />
        <div className="moe-mt-6">
          <Form.Item name="includeServiceExpired" label="Expires after">
            <RadioGroup orientation="horizontal" isDisabled={isDisabled}>
              <Radio value={PeriodType.FOLLOW_MEMBERSHIP}>{ServiceExpiredTypeMap[PeriodType.FOLLOW_MEMBERSHIP]}</Radio>
              <Radio value={PeriodType.SPECIFIED}>
                {ServiceExpiredTypeMap[PeriodType.SPECIFIED]}
                <QuestionTooltip content="Benefits expiry will start from the first day of billing cycle" />
              </Radio>
            </RadioGroup>
          </Form.Item>
        </div>
        <Condition if={includeServiceExpired === PeriodType.SPECIFIED}>
          <div className="moe-flex moe-ml-[214px] moe-gap-4 moe-mt-2">
            <Form.Item
              name="includeServiceExpiredValue"
              rules={{
                required: 'Billing cycle num is required',
              }}
            >
              <Input.Number isDisabled={isDisabled} minValue={1} step={1} className="moe-w-[238px]" />
            </Form.Item>
            <Form.Item
              name="includeServiceExpiredUnit"
              rules={{
                required: 'Billing cycle unit is required',
              }}
            >
              <Select
                className="moe-w-[238px]"
                isDisabled={isDisabled}
                options={BillingCycle.values.map((value) => ({
                  value,
                  label: BillingCycle.mapLabels[value],
                }))}
              ></Select>
            </Form.Item>
          </div>
        </Condition>
      </BenefitsCard>
    </div>
  );
});
