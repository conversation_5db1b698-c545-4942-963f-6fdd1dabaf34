import { But<PERSON>, Heading } from '@moego/ui';
import { useSelector } from 'amos';
import React, { useRef } from 'react';
import { Condition } from '../../../../../../components/Condition';
import { Case, Switch } from '../../../../../../components/SwitchCase';
import { OBTestIds } from '../../../../../../config/testIds/onlineBooking';
import { selectOnlineBookingPreference } from '../../../../../../store/onlineBooking/onlineBookingPreference.selectors';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { useInvoiceReinvent } from '../../../../../PaymentFlow/hooks/useInvoiceReinvent';
import { DisplayPaymentType } from '../components/DisplayPaymentType';
import { DisplayPaymentTypeDetail } from '../components/DisplayPaymentTypeDetail';
import { PaymentTypeAndOptionsDefaultProvider, usePaymentType } from '../components/PaymentOptionsContext';
import { PaymentOptionsForm } from '../components/PaymentOptionsForm';
import { usePaymentSettingFormState } from '../components/PaymentSettingContext';

const { SettingPaymentDefaultCancel, SettingPaymentDefaultUpdate } = OBTestIds;

const DefaultSettingContent = () => {
  const {
    isDirty,
    isExpanded: isDefaultExpanded,
    onFinishEditing,
    onStartEditing,
    formRef,
    submit,
  } = usePaymentSettingFormState('default');
  const { resetPaymentType } = usePaymentType();
  const [preference] = useSelector(selectOnlineBookingPreference());
  const { isNewOrderV4Flow } = useInvoiceReinvent();

  const rootRef = useRef<HTMLDivElement>(null);

  // Scroll this form into the viewport on collapsing
  const scrollToRoot = () => {
    setTimeout(() => {
      rootRef.current?.scrollIntoView();
    }, 0);
  };

  const onSubmit = useSerialCallback(async () => {
    await submit();
    scrollToRoot();
  });

  return (
    <div ref={rootRef} className="moe-bg-white moe-rounded-[8px]">
      <div className="moe-flex moe-justify-between moe-items-center">
        {isNewOrderV4Flow ? (
          <Heading size="4">Default payment option</Heading>
        ) : (
          <Heading size="3">Payment option</Heading>
        )}
        <Condition if={!isDefaultExpanded}>
          <Button variant="tertiary" onPress={onStartEditing} className="moe-text-primary">
            Edit
          </Button>
        </Condition>
      </div>
      <Switch>
        <Case if={isDefaultExpanded}>
          {/* Only send handle to the ref on form expanded */}
          <PaymentOptionsForm ref={isDefaultExpanded ? formRef : undefined} className="moe-mt-[24px]" type="default" />
          {/* A little trick to disable sticky positioning of the update button */}
          <div className="moe-mt-[24px] moe-flex moe-justify-start moe-gap-[16px]">
            <Button
              variant="secondary"
              onPress={() => {
                onFinishEditing();
                resetPaymentType();
                scrollToRoot();
              }}
              data-testid={SettingPaymentDefaultCancel}
            >
              Cancel
            </Button>
            <Button
              isDisabled={!isDirty || onSubmit.isBusy()}
              onPress={onSubmit}
              data-testid={SettingPaymentDefaultUpdate}
            >
              Update
            </Button>
          </div>
        </Case>
        <Case else>
          <div className="moe-mt-[20px]">
            <DisplayPaymentType />
            <DisplayPaymentTypeDetail paymentSetting={preference.paymentSetting} />
          </div>
        </Case>
      </Switch>
    </div>
  );
};

export const DefaultSetting = () => {
  return (
    <PaymentTypeAndOptionsDefaultProvider>
      <DefaultSettingContent />
    </PaymentTypeAndOptionsDefaultProvider>
  );
};
