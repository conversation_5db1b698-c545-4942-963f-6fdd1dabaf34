import { useDispatch, useSelector, useStore } from 'amos';
import type Big from 'big.js';
import dayjs from 'dayjs';
import { groupBy, isNumber } from 'lodash';
import { useMultiStaffPermission } from '../../../components/ServiceStaffPicker/hooks/useMultiStaffPermission';
import { toastApi } from '../../../components/Toast/Toast';
import { type IPet } from '../../../config/interface';
import { http } from '../../../middleware/api';
import { type OpenApiModels } from '../../../openApi/schema';
import { type TicketFormType } from '../../../router/paths';
import { getTicketDetailInfo } from '../../../store/calendarLatest/actions/private/calendar.actions';
import { MultiStaffWorkMode } from '../../../store/createTicket/createTicket.types';
import { isValidAutoAssign } from '../../../store/createTicket/ticket.utils';
import { getGroomingTicketInvoice } from '../../../store/grooming/grooming.actions';
import { ScopeType, TICKET_SOURCE } from '../../../store/grooming/grooming.boxes';
import { BookOnlineStatusMap } from '../../../store/onlineBooking/onlineBooking.boxes';
import { PetLifeStatus } from '../../../store/pet/pet.boxes';
import { selectAllActiveServiceIdList } from '../../../store/service/service.selectors';
import { flat } from '../../../store/utils/utils';
import { DATE_FORMAT_EXCHANGE } from '../../../utils/DateTimeUtil';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { createBigNum } from '../../../utils/utils';
import { type ServiceOperationDTO } from '../../Calendar/Grooming/GroomingTicketModal/components/OperationItem';
import { type LocalSelectedServiceInfo } from '../interfaces';
import { changeOperationPriceOrRatio } from './operation-helper';
import { type PartOfTicketForm, getOperationFromServer } from './ticketForm';
import { syncServiceTime } from './ticketTimeSync';
import { useClientDetail } from './useClientDetail';
import { useInitPetService } from './useInitPetService';
import { usePetService } from './usePetService';
import { useTicketStoreCtx } from './useTicketForm';
import { isNormal } from '../../../store/utils/identifier';
import { selectIsEnableSlotCalender } from '../../../store/calendarLatest/calendar.selectors';

interface FetchDetailParams {
  useCurrentDate?: boolean;
  addUpStartTime?: boolean;
  isBookAgain?: boolean;
}

export const mergeGroomingDataWithPresetInfo = (
  groomingData: OpenApiModels['GET/grooming/appointment/detail']['Res']['data'],
  presetInfo?: TicketFormType,
) => {
  if (!presetInfo) {
    return groomingData;
  }
  const groomingDetailsArray =
    presetInfo.petAndServices?.map((petService) => {
      return petService.serviceItem.map((serviceItem) => {
        const groomingServiceItem = groomingData.groomingPetDetails.find(
          (item) => item.petId === petService.pet && item.serviceId === serviceItem.value,
        );
        return {
          ...groomingServiceItem,
          petId: petService.pet,
          servicePrice: serviceItem.price,
          serviceId: serviceItem.value,
          serviceTime: serviceItem.duration,
          staffId: serviceItem.staffId,
          serviceType: serviceItem.type,
          serviceName: serviceItem.name,
          priceOverrideType: serviceItem.priceOverrideType,
          durationOverrideType: serviceItem.durationOverrideType,
          // scopeTypePrice: 2, // don't know why
          // durationChecked: 2,
        };
      });
    }) ?? [];
  return {
    ...groomingData,
    groomingPetDetails: flat(groomingDetailsArray),
    alertNotes: presetInfo.alertNotes,
    colorCode: presetInfo.colorCode,
    ticketComments: presetInfo.ticketComments,
  };
};

export function useTicketDetail() {
  const store = useStore();
  const isEnableSlotCalender = store.select(selectIsEnableSlotCalender);
  const { loadClientDetail } = useClientDetail();
  const { getPetServices } = usePetService();
  const [
    { presetInfo, date: serviceDate, time, petsService, repeatModalProps, openRepeat, staffLists },
    updateTicketStore,
  ] = useTicketStoreCtx();
  const dispatch = useDispatch();
  const { hasMultiStaffPermission } = useMultiStaffPermission();
  const [activeServiceIds] = useSelector(selectAllActiveServiceIdList);
  const { initServiceItem } = useInitPetService();
  const initApptStaffs = useLatestCallback((groomingPetDetails: any[]) => {
    updateTicketStore({ loading: true });
    http
      .open('POST/business/workingDaily/queryRange', {
        startDate: dayjs().format(DATE_FORMAT_EXCHANGE),
        endDate: dayjs().format(DATE_FORMAT_EXCHANGE),
      })
      .then(({ data }) => {
        const staffLists = data.map((i: any) => {
          let starStaffId = 0;
          if (groomingPetDetails && groomingPetDetails[0]) {
            // 所有的starStaffId都是统一的
            starStaffId = groomingPetDetails[0].starStaffId;
          }
          i.star = i.staffId === starStaffId;
          return i;
        });
        updateTicketStore({ staffLists });
      })
      .finally(() => updateTicketStore({ loading: false }));
  });

  const getTicketDetail = useLatestCallback(
    async (
      ticketId: string,
      { useCurrentDate = false, addUpStartTime = false, isBookAgain = false }: FetchDetailParams,
    ) => {
      updateTicketStore({ loading: true });
      dispatch(getTicketDetailInfo(+ticketId)).then((data) => {
        if (!isNormal(data.invoice?.invoiceId) || data.appointment.isNewOrder) return;
        const invoiceId = Number(data.invoice.invoiceId);
        updateTicketStore({
          invoiceId,
        });
        dispatch(getGroomingTicketInvoice(invoiceId));
      });
      http
        .open('GET/grooming/appointment/detail', { id: +ticketId })
        .then(({ data }) => {
          if (!data.id) {
            toastApi.error('Not found');
            return;
          }
          const isOB = data.bookOnlineStatus === BookOnlineStatusMap.OnlineBookingRequest;
          // 如果是 book again，且多宠物，则需要判断是否用串行累加时间。
          if (isBookAgain && data.groomingPetDetails.length > 1) {
            const serviceListGroupByPetKey = groupBy(data.groomingPetDetails, 'petId');
            const startTimeList = Object.keys(serviceListGroupByPetKey).map(
              (petKey) => serviceListGroupByPetKey[petKey][0].startTime,
            );
            const firstStartTime = startTimeList[0];
            //  如果某个 start time 不一致，则代表不是 same time。所以需要使用串行累加时间
            addUpStartTime = startTimeList.some((startTime) => startTime !== firstStartTime);
          }

          // isBookAgain ? undefined : presetInfo 用于解决 advance setting 进来 book again 无法获取数据的逻辑。
          const groomingData = mergeGroomingDataWithPresetInfo(data, isBookAgain ? undefined : presetInfo);
          const { appointmentDate } = groomingData;
          const initParams: PartOfTicketForm = {
            isOB,
            isSourceFromOB: data.source === TICKET_SOURCE.OnlineBooking,
          };
          // 是否使用ticket中的时间
          const ifUseTicketDate = !useCurrentDate && appointmentDate;
          if (appointmentDate) {
            // 初始时间始终为当前ticket后台接口查询到的时间，用于判断ticket时间是否被修改
            const apptDate = dayjs(appointmentDate);
            const apptTime = data.noStartTime
              ? null
              : apptDate
                  .startOf('date')
                  .hour(Math.floor(data.appointmentStartTime / 60))
                  .minute(data.appointmentStartTime % 60);
            Object.assign(initParams, {
              initDate: apptDate,
              initTime: apptTime,
            });
            if (ifUseTicketDate) {
              Object.assign(initParams, {
                date: apptDate,
                time: apptTime,
              });
            }
          }
          // 初始化 staff
          initApptStaffs(groomingData.groomingPetDetails);
          // 初始化 customer 并将 customer 拥有的 pets 与 ticket 里面的 pets 进行匹配筛选。
          loadClientDetail(
            { customerId: groomingData.customerId, isOB },
            (customerPets: IPet[]) => {
              // 初始化 pet 、 service
              const petsDetail: any[] = [];
              groomingData.groomingPetDetails?.forEach((groomingPetDetail: any) => {
                const { enableOperation, operationList, startTime, workMode, servicePrice } = groomingPetDetail;
                const customerPet = customerPets.find((customerPet) => customerPet.petId === groomingPetDetail.petId);
                // 如果 customer 没有这只 pet，或者这个 pet 被标记位 pass away
                if (!customerPet || customerPet.lifeStatus === PetLifeStatus.PassAway) return;

                if (!petsDetail.some((petDetail) => petDetail.petId === groomingPetDetail.petId)) {
                  const pet = Object.assign({}, customerPet, {
                    localId: petsDetail.length,
                    services: [],
                  });
                  petsDetail.push(pet);
                  // 初始化 pet 对应的服务
                  getPetServices(groomingPetDetail.petId, customerPet);
                }
                const petIndex = petsDetail.findIndex((petDetail) => petDetail.petId === groomingPetDetail.petId);
                groomingPetDetail.type = groomingPetDetail.serviceType;
                const hasOperation = operationList && enableOperation;
                const isOperationParallel =
                  hasOperation &&
                  operationList.every((i: ServiceOperationDTO) => i.startTime === operationList[0].startTime);
                const isPriceRatioUndefined =
                  hasOperation &&
                  operationList
                    .reduce(
                      (acc: Big, curr: ServiceOperationDTO) => acc.plus(createBigNum(curr.priceRatio)),
                      createBigNum(0),
                    )
                    .eq(0);
                let localOperationList = hasOperation
                  ? operationList.map((i: ServiceOperationDTO, index: number) =>
                      getOperationFromServer(i, { localId: index }),
                    )
                  : null;
                if (isPriceRatioUndefined) {
                  // 如果没有定义price ratio，同步一下price ratio
                  localOperationList = changeOperationPriceOrRatio(localOperationList, {
                    isPriceRatio: false,
                    serviceTotal: servicePrice,
                  });
                }
                let staffId = groomingPetDetail.staffId || null;
                const bookAgainNoMultiStaffPermission = hasOperation && isBookAgain && !hasMultiStaffPermission;
                // book again 如果multiple staff没有权限，则晴空
                if (bookAgainNoMultiStaffPermission) {
                  staffId = operationList[0].staffId;
                  localOperationList = null;
                }
                Object.assign(groomingPetDetail, {
                  servicLocalId: petsDetail[petIndex].services.length,
                  // 优先后端接口的值，如果没有则降级为前端计算的值
                  workMode: workMode ?? (isOperationParallel ? MultiStaffWorkMode.PARALLEL : MultiStaffWorkMode.SERIAL),
                  priceChecked: groomingPetDetail.scopeTypePrice !== 2,
                  showSavePrice: false,
                  isSavePrice: false,
                  showSaveDuration: false,
                  isSaveDuration: false,
                  durationChecked: groomingPetDetail.scopeTypeTime !== 2,
                  duration: groomingPetDetail.serviceTime || 0,
                  price: groomingPetDetail.servicePrice || 0,
                  serviceId: groomingPetDetail.serviceId || null,
                  staffId,
                  /**
                   * 每次修改时间和金额 都默认不save
                   */
                  scopeTypeTime: ScopeType.NotSaved,
                  scopeTypePrice: ScopeType.NotSaved,
                  operationList: localOperationList,
                });
                groomingPetDetail.startTime = data?.noStartTime
                  ? null
                  : dayjs(data.appointmentDate)
                      .startOf('day')
                      .set('minute', isNumber(startTime) ? startTime : 0);
                petsDetail[petIndex].services.push(groomingPetDetail);
                Object.assign(petsService, petsDetail);
              });
              const newPetsService = [...petsService];
              let isSameTime = false;
              // if first pet service start time same as second pet's first start time
              // we mark checkbox
              if (petsService.length >= 2) {
                const firstPetStartTime = petsService[0].services[0].startTime;
                isSameTime = petsService.every(
                  (pet) => pet.services[0].startTime?.valueOf() === firstPetStartTime?.valueOf(),
                );
              }
              if (isBookAgain) {
                // 过滤出active的service
                newPetsService.forEach((i) => {
                  const newServices = i.services.filter((service) => activeServiceIds.includes(service.serviceId!));
                  // 如果一个service都没有了，则自动生成一个空的service，并自动添加staff合startTime
                  if (newServices.length === 0) {
                    const emptyService: LocalSelectedServiceInfo = {
                      servicLocalId: 0,
                      ...initServiceItem(),
                    };
                    newServices.push(emptyService);
                    emptyService.staffId = staffLists[0]?.staffId;
                  }
                  i.services = newServices;
                });
              }

              updateTicketStore({
                clientId: groomingData.customerId,
                // By slot 模式下的时间无需按照 working hours 计算，直接使用后端返回数据
                petsService: isEnableSlotCalender
                  ? newPetsService
                  : syncServiceTime(newPetsService, {
                      isSameTime,
                      time: ifUseTicketDate ? initParams.time : time,
                    }),
                isSameTime,
              });
            },
            !data.alertNotes, // for alert notes
          );

          Object.assign(initParams, {
            repeatModalProps: {
              ...repeatModalProps,
              startOnDate: serviceDate,
              repeatId: groomingData.repeatId,
              ...{ visible: !!openRepeat },
            },
          });

          if (addUpStartTime) {
            Object.assign(initParams, { ticketChanged: true });
          }

          /**
           * 展示 auto assign bar 的条件
           * 1. auto assign 有内容
           * 2. ob request pending 状态下展示 (FYI, auto accept 的 appt 不展示)
           */
          const { autoAssign } = groomingData || {};
          const isShowAutoAssign = isValidAutoAssign(autoAssign) && isOB;

          updateTicketStore({
            ...initParams,
            ticketComments: isOB ? groomingData.additionalNote : groomingData.ticketComments,
            alertNotes: isOB ? '' : groomingData.alertNotes,
            colorCode: groomingData.colorCode,
            loading: false,
            autoAssign,
            isShowAutoAssign,
          });
        })
        .catch((err) => {
          console.log('err :>> ', err);
          updateTicketStore({ loading: false });
        });
    },
  );

  return {
    getTicketDetail,
  };
}
