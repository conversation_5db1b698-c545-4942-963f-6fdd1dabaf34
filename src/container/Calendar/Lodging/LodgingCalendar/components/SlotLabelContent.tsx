import { type SlotLabelContentArg } from '@fullcalendar/common';
import { Heading, Text, Tooltip, cn } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { memo } from 'react';
import { Condition } from '../../../../../components/Condition';
import { selectBusiness } from '../../../../../store/business/business.selectors';
import { formatDateToUniqueString } from '../../../../../store/calendarLatest/calendar_data.utils';
import { selectLodgingCalendarCurrentBusinessId } from '../../../../../store/calendarLatest/lodgingCalendar.selectors';
import { allLodgingTypesDateOccupiedMapBox } from '../../../../../store/lodging/lodgingUnit.boxes';
import { FullWeekSimpleDayList } from '../../../../../store/onlineBooking/onlineBooking.boxes';
import { renderCountableNounPlurals } from '../../../../../utils/utils';
import { selectCompanyCareTypeNameMap } from '../../../../../store/careType/careType.selectors';

export interface SlotLabelContentProps {
  slotLabelContentArg: SlotLabelContentArg;
  showCloseDate?: boolean;
}

export const SlotLabelContent = memo<SlotLabelContentProps>((props) => {
  const { slotLabelContentArg, showCloseDate } = props;
  const [businessId, allLodgingTypesDateOccupiedMap, companyCareTypeNameMap] = useSelector(
    selectLodgingCalendarCurrentBusinessId,
    allLodgingTypesDateOccupiedMapBox,
    selectCompanyCareTypeNameMap,
  );
  const { date } = slotLabelContentArg;
  const dateUnique = formatDateToUniqueString(date);
  const { occupiedPercents, petCountBoarding, petCountDaycare, petCountEvaluation } =
    (allLodgingTypesDateOccupiedMap.get(businessId) || {})[dateUnique] || {};
  const isToday = dayjs(date).isSame(dayjs(), 'day');
  const [business] = useSelector(selectBusiness(Number(businessId)));

  const weekDay = FullWeekSimpleDayList[date.getDay()];

  const classData = {
    'moe-text-brand': isToday,
    'moe-text-disabled': showCloseDate,
  };

  return (
    <Tooltip
      backgroundTheme="light"
      content={
        <div className="moe-flex moe-flex-col moe-gap-s">
          <Heading size="5" className="moe-text-primary">
            {weekDay}, {business.formatDate(date)}
          </Heading>

          <div className="moe-flex moe-flex-col moe-gap-4px">
            <Text variant="small" className="moe-text-primary">
              Occupancy rate:{' '}
              <Heading size="6" className="moe-text-primary moe-inline-block">
                {occupiedPercents ?? 0}%
              </Heading>
            </Text>
            <Condition if={petCountBoarding}>
              <Text variant="small" className="moe-text-primary">
                {`${companyCareTypeNameMap.Boarding}: `}
                <Heading size="6" className="moe-text-primary moe-inline-block">
                  {renderCountableNounPlurals(petCountBoarding, 'pet')}
                </Heading>
              </Text>
            </Condition>
            <Condition if={petCountDaycare}>
              <Text variant="small" className="moe-text-primary">
                {`${companyCareTypeNameMap.Daycare}: `}
                <Heading size="6" className="moe-text-primary moe-inline-block">
                  {renderCountableNounPlurals(petCountDaycare, 'pet')}
                </Heading>
              </Text>
            </Condition>
            <Condition if={petCountEvaluation}>
              <Text variant="small" className="moe-text-primary">
                {`${companyCareTypeNameMap.Evaluation}: `}
                <Heading size="6" className="moe-text-primary moe-inline-block">
                  {renderCountableNounPlurals(petCountEvaluation, 'pet')}
                </Heading>
              </Text>
            </Condition>
          </div>
        </div>
      }
    >
      <div className="moe-absolute moe-w-full moe-left-0 moe-top-0 moe-flex moe-flex-col moe-items-center moe-justify-center moe-px-[13px] moe-py-[12px] hover:moe-bg-neutral-sunken-light moe-bg-white">
        <Heading size="5" className={cn('moe-text-secondary', classData)}>
          {date.getDate()}
        </Heading>
        <Text variant="caption" className={cn('moe-mt-xxs moe-text-secondary', classData)}>
          {weekDay}
          {` ${occupiedPercents ?? 0}`}%
        </Text>
      </div>
    </Tooltip>
  );
});

SlotLabelContent.displayName = 'SlotLabelContent';
