import React, { type ReactNode, useEffect, useState } from 'react';
import { useSelector } from 'amos';
import { selectBusinessStaffs } from '../../../../../../../../../store/staff/staff.selectors';
import { CheckStaff } from './CheckStaff';
import { ID_ANONYMOUS } from '../../../../../../../../../store/utils/identifier';
import { useSMStaffSchedule } from '../../hooks/useSMStaffSchedule';
import { staffMapBox } from '../../../../../../../../../store/staff/staff.boxes';
import { staffIsAvailableMapBox } from '../../../../../../../../../store/onlineBooking/settings/availabilityGrooming.boxes';
import { useDispatch } from 'amos';
import { useLatestCallback } from '../../../../../../../../../utils/hooks/useLatestCallback';
import { Scroll } from '@moego/ui';
import cn from 'classnames';

interface StaffListBoxProps {
  className?: string;
  selectedId?: number;
  onStaffSelect?: (staffId: number) => void;
  children?: (selectedId: number) => ReactNode;
}

export const StaffListBox = (props: StaffListBoxProps) => {
  const { children, className } = props;
  const dispatch = useDispatch();
  const [staffIdList, staffMap, staffIsAvailableMap] = useSelector(
    selectBusinessStaffs(),
    staffMapBox,
    staffIsAvailableMapBox,
  );
  const [currentStaffId, setCurrentStaffId] = useState(ID_ANONYMOUS);
  const isCurrentStaffAvailable = staffIsAvailableMap.mustGetItem(currentStaffId).isAvailable;

  const onStaffCheck = useLatestCallback((nextChecked, { staffId }) => {
    dispatch(
      staffIsAvailableMapBox.mergeItem(staffId, {
        isAvailable: nextChecked,
      }),
    );
  });

  useSMStaffSchedule(currentStaffId, true);

  useEffect(() => {
    const defaultSelectedId = staffIdList.find((id) => staffMap.mustGetItem(id).isShowOnCalendar) ?? ID_ANONYMOUS;
    setCurrentStaffId(defaultSelectedId);
  }, [staffIdList, staffMap]);

  const renderEmptyContent = (staffId: number) => {
    return (
      <div className="moe-flex moe-p-[20px]">
        <div className="moe-text-regular moe-text-tertiary">{`${staffMap.mustGetItem(staffId).fullName()} is disabled for online booking.`}</div>
      </div>
    );
  };

  return (
    <div
      className={cn(
        'moe-max-h-[600px] moe-min-w-fit moe-h-full moe-flex moe-items-stretch moe-border-divider moe-border-solid moe-border moe-rounded-s moe-mt-[24px]',
        className,
      )}
    >
      <Scroll className="moe-w-[258px] moe-shrink-0 moe-border-r moe-border-solid moe-border-r-divider">
        {staffIdList.map((staffId: number) => {
          return (
            <CheckStaff
              key={staffId}
              staffId={staffId}
              className="first:moe-rounded-tl-s"
              isSelected={currentStaffId === staffId}
              isChecked={staffIsAvailableMap.mustGetItem(staffId).isAvailable}
              onStaffSelect={setCurrentStaffId}
              onStaffCheck={onStaffCheck}
            />
          );
        })}
      </Scroll>

      <Scroll
        className="moe-w-full"
        classNames={{
          viewport: '[&>div]:moe-h-full',
        }}
      >
        {isCurrentStaffAvailable ? children?.(currentStaffId) : renderEmptyContent(currentStaffId)}
      </Scroll>
    </div>
  );
};
