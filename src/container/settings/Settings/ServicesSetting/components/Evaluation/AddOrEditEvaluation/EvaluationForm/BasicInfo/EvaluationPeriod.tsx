import { Form, Input, Radio, RadioGroup, Text, useWatch } from '@moego/ui';
import React, { memo } from 'react';
import { Condition } from '../../../../../../../../../components/Condition';
import { ReportActionName } from '../../../../../../../../../utils/reportType';
import { reportData } from '../../../../../../../../../utils/tracker';
import { useServiceStaffPermissions } from '../../../../AddOrEditComponents/hooks/useServiceStaffPermissions';
import { useEvaluationFormContext } from '../EvaluationFormContext';

const DefaultResetIntervalMonth = 6;

export const EvaluationPeriod = memo(() => {
  const { form, isEdit } = useEvaluationFormContext();
  const [isResettable, resetIntervalDays] = useWatch({
    control: form?.control,
    name: ['isResettable', 'resetIntervalDays'],
  });

  const { isDisabledForRemains } = useServiceStaffPermissions(isEdit);

  return (
    <div>
      <Form.Item name="isResettable" label="Evaluation results validity period">
        <RadioGroup
          isDisabled={isDisabledForRemains}
          value={isResettable?.toString()}
          isRequired
          onChange={(value: string) => {
            const resettable = value === 'true';
            form?.setValue('isResettable', resettable);
            resettable && form?.setValue('resetIntervalDays', DefaultResetIntervalMonth);
            reportData(ReportActionName.evaluationSetPeriodValid);
          }}
        >
          <Radio value={'false'}>Always valid</Radio>
          <Radio value={'true'}>
            {`Clear 'Pass' if the pet hasn't had relevant boarding/daycare services for a specified period.`}
          </Radio>
        </RadioGroup>
      </Form.Item>
      <Condition if={isResettable}>
        <div className="moe-bg-neutral-sunken-0 moe-rounded-[16px] moe-p-spacing-s moe-flex moe-flex-col moe-gap-y-s moe-mt-8px-200 moe-ml-[28px]">
          <Form.Item
            name="resetIntervalDays"
            label="Clear the evaluation ‘Pass’ field if pet(s) are not at the facility for a certain period of time"
          >
            <div className="moe-flex moe-items-center">
              <Input.Number
                isDisabled={isDisabledForRemains}
                onChange={(value) => {
                  form?.setValue('resetIntervalDays', +(value || 0));
                  reportData(ReportActionName.evaluationSetPeriodMonth);
                }}
                suffix="month(s)"
                value={resetIntervalDays}
                minValue={1}
                maxValue={100}
                classNames={{ base: 'moe-w-[215px] moe-mr-[10px]' }}
              />
              <Text variant="regular-short">without the relevant services in store</Text>
            </div>
          </Form.Item>
        </div>
      </Condition>
    </div>
  );
});
