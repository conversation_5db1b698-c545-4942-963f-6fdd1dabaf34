import { LegacySelect as Select } from '@moego/ui';
import { useDispatch } from 'amos';
import React, { memo } from 'react';
import { Condition } from '../../../../../../../../../../components/Condition';
import { OBTestIds } from '../../../../../../../../../../config/testIds/onlineBooking';
import { setAvailabilityDraftSettings } from '../../../../../../../../../../store/onlineBooking/actions/private/availabilityGrooming.actions';
import { TimeSlotFormatKind } from '../../../../../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { DraftAvailableSlotRecord } from '../../../../../../../../../../store/onlineBooking/settings/availabilityGrooming.boxes';
import { OB_TIME_SLOTS } from '../../../../../type';
import { TimePreferenceSettingsMap, useGetTimePreferenceSettings } from '../TimePreference.hooks';
import { TimePreferenceSettingItem } from './TimePreferenceSettingItem';

export const TimeSlotInterval = memo<{ draftSettings: DraftAvailableSlotRecord }>((props) => {
  const { draftSettings } = props;
  const dispatch = useDispatch();
  const [timeSlotFormat, timeSlotMins] = useGetTimePreferenceSettings(
    TimePreferenceSettingsMap.TimeSlotFormat,
    TimePreferenceSettingsMap.TimeSlotMins,
  );
  const { isByWorkingHour, isBySlot } = draftSettings;
  /**
   * 不展示 time slot interval:
   * 1. by working hour 下，date only
   * 2. by slot 下，任意 format
   */
  const hideTimeSlotInterval = isBySlot || (isByWorkingHour && timeSlotFormat.value === TimeSlotFormatKind.DateOnly);

  const handleTimeSlotIntervalChange = (value: number) => {
    dispatch(setAvailabilityDraftSettings({ [timeSlotMins.updateKey]: value }));
  };

  return (
    <Condition if={!hideTimeSlotInterval}>
      <TimePreferenceSettingItem label="Slot interval">
        <Select
          className="moe-w-[352px]"
          value={timeSlotMins.value as number}
          options={OB_TIME_SLOTS.map((d) => ({ label: `${d} mins`, value: d }))}
          onChange={(e) => handleTimeSlotIntervalChange(e as number)}
          data-testid={OBTestIds.SettingSlotIntervalSelect}
        />
      </TimePreferenceSettingItem>
    </Condition>
  );
});
