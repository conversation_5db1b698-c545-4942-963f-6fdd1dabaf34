/*
 * @since 2022-02-11 18:26:51
 * <AUTHOR> <<EMAIL>>
 */

import { BaseRichEditor, toolbarPlugins } from '@moego/fn-components';
import { Editor } from '@tinymce/tinymce-react';
import React, { memo, useEffect, useMemo, useState } from 'react';
import { useLatest, useWindowSize } from 'react-use';
import { baseRichEditorDragDropPasteOptions } from '../../components/LexicalRichEditor/const';
import { RICHTEXT_API_KEY, RICHTEXT_INIT_CONFIG } from '../../config/tinymaceEditor';
import { useRouteQueryV2 } from '../../utils/RoutePath';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';

export interface MobileEditorProps {
  className?: string;
}

const plugins: string[] = [];

interface MobileEditorParams {
  /** 是否用旧的富文本 */
  useOld: string;
  isDisable?: string;
  /** 是否允许插入输入框占位 */
  allowEditorInput?: string;
}

export const MobileEditor = memo<MobileEditorProps>(({ className }) => {
  const size = useWindowSize();
  const init = useMemo(
    () => ({
      ...RICHTEXT_INIT_CONFIG,
      plugins: 'wordcount image link emoticons lists autoresize',
      min_height: size.height,
      max_height: size.height,
    }),
    [size],
  );

  const [content, setContent] = useState<string | undefined>(undefined);
  const lastContent = useLatest(content);
  const { useOld, isDisable = 'false', allowEditorInput = 'false' } = useRouteQueryV2<MobileEditorParams>();

  const handleInit = useLatestCallback(() => {
    try {
      (window as any).ReactNativeWebView.postMessage(JSON.stringify({ action: 'MobileEditor.ready' }));
    } catch {}
  });

  useEffect(() => {
    const handler = (e: MessageEvent) => {
      try {
        const data = JSON.parse(e.data);
        if (data.action === 'MobileEditor.setContent') {
          setContent(data.data);
        } else if (data.action === 'MobileEditor.acquireContent') {
          (window as any).ReactNativeWebView.postMessage(
            JSON.stringify({
              action: 'MobileEditor.syncContent',
              data: lastContent.current,
            }),
          );
        }
      } catch {}
    };
    const isIos = /cfnetwork|ios|ipados/i.test(navigator.userAgent);
    const receiver = isIos ? window : (document as unknown as Window);
    receiver.addEventListener('message', handler);
    return () => receiver.removeEventListener('message', handler);
  }, []);

  return (
    <>
      {useOld === 'true' ? (
        <Editor
          onInit={handleInit}
          init={init}
          value={content}
          onEditorChange={setContent}
          apiKey={RICHTEXT_API_KEY}
          plugins={plugins}
          disabled={isDisable === 'true'}
        />
      ) : (
        <BaseRichEditor
          initConfig={{
            maxHeight: size.height,
            height: size.height,
            toolbarPlugins: [...toolbarPlugins].filter((p) => p !== 'moreDropdown/fullscreen'),
            dragDropPasteOptions: baseRichEditorDragDropPasteOptions,
            editable: isDisable !== 'true',
            showInput: allowEditorInput === 'true',
          }}
          onInit={handleInit}
          className="moe-my-0"
          classNames={{
            input: 'moe-h-full moe-min-h-[calc(100vh_-_47px)]',
          }}
          value={content}
          onEditorChange={setContent}
        />
      )}
    </>
  );
});
