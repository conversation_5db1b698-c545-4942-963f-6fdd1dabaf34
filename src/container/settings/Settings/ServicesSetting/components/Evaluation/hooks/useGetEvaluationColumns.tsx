import { Text, Tooltip } from '@moego/ui';
import { useStore } from 'amos';
import React from 'react';
import { type BasedTableColumnProps } from '../../../../../../../components/BasedTable/types';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import { taxMapBox } from '../../../../../../../store/business/tax.boxes';
import { type EvaluationRecord } from '../../../../../../../store/evaluation/evaluation.boxes';
import { lodgingTypeMapBox } from '../../../../../../../store/lodging/lodgingType.boxes';
import { staffMapBox } from '../../../../../../../store/staff/staff.boxes';
import { isNormal, toNumber } from '../../../../../../../store/utils/identifier';
import { withPl } from '../../../../../../../utils/calculator';
import { Actions } from '../../Actions';

interface EvaluationColumnProps {
  onEdit: (data: EvaluationRecord) => void;
  onDuplicate: (data: EvaluationRecord) => void;
  onDelete: (data: EvaluationRecord) => void;
}

export const useGetEvaluationColumns = (props: EvaluationColumnProps): BasedTableColumnProps<EvaluationRecord>[] => {
  const store = useStore();

  return [
    {
      title: 'Services',
      id: 'Services',
      prop: 'name',
      minWidth: 320,
      render(data) {
        return (
          <div className="moe-flex moe-items-center moe-gap-x-[8px]">
            <div
              style={{ backgroundColor: data.colorCode }}
              className="moe-w-[12px] moe-h-[12px] moe-rounded-full moe-flex-shrink-0"
            />
            <Tooltip content={data.name} delay={0} side="top">
              <div className={`moe-mb-0 moe-line-clamp-2 moe-text-base moe-font-bold moe-font-manrope`}>
                {data.name}
              </div>
            </Tooltip>
          </div>
        );
      },
    },
    {
      width: 136,
      title: 'Price',
      prop: 'price',
      render: (data) => {
        const business = store.select(selectCurrentBusiness);
        return business.formatAmount(data.price ?? 0);
      },
    },
    {
      width: 96,
      title: 'Tax',
      prop: 'taxId',
      render: (data) => {
        const taxMap = store.select(taxMapBox);
        if (!isNormal(data?.taxId)) {
          return '-';
        }
        return taxMap.mustGetItem(Number(data.taxId)).taxRate! + '%';
      },
    },
    {
      width: 144,
      title: 'Duration',
      prop: 'duration',
      render: (data) => {
        return withPl(data.duration ?? 0, 'min');
      },
    },
    {
      minWidth: 168,
      maxWidth: 300,
      title: 'Applicable staff',
      prop: 'allowedStaffList',
      render: (data) => {
        const staffMap = store.select(staffMapBox);
        const { allowedStaffList, isAllStaff } = data;
        const values = allowedStaffList?.map((id) => staffMap.mustGetItem(toNumber(id)).fullName());
        const content = values?.join(', ');

        if (isAllStaff) {
          return <div>All staff</div>;
        }

        return (
          <Tooltip content={content}>
            <Text variant="regular-short" ellipsis>
              {content}
            </Text>
          </Tooltip>
        );
      },
    },
    {
      minWidth: 232,
      maxWidth: 300,
      title: 'Eligible lodging type',
      prop: 'customizedLodgingIds',
      render: (data) => {
        const lodgingTypeMap = store.select(lodgingTypeMapBox);
        const { customizedLodgingIds = [], lodgingFilter } = data;
        const values = customizedLodgingIds?.map((id) => lodgingTypeMap.mustGetItem(id).name);
        const content = values?.join(', ');

        if (!lodgingFilter) {
          return <div>All lodging types</div>;
        }

        return (
          <Tooltip content={content}>
            <Text variant="regular-short" ellipsis>
              {content}
            </Text>
          </Tooltip>
        );
      },
    },
    {
      width: 64,
      title: '',
      prop: 'handler',
      stickyRight: true,
      alignRight: true,
      customClassNames: '!moe-pr-s moe-bg-white group-hover:moe-bg-neutral-sunken-light',
      render(data) {
        return <Actions data={data} onEdit={props.onEdit} onDuplicate={props.onDuplicate} onDelete={props.onDelete} />;
      },
    },
  ];
};
