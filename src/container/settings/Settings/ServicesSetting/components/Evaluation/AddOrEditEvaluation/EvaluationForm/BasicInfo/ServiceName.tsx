import { Form, Input } from '@moego/ui';
import React, { memo } from 'react';
import { useServiceStaffPermissions } from '../../../../AddOrEditComponents/hooks/useServiceStaffPermissions';
import { useEvaluationFormContext } from '../EvaluationFormContext';

export const ServiceName = memo(() => {
  const { isEdit } = useEvaluationFormContext();
  const { isDisabledForRemains } = useServiceStaffPermissions(isEdit);
  return (
    <Form.Item
      name="name"
      label="Evaluation service name"
      rules={{
        required: 'Evaluation service name is required',
      }}
    >
      <Input
        disabled={isDisabledForRemains}
        isRequired
        className="moe-w-full"
        placeholder="Service name"
        maxLength={70}
        description="Private to business only"
      />
    </Form.Item>
  );
});
