import { useSelector } from 'amos';
import React, { memo } from 'react';
import { Condition } from '../../../../../../components/Condition';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { OrderItemType } from '../../../../../../store/grooming/grooming.boxes';
import { selectGroomingTicketInvoice } from '../../../../../../store/grooming/grooming.selectors';
import { staffMapBox } from '../../../../../../store/staff/staff.boxes';
import { isNormal } from '../../../../../../store/utils/identifier';
import { OrderSection, RegularText12, SectionContent, SectionTitle } from '../InvoiceInfo.style';
import { Edit } from './AddAndEdit';
import { InvoiceRowItem } from './InvoiceRowItem';
import { OrderLineItem } from './OrderLineItem';
import { MembershipBenefits } from '../../MembershipRedeem/MembershipBenefits';
import { getDiscountTag } from '../../ProductInfo/Product.utils';

export interface PackagesProps {
  invoiceId: number;
}

export const Packages = memo<PackagesProps>(({ invoiceId }) => {
  const [business, invoice, staffMap] = useSelector(
    selectCurrentBusiness,
    selectGroomingTicketInvoice(invoiceId),
    staffMapBox,
  );
  const packageList = invoice.filterItemsByType(OrderItemType.Package);
  const packageDiscount = invoice.discountMap?.package?.discountAmount ?? 0;
  const packageSubtotal = invoice.subTotalAmountMap?.package;

  return (
    <Condition if={packageList.length > 0}>
      <OrderSection>
        <SectionTitle>Packages</SectionTitle>
        <SectionContent>
          {packageList.map((packageInfo) => {
            const { discountInfo } = packageInfo;
            const { discountAmount, discountCode } = discountInfo || {};
            const discountName = discountCode || getDiscountTag(business.currencySymbol, [packageInfo.discountInfo]);

            return (
              <OrderLineItem
                key={packageInfo.id}
                lineItem={packageInfo}
                extra={
                  <>
                    <MembershipBenefits items={packageInfo.membershipDetailList} />
                    {/* discountAmount 为0 时不展示 */}
                    {discountAmount ? (
                      <InvoiceRowItem
                        label={`Discount: ${discountName}`}
                        style={{ marginTop: '4px' }}
                        labelClassName="!moe-text-[12px]"
                      >
                        {`-${business.formatAmount(discountAmount)}`}
                      </InvoiceRowItem>
                    ) : null}
                    <Condition if={isNormal(packageInfo.staffId)}>
                      <RegularText12 colorType="secondary" style={{ marginTop: 4 }}>
                        Sold by {staffMap.mustGetItem(packageInfo.staffId).fullName()}
                      </RegularText12>
                    </Condition>
                  </>
                }
              />
            );
          })}
        </SectionContent>
        <InvoiceRowItem
          label="Package subtotal"
          className="moe-pt-[12px] moe-border-t moe-border-t-solid moe-border-[#E6E6E6]"
          labelClassName="!moe-text-[#333]"
        >
          {business.formatAmount(packageSubtotal ?? 0)}
        </InvoiceRowItem>
        <Condition if={Number(packageSubtotal) > 0 && packageDiscount > 0}>
          <Edit title="Discount" value={`-${business.formatAmount(packageDiscount)}`} />
        </Condition>
      </OrderSection>
    </Condition>
  );
});
