import { useDispatch } from 'amos';
import { Form, Input } from 'antd';
import React, { memo, useEffect } from 'react';
import { Button } from '../../../../../../components/Button/Button';
import { Modal } from '../../../../../../components/Modal/Modal';
import { FormFooter } from '../../../../../../components/Style/Style';
import { addPaymentMethod, updatePaymentMethod } from '../../../../../../store/payment/actions/private/payment.actions';
import { PaymentMethodRecord } from '../../../../../../store/payment/payment.boxes';
import { formInput } from '../../../../../../utils/form';
import { useFormRef } from '../../../../../../utils/hooks/hooks';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';

export interface PaymentMethodModalProps {
  item: PaymentMethodRecord | null | undefined;
  onClose: () => void;
}

const methodInput = formInput(PaymentMethodRecord).copy('name');

export const PaymentMethodModal = memo<PaymentMethodModalProps>(({ item, onClose }) => {
  const form = useFormRef();
  const dispatch = useDispatch();
  useEffect(() => {
    methodInput.attach(form, item);
  }, [item]);
  const handleSubmit = useSerialCallback(async () => {
    const input = await methodInput.validate(form);
    if (!item) {
      await dispatch(addPaymentMethod(input.name));
    } else {
      await dispatch(updatePaymentMethod({ id: item.id, ...input }));
    }
    onClose();
  });

  return (
    <Modal visible={item !== void 0} onClose={onClose} title={`${item === null ? 'Add new' : 'Edit'} payment method`}>
      <Form ref={form} labelAlign="left" size={'large'}>
        <Form.Item name="name" label="Name" rules={[{ required: true, message: 'Please input payment name!' }]}>
          <Input />
        </Form.Item>
        <FormFooter>
          <Button
            buttonRadius="circle"
            btnType="primary"
            onClick={handleSubmit}
            htmlType="submit"
            loading={handleSubmit.isBusy()}
          >
            {item ? 'Save' : 'Add'}
          </Button>
        </FormFooter>
      </Form>
    </Modal>
  );
});
