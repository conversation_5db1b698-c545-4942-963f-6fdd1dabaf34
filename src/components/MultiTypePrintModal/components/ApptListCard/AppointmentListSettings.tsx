import { Checkbox, Heading, Radio, RadioGroup } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo } from 'react';
import type {
  ApptListPrintCardSettingsChildrenKey,
  ApptListPrintCardSettingsParentKey,
} from '../../../../store/printCard/appointmentList/appointmentList.actions';
import { setApptListPrintCardSettings } from '../../../../store/printCard/appointmentList/appointmentList.actions';
import { selectApptListSettings } from '../../../../store/printCard/appointmentList/appointmentList.selectors';
import { createEnum } from '../../../../store/utils/createEnum';
import { StaticPrintSettingItem } from '../common/StaticPrintSettingItem';
import { AppointmentListCardSortEnum } from './AppointmentListCardConfig';
import { PetCodeSelector } from '../common/PetCodes/PetCodeSelector';
import { UniqueCommentCheckbox } from '../common/PetCodes/UniqueCommentCheckbox';

interface CommentsAndNotesEnumLabels {
  valueKey: ApptListPrintCardSettingsChildrenKey<'commentsAndNotes'>;
  label: string;
}

const CommentsAndNotesEnum = createEnum<string, string, CommentsAndNotesEnumLabels>({
  PetNotes: [
    '1',
    {
      valueKey: 'showPetNotes',
      label: 'Pet notes',
    },
  ],
  AlertNotes: [
    '2',
    {
      valueKey: 'showAlertNotes',
      label: 'Alert notes',
    },
  ],
  TicketComments: [
    '3',
    {
      valueKey: 'showTicketComments',
      label: 'Ticket comments',
    },
  ],
});

export const AppointmentListSettings = memo(() => {
  const [settings] = useSelector(selectApptListSettings);

  const dispatch = useDispatch();

  const handleChange = <
    PK extends ApptListPrintCardSettingsParentKey,
    CK extends ApptListPrintCardSettingsChildrenKey<PK>,
  >(
    parentKey: PK,
    childrenKey: CK,
    value: boolean | string | string[],
  ) => {
    dispatch(setApptListPrintCardSettings({ parentKey, childrenKey, value }));
  };

  return (
    <div className="moe-m-m moe-flex moe-flex-col moe-gap-m">
      <Heading size="4">Settings</Heading>
      <StaticPrintSettingItem title="Comments & Notes">
        {CommentsAndNotesEnum.values.map((value) => {
          const { label, valueKey } = CommentsAndNotesEnum.mapLabels[value];
          return (
            <Checkbox
              key={value}
              isSelected={settings.commentsAndNotes[valueKey]}
              onChange={(isSelected) => {
                handleChange('commentsAndNotes', valueKey, isSelected);
              }}
            >
              {label}
            </Checkbox>
          );
        })}
      </StaticPrintSettingItem>
      <StaticPrintSettingItem title="Lodging breakdown">
        <Checkbox
          isSelected={settings.lodgingBreakdown?.showLodgingBreakdown}
          onChange={(isSelected) => {
            handleChange('lodgingBreakdown', 'showLodgingBreakdown', isSelected);
          }}
        >
          Show lodging breakdown
        </Checkbox>
      </StaticPrintSettingItem>

      <StaticPrintSettingItem title="Sort by">
        <RadioGroup value={settings.sort.sortBy} onChange={(value) => handleChange('sort', 'sortBy', value)}>
          {AppointmentListCardSortEnum.values.map((value) => (
            <Radio key={value} value={value}>
              {AppointmentListCardSortEnum.mapLabels[value]}
            </Radio>
          ))}
        </RadioGroup>
      </StaticPrintSettingItem>
      <StaticPrintSettingItem title="Pet code filter">
        <PetCodeSelector
          value={settings.petCodeFilter.petCodeIdList}
          onChange={(value) => handleChange('petCodeFilter', 'petCodeIdList', value)}
        />
        <UniqueCommentCheckbox
          isSelected={settings.petCodeFilter.showUniqueComment}
          onChange={(isSelect) => {
            handleChange('petCodeFilter', 'showUniqueComment', isSelect);
          }}
        />
      </StaticPrintSettingItem>
    </div>
  );
});
