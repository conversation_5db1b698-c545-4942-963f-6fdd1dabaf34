import React, { FC, useEffect } from 'react';
import { useBusinessApplicableEvaluation } from '../../../../../../../store/evaluation/evaluation.hooks';
import { useMount } from 'react-use';
import { Condition } from '../../../../../../../components/Condition';
import { EvaluationTestDetailModel } from '@moego/api-web/moego/models/online_booking/v1/evaluation_test_detail_models';
import { EvaluationDateTime } from './EvaluationDateTime';
import { EvaluationBriefView } from '@moego/api-web/moego/models/offering/v1/evaluation_models';
import { EvaluationServicePicker } from '../../../../../../Appt/components/Evaluation/EvaluationServicePicker';
import { getEvaluationIdKey, getEvaluationStaffIdKey } from './Evaluation.utils';
import { Form, useForm, useWatch } from '@moego/ui';
import { useEvaluationAutoAssignStaff } from '../../../../../../Appt/hooks/useEvaluationAutoAssignStaff';
import { EvaluationStaffPicker } from '../../../../../../Appt/components/Evaluation/EvaluationStaffPicker';
import { stringToNumberV2 } from '../../../../../../settings/Settings/ServicesSetting/utils/inputTransformer';
import { InformationRequireFormRecord } from '../InformationRequireModal.types';

interface EvaluationFormProps {
  itemKey: string;
  form: ReturnType<typeof useForm<InformationRequireFormRecord>>;
  evaluation?: EvaluationBriefView;
  serviceDetail: EvaluationTestDetailModel;
}

export const EvaluationForm: FC<EvaluationFormProps> = (props) => {
  const { itemKey, form, evaluation, serviceDetail } = props;
  const evaluationIdKey = getEvaluationIdKey(itemKey);
  const evaluationStaffIdKey = getEvaluationStaffIdKey(itemKey);
  const { evaluationList, fetchEvaluationList } = useBusinessApplicableEvaluation();
  const [evaluationId = '', evaluationStaffId = ''] = useWatch({
    control: form?.control,
    name: [evaluationIdKey, evaluationStaffIdKey],
  });
  const selectedEvaluation = evaluationList.find((item) => item.id === evaluationId);
  useEvaluationAutoAssignStaff({
    evaluationId: `${evaluationId}`,
    defaultStaffId: `${evaluationStaffId}`,
    onStaffReady: (autoAssignedStaffId) => {
      form.setValue(evaluationStaffIdKey, autoAssignedStaffId, { shouldDirty: true });
    },
  });
  useEffect(() => {
    if (form && evaluation?.id) {
      form.setValue(evaluationIdKey, evaluation.id);
    }
  }, [form, evaluation?.id, evaluationIdKey]);
  useMount(() => fetchEvaluationList());
  return (
    <div className="moe-flex moe-flex-col moe-gap-y-s">
      <EvaluationDateTime
        startDate={serviceDetail.startDate}
        startTime={serviceDetail.startTime}
        evaluationName={selectedEvaluation?.name || 'Evaluation'}
        endTime={serviceDetail.startTime + (selectedEvaluation?.duration || 0)}
      />
      <Form.Item name={evaluationIdKey} label="Evaluation service">
        <EvaluationServicePicker evaluationList={evaluationList.toArray()} />
      </Form.Item>
      <Condition if={evaluation?.allowStaffAutoAssign}>
        <Form.Item name={evaluationStaffIdKey} label="Staff assignment" transformer={stringToNumberV2}>
          <EvaluationStaffPicker
            evaluationId={`${evaluationId}`}
            appointmentDate={serviceDetail.startDate}
            serviceStartTime={serviceDetail.startTime}
            serviceTime={serviceDetail.duration}
            placeholder="Select staff"
          />
        </Form.Item>
      </Condition>
    </div>
  );
};
