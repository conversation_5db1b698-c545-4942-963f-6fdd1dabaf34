import React from 'react';

interface DepositEllipsisLinesProps {
  limit: number;
  texts: string[] | null;
  unit: string;
}

export const DepositEllipsisLines = ({ limit, texts = [], unit }: DepositEllipsisLinesProps) => {
  if (!texts?.length) {
    return <div>/</div>;
  }

  const renderedTexts = texts.length > limit ? texts.slice(0, limit - 1) : texts;
  return (
    <div className="moe-flex moe-flex-col moe-gap-[4px]">
      {renderedTexts.map((item, index) => (
        <div key={index} className="moe-line-clamp-1">
          {item}
        </div>
      ))}
      {texts.length > limit && (
        <div key="more">
          +{texts.length - limit + 1} {unit}
        </div>
      )}
    </div>
  );
};
