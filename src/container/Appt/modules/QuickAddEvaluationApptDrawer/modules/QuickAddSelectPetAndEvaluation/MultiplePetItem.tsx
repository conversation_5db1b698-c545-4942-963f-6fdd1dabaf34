import { MajorChevronDownOutlined } from '@moego/icons-react';
import { Collapse, Form, Heading, cn, useWatch, type useForm } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useEffect } from 'react';
import { useBusinessApplicableEvaluation } from '../../../../../../store/evaluation/evaluation.hooks';
import { getDefaultEvaluationServiceListDetail } from '../../../../../../store/evaluation/evaluation.types';
import { petMapBox } from '../../../../../../store/pet/pet.boxes';
import { useBool } from '../../../../../../utils/hooks/useBool';
import { EvaluationServicePicker } from '../../../../components/Evaluation/EvaluationServicePicker';
import { useEvaluationQuickAdd } from '../../hooks/useEvaluationQuickAdd';
import { EvaluationLodgingInfo } from '../QuickAddInfoPanel/EvaluationLodgingInfo';
import { EvaluationStaffInfo } from '../QuickAddInfoPanel/EvaluationStaffInfo';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useEvaluationAutoAssignStaff } from '../../../../hooks/useEvaluationAutoAssignStaff';
import { type EvaluationItemFormFields } from '../../components/PetEvaluationInfoForm';
import { toNumber } from '../../../../../../store/utils/identifier';

export interface MultiplePetItemProps {
  petIds: string[];
  petId: number;
  index: number;
  form: ReturnType<typeof useForm<Record<string, EvaluationItemFormFields>>>;
  startDate?: string;
  startTime?: number;
}

export const MultiplePetItem = memo<MultiplePetItemProps>((props) => {
  const { petId, index, petIds, form, startDate, startTime } = props;
  const [petMap] = useSelector(petMapBox);
  const expanded = useBool(true);
  const pet = petMap.mustGetItem(petId);
  const { currentServiceItemType, evaluationInfo } = useEvaluationQuickAdd();
  const { evaluationList } = useBusinessApplicableEvaluation(currentServiceItemType);
  const defaultEvaluation = evaluationList.first()?.id;
  const isMultipleEvaluationMode = evaluationList.size > 1;
  const [currentServiceId] = useWatch({ control: form.control, name: [`${petId}.serviceId`] });
  const currentEvaluationInfo = evaluationList.get(toNumber(currentServiceId));

  useEffect(() => {
    let service = evaluationInfo.petServiceList.find((s) => s.petId === petId.toString());

    if (!service) {
      service = {
        ...getDefaultEvaluationServiceListDetail(),
        serviceId: defaultEvaluation || '',
      };
    }

    form.setValue(`${petId}.serviceId`, service.serviceId);
    form.setValue(`${petId}.staffId`, service.staffId);
    form.setValue(`${petId}.lodgingId`, service.lodgingId || '');
  }, [petId, evaluationInfo.petServiceList, defaultEvaluation, form]);

  const onStaffReady = useLatestCallback((staffId: string) => {
    form.setValue(`${petId}.staffId`, staffId);
  });

  useEvaluationAutoAssignStaff({
    evaluationId: currentServiceId || '',
    defaultStaffId: form.getValues(`${petId}.staffId`) || '',
    onStaffReady,
  });

  return (
    <>
      {petIds?.length > 1 && <div className="moe-border-t-[1px] moe-border-divider moe-border-dashed" />}
      <Collapse.Panel
        classNames={{
          button: 'moe-w-full',
          header: 'moe-w-full',
          headerMain: 'moe-w-full',
          title: 'moe-w-full moe-flex moe-justify-between',
          content: 'moe-p-0',
          body: cn('moe-mt-[24px] moe-bg-transparent moe-rounded-none', {
            'moe-mt-0': !expanded.value,
          }),
          base: cn('moe-gap-y-0', {
            'moe-mb-m': index === petIds.length - 1,
          }),
        }}
        indicatorIcon={<MajorChevronDownOutlined />}
        keepContentMounted
        isExpanded={expanded.value}
        onExpandedChange={expanded.as}
        title={
          <Heading size="4" className="moe-text-primary">
            {pet.petName}
          </Heading>
        }
      >
        <Form.Item name={`${petId}.serviceId`} label="Evaluation service">
          <EvaluationServicePicker
            classNames={{
              formItemWrapper: cn([{ 'moe-hidden': !isMultipleEvaluationMode }]),
            }}
            evaluationList={evaluationList.toArray()}
          />
        </Form.Item>
        <div className={cn('moe-flex moe-gap-8px-200', { 'moe-mt-[-24px]': !isMultipleEvaluationMode })}>
          <div className="moe-flex-1">
            <Form.Item name={`${petId}.staffId`}>
              <EvaluationStaffInfo
                serviceId={currentServiceId}
                startDate={startDate}
                startTime={startTime}
                serviceTime={currentEvaluationInfo?.duration || 0}
              />
            </Form.Item>
          </div>
          <div className="moe-flex-1">
            <Form.Item name={`${petId}.lodgingId`}>
              <EvaluationLodgingInfo serviceId={currentServiceId} startDate={startDate} petId={petId.toString()} />
            </Form.Item>
          </div>
        </div>
      </Collapse.Panel>
    </>
  );
});
