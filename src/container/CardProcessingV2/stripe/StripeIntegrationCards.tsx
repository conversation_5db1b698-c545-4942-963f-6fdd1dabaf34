/*
 * @since 2020-09-30 11:00:22
 * <AUTHOR> <<EMAIL>>
 */

import { type BankAccount } from '@stripe/stripe-js';
import { useDispatch, useSelector } from 'amos';
import { Switch } from 'antd';
import classNames from 'classnames';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import React, { type ReactNode, memo, useEffect, useRef, useState } from 'react';
import { useHistory } from 'react-router';
import { useAsync } from 'react-use';
import IconIconExclamationYellowFilledSvg from '../../../assets/icon/icon-exclamation-yellow-filled.svg';
import IconIconInfoRedFilledSvg from '../../../assets/icon/icon-info-red-filled.svg';
import IconEasterRabbitIconPng from '../../../assets/image/easter-rabbit-icon.png';
import ImagePaymentSmartReaderPng from '../../../assets/image/payment-smart-reader.png';
import SvgIconArrowRightRoundEdgeSvg from '../../../assets/svg/icon-arrow-right-round-edge.svg';
import SvgIconJumpLinkSvg from '../../../assets/svg/icon-jump-link.svg';
import SvgIconMoegoPayAccountStatusDisabledSvg from '../../../assets/svg/icon-moego-pay-account-status-disabled.svg';
import SvgIconMoegoPayAccountStatusEnabledSvg from '../../../assets/svg/icon-moego-pay-account-status-enabled.svg';
import { Button } from '../../../components/Button/Button';
import { Card } from '../../../components/Card/Card';
import { InfoHint, WarningHint } from '../../../components/ColoredHint/ColoredHint';
import { ImgIcon, SvgIcon } from '../../../components/Icon/Icon';
import { Loading } from '../../../components/Loading/Loading';
import { modalApi } from '../../../components/Modal/Modal';
import { OnboardingPopover } from '../../../components/OnboardingPopover/OnboardingPopover';
import { WithPricingEnableUpgrade } from '../../../components/Pricing/WithPricingComponents';
import { toastApi } from '../../../components/Toast/Toast';
import { getClosestScroller } from '../../../layout/components/ScrollerProvider';
import { PATH_CREDIT_CARD_SETTING, PATH_TRANSITION_ACTIVITY } from '../../../router/paths';
import { updateBusiness } from '../../../store/business/business.actions';
import { PreferPayTypes } from '../../../store/business/business.boxes';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { type PricingPermissionKey } from '../../../store/company/company.boxes';
import { selectPricingPermission } from '../../../store/company/company.selectors';
import { getMetadataByKey, updateMetadata } from '../../../store/metadata/metadata.actions';
import { META_DATA_KEY_LIST } from '../../../store/metadata/metadata.config';
import { selectMetaDataByKey } from '../../../store/metadata/metadata.selectors';
import { getDisputeRedDotStatus } from '../../../store/notification/red-dot.actions';
import { selectDisputeHasUnread } from '../../../store/notification/red-dot.selectors';
import { updatePaymentSettingInfo } from '../../../store/payment/actions/private/payment.actions';
import { getPaymentSettingInfo } from '../../../store/payment/actions/public/payment.actions';
import { ProcessingFeePayBy, isCardAuthLimitCountry } from '../../../store/payment/payment.boxes';
import { selectPaymentSettingInfo } from '../../../store/payment/payment.selectors';
import {
  getLatestStripePayoutList,
  getStripeAccountLink,
  getStripeBalanceStatus,
} from '../../../store/stripe/actions/private/stripe.actions';
import { enableStripeNextDayPayout, getStripeScheduledPayoutSetting } from '../../../store/stripe/payout.actions';
import { NextDayPayoutStatus } from '../../../store/stripe/payout.boxes';
import { selectStripeScheduledPayoutSetting } from '../../../store/stripe/payout.selectors';
import { StripeAccountStatus, printClassName } from '../../../store/stripe/stripe.boxes';
import { selectBusinessRecentStripePayouts, selectBusinessStripeAccount } from '../../../store/stripe/stripe.selectors';
import {
  currentStripeTerminalLocationIdBox,
  isStripeSmartReaderAvailableCountry,
} from '../../../store/stripe/stripeTerminal.boxes';
import { useRouteQuery } from '../../../utils/RoutePath';
import { useBool } from '../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { isAfterEasterTheme } from '../../../utils/utils';
import { getPurchaseHardwareLink } from '../../CardProcessing/CardProcessing.utils';
import { EligibleMarketBanner } from '../../Finance/FinanceCapital/components/MarketEntry/EligibleMarketBanner';
import { RedDot } from '../components/RedDot';
import { CustomTipScreenModal } from './CustomTipScreenModal';
import { ProcessingFeeSettingModal } from './ProcessingFeeSettingModal';
import { SmartTipSettingModal } from './SmartTipSettingModal';
import { StripeBankAccount } from './StripeBankAccount';
import { StripeCardListModal } from './StripeBankAccountList';
import { PaymentPreferenceCard, PurchaseBtn, StripeIntegrationCardsView } from './StripeIntegrationCard.style';
import { StripeLocationSettingModal } from './StripeLocationSettingModal';
import { stripeAvailableCountryOption } from './StripeSetup';
import { StripeSmartReaderListModal } from './StripeSmartReaderListModal';
import { usePayoutsApartDay } from './hooks/useFeaturePayout';
import { selectCurrentStaff } from '../../../store/staff/staff.selectors';

// New tags local storage key，已下线
// const PROCESSING_FEE_EDITED_KEY = 'processing_fee_edited';
// New tags local storage key，已下线
// const SMART_TIP_EDITED_KEY = 'smart_tip_edited';
// 这个 flag 最初是用于圣诞节皮肤的的，但是现在下掉了；如果后续要提醒新皮肤，要记得把这个 key 的名字改一下
// 历史：
// 1. custom_tip_screen_edited 圣诞节
const RECENT_PAYOUTS_COUNT = 2;
const PRE_AUTH_GOT_IT_KEY = 'pre_auth_got_it';

const PayoutSettingsCard = () => {
  const dispatch = useDispatch();
  const [setting, isNextDayPayoutsNewDismissed, pricingPermission] = useSelector(
    selectStripeScheduledPayoutSetting(),
    selectMetaDataByKey<boolean>(META_DATA_KEY_LIST.NextDayPayoutsNewDismissed),
    selectPricingPermission(),
  );
  // allowlist 逻辑部分放前端判断了，加入 allowlist 的商家后台仍然返回 NotAvailable，但是前端要当作是 Available
  const status =
    setting.payoutScheduleStatus === NextDayPayoutStatus.NotAvailable &&
    pricingPermission.enable.has('payoutT1WhiteList')
      ? NextDayPayoutStatus.Available
      : setting.payoutScheduleStatus;

  useAsync(async () => {
    // Load status
    await dispatch(getStripeScheduledPayoutSetting());
    await dispatch(getMetadataByKey<boolean>(META_DATA_KEY_LIST.NextDayPayoutsNewDismissed));
  }, []);

  const handleEnable = useSerialCallback(async () => {
    dispatch(updateMetadata(META_DATA_KEY_LIST.NextDayPayoutsNewDismissed, true));
    const { payoutScheduleStatus } = await dispatch(enableStripeNextDayPayout());
    if (payoutScheduleStatus === NextDayPayoutStatus.Enabled) {
      toastApi.success('Next-day payout enabled.');
    }
  });

  const handleRequest = useSerialCallback(async () => {
    window.Intercom('showNewMessage', 'I would like to request an individual account review for next-day payout.');
  });

  const handleContactSupport = () => {
    window.Intercom('show');
  };

  const renderNextDayPayoutsAction = () => {
    if (status === NextDayPayoutStatus.Enabled) {
      return <div className="moe-text-sm moe-font-medium moe-text-[#999]">Enabled</div>;
    }
    const enabled = status === NextDayPayoutStatus.Available && !handleEnable.isBusy();
    return (
      <a
        className={`moe-text-sm moe-font-medium ${
          enabled
            ? 'moe-text-brand'
            : 'moe-text-icon-brand-hover hover:moe-text-icon-brand-hover moe-cursor-not-allowed'
        }`}
        onClick={enabled ? handleEnable : undefined}
      >
        Enable
      </a>
    );
  };

  const renderNextDayPayoutsInfo = () => {
    if (status === NextDayPayoutStatus.NotAvailable) {
      return (
        <InfoHint
          className="moe-mt-[8px]"
          text={
            <div className="moe-ml-[8px] moe-text-xs moe-font-medium moe-text-[#666]">
              To enable next-day payout, a minimum transaction amount of $30,000.00 is required within a six-month
              period (from all locations in your account), or you can{' '}
              <a className="moe-text-brand" onClick={handleRequest}>
                request account review
              </a>{' '}
              with us.
            </div>
          }
        />
      );
    }
    if (status === NextDayPayoutStatus.Reviewing) {
      return (
        <InfoHint
          className="moe-mt-[8px]"
          text="Next-day payout schedule is under review. You will also get results via email."
        />
      );
    }
    if (status === NextDayPayoutStatus.ReviewFailed) {
      return (
        <WarningHint
          className="moe-mt-[8px]"
          text={
            <div className="moe-ml-[8px] moe-text-xs moe-font-medium moe-text-[#333]">
              Fail to enable next-day payout. Please contact support if you still want to enable.{' '}
              <a className="moe-font-bold moe-text-brand" onClick={handleContactSupport}>
                Contact support
              </a>
            </div>
          }
        />
      );
    }
    return null;
  };

  // For now we only have Next-day Payouts entry in the Payout settings card, so hide the whole card if Next-day Payouts
  // is not supported.
  if (status === NextDayPayoutStatus.NotSupportedCountry) {
    return null;
  }
  return (
    <Card className="card" title="Payout settings">
      <div className="moe-flex moe-items-center moe-justify-between">
        <div className="moe-text-sm moe-font-bold moe-text-[#333]">Next-day payout</div>
        {!isNextDayPayoutsNewDismissed && (
          <div className="moe-ml-[8px] moe-px-[8px] moe-py-[2px] moe-rounded-[37.5px] moe-bg-brand-bold moe-text-xs moe-font-medium moe-text-[#FFF]">
            New
          </div>
        )}
        <div className="moe-grow" />
        {renderNextDayPayoutsAction()}
      </div>
      <div className="moe-mt-[8px] moe-text-xs moe-font-medium moe-text-[#999]">
        Next-day payouts in transit will arrive in the next business day.
      </div>
      {renderNextDayPayoutsInfo()}
    </Card>
  );
};

interface Props {
  isSetupFinishModalVisible: boolean;
}

export const StripeIntegrationCards = memo(({ isSetupFinishModalVisible }: Props) => {
  const history = useHistory();
  const routeQuery = useRouteQuery(PATH_CREDIT_CARD_SETTING);

  const rootRef = useRef<HTMLDivElement>(null);
  const cardListModalVisible = useBool();
  const smartReaderListModalVisible = useBool();
  const locationSettingModalVisible = useBool();
  const [futurePayoutsTotal, setFuturePayoutsTotal] = useState(0);
  const [business, account, locationId, paymentSettingInfo, pricingPermission, payouts, disputeHasUnread, staff] =
    useSelector(
      selectCurrentBusiness(),
      selectBusinessStripeAccount(),
      currentStripeTerminalLocationIdBox,
      selectPaymentSettingInfo(),
      selectPricingPermission(),
      selectBusinessRecentStripePayouts(RECENT_PAYOUTS_COUNT),
      selectDisputeHasUnread(),
      selectCurrentStaff(),
    );
  const primary = account.primaryCard();
  const getApartDay = usePayoutsApartDay();

  const dispatch = useDispatch();

  const loadRecentPayouts = useSerialCallback(() =>
    dispatch(
      getLatestStripePayoutList({
        loadMode: 'PREPEND',
        limit: RECENT_PAYOUTS_COUNT,
      }),
    ),
  );
  const loadFuturePayoutsTotal = useSerialCallback(async () => {
    const { availableBalance, pendingBalance } = await dispatch(getStripeBalanceStatus());
    setFuturePayoutsTotal(availableBalance + pendingBalance);
  });

  useEffect(() => {
    if (business.primaryPayType === 0) {
      dispatch(updateBusiness({ primaryPayType: PreferPayTypes.Stripe }));
    }
    dispatch(getDisputeRedDotStatus());
  }, []);

  useEffect(() => {
    smartReaderListModalVisible.as(routeQuery.stripeModal === 'SMART_READER_MANAGEMENT');
    locationSettingModalVisible.as(routeQuery.stripeModal === 'LOCATION_MODAL');
  }, [routeQuery]);

  useAsync(() => Promise.all([loadRecentPayouts(), loadFuturePayoutsTotal()]), []);

  const handleStripeSmartReaderSetting = useLatestCallback(() => {
    if (locationId) {
      smartReaderListModalVisible.open();
    } else {
      modalApi.confirm({
        title: 'Address information should be completed first',
        okText: 'Go to settings',
        okType: 'primary',
        cancelText: 'Cancel',
        onOk: () => {
          locationSettingModalVisible.open();
        },
      });
    }
  });

  // processing fee settings
  const processingFeeSettingModalVisible = useBool();
  const handleProcessingFeeSettingOpen = useLatestCallback(() => {
    processingFeeSettingModalVisible.open();
  });

  const handleProcessingFeeClose = useLatestCallback(() => {
    processingFeeSettingModalVisible.close();
    dispatch(getPaymentSettingInfo());
  });

  const handleProcessingFeeSubmitAndClose = useLatestCallback(() => {
    processingFeeSettingModalVisible.close();
  });

  const handleBizVerificationLinkClick = async () => {
    if (!stripeAvailableCountryOption(business.country)) {
      modalApi.error({
        content: 'MoeGo Pay currently only supports businesses from US, AU, UK, CA.',
      });
      return;
    }
    const link = await dispatch(getStripeAccountLink(location.href, account.id));
    window.open(link.url, '_self');
  };

  const justFinishedSetup = useBool();
  useEffect(() => {
    if (isSetupFinishModalVisible) {
      justFinishedSetup.as(true);
    }
  }, [isSetupFinishModalVisible]);
  const preAuthPopover = useBool();
  const preAuthPopoverDismissed = useBool(localStorage.getItem(PRE_AUTH_GOT_IT_KEY) === '1');
  useEffect(() => {
    preAuthPopover.as(
      // Payment setting loaded
      !!paymentSettingInfo.businessId &&
        // Loading spinner not visible
        !(loadRecentPayouts.isBusy() || loadFuturePayoutsTotal.isBusy()) &&
        // Pre-auth not enabled
        paymentSettingInfo.cardAuthEnable !== 1 &&
        // Don't overlap onboarding modal
        !isSetupFinishModalVisible &&
        !isCardAuthLimitCountry(business.country) &&
        // Not dismissed yet
        !preAuthPopoverDismissed.value,
    );
  }, [
    paymentSettingInfo.businessId,
    loadRecentPayouts.isBusy(),
    loadFuturePayoutsTotal.isBusy(),
    isSetupFinishModalVisible,
    preAuthPopoverDismissed.value,
    business.country,
  ]);
  useEffect(() => {
    // We want to disable page content scrolling on showing onboarding with highlight
    if (rootRef.current) {
      const classList = getClosestScroller(rootRef.current).classList;
      if (preAuthPopover.value) {
        classList.add('!moe-pointer-events-none');
      } else {
        classList.remove('!moe-pointer-events-none');
      }
    }
  }, [preAuthPopover.value, rootRef.current]);
  const dismissPreAuthPopover = () => {
    preAuthPopoverDismissed.as(true);
    localStorage.setItem(PRE_AUTH_GOT_IT_KEY, '1');
  };

  const checkShow3DSTips = (checked: boolean) => {
    if (checked && isCardAuthLimitCountry(business.country)) {
      modalApi.error({
        content:
          'Card authentication currently unavailable in UK due to 3DS. We apologize for the inconvenience and hope to make it available soon. Thank you for your patience and understanding.',
      });
      return true;
    }
    return false;
  };
  const handleChangeCardAuth = useSerialCallback(async (checked: boolean) => {
    dismissPreAuthPopover();
    if (checkShow3DSTips(checked)) return;
    // if (paymentSettingInfo.preauthEnable && !checked) {
    //   await dispatch(updatePaymentSettingInfo({ cardAuthEnable: checked ? 1 : 0, preauthEnable: false }));
    // } else {
    await dispatch(updatePaymentSettingInfo({ cardAuthEnable: checked ? 1 : 0 }));
    // }
  });

  // const handleChangePreauth = useSerialCallback(async (checked: boolean) => {
  //   dismissPreAuthPopover();
  //   if (paymentSettingInfo.cardAuthEnable === 0 && checked) {
  //     if (checkShow3DSTips(checked)) return;
  //     await dispatch(updatePaymentSettingInfo({ preauthEnable: checked, cardAuthEnable: 1 }));
  //   } else {
  //     await dispatch(updatePaymentSettingInfo({ preauthEnable: checked }));
  //   }
  // });

  // const handleChangePreauthPeriod = useSerialCallback(async (hours: number) => {
  //   await dispatch(updatePaymentSettingInfo({ preauthBspd: hours }));
  // });

  const {
    processingFeePayBy,
    // onlineFeeRate, onlineFeeCents, readerFeeRate, readerFeeCents
  } = paymentSettingInfo;
  const isClient = processingFeePayBy === ProcessingFeePayBy.Client;
  const showAccountWarnMessage =
    account.status === StripeAccountStatus.Enabled || account.status === StripeAccountStatus.RestrictedSoon;
  const showAccountErrorMessage = account.status === StripeAccountStatus.Restricted;
  const showAccountStatusMessage = showAccountWarnMessage || showAccountErrorMessage;
  const renderProcessingFeeSettingEntry = () => (
    <>
      <div className="cell">
        <div className="cell__title !moe-font-bold">Credit card processing fee</div>
        <WithPricingEnableUpgrade permission="processFee">
          <div className="cell__value f-14-gray" onClick={handleProcessingFeeSettingOpen}>
            <span className="paid-by">Paid by {isClient ? 'clients' : 'business'}</span>
            Edit
          </div>
        </WithPricingEnableUpgrade>
      </div>
      {/* {isClient && (
        <div className="mt-24 fee-detail">
          <div>
            Online pay: {onlineFeeRate}% + ${onlineFeeCents / 100}
          </div>
          <div>
            Reader pay: {readerFeeRate}% + ${readerFeeCents / 100}
          </div>
        </div>
      )} */}
    </>
  );
  const renderPreAuthSettingEntry = () => (
    <>
      <OnboardingPopover
        title="Card authentication"
        message="Add an additional layer of security to your revenue and ensure all cards stored on file are valid."
        showIgnore
        showHighlight
        highlightInset={{ top: 12, bottom: 12, left: 16, right: 16 }}
        offset={[0, -36]}
        visible={preAuthPopover.value}
        onClickConfirm={async () => {
          await handleChangeCardAuth(true);
        }}
        onClickIgnore={() => {
          dismissPreAuthPopover();
        }}
      >
        <div>
          <div className="!moe-flex !moe-items-center !moe-justify-between">
            <div className="!moe-text-sm !moe-font-bold !moe-text-[#333333]">Card authentication</div>
            <WithPricingEnableUpgrade permission="processFee">
              {(onCapture) => {
                return (
                  // <Tooltip
                  //   theme="black"
                  //   overlay="If you turn off Card authentication, you will also turn off Pre-authorization too"
                  //   disabled={!(paymentSettingInfo.cardAuthEnable === 1 && paymentSettingInfo.preauthEnable)}
                  //   width={280}
                  // >
                  <Switch
                    disabled={handleChangeCardAuth.isBusy()}
                    checked={paymentSettingInfo.cardAuthEnable === 1}
                    onChange={onCapture || handleChangeCardAuth}
                  />
                  // </Tooltip>
                );
              }}
            </WithPricingEnableUpgrade>
          </div>
          <div className="!moe-mt-[8px] !moe-text-xs !moe-font-medium !moe-text-[#999999]">
            Enabling this feature means all future card info will be authenticated with a $0.50 pre-authorization charge
            before storing on file. This charge will be dropped immediately after the card is authenticated.
          </div>
        </div>
      </OnboardingPopover>

      {/* <OnboardingPopover
        title="Card authentication"
        message="Add an additional layer of security to your revenue and ensure all cards stored on file are valid."
        showIgnore
        showHighlight
        highlightInset={{ top: 12, bottom: 12, left: 16, right: 16 }}
        offset={[0, -36]}
        visible={preAuthPopover.value}
        onClickConfirm={async () => {
          await handleChangePreauth(true);
        }}
        onClickIgnore={() => {
          dismissPreAuthPopover();
        }}
      >
        <div className="!moe-mt-[24px]">
          <div className="!moe-flex !moe-items-center !moe-justify-between">
            <div className="!moe-text-sm !moe-font-bold !moe-text-[#333333]">Pre-authorization</div>
            <WithPricingEnableUpgrade permission="processFee">
              {(onCapture) => {
                return (
                  <Tooltip
                    theme="black"
                    overlay="If you turn on pre-authorization, you will also turn on card authentication too"
                    disabled={!(paymentSettingInfo.cardAuthEnable === 0 && !paymentSettingInfo.preauthEnable)}
                    width={290}
                  >
                    <Switch
                      disabled={handleChangePreauth.isBusy()}
                      checked={paymentSettingInfo.preauthEnable}
                      onChange={onCapture || handleChangePreauth}
                    />
                  </Tooltip>
                );
              }}
            </WithPricingEnableUpgrade>
          </div>
          <div className="!moe-mt-[8px] !moe-text-xs !moe-font-medium !moe-text-[#999999]">
            Enabling this feature means all appointments will be paid certain time before they start.
          </div>
          {paymentSettingInfo.preauthEnable ? (
            <div className="!moe-flex !moe-items-center !moe-justify-between">
              <div className="!moe-flex !moe-items-center">
                <span>Set pre-authentication period</span>
                <QuestionTooltip overlay="我需要更多的补充" className="!moe-ml-[4px]" theme="black" />
              </div>
              <Select
                options={[
                  { value: 6, label: '6 hours' },
                  { value: 24, label: '24 hours' },
                  { value: 48, label: '48 hours' },
                ]}
                value={paymentSettingInfo.preauthBspd}
                onChange={handleChangePreauthPeriod}
                loading={handleChangePreauthPeriod.isBusy()}
              />
            </div>
          ) : null}
        </div>
      </OnboardingPopover> */}
    </>
  );

  // smart tip settings
  const smartTipSettingModalVisible = useBool();
  const handleSmartTipSettingOpen = useLatestCallback(() => {
    smartTipSettingModalVisible.open();
  });
  const handleSmartTipSettingClose = useLatestCallback(() => {
    smartTipSettingModalVisible.close();
  });

  // custom tip screen
  const customTipScreenModalVisible = useBool();
  const handleCustomTipScreenOpen = useLatestCallback(() => {
    customTipScreenModalVisible.open();
  });
  const handleCustomTipScreenClose = useLatestCallback(() => {
    customTipScreenModalVisible.close();
  });

  const handleViewMorePayouts = () => {
    history.push(PATH_TRANSITION_ACTIVITY.build({ vendor: 'MoeGoPay', panel: 'payout' }));
  };

  const displayTransactionRedDot = disputeHasUnread;

  const renderEntry = (
    title: string,
    isNew: boolean,
    onClick: () => void,
    permission: PricingPermissionKey,
    icon?: ReactNode,
  ) => (
    <div className="cell">
      <div className="cell__title !moe-flex !moe-items-center">
        {title}
        {isNew ? icon ? icon : <div className="tag-new">New</div> : null}
      </div>
      <WithPricingEnableUpgrade permission={permission}>
        <div className="cell__value f-14-gray" onClick={onClick}>
          Edit
        </div>
      </WithPricingEnableUpgrade>
    </div>
  );

  const smartTipSettingEntry = renderEntry('Tip rates', false, handleSmartTipSettingOpen, 'smartTip');
  const tipScreenEntry = renderEntry(
    'Appearance',
    !isAfterEasterTheme(),
    handleCustomTipScreenOpen,
    'smartTip',
    <img src={IconEasterRabbitIconPng} className="!moe-ml-[4px] !moe-w-[20px]" />,
  );

  const renderStatusIcon = (enabled: boolean) => {
    const src = enabled ? SvgIconMoegoPayAccountStatusEnabledSvg : SvgIconMoegoPayAccountStatusDisabledSvg;
    return (
      <SvgIcon
        className={classNames('icon', { enabled })}
        src={src}
        color={enabled ? '#07AB4C' : '#D0021B'}
        size={20}
      />
    );
  };

  return (
    <StripeIntegrationCardsView ref={rootRef}>
      <StripeCardListModal visible={cardListModalVisible.value} onClose={cardListModalVisible.close} />
      <Loading loading={loadRecentPayouts.isBusy() || loadFuturePayoutsTotal.isBusy()}>
        <div className="cards-column moe-shrink-0">
          <Card
            className="card-account"
            title="Account info"
            extra={
              <PurchaseBtn target="_blank" href={getPurchaseHardwareLink(staff.isEnterpriseRelatedStaff())}>
                <SvgIcon src={SvgIconJumpLinkSvg} size={16} style={{ marginRight: 4 }} color="#F96B18" />
                Purchase hardware
              </PurchaseBtn>
            }
          >
            <div className="section account">
              <div className="status">
                <span className="subtitle">Account status</span>
                <span className={classNames('value', printClassName(StripeAccountStatus.mapLabels[account.status]))}>
                  {StripeAccountStatus.mapLabels[account.status]}
                </span>
              </div>
              <div className="summary">
                <div className="item">
                  Payments
                  {renderStatusIcon(account.charges_enabled)}
                </div>
                <div className="item">
                  Payouts
                  {renderStatusIcon(account.payouts_enabled)}
                </div>
              </div>
              {showAccountStatusMessage && (
                <div
                  className={classNames(
                    'restricted-reason',
                    showAccountErrorMessage && 'error',
                    showAccountWarnMessage && 'warning',
                  )}
                >
                  <div className="reason-row">
                    <ImgIcon
                      src={showAccountWarnMessage ? IconIconExclamationYellowFilledSvg : IconIconInfoRedFilledSvg}
                      width={20}
                      style={{ marginRight: 0 }}
                    />
                    <div className="reason moe-whitespace-pre-line">{account.readableRestrictedReason(business)}</div>
                  </div>
                  <Button className={'verify-button'} buttonRadius={'circle'} onClick={handleBizVerificationLinkClick}>
                    Verify now
                  </Button>
                </div>
              )}
              {account.status === StripeAccountStatus.Completed && (
                <div className="update-info-button">
                  <a
                    onClick={(e) => {
                      e.preventDefault();
                      handleBizVerificationLinkClick();
                    }}
                  >
                    Update info
                  </a>
                </div>
              )}
            </div>
            <div className="divider"></div>
            <div className="section future-payouts">
              <div className="subtitle">Future payouts</div>
              <div className="payouts-amount">{business.formatAmountSpecial(futurePayoutsTotal)}</div>
              {payouts.size > 0 && (
                <>
                  <div className="records">
                    {payouts.map((record) => {
                      const { startDay, endDay } = getApartDay(
                        business.localDate((record.createTime as number) * T_SECOND),
                        business.localDate((record.arrivalTime as number) * T_SECOND),
                      );
                      return (
                        <div className="record" key={record.id}>
                          <span className="date">{business.formatDate(startDay)}</span>&ensp;
                          {business.formatAmountSpecial(record.amount as number, { isCents: true })} (Est. arrival date{' '}
                          {business.formatDate(endDay)})
                        </div>
                      );
                    })}
                  </div>
                  <div className="view-more">
                    <a onClick={handleViewMorePayouts}>View more</a>
                  </div>
                </>
              )}
            </div>
            <div className="divider"></div>
            <div
              className="section transaction-activity"
              onClick={() => history.push(PATH_TRANSITION_ACTIVITY.build({ panel: 'history', vendor: 'MoeGoPay' }))}
            >
              <span className="subtitle">Transaction activity</span>
              <section className="indicator">
                {displayTransactionRedDot && <RedDot />}
                <SvgIcon className="tail" src={SvgIconArrowRightRoundEdgeSvg} size={24} />
              </section>
            </div>
          </Card>

          {pricingPermission.enable.has('stripeReader') && isStripeSmartReaderAvailableCountry(business.country) && (
            <Card className="card card-location-and-device" title={'Location and device'}>
              <div className="cell cell-subtitle">
                <div className="cell__title">Location</div>
                <div
                  className="cell__value f-14-gray"
                  onClick={() => {
                    locationSettingModalVisible.open();
                  }}
                >
                  Edit
                </div>
              </div>

              {locationId && (
                <div className="cell cell-completed">
                  <div className="cell__title">Completed</div>
                </div>
              )}

              <div className="divider"></div>

              <div className="cell cell-subtitle">
                <div className="cell__title">Device</div>
              </div>

              <div className="cell cell-reader">
                <div className="cell__title">
                  <span className="icon">
                    <ImgIcon width={50} src={ImagePaymentSmartReaderPng} />
                  </span>
                  Smart reader
                </div>
                <div className="cell__value f-14-gray" onClick={handleStripeSmartReaderSetting}>
                  Setting
                </div>
              </div>
            </Card>
          )}

          <Card
            className="card banking-info-card"
            title={'Banking information'}
            extra={
              <div className={'action-more'} onClick={cardListModalVisible.open}>
                More
              </div>
            }
          >
            {primary && (
              <>
                <div className={'subtitle'}>Primary bank account to receive payout</div>
                <StripeBankAccount bankAccount={primary as BankAccount} />
              </>
            )}
          </Card>
        </div>
        <div className="cards-column">
          <PaymentPreferenceCard className="card payment-preference-card" title={'Payment preference'}>
            {renderPreAuthSettingEntry()}
            {renderProcessingFeeSettingEntry()}
            <p className="!moe-font-bold !moe-leading-[18px] !moe-text-[#333] !moe-mt-[20px] !moe-pt-[24px] !moe-border-0 !moe-border-t !moe-border-[#E6E6E6] !moe-border-solid">
              Tip setting
            </p>
            {smartTipSettingEntry}
            {tipScreenEntry}
          </PaymentPreferenceCard>
          <PayoutSettingsCard />
          <EligibleMarketBanner />
        </div>
      </Loading>

      <ProcessingFeeSettingModal
        visible={processingFeeSettingModalVisible.value}
        onClose={handleProcessingFeeClose}
        onSubmit={handleProcessingFeeSubmitAndClose}
      />

      <SmartTipSettingModal visible={smartTipSettingModalVisible.value} onClose={handleSmartTipSettingClose} />

      <CustomTipScreenModal visible={customTipScreenModalVisible.value} onClose={handleCustomTipScreenClose} />

      <StripeSmartReaderListModal
        visible={smartReaderListModalVisible.value}
        locationId={locationId}
        onClose={smartReaderListModalVisible.close}
      />

      <StripeLocationSettingModal
        visible={locationSettingModalVisible.value}
        onClose={locationSettingModalVisible.close}
      />
    </StripeIntegrationCardsView>
  );
});
