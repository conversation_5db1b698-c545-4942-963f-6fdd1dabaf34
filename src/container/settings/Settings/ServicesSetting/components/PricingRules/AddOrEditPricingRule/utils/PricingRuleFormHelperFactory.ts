import { RecordProps } from '../../../../../../../../store/utils/RecordMap';
import { RuleType } from '@moego/api-web/moego/models/offering/v2/pricing_rule_enums';
import { MultiplePetsPricingRuleFormHelper } from './MultiplePetsPricingRuleFormHelper';
import { MultipleNightsPricingRuleFormHelper } from './MultipleNightsPricingRuleFormHelper';
import { PricingRuleRecord } from '../../../../../../../../store/pricingRule/pricingRule.boxes';
import { LocalPricingRule } from '../PricingRuleSettingContext';
import { BasePricingRuleFormHelper } from './BasePricingRuleFormHelper';
import { PeakDatePricingRuleFormHelper } from './PeakDatePricingRuleFormHelper';

class PricingRuleFormHelperFactory {
  private static instance: PricingRuleFormHelperFactory;
  private helperMap: Map<RuleType, BasePricingRuleFormHelper>;
  private helperCreators: Map<RuleType, () => BasePricingRuleFormHelper>;

  private constructor() {
    this.helperMap = new Map<RuleType, BasePricingRuleFormHelper>();
    this.helperCreators = new Map<RuleType, () => BasePricingRuleFormHelper>([
      [RuleType.PEAK_DATE, () => new PeakDatePricingRuleFormHelper()],
      [RuleType.MULTIPLE_PET, () => new MultiplePetsPricingRuleFormHelper()],
      [RuleType.MULTIPLE_STAY, () => new MultipleNightsPricingRuleFormHelper()],
    ]);
  }

  public static getInstance(): PricingRuleFormHelperFactory {
    if (!PricingRuleFormHelperFactory.instance) {
      PricingRuleFormHelperFactory.instance = new PricingRuleFormHelperFactory();
    }
    return PricingRuleFormHelperFactory.instance;
  }

  private readonly createHelper = (ruleType: RuleType): BasePricingRuleFormHelper => {
    const creator = this.helperCreators.get(ruleType);
    if (!creator) {
      throw new Error(`No form helper creator found for rule type: ${ruleType}`);
    }
    const helper = creator();
    this.helperMap.set(ruleType, helper);
    return helper;
  };

  public readonly getHelper = (ruleType: number): BasePricingRuleFormHelper => {
    const existingHelper = this.helperMap.get(ruleType);
    return existingHelper || this.createHelper(ruleType);
  };

  public readonly initFormValue = (params: {
    ruleType: RuleType;
    pricingRule?: RecordProps<PricingRuleRecord>;
    isActive?: boolean;
  }): Partial<LocalPricingRule> => {
    const helper = this.getHelper(params.ruleType);
    return helper.initFormValue(params);
  };
}

export const pricingRuleFormHelperFactory = PricingRuleFormHelperFactory.getInstance();
