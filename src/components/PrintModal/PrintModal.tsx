import { Button, Modal, type ModalProps, Scroll, Spin, Text, cn } from '@moego/ui';
import { sleep } from 'monofile-utilities/lib/sleep';
import React, { useRef } from 'react';
import { type IReactToPrintProps, useReactToPrint } from 'react-to-print';
import { useUpdate } from 'react-use';
import ImageNothingPng from '../../assets/image/nothing.png';
import { useBool } from '../../utils/hooks/useBool';
import { Condition } from '../Condition';
import { Switch } from '../SwitchCase';
import { useScrollStatus } from './PrintModal.hooks';

export type PrintModalProps = {
  content: React.ReactNode;
  isLoadingContent?: boolean;
  // printContent?: React.ReactNode;
  contentTitle?: React.ReactNode;
  sideContent?: React.ReactNode;
  footerLeftSide?: React.ReactNode;
  isLoadingSideContent?: boolean;
  loadingContentClassName?: string;
  loadingSideContentClassName?: string;
  noDataClassName?: string;
  contentClassName?: string;
  contentBodyClassName?: string;
  containerClassName?: string;
  isDisabled?: boolean;
  isNoData?: boolean;
  noDataDescription?: string;
} & Pick<ModalProps, 'isOpen' | 'onClose' | 'title' | 'footer'> & {
    printProps?: Partial<IReactToPrintProps>;
  };

export function PrintModal(props: PrintModalProps) {
  const printContentRef = useRef(null);
  const contentContainerRef = useRef<HTMLDivElement>(null);
  const settingsContainerRef = useRef<HTMLDivElement>(null);
  const printLoading = useBool(false);

  const {
    content,
    isLoadingContent,
    contentTitle,
    sideContent,
    isLoadingSideContent,
    loadingContentClassName,
    loadingSideContentClassName,
    noDataClassName,
    contentClassName,
    contentBodyClassName,
    containerClassName,
    title,
    isOpen,
    onClose,
    printProps,
    footerLeftSide,
    isDisabled,
    isNoData,
    noDataDescription = 'No data',
    footer,
  } = props;
  const update = useUpdate();
  // const printContent = props.printContent ?? content;
  const contentContainerScrollStatus = useScrollStatus(contentContainerRef, [isOpen]);
  const settingsContainerScrollStatus = useScrollStatus(settingsContainerRef, [isOpen]);

  const getLastScrollStatus = () => {
    const isAllNonScrollable = !contentContainerScrollStatus.scrollable && !settingsContainerScrollStatus.scrollable;
    const onlyOneCanScroll =
      (contentContainerScrollStatus.scrollable && !settingsContainerScrollStatus.scrollable) ||
      (settingsContainerScrollStatus.scrollable && !contentContainerScrollStatus.scrollable);

    if (isAllNonScrollable) return 'non-scrollable';
    if (onlyOneCanScroll) {
      const scrollStatus = contentContainerScrollStatus.scrollable
        ? contentContainerScrollStatus
        : settingsContainerScrollStatus;
      return scrollStatus.isInitial ? 'initial' : scrollStatus.isFinished ? 'finished' : 'inprogress';
    }

    if (contentContainerScrollStatus.isInitial && settingsContainerScrollStatus.isInitial) return 'initial';
    if (contentContainerScrollStatus.isFinished && settingsContainerScrollStatus.isFinished) return 'finished';
    return 'inprogress';
  };

  const scrollStatus = getLastScrollStatus();

  const print = useReactToPrint({
    ...printProps,
    content: () => printContentRef.current,
    onBeforePrint: () => {
      printLoading.open();
      printProps?.onBeforePrint?.();
    },
    onBeforeGetContent: () => {
      printLoading.open();
      printProps?.onBeforeGetContent?.();
    },
    onPrintError: (...args) => {
      printLoading.close();
      printProps?.onPrintError?.(...args);
    },
    onAfterPrint: () => {
      printLoading.close();
      printProps?.onAfterPrint?.();
    },
    bodyClass: 'light-theme core-theme',
  });

  const handlePrint = async () => {
    printLoading.open();
    update();
    await sleep(500);
    print?.();
  };

  const renderContent = () => {
    if (isLoadingContent) {
      return (
        <div
          className={cn(
            'moe-flex moe-flex-1 moe-justify-center moe-items-center moe-bg-neutral-sunken-0',
            loadingContentClassName,
          )}
        >
          <Spin isLoading />
        </div>
      );
    }

    if (isNoData) {
      return (
        <div
          className={cn(
            'moe-flex moe-flex-1 moe-justify-center moe-items-center moe-flex-col moe-bg-neutral-sunken-0',
            noDataClassName,
          )}
        >
          <img src={ImageNothingPng} className="moe-w-[140px] moe-h-[140px]"></img>
          <Text variant="small" className="moe-mt-s">
            {noDataDescription}
          </Text>
        </div>
      );
    }

    return (
      <Scroll
        classNames={{
          base: cn('moe-overflow-hidden moe-flex-1 moe-bg-neutral-sunken-0 moe-flex', contentClassName),
          viewPort: 'moe-flex-1',
          scrollbar: 'moe-pr-[2px] moe-w-[8px] moe-py-none',
          thumb: '!moe-w-[0px]',
        }}
        viewportRef={contentContainerRef}
      >
        <div className={cn('moe-mx-[44px] moe-my-8px-500', contentBodyClassName)}>
          <Condition if={contentTitle}>
            <div className="moe-mt-s">{contentTitle}</div>
          </Condition>
          <div ref={printContentRef} className="moe-font-manrope">
            {content}
          </div>
          {/* Sam: 本来是要用这个的切去掉 content 的 div wrapper，但是 content 内部可能有局部数据，例如 appt card info -- appt info 里面的 checkbox，暂时没有好的处理方式。 */}
          {/* <div className="moe-absolute moe-left-[999999px] moe-top-[999999px]">
            <div ref={ref}>{printContent}</div>
          </div> */}
        </div>
      </Scroll>
    );
  };

  const renderSideContent = () => {
    return (
      <Condition if={sideContent}>
        {/* TODO(SAM)：使用 scroll 组件会抖动，导致内容跑不见了。 */}
        {/* <Scroll
          classNames={{
            base: 'moe-overflow-hidden moe-flex',
            viewPort: 'moe-flex-1',
            scrollBar: 'moe-pr-[2px] moe-w-[8px] moe-py-none',
            scrollThumb: '!moe-w-[4px]',
          }}
        > */}
        <div className="moe-overflow-y-auto moe-relative" ref={settingsContainerRef}>
          <Switch shortCircuit>
            <Switch.Case if={isLoadingSideContent}>
              <div className={cn('moe-flex moe-justify-center moe-mt-xl moe-w-[200px]', loadingSideContentClassName)}>
                <Spin isLoading />
              </div>
            </Switch.Case>
            <Switch.Case else>{<div>{sideContent}</div>}</Switch.Case>
          </Switch>
        </div>
        {/* </Scroll> */}
      </Condition>
    );
  };

  const renderFooter = () => {
    if (footer) return footer;
    return (
      <div className="moe-flex moe-justify-between moe-items-center moe-w-full">
        <div>{footerLeftSide}</div>
        <div className="moe-flex moe-items-center">
          <Button onPress={onClose} variant="secondary">
            Cancel
          </Button>
          <Button onPress={handlePrint} isDisabled={isDisabled} isLoading={printLoading.value} className="moe-ml-s">
            Print
          </Button>
        </div>
      </div>
    );
  };

  return (
    <Modal
      title={title}
      isOpen={isOpen}
      onClose={onClose}
      classNames={{
        container: cn(`moe-w-auto moe-min-w-[1082px] moe-min-h-[400px] ${containerClassName}`),
        body: cn('moe-p-none moe-border-white moe-border-b moe-border-t moe-relative', {
          'moe-border-b-divider': scrollStatus === 'initial',
          'moe-border-t-divider moe-border-b-divider': scrollStatus === 'inprogress',
          'moe-border-t-divider': scrollStatus === 'finished',
        }),
      }}
      autoCloseOnConfirm={false}
      footer={renderFooter()}
    >
      <div className="moe-flex moe-flex-1 moe-h-full">
        {renderContent()}
        {renderSideContent()}
      </div>
    </Modal>
  );
}
