import { useDispatch, useSelector, useStore } from 'amos';
import dayjs from 'dayjs';
import { apptAddOnMapBox, ApptAddOnRecord, apptServiceMapBox, ApptServiceRecord } from '../../../store/appt.boxes';
import { selectApptPetAddon, selectApptPetService, selectMainServiceInAppt } from '../../../store/appt.selectors';
import { useEffect } from 'react';
import { type ValuesType } from 'utility-types';
import {
  type ServiceDetailFormFields,
  type ServiceDetailFormFieldsAddon,
  type ServiceDetailFormFieldsService,
} from '../SelectServiceDetail.type';
import { type ValueType } from '../../../../../types/common';
import { type ApptAddOnInfoRecord, type ApptPetServiceItem, ApptServiceScene } from '../../../store/appt.types';
import { setAddonForPet } from '../../../store/appt.actions';
import { getDisabledDays } from '../../../../../components/DateType/DateType.utils';
import { ServiceType } from '../../../../../store/service/category.boxes';
import { matchApptServiceScene } from '../../../store/appt.options';
import { isNil, isUndefined } from 'lodash';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type useForm } from '@moego/ui';

export interface ServiceDetailFormDataSyncParams {
  appointmentId: string;
  serviceItemType: ServiceItemType;
  petIds?: number[];
  form: ReturnType<typeof useForm<ServiceDetailFormFields>>;
  petServiceAddOnList: ApptPetServiceItem[];
}

/**
 * 处理 store 同步 form data 的部分
 * 考虑点：这里的 form 只用做校验，实际更改是基于 store 做的，所以通过 useEffect 来同步
 */
export const useServiceDetailFormDataSync = (params: ServiceDetailFormDataSyncParams) => {
  const { appointmentId, serviceItemType, petIds, form, petServiceAddOnList } = params;
  const dispatch = useDispatch();
  const store = useStore();
  const [appServiceMap, apptAddonMap, mainService] = useSelector(
    apptServiceMapBox,
    apptAddOnMapBox,
    selectMainServiceInAppt(appointmentId),
  );

  useEffect(() => {
    const setFormValue = (
      key: string,
      v?: ValuesType<ServiceDetailFormFieldsService | ServiceDetailFormFieldsAddon>,
      validate?: boolean,
    ) => {
      form.setValue(key, v as ValueType<ServiceDetailFormFields>, {
        shouldValidate: !isUndefined(validate) ? validate : Boolean(v),
      });
    };

    const resetInvalid = (associatedId: string, s: ApptPetServiceItem) => {
      const { startDate, specificDates } = store.select(selectApptPetAddon(appointmentId, s.id));
      const associatedService = store.select(selectApptPetService(appointmentId, associatedId));
      const params = {
        minDate: associatedService.startDate,
        maxDate: associatedService.endDate,
        specificDates: associatedService.specificDates,
      };

      const changeAddon = (newVal: Partial<ApptAddOnInfoRecord>) => {
        dispatch(setAddonForPet(appointmentId, s.id, newVal));
      };

      /**
       * 需要注意 addOn 的 startDate 的预填逻辑，
       * Daycare / Grooming 等单日服务的 addOn 的 startDate 不需要填写，但后端仍依赖，添加阶段 addOn 预填的 startDate 会在 main service 的 date 改变时清空
       */
      if (getDisabledDays(dayjs(startDate), params)) {
        changeAddon({ startDate: undefined });
      }
      if (specificDates.some((v) => getDisabledDays(dayjs(v), params))) {
        changeAddon({
          specificDates: specificDates.filter((v) => !getDisabledDays(dayjs(v), params)),
        });
      }
    };

    petServiceAddOnList.forEach((s) => {
      const key = ApptServiceRecord.createOwnId(appointmentId, s.id);

      if (s.serviceType === ServiceType.Service) {
        const apptPetService = store.select(selectApptPetService(appointmentId, s.id));
        const {
          startDate,
          startTime,
          endDate,
          feedings,
          medications,
          lodgingId,
          lodgingName,
          staffId,
          specificDates,
          dateType,
          endTime,
          splitLodgings,
        } = apptPetService;

        // Daycare 如果开始时间大于结束时间，则不显示结束时间
        const endTimeValue =
          serviceItemType === ServiceItemType.DAYCARE && startTime && endTime && startTime > endTime ? null : endTime;

        setFormValue(`${key}.startDate`, startDate ? dayjs(startDate) : null);
        setFormValue(`${key}.startTime`, isUndefined(startTime) ? null : setTime(startDate, startTime));
        setFormValue(`${key}.endDate`, endDate ? dayjs(endDate) : null);
        setFormValue(`${key}.endTime`, isNil(endTimeValue) ? null : setTime(endDate, endTimeValue));
        setFormValue(`${key}.feedings`, feedings);

        if (matchApptServiceScene(ApptServiceScene.ServiceSplitLodgings, { serviceItemType: s.serviceItemType })) {
          setFormValue(
            `${key}.splitLodgings`,
            splitLodgings.map((item) => ({
              ...item,
              endDate: isNil(item.endDate) ? null : dayjs(item.endDate),
              endTime: isUndefined(item.endTime) ? null : setTime(item.endDate, item.endTime),
              startDate: isNil(item.startDate) ? null : dayjs(item.startDate),
              startTime: isUndefined(item.startTime) ? null : setTime(item.startDate, item.startTime),
            })),
            false,
          );
        }

        setFormValue(`${key}.lodgingId`, lodgingId);
        setFormValue(`${key}.lodgingName`, lodgingName);
        setFormValue(`${key}.medications`, medications);
        setFormValue(`${key}.staffId`, staffId);
        setFormValue(`${key}.dateType`, dateType);
        setFormValue(
          `${key}.specificDates`,
          specificDates?.map((d) => dayjs(d)),
        );
      } else {
        const { startDate, startTime, staffId, dateType, specificDates, associatedId } = store.select(
          selectApptPetAddon(appointmentId, s.id),
        );

        const key = ApptAddOnRecord.createOwnId(appointmentId, s.id);
        setFormValue(`${key}.startDate`, startDate ? dayjs(startDate) : null);
        setFormValue(`${key}.startTime`, isUndefined(startTime) ? null : setTime(startDate, startTime));
        setFormValue(`${key}.staffId`, staffId);
        setFormValue(`${key}.dateType`, dateType);
        setFormValue(
          `${key}.specificDates`,
          specificDates?.map((d) => dayjs(d)),
        );

        if (associatedId) {
          resetInvalid(associatedId, s);
        }
      }
    });
  }, [
    petServiceAddOnList.length,
    petIds?.length,
    serviceItemType,
    appServiceMap,
    apptAddonMap,
    appointmentId,
    mainService.serviceItemType,
  ]);
};

const setTime = (d?: string, t = 0) => {
  return dayjs(d || dayjs()).setMinutes(t);
};
