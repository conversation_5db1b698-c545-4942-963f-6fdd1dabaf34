import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useSelector } from 'amos';
import React, { memo } from 'react';
import { toastApi } from '../../../../../../../components/Toast/Toast';
import {
  getInitAvailableSettings,
  getOBStaffAvailability,
  getOnlineBookingGroomingAvailabilitySettings,
  submitAvailability,
} from '../../../../../../../store/onlineBooking/actions/private/availabilityGrooming.actions';
import {
  selectDraftAvailabilitySettings,
  selectGroomingDraftAvailabilityDirty,
} from '../../../../../../../store/onlineBooking/settings/availabilityGrooming.selectors';
import { getPetTypeList } from '../../../../../../../store/pet/petType.actions';
import { abortNavigation } from '../../../../../../../utils/abortNavigation';
import { useBizIdReadyEffect } from '../../../../../../../utils/hooks/useBizIdReadyEffect';
import { useBool } from '../../../../../../../utils/hooks/useBool';
import { useSerialCallback } from '../../../../../../../utils/hooks/useSerialCallback';
import { Update } from '../../../components/Update';
import { AcceptClientPet } from '../../components/AcceptClientPet/AcceptClientPet';
import { useValidateArrivalWindow } from './hooks/useValidateArrivalWindow';
import { useValidateEndDate } from './hooks/useValidateEndDate';
import { SyncArrivalWindowModal } from './modules/SyncArrivalWindowModal/SyncArrivalWindowModal';
import { useSyncArrivalWindow } from './modules/SyncArrivalWindowModal/useSyncArrivalWindow';
import { TeamSchedule } from './modules/TeamSchedule/TeamSchedule';

export const GroomingAvailability = memo(function GroomingAvailability() {
  const dispatch = useDispatch();
  const [isDirty, settings] = useSelector(selectGroomingDraftAvailabilityDirty(), selectDraftAvailabilitySettings());
  const { isDisableSelectTime } = settings;
  const { isValid, message } = useValidateArrivalWindow();
  const { shouldOfferSyncOption } = useSyncArrivalWindow();
  const { isValid: isEndDateValid, message: endDateInvalidMessage } = useValidateEndDate();

  const syncArrivalWindowModalVisible = useBool(false);
  const submit = useSerialCallback(async () => {
    // 校验 arrival window(如有需要)
    if (!isValid) {
      toastApi.neutral(message);
      abortNavigation();
    }
    const res = await dispatch(submitAvailability());
    if (res) {
      toastApi.success('Availability settings have been saved');
    } else {
      abortNavigation();
    }
  });
  const handleSubmit = useSerialCallback(async () => {
    if (!isEndDateValid) {
      toastApi.error(endDateInvalidMessage);
      abortNavigation();
    }
    if (shouldOfferSyncOption) {
      syncArrivalWindowModalVisible.open();
      abortNavigation();
    }
    await submit();
  });

  useBizIdReadyEffect(() => {
    dispatch([
      // 获取 grooming 相关设置
      getOBStaffAvailability(),
      getOnlineBookingGroomingAvailabilitySettings(),
      getInitAvailableSettings(),
      // 获取 grooming 的 pet type list
      getPetTypeList({
        useNewASApi: false,
      }),
    ]);
  }, []);

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-[40px]">
      <AcceptClientPet serviceItemType={ServiceItemType.GROOMING} />

      <TeamSchedule />

      <SyncArrivalWindowModal
        visible={syncArrivalWindowModalVisible.value}
        onClose={syncArrivalWindowModalVisible.close}
        onProceed={submit}
      />
      <Update
        updateTxt="Update availability settings"
        style={isDisableSelectTime ? { backgroundColor: 'transparent', borderTop: 'none' } : void 0}
        active={isDirty}
        onSubmit={handleSubmit}
      />
    </div>
  );
});
