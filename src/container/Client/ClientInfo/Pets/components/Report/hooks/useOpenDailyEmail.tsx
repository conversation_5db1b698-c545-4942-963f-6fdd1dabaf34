import { type DailyReportConfigDef } from '@moego/api-web/moego/models/appointment/v1/daily_report_defs';
import { Modal } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { calcEmailProps } from '../../../../../../../components/DailyReport/utils/calcEmailProps';
import { useModal } from '../../../../../../../components/Modal/useModal';
import { DailyReportEmail } from '../../../../../../../components/ReportEmail/DailyReportEmail/DailyReportEmail';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import { petMapBox } from '../../../../../../../store/pet/pet.boxes';

export interface DailyEmailPreviewProps {
  onClose?: () => void;
  value: DailyReportConfigDef;
}

const DailyEmailPreview = memo((props: DailyEmailPreviewProps) => {
  const { onClose, value } = props;
  const { report, petId, serviceDate } = value;

  const [business, { petName, petTypeId, avatarPath }] = useSelector(
    selectCurrentBusiness(),
    petMapBox.mustGetItem(Number(petId)),
  );

  const previewProps = calcEmailProps({
    report,
    business: { avatarPath: business.avatarPath, businessName: business.businessName, businessId: String(business.id) },
    pet: { petName, petId: String(petId), petType: petTypeId, avatarPath },
    serviceDate,
  });

  return (
    <Modal isOpen footer={null} onClose={onClose}>
      <DailyReportEmail {...previewProps} />
    </Modal>
  );
});

export const useOpenDailyEmail = () => {
  return useModal(DailyEmailPreview);
};
