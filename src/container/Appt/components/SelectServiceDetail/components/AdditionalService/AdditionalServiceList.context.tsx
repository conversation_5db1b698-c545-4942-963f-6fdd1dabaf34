import React, { createContext, type FC } from 'react';

export const AdditionalServiceListContext = createContext<boolean>(false);

export const AdditionalServiceListProvider: FC = (props) => {
  return <AdditionalServiceListContext.Provider value={true}>{props.children}</AdditionalServiceListContext.Provider>;
};

export const useAdditionalServiceListContext = () => {
  const isInAdditionalServiceList = React.useContext(AdditionalServiceListContext);
  return {
    isInAdditionalServiceList,
  };
};
