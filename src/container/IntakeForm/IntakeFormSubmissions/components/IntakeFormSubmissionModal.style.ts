/*
 * @since 2021-01-04 18:10:33
 * <AUTHOR> <<EMAIL>>
 */
import styled from 'styled-components';
import { Modal } from '../../../../components/Modal/Modal';
import { c_font_weight_bold, c_secondary } from './../../../../style/_variables';

export const IntakeFormSubmissionModalView = styled(Modal)`
  .remove-border-radius {
    border-radius: 0;
    img {
      border-radius: 0;
    }
  }
  .vaccine-row {
    margin-bottom: 4px;
    &:last-child {
      margin-bottom: 0;
    }
    .vaccine-doc-view-button {
      color: ${c_secondary};
      cursor: pointer;
      margin-left: 12px;
      text-decoration: underline;
      font-weight: ${c_font_weight_bold};
    }
  }

  > .modal {
    position: fixed;
    bottom: 105px;
    right: 25px;

    > .modal-header > .modal-title {
      white-space: pre;
      overflow: hidden;
      text-overflow: ellipsis;
      padding-right: 24px;
    }

    > .modal-container {
      height: calc(100% - 55px);
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: stretch;

      > .content {
        flex: 1;
        overflow: auto;
        padding: 12px 24px;

        > .block {
          padding-top: 24px;
          padding-bottom: 8px;
          font-weight: 600;
          font-size: 16px;
          line-height: 22px;
          color: #2a2d34;

          &.border {
            border-top: 1px dashed #e6e6e6;
            margin-top: 16px;
          }
        }

        > .meta {
          display: flex;
          align-items: flex-start;
          font-weight: 600;
          font-size: 14px;
          line-height: 19px;
          margin-bottom: 8px;

          > .label {
            flex: 0 0 139px;
            color: rgba(42, 45, 52, 0.6);
          }

          > .value {
            flex: 1;
            color: #2a2d34;

            > img {
              width: 100%;
              height: auto;
              display: block;
              box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);
            }
          }

          &.gray {
            > .value {
              color: rgba(42, 45, 52, 0.6);
            }
          }

          &.red {
            > .label,
            > .value {
              color: #d0021b;
            }
          }

          &:not(.custom) + .custom {
            padding-top: 8px;
          }

          &.custom {
            display: block;

            > .label,
            > .value {
              padding-bottom: 8px;
            }
          }

          + .block:not(.border) {
            padding-top: 16px;
          }
        }
      }

      > .footer {
        flex-shrink: 0;
        padding: 16px 24px;
        border-top: 1px solid #e6e6e6;
        position: relative;
        .additional-msg {
          display: flex;
          align-items: center;
          span {
            margin-left: 8px;
          }
        }
        .add-wait-list-msg {
          height: 46px;
          position: absolute;
          display: flex;
          align-items: center;
          padding: 0 18px 0 20.4px;
          top: -86px;
          left: 62px;
          background: #ffffff;

          box-shadow: 0px 2px 12px rgba(42, 45, 52, 0.15);
          border-radius: 46px;
          span {
            margin: 0 16px;
          }
          img :last-of-type {
            cursor: pointer;
            margin-left: 0;
          }
        }
        > .actions {
          display: flex;
          justify-content: flex-end;
          margin-top: 15px;
          column-gap: 15px;
        }
      }
    }
  }
`;

export const IntakeFormSubmissionImgPreviewModal = styled(Modal)`
  .modal-preview-img {
    img {
      object-fit: contain;
    }
  }
`;
