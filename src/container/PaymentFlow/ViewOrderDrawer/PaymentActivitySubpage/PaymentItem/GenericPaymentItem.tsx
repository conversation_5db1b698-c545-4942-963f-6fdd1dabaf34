import { RealmType, assertModel } from '@moego/finance-web-kit';
import { memo } from 'react';
import React from 'react';
import { type IPaymentActivityListProps } from '../PaymentActivityList';
import { GeneralItem } from './GeneralItem';
import { RefundItem } from './RefundItem';

export interface IGenericPaymentItemProps {
  item: IPaymentActivityListProps['paymentList'][number];
  handleRefunded: (invoiceId: number) => void;
}

export const GenericPaymentItem = memo(({ item, handleRefunded }: IGenericPaymentItemProps) => {
  if (
    assertModel(item.payment, RealmType.OrderPayment) &&
    (assertModel(item.order, RealmType.OrderV2) || assertModel(item.order, RealmType.OrderV1))
  ) {
    return (
      <GeneralItem
        payment={item.payment}
        order={item.order}
        promotions={item.promotions}
        handleRefunded={handleRefunded}
      />
    );
  } else if (assertModel(item.payment, RealmType.RefundOrderPayment)) {
    return <RefundItem payment={item.payment} order={item.order} />;
  }
  return null;
});
