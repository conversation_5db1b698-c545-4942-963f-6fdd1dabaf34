import {
  type AcceptBookingRequestV2Request,
  type IncompleteDetails,
} from '@moego/api-web/moego/api/online_booking/v1/booking_request_api';
import { Avatar, Form, Heading, Modal, Text, useForm } from '@moego/ui';
import { useSelector } from 'amos';
import { type Dayjs, isDayjs } from 'dayjs';
import React, { memo, useMemo } from 'react';
import { Condition } from '../../../../../../components/Condition';
import { petMapBox } from '../../../../../../store/pet/pet.boxes';
import { getPetAvatarType } from '../../../../../../utils/BusinessUtil';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import {
  type EvaluationDetailInput,
  type InformationRequireFormRecord,
  type IncompleteDetailFormItems,
  type LodgingItemsDetailInput,
  type StaffTimeDetailInput,
} from './InformationRequireModal.types';
import {
  KEY_FIELD_DIVIDER,
  buildAcceptParamsFormInput,
  extractEvaluationItemKey,
  extractLodgingItemKey,
  extractStaffTimeItemKey,
  getEvaluationItemKey,
  getLodgingItemKey,
  getPetMainServiceFormIncompleteFromItems,
  getStaffTimeItemKey,
  incompleteDetailsToFormItems,
} from './InformationRequireModal.utils';
import { RoomFiledPrefix, SelectRoomForm } from './SelectRoomForm';
import { SelectStaffForm, StaffFiledPrefix, TimeFiledPrefix } from './SelectStaffForm';
import { Evaluation } from './Evaluation/Evaluation';
import { CreateEvaluationInfoPrefix, EvaluationIdPrefix, EvaluationStaffIdPrefix } from './Evaluation/Evaluation.utils';
import { type AcceptBookingRequestV2RequestCreateEvaluationRequest } from '@moego/api-web/moego/service/online_booking/v1/booking_request_service';
import { isUndefined } from 'lodash';
import { ServiceDateTimeText } from './components/ServiceDateTimeText';

export interface InformationRequireModalProps {
  requestId: string;
  onClose?: () => void;
  onSave?: (val: AcceptBookingRequestV2Request) => void;
  defaultLodgingInputList?: LodgingItemsDetailInput[]; // 有 assign 上房间的 pet，会填充默认值
  incompleteDetails: IncompleteDetails;
  useMergedGroomingDetail: boolean;
}

export const InformationRequireModal = memo<InformationRequireModalProps>((props) => {
  const informationRequireForm = useForm<InformationRequireFormRecord>();
  const { incompleteDetails, onClose, onSave, requestId, defaultLodgingInputList, useMergedGroomingDetail } = props;
  const [petMap] = useSelector(petMapBox);

  const [incompleteFromItemsPetMap, incompleteFromItems, totalDetailsLength] = useMemo(() => {
    const petMap = new Map<string, IncompleteDetailFormItems>();
    const incompleteFromItems = incompleteDetailsToFormItems(incompleteDetails, useMergedGroomingDetail);
    const { lodgingItems, staffTimeItems, evaluationItems } = incompleteFromItems;

    /**
     * 数据按 petId 来归类
     */
    lodgingItems.forEach((item) => {
      const key = item.petId;
      const current = petMap.get(key) || { lodgingItems: [], staffTimeItems: [], evaluationItems: [] };
      current.lodgingItems.push(item);
      petMap.set(key, current);
    });
    staffTimeItems.forEach((item) => {
      const key = item.petId;
      const current = petMap.get(key) || { lodgingItems: [], staffTimeItems: [], evaluationItems: [] };
      current.staffTimeItems.push(item);
      petMap.set(key, current);
    });
    evaluationItems.forEach((item) => {
      const key = item.petId;
      const current = petMap.get(key) || { lodgingItems: [], staffTimeItems: [], evaluationItems: [] };
      current.evaluationItems.push(item);
      petMap.set(key, current);
    });
    const totalDetailsLength = lodgingItems.length + staffTimeItems.length + evaluationItems.length;
    return [petMap, incompleteFromItems, totalDetailsLength];
  }, [incompleteDetails, useMergedGroomingDetail]);

  const handleConfirm = useSerialCallback(async () => {
    await informationRequireForm.handleSubmit(async (values) => {
      const lodgingInputList: LodgingItemsDetailInput[] = [];
      const staffTimeInputList: StaffTimeDetailInput[] = [];
      const evaluationInputList: EvaluationDetailInput[] = [];
      const evaluationInputMap: Record<string, EvaluationDetailInput> = {};
      const createEvaluationInputList: AcceptBookingRequestV2RequestCreateEvaluationRequest[] = [];
      const staffTimeInputMap: Record<
        string,
        {
          staffId: string;
          startTime: number;
        }
      > = {};
      Object.keys(values).forEach((key) => {
        const [prefix, ...rest] = key.split(KEY_FIELD_DIVIDER);
        const itemKey = rest.join(KEY_FIELD_DIVIDER);
        switch (prefix) {
          case RoomFiledPrefix: {
            const { serviceDetailId, careType } = extractLodgingItemKey(itemKey);
            lodgingInputList.push({
              serviceDetailId,
              careType,
              lodgingUnitId: values[key] as string,
            });
            break;
          }

          case StaffFiledPrefix:
            staffTimeInputMap[itemKey] = {
              ...staffTimeInputMap[itemKey],
              staffId: values[key] as string,
            };
            break;

          case TimeFiledPrefix:
            staffTimeInputMap[itemKey] = {
              ...staffTimeInputMap[itemKey],
              startTime: isDayjs(values[key]) ? (values[key] as Dayjs)?.getMinutes() : 0,
            };
            break;

          case EvaluationIdPrefix:
            evaluationInputMap[itemKey] = {
              ...evaluationInputMap[itemKey],
              evaluationId: values[key] as string,
            };
            break;

          case EvaluationStaffIdPrefix:
            evaluationInputMap[itemKey] = {
              ...evaluationInputMap[itemKey],
              staffId: values[key] as string,
            };
            break;

          case CreateEvaluationInfoPrefix: {
            if (!isUndefined(values[key])) {
              createEvaluationInputList.push({
                ...(values[key] as AcceptBookingRequestV2RequestCreateEvaluationRequest),
              });
            }
            break;
          }
        }
      });

      // 依赖一个假设：staffTimeInputMap 的 staffId 和 startTime 是成对出现的，业务上可以保证，不作其他处理
      Object.keys(staffTimeInputMap).forEach((key) => {
        const { careType, serviceType, serviceDetailId } = extractStaffTimeItemKey(key);
        staffTimeInputList.push({
          careType,
          serviceType,
          serviceDetailId,
          ...staffTimeInputMap[key],
        });
      });

      Object.keys(evaluationInputMap).forEach((key) => {
        const { serviceDetailId } = extractEvaluationItemKey(key);
        evaluationInputList.push({
          ...evaluationInputMap[key],
          id: serviceDetailId,
        });
      });

      onSave?.({
        id: requestId,
        ...buildAcceptParamsFormInput(incompleteFromItems, {
          lodgingInputList,
          staffTimeInputList,
          evaluationInputList,
          createEvaluationInputList,
        }),
      });
    })();
  });

  return (
    <Modal
      isOpen
      title={totalDetailsLength > 1 ? 'Edit service details' : 'Edit service detail'}
      size="s"
      onConfirm={handleConfirm}
      onClose={onClose}
      autoCloseOnConfirm={false}
      confirmButtonProps={{
        isLoading: handleConfirm.isBusy(),
      }}
      className="moe-max-h-[564px]"
    >
      <Text variant="small" className="moe-mb-[24px]">
        To schedule this booking request, please complete the information below.
      </Text>
      <Form form={informationRequireForm} classNames={{ footer: 'moe-hidden' }}>
        <>
          {[...incompleteFromItemsPetMap.keys()].map((petId, index) => {
            const formItems = incompleteFromItemsPetMap.get(petId);
            if (!formItems) return null;
            const { lodgingItems, staffTimeItems, evaluationItems } = formItems;
            const pet = petMap.mustGetItem(+petId);
            const petName = pet.petName;
            const { service, serviceDetail } = getPetMainServiceFormIncompleteFromItems(formItems) || {};
            return (
              <React.Fragment key={petId}>
                <Condition if={index > 0}>
                  <div className="moe-w-full moe-border-t moe-border-divider"></div>
                </Condition>
                <div key={petId} className="moe-flex moe-flex-col moe-gap-y-s">
                  <div className="moe-flex moe-items-center moe-gap-x-xs">
                    <Avatar.Pet src={pet.avatarPath} type={getPetAvatarType(pet.petTypeId)} size="xs" color="neutral" />
                    <Heading size="5">{petName}</Heading>
                    <Text variant="small" className="moe-text-tertiary">
                      ({pet.breed})
                    </Text>
                  </div>
                  {service && serviceDetail && <ServiceDateTimeText service={service} serviceDetail={serviceDetail} />}
                  {evaluationItems?.map((item) => {
                    const key = getEvaluationItemKey(item);
                    return (
                      <Evaluation
                        key={key}
                        itemKey={key}
                        petId={petId}
                        form={informationRequireForm}
                        careType={item.careType}
                        service={item.service}
                        serviceDetail={item.serviceDetail}
                        evaluation={item.evaluation}
                      />
                    );
                  })}
                  {lodgingItems.map((item) => {
                    const key = getLodgingItemKey(item);
                    return (
                      <SelectRoomForm
                        key={key}
                        itemKey={key}
                        serviceDetail={item}
                        petId={item.petId}
                        form={informationRequireForm}
                        defaultValue={
                          defaultLodgingInputList?.find((lodging) => lodging.serviceDetailId === item.serviceDetailId)
                            ?.lodgingUnitId
                        }
                      />
                    );
                  })}
                  {staffTimeItems.map((item) => {
                    const key = getStaffTimeItemKey(item);
                    return (
                      <SelectStaffForm key={key} itemKey={key} serviceDetail={item} form={informationRequireForm} />
                    );
                  })}
                </div>
              </React.Fragment>
            );
          })}
        </>
      </Form>
    </Modal>
  );
});
