import { captureException } from '@sentry/browser';
import {
  loadStripeTerminal,
  type IPaymentIntent,
  type ISdkManagedPaymentIntent,
  type Terminal,
  type TerminalProps,
} from '@stripe/terminal-js';
import { useDispatch } from 'amos';
import { useCallback, useEffect, useRef } from 'react';
import { createStripeTerminalConnectToken } from '../../../store/stripe/stripeTerminal.actions';

export interface CollectPaymentMethodResponse {
  paymentIntent: ISdkManagedPaymentIntent;
}

export interface ProcessPaymentResponse {
  paymentIntent: IPaymentIntent;
}

let terminalInstance: Terminal | null = null;
const terminalEventTarget = new EventTarget();

const DISCONNECT_EVENT = 'disconnect';

export const READER_USED_IN_PARALLEL_ERROR = 'The POS is no longer authenticated.';
export const CANCEL_PAY_CODE = 'canceled';

// This hook could be called multiple times:
// - In "getStripe", "loadStripeTerminal" can be called multiple times, and "StripeTerminal.create" will be called only
//   once and then be saved to "terminalInstance".
// But only the very first call will call the "StripeTerminal.create" with the passed in options. Thus, the later calls
// are only for getting terminal instance and registering "onUnexpectedReaderDisconnect" callbacks.
export function useStripeTerminal(options?: Partial<TerminalProps>) {
  const terminal = useRef<Terminal>();
  const dispatch = useDispatch();
  const fetchConnectToken = useCallback(async () => {
    const token = await dispatch(createStripeTerminalConnectToken());
    return token;
  }, []);

  const getStripe = useCallback(async () => {
    const StripeTerminal = await loadStripeTerminal();
    if (terminalInstance) {
      terminal.current = terminalInstance;
    } else {
      terminal.current = StripeTerminal?.create({
        ...options,
        onFetchConnectionToken: fetchConnectToken,
        onUnexpectedReaderDisconnect: (event) => {
          terminalEventTarget.dispatchEvent(Object.assign(new Event(DISCONNECT_EVENT), event));
          captureException(event.error);
        },
      });
    }

    terminalInstance = terminal.current || null;
  }, []);

  useEffect(() => {
    getStripe();
  }, []);
  useEffect(() => {
    const callback = options?.onUnexpectedReaderDisconnect as EventListener | undefined;
    callback && terminalEventTarget.addEventListener(DISCONNECT_EVENT, callback);
    return () => {
      callback && terminalEventTarget.removeEventListener(DISCONNECT_EVENT, callback);
    };
  }, [options?.onUnexpectedReaderDisconnect]);

  return terminal;
}
