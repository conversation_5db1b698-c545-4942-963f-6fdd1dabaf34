import { default as chroma } from 'chroma-js';

export function generateThemePattern(theme: string) {
  const primary = chroma(theme);
  // focus = L(theme color) - 10%
  // const focus = chroma(theme).set('hsl.l', Math.max(0.05, primary.get('hsl.l') - 0.1));
  // light = H(theme color), S = 92, L = 95
  const light = chroma(theme).set('hsl.s', 0.92).set('hsl.l', 0.95);
  // Disabled color: 为 Primary + 60% 白色蒙版
  // const disabled = chroma.mix(theme, '#fff', 0.6, 'rgb');

  const whiteContrastAPCA = Math.abs(chroma.contrastAPCA(theme, '#fff'));
  const blackContrastAPCA = Math.abs(chroma.contrastAPCA(theme, '#333'));
  const text = whiteContrastAPCA > 54.8 || whiteContrastAPCA > blackContrastAPCA ? '#fff' : '#333';

  const [r, g, b] = primary.rgb();
  const shadow = `rgba(${[r, g, b, 0.45].join(', ')})`;
  return {
    primary: primary.hex(),
    text: chroma(text).hex(),
    light: light.hex(),
    shadow: `0 2px 6px -1px ${shadow}, 0px 8px 24px -2px ${shadow}`,
  };
}

/**
 * 根据背景色反转颜色，获取对应的文字颜色
 */
export function getContrastTxtColor(bgColor: string, defaultTxtColor: string = '#333333') {
  let txtColor = '';
  try {
    txtColor = generateThemePattern(bgColor).text;
  } catch (e) {
    console.error(e);
    txtColor = defaultTxtColor;
  }
  return txtColor;
}
