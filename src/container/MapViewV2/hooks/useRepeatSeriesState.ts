import { DATE_FORMAT_EXCHANGE } from '@moego/reporting';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { useCallback, useMemo } from 'react';
import { ID_ANONYMOUS, isNormal } from '../../../store/utils/identifier';
import { useRepeatSeriesData } from '../../Appt/components/RepeatSeries/hooks/useRepeatSeriesData';
import {
  type RepeatPreviewInfoRecord,
  ticketPreviewInfoMapBox,
} from '../../Appt/components/RepeatSeries/store/repeatSeries.boxes';
import { selectTicketRepeatPreviewState } from '../../Appt/components/RepeatSeries/store/repeatSeries.selectors';
import { useMapViewConfig } from './useMapViewConfig';

// 参考：src/container/Calendar/latest/ApptCalendar/components/RepeatSeries/RepeatSeriesPreviewDrawer/RepeatSeriesPreviewDrawer.tsx
export const useRepeatSeriesState = () => {
  const [ticketRepeatPreviewState, ticketMap] = useSelector(selectTicketRepeatPreviewState(), ticketPreviewInfoMapBox);
  const { previewInfoListWithoutHistory, staffIdList } = useRepeatSeriesData();
  const { drawerVisible, selectedItemId } = ticketRepeatPreviewState;

  const resolveSelectedItem = useCallback(
    (itemId: string) => {
      const target = ticketMap.mustGetItem(itemId);
      const staffId = getRepeatItemStaffId(target);
      return {
        date: target.draftDateTime?.format(DATE_FORMAT_EXCHANGE) ?? target.date,
        startTime: target.draftDateTime?.getMinutes() ?? target.startTime,
        staffId: getRepeatItemStaffId(target),
        /**
         * 当前选择项的 staffId 列表
         * - single staff 切换 staff 只会变 draftStaffId，原 staffIdList 不会变，这里手动纠正回来；
         * - multi-staff 不能切换 staff，所以没有这个问题，故这里没有其他处理。
         */
        staffIdList: isMultiStaff(target) ? target.staffIdList : [staffId],
      };
    },
    [ticketMap],
  );

  const data = useMemo(() => {
    return {
      visible: drawerVisible,
      selectedItemId,
      // ss 后 preview 项数据会变为 ID_ANONYMOUS
      selectedItem: resolveSelectedItem(selectedItemId),
      // 所有的 repeat 项选择的 staffId
      staffIdList,
      previewInfoItems: previewInfoListWithoutHistory.map((itemId) => {
        return ticketMap.mustGetItem(itemId);
      }),
    };
  }, [drawerVisible, selectedItemId, staffIdList, previewInfoListWithoutHistory, resolveSelectedItem]);

  return {
    data,
  };
};

export const getRepeatItemStaffId = (item: RepeatPreviewInfoRecord) => {
  return isNormal(item.draftStaffId) ? item.draftStaffId : (item.staffIdList[0] ?? ID_ANONYMOUS);
};

function isMultiStaff(item: RepeatPreviewInfoRecord) {
  return item.staffIdList.length > 1;
}

/**
 * 在 repeat preview 下，返回当前正在 preview 项的信息
 * 1. 选择了某一个 repeat 项，返回该已选择的项
 * 2. 未选择项，返回与 mapview 时间对应的的项
 * 3. 否则返回第一个项
 * @returns
 */
export function useMatchRepeatItem():
  | {
      staffId: number;
      staffIdList: number[];
      startTime: number;
      date: string;
    }
  | undefined {
  const {
    data: { visible, selectedItemId, selectedItem, previewInfoItems },
  } = useRepeatSeriesState();
  const [{ selectedDate }] = useMapViewConfig();

  return useMemo(() => {
    if (!visible) {
      return undefined;
    }

    if (selectedItemId && selectedItem) {
      const { staffId, staffIdList, startTime, date } = selectedItem;
      return { staffId, staffIdList, startTime, date };
    }

    // 匹配一个与当前日期匹配的数据
    let targetItem: RepeatPreviewInfoRecord = previewInfoItems.find((item) =>
      dayjs(selectedDate).isSame(item.date, 'day'),
    )!;
    if (!targetItem) {
      // 返回第一个
      targetItem = previewInfoItems[0];
    }

    const { staffIdList, date, startTime } = targetItem;
    return { staffIdList, staffId: staffIdList[0], startTime, date };
  }, [visible, selectedItemId, selectedItem, previewInfoItems, selectedDate]);
}
