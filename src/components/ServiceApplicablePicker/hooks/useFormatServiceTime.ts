import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { T_MINUTE } from 'monofile-utilities/lib/consts';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { type ServiceEntry } from '../types/serviceEntry';
import { getDateTypeDisplayInfo, getFormattedDateTypeDateLabel } from '../../DateType/DateType.utils';

export function useFormatServiceTime() {
  const [business] = useSelector(selectCurrentBusiness());

  return useLatestCallback(
    (record: { startDate?: string | undefined; startTime?: number | undefined; onlyTime?: boolean }) => {
      const { startDate, startTime, onlyTime = false } = record;
      const startDayTime = dayjs().setMinutes(startTime || 0);
      const date = business.formatDate(startDate);
      const time = business.formatTime(startDayTime);
      return onlyTime ? time : [date, time].join('\u{20}');
    },
  );
}

export function useFormatDaycareSchedule(mainServiceItemType?: ServiceItemType) {
  const [business] = useSelector(selectCurrentBusiness());

  return useLatestCallback((service: ServiceEntry) => {
    const { startDate, startTime = 0, specificDates, dateType } = service;

    const date = business.formatDate(startDate);
    const time = business.formatFixedTime(startTime * T_MINUTE);

    const withTime = (s = '') => `${s}, ${time}`;

    const { showDateTypeLabel, isSpecificDate } = getDateTypeDisplayInfo({
      dateType,
      mainServiceItemType,
      isRequireDedicatedStaff: false,
    });

    if (!showDateTypeLabel) {
      return withTime(date);
    }

    const dateTypeTxt = getFormattedDateTypeDateLabel(business, dateType, isSpecificDate, specificDates);

    return withTime(dateTypeTxt);
  });
}
