import { useDispatch, useSelector } from 'amos';
import { isDayjs } from 'dayjs';
import { sleep } from 'monofile-utilities/lib/sleep';
import {
  calendarAddBlockTimeConfigBox,
  calendarQuickAddApptVisible,
  calendarSelectedDate,
} from '../../../../../store/calendarLatest/calendar.boxes';
import { isBlockDrawerEditPreset } from '../../../../../store/calendarLatest/calendar.types';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { selectApptStartAndEndTime } from '../../../../Appt/store/appt.selectors';
import { useCheckDateInSelectedRangeDates } from '../../hooks/useSelectedRangeDates';
import { getCalendarScroller, getEventCardBoxEle } from '../ApptCalendar.utils';
import { store } from '../../../../../provider';
import { selectIsEnableSlotCalender } from '../../../../../store/calendarLatest/calendar.selectors';
import { ApptNormalDrawerWidthV3 } from '../../AwesomeCalendar.utils';

/**
 * 导航到card的动效
 */
export function useNavigateToCard() {
  return useLatestCallback(async (cardQuery: string = '', delay = 150) => {
    await sleep(delay);
    const scroller = getCalendarScroller();
    const elem = getEventCardBoxEle(cardQuery) as HTMLDivElement;

    if (!scroller || !elem) {
      return;
    }
    const isEnableSlotCalender = store.select(selectIsEnableSlotCalender);

    const { left: quickAddCardLeft, top: quickAddCardTop, width, height } = elem.getBoundingClientRect();
    const { left: boxLeft, top: boxTop } = scroller.getBoundingClientRect();
    const scrollViewHeight = scroller.clientHeight;
    const scrollViewWidth = scroller.clientWidth;

    const scrollTop = scroller.scrollTop;
    const scrollLeft = scroller.scrollLeft;

    const cardTop = quickAddCardTop - boxTop;
    const cardLeft = quickAddCardLeft - boxLeft;

    const cardOffsetTop = cardTop + scrollTop;
    const cardOffsetLeft = cardLeft + scrollLeft;

    const cardBottom = cardTop + height;
    const cardRight = cardLeft + width;

    const isYHiddenTop = cardTop < 0;
    const isYHiddenBottom = cardBottom > scrollViewHeight;
    const isYInView = !isYHiddenTop && !isYHiddenBottom;

    const isXHiddenLeft = cardLeft < 0;
    const isXHiddenRight = cardRight > scrollViewWidth;
    const isXInView = !isXHiddenLeft && !isXHiddenRight;

    const scrollParams = { top: scrollTop, left: scrollLeft };

    // 针对 slotCalendar 的偏移量，让 card 能展示更多区域
    const SCROLL_OFFSET = isEnableSlotCalender ? 60 : 0;
    const SCROLL_OFFSET_LEFT = isEnableSlotCalender ? 320 : 0;

    if (!isYInView) {
      scrollParams.top = isYHiddenTop ? cardOffsetTop : cardOffsetTop - scrollViewHeight + height + SCROLL_OFFSET;
    }

    if (!isXInView) {
      scrollParams.left = isXHiddenLeft
        ? cardOffsetLeft
        : cardOffsetLeft - scrollViewWidth + width + SCROLL_OFFSET_LEFT;
    }

    // 处理容器内滚动
    scroller.scrollTo({
      ...scrollParams,
      behavior: 'smooth',
    });

    if (isEnableSlotCalender) {
      // 检查 layout-body 是否有横向滚动条，并处理横向滚动
      const layoutBodyElement = document.querySelector('[data-slot="layout-body"]') as HTMLDivElement;
      const hasHorizontalScrollbar = layoutBodyElement?.scrollWidth > layoutBodyElement?.clientWidth;

      if (hasHorizontalScrollbar) {
        const layoutBodyScrollX = layoutBodyElement.scrollLeft;
        const layoutBodyWidth = layoutBodyElement.clientWidth - ApptNormalDrawerWidthV3;

        // 获取卡片相对于 layout-body 元素的位置
        const layoutBodyRect = layoutBodyElement.getBoundingClientRect();
        const cardInLayoutBodyLeft = quickAddCardLeft - layoutBodyRect.left;
        const cardInLayoutBodyRight = cardInLayoutBodyLeft + width;

        const isCardXHiddenLeft = cardInLayoutBodyLeft < 0;
        const isCardXHiddenRight = cardInLayoutBodyRight > layoutBodyWidth;

        if (isCardXHiddenLeft || isCardXHiddenRight) {
          const LAYOUT_BODY_MARGIN = 20; // 边距
          let targetScrollX = layoutBodyScrollX;

          if (isCardXHiddenLeft) {
            targetScrollX = layoutBodyScrollX + cardInLayoutBodyLeft - LAYOUT_BODY_MARGIN;
          } else if (isCardXHiddenRight) {
            targetScrollX = layoutBodyScrollX + (cardInLayoutBodyRight - layoutBodyWidth) + LAYOUT_BODY_MARGIN;
          }

          layoutBodyElement.scrollTo({
            left: Math.max(0, targetScrollX),
            behavior: 'smooth',
          });
        }
      }
    }
  });
}

// 点击calendar上的锚点，重置成抽屉中的日期，相当于返回第一次设置的时间中
export function useGoBack2DrawerDate() {
  const dispatch = useDispatch();
  const move2CardVisible = useNavigateToCard();
  const checkDateInSelectedRangeDates = useCheckDateInSelectedRangeDates();
  const [quickAddVisible, blockConfig, { startDateTime: appointmentDate }] = useSelector(
    calendarQuickAddApptVisible,
    calendarAddBlockTimeConfigBox,
    selectApptStartAndEndTime,
  );

  const goBack2Date = useLatestCallback(async () => {
    if (!appointmentDate) {
      return;
    }
    if (quickAddVisible) {
      const isInCalendarDate = checkDateInSelectedRangeDates(appointmentDate);
      if (isInCalendarDate) {
        return move2CardVisible();
      }
      dispatch(calendarSelectedDate.setState(appointmentDate));
    }
    const { visibleAddBlockDrawer, preset } = blockConfig;
    const isEditBlock = isBlockDrawerEditPreset(preset);
    if (visibleAddBlockDrawer && !!preset && !isEditBlock && isDayjs(preset.blockStartDate)) {
      const isInCalendarDate = checkDateInSelectedRangeDates(preset.blockStartDate);
      if (isInCalendarDate) {
        return move2CardVisible();
      }
      dispatch(calendarSelectedDate.setState(preset.blockStartDate));
    }
  });
  return goBack2Date;
}
