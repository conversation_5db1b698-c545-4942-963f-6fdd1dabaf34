import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { uniq } from 'lodash';
import React from 'react';
import {
  editingWorkingHourStaffIdBox,
  staffScheduleAreaOverrideMapBox,
} from '../../../../../../../../store/staffSchedule/staffSchedule.boxes';
import { DateOverrideType } from '../../../../../../../../store/staffSchedule/staffSchedule.types';
import { isNormal } from '../../../../../../../../store/utils/identifier';
import { EmptyDateOverride } from '../../EmptyDateOverride';
import { OverrideItem } from './OverrideItem';
import { selectStaffScheduleOverride } from '../../../../../../../../store/staffSchedule/staffSchedule.selectors';

export interface OverridesProps {
  businessId: number;
  type: DateOverrideType;
}
export function OverridesByTime(props: OverridesProps) {
  const { businessId, type } = props;
  const [staffId] = useSelector(editingWorkingHourStaffIdBox);
  const [data, serviceAreaData] = useSelector(
    selectStaffScheduleOverride(staffId),
    staffScheduleAreaOverrideMapBox.mustGetItem(staffId),
  );

  if (!isNormal(staffId)) return null;
  const k = type === DateOverrideType.Ongoing ? 'ongoing' : 'history';

  const workingHourDate = data[k].map((v) => v.overrideDate);
  const workingAreaDate = serviceAreaData[k].map((v) => v.overrideDate);
  const list = uniq(workingHourDate.concat(workingAreaDate)).sort((a, b) => (dayjs(a) > dayjs(b) ? 1 : -1));

  return (
    <>
      {list.length > 0 ? (
        <div className="moe-border-b moe-border-solid moe-border-divider">
          {list.map((v, index) => {
            return <OverrideItem key={index} type={type} date={v} businessId={businessId} />;
          })}
        </div>
      ) : (
        <EmptyDateOverride />
      )}
    </>
  );
}
