import { type BookingCareTypeView } from '@moego/api-web/moego/models/online_booking/v1/ob_customize_care_type_defs';
import { type FormValues } from './types';
import { type ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { isNormal } from '../../../../../../../../../store/utils/identifier';
import { generateUID } from '../../../../../../../../../components/Upload/MoeGoUIUpload';
import { UploadStatus } from '@moego/ui';

export interface TransformFormDataToPayloadOptions {
  id?: string;
  serviceType: ServiceType;
}

export const transformFormDataToPayload = (
  formData: FormValues,
  options: TransformFormDataToPayloadOptions,
): BookingCareTypeView | Omit<BookingCareTypeView, 'id'> => {
  const { id, serviceType } = options;
  const { name, ...rest } = formData;
  return {
    id: isNormal(id) ? id : undefined,
    serviceType,
    name: name.trim(),
    ...rest,
  };
};

export const transformPayloadToFormData = (bookingCareType: BookingCareTypeView): FormValues => {
  const { name, description, icon, image, serviceItemType, applicableServices } = bookingCareType;

  return {
    name,
    description,
    icon,
    image,
    serviceItemType,
    applicableServices,
  };
};

export const transformToUploadItem = (url?: string) =>
  isNormal(url)
    ? [
        {
          url,
          uid: generateUID(0),
          status: UploadStatus.success,
        },
      ]
    : [];
