import { type DateClickArg } from '@fullcalendar/interaction';
import { ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useStore } from 'amos';
import dayjs, { type Dayjs } from 'dayjs';
import { isNil } from 'lodash';
import {
  apptDetailDrawerAddDetailBox,
  apptDetailDrawerBox,
  calendarQuickAddApptFields,
  calendarQuickAddApptVisible,
} from '../../../../../store/calendarLatest/calendar.boxes';
import { serviceMapBox } from '../../../../../store/service/service.boxes';
import { DATE_FORMAT_EXCHANGE } from '../../../../../utils/DateTimeUtil';
import { setAddonForPet, setServiceForPet } from '../../../../Appt/store/appt.actions';
import { matchApptFlowScene, matchApptServiceScene } from '../../../../Appt/store/appt.options';
import {
  selectApptPetAddon,
  selectApptPetService,
  selectApptStartAndEndTime,
  selectMainServiceInAppt,
} from '../../../../Appt/store/appt.selectors';
import {
  ApptFlowScene,
  type ApptPetServiceItem,
  ApptServiceScene,
  CreateApptId,
} from '../../../../Appt/store/appt.types';
import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';

/**
 * 如果已经添加b/d, 再添加grooming 同步preview card数据
 * visibleApptDetail: 编辑详情打开的详情drawer
 * quickAddApptDrawerVisible: 新增时打开的drawer
 * */
export const useDateClickBD = () => {
  const store = useStore();
  const dispatch = useDispatch();

  /**
   * @returns true continue / false stop
   */
  const task = (dateClickInfo: DateClickArg) => {
    const { date, resource } = dateClickInfo;
    const { staffId } = resource!.extendedProps;
    const apptDateTime = dayjs(date);

    const quickAddInfo = store.select(calendarQuickAddApptFields);
    const addDetail = store.select(apptDetailDrawerAddDetailBox);
    const appointmentId = addDetail.appointmentId || CreateApptId;
    const isCreate = appointmentId === CreateApptId;

    const createFlowServices =
      quickAddInfo.stepInfo.payload && 'services' in quickAddInfo.stepInfo.payload
        ? quickAddInfo.stepInfo.payload.services
        : [];
    const services: ApptPetServiceItem[] = isCreate ? createFlowServices : addDetail.services;

    // appt drawer 下，只要进入 service detail，说明有 services ，就可能会 match 到此逻辑
    // 主要是快捷同步 calendar 卡片和右侧 drawer 的数据

    const mainService = store.select(selectMainServiceInAppt(appointmentId));
    const apptStartEndTime = store.select(selectApptStartAndEndTime(appointmentId));
    if (matchApptFlowScene(ApptFlowScene.ApptLodging, mainService.serviceItemType)) {
      if (services.length) {
        services.forEach(({ id, serviceId, serviceType, serviceItemType }) => {
          if (!matchApptServiceScene(ApptServiceScene.ServiceRequireStaff, { serviceItemType })) {
            // 目前 bd 直接加 addon 的情况，即使 required staff 也先不管，因为暂无这个需求，所以不做过度设计
            // 长期来看，加上也是可以的，因为下方有对 addon 是否 require staff 做判断
            return;
          }
          if (serviceType === ServiceType.SERVICE) {
            const service = store.select(selectApptPetService(appointmentId, id));
            dispatch(
              setServiceForPet(
                appointmentId,
                id,
                resolveEventNewSchedule(apptDateTime, staffId, service, apptStartEndTime),
              ),
            );
          } else {
            const { requireDedicatedStaff } = store.select(serviceMapBox.mustGetItem(Number(serviceId)));
            if (requireDedicatedStaff) {
              const addon = store.select(selectApptPetAddon(appointmentId, id));
              dispatch(
                setAddonForPet(
                  appointmentId,
                  id,
                  resolveEventNewSchedule(apptDateTime, staffId, addon, apptStartEndTime),
                ),
              );
            }
          }
        });
        return false;
      }
    }

    return true;
  };
  return {
    isMatch: () => {
      return store.select(apptDetailDrawerBox).visible || store.select(calendarQuickAddApptVisible);
    },
    task,
  };
};

/**
 * 对于本身就有 endDate , endTime 的情况，我们需要重新修正它们
 */
export const resolveEventNewSchedule = (
  newStartDateTime: Dayjs,
  staffId: number,
  params: {
    dateType?: PetDetailDateType;
    serviceTime?: number;
    endDate?: string;
    endTime?: number;
  },
  apptStartEndTime: {
    startDateTime: Dayjs | null;
    endDateTime: Dayjs | null;
  },
) => {
  const { serviceTime, endDate, endTime, dateType: origDateType } = params;

  // first day / last day 在拖拽发生日期变化，需要改为 date point 类型
  let dateType = origDateType;
  const isOutOfFirstDay =
    origDateType === PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY &&
    !newStartDateTime.isSame(apptStartEndTime.startDateTime, 'day');
  const isOutOfLastDay =
    origDateType === PetDetailDateType.PET_DETAIL_DATE_LAST_DAY &&
    !newStartDateTime.isSame(apptStartEndTime.endDateTime, 'day');
  if (isOutOfFirstDay || isOutOfLastDay) {
    dateType = PetDetailDateType.PET_DETAIL_DATE_DATE_POINT;
  }

  let newEndSchedule = undefined;
  if (serviceTime && !isNil(endDate) && !isNil(endTime)) {
    const newEnd = newStartDateTime.add(serviceTime, 'minute');
    newEndSchedule = {
      endDate: newEnd.format(DATE_FORMAT_EXCHANGE),
      endTime: newEnd.getMinutes(),
      dateType,
    };
  }
  return {
    startDate: newStartDateTime.format(DATE_FORMAT_EXCHANGE),
    startTime: newStartDateTime.getMinutes(),
    staffId: String(staffId),
    dateType,
    ...newEndSchedule,
  };
};
