import { Select, Text, cn, useFormContext, useWatch, type Node } from '@moego/ui';
import { useMemoizedFn } from 'ahooks';
import { useSerialCallback } from '@moego/tools';
import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect, useMemo } from 'react';
import { type MembershipModel } from '../../../../store/membership/membership.boxes';
import { getBreedList } from '../../../../store/pet/petBreed.actions';
import { petBreedMapBox } from '../../../../store/pet/petBreed.boxes';
import { selectPetBreeds } from '../../../../store/pet/petBreed.selectors';
import { ID_ANONYMOUS, isNormal } from '../../../../store/utils/identifier';
import { getLongListText } from '../../../../utils/getLongListText';

export interface BreedSelectorProps {
  petTypeId?: number;
  index: number;
  className?: string;
  isDisabled?: boolean;
  isAll?: boolean;
}

export const BreedSelector = memo(function BreedSelector(props: BreedSelectorProps) {
  const { petTypeId = ID_ANONYMOUS, index, className = '', isDisabled, isAll = false } = props;
  const form = useFormContext<MembershipModel>();
  const { control } = form;

  const dispatch = useDispatch();

  const [breedIdList, breedMap] = useSelector(selectPetBreeds(petTypeId), petBreedMapBox);

  useEffect(() => {
    if (isNormal(petTypeId)) {
      handleGetBreedList();
    }
  }, [petTypeId]);

  const handleGetBreedList = useSerialCallback(async () => {
    await dispatch(getBreedList(petTypeId));
  });

  const breedOptionList = useMemo(() => {
    return breedIdList.toJSON().map((id) => {
      const item = breedMap.mustGetItem(id);
      return {
        value: item.name,
        label: item.name,
      };
    });
  }, [breedIdList, breedMap]);

  const [customizedBreed] = useWatch({
    control,
    name: ['customizedBreed'],
  });

  const values = customizedBreed?.[index]?.breeds;

  const isSelectedAll = values?.length === breedOptionList.length || isAll;

  const calcValues = useMemo(() => {
    if (isAll) {
      return breedOptionList.map((item) => item.value);
    } else {
      return values;
    }
  }, [values, breedOptionList, isAll]);

  const handleRenderValues = useMemoizedFn(
    (
      values: Node<{
        value: string;
        label: string;
      }>[],
    ) => {
      const text = isSelectedAll
        ? 'All breeds'
        : getLongListText(
            values.map((item) => item.props.title),
            'breeds',
          );
      return (
        <Text className="group-data-[open]/select:moe-text-disabled" variant="regular-short">
          {text}
        </Text>
      );
    },
  );

  return (
    <Select.Multiple
      isLoading={handleGetBreedList.isBusy()}
      className={cn('moe-w-full', className)}
      classNames={{
        base: 'moe-w-full',
        placeholder: '!moe-whitespace-normal',
      }}
      listClassNames={{
        base: 'moe-max-h-[240px] moe-overflow-y-auto',
      }}
      value={calcValues}
      onChange={(value = []) => {
        form.setValue(
          `customizedBreed.${index}`,
          {
            breeds: value as string[],
            isAll: value.length === breedOptionList.length,
            petTypeId: String(petTypeId),
          },
          { shouldDirty: true },
        );

        window.requestAnimationFrame(() => {
          form.trigger(`breedFilter`);
        });
      }}
      mode="value"
      showSelectAll
      items={breedOptionList}
      renderValues={handleRenderValues}
      isDisabled={isDisabled}
    >
      {(item) => <Select.Item key={item.value} title={item.label} />}
    </Select.Multiple>
  );
});
