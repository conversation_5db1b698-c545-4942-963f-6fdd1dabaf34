import { useDispatch, useSelector } from 'amos';
import React, { memo, useEffect } from 'react';
import { Button } from '../../../../../components/Button/Button';
import { modalApi } from '../../../../../components/Modal/Modal';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { selectOnlineBookingAbandonRecord } from '../../../../../store/onlineBooking/abandonBooking.selectors';
import {
  getOnlineBookingAbandonRecord,
  removeOnlineBookingAbandonRecord,
} from '../../../../../store/onlineBooking/actions/private/abandonBooking.actions';
import { BookingInfo } from '../../../components/BookingRequestModal/BookingInfo';
import { ClientInfo } from '../../../components/BookingRequestModal/ClientInfo';
import { PetInfo } from '../../../components/BookingRequestModal/PetInfo';
import { isValidAddress } from '../../../components/ClientLocation/ClientLocation';
import { ReviewUpdateTips } from '../../../components/OnlineBookingInfoUpdateDrawer/components/ReviewUpdateTips';
import { type UpdateRecordType } from '../../../components/OnlineBookingInfoUpdateDrawer/type';
import {
  FooterView,
  OnlineBookingRequestContentView,
  OnlineBookingRequestModalView,
} from '../../OnlineBookingRequests/components/OnlineBookingRequestModal/OnlineBookingRequestModal.style';
import { useAbandonBtnAction } from './hooks/useAbandonBtnAction';
import { useAbandonClientInfoList } from './hooks/useAbandonClientInfoList';
import { useAbandonInfoList } from './hooks/useAbandonInfoList';
import { useAbandonPetInfoList } from './hooks/useAbandonPetInfoList';

export interface AbandonBookingRequestModalProps {
  className?: string;
  bookingFlowId: string;
  onClose: () => void;
  onReviewUpdate: (v: UpdateRecordType) => void;
}

export const AbandonBookingRequestModal = memo<AbandonBookingRequestModalProps>(
  ({ className, bookingFlowId, onClose, onReviewUpdate }) => {
    const dispatch = useDispatch();
    const [onlineBookingAbandonRecord, business] = useSelector(
      selectOnlineBookingAbandonRecord(bookingFlowId),
      selectCurrentBusiness,
    );

    useEffect(() => {
      if (bookingFlowId) {
        dispatch(getOnlineBookingAbandonRecord(bookingFlowId));
      }
    }, [bookingFlowId]);

    const abandonBookingInfoList = useAbandonInfoList(bookingFlowId);
    const abandonClientInfoList = useAbandonClientInfoList(bookingFlowId);
    const abandonPetInfoList = useAbandonPetInfoList(bookingFlowId);
    const { btnText, btnOnClick } = useAbandonBtnAction(bookingFlowId);

    const ifNeedDiff = onlineBookingAbandonRecord.hasRequestUpdate;

    const handleDelete = () => {
      modalApi.confirmDelete({
        content: 'Are you sure to delete it?',
        onOk: async () => {
          await dispatch(removeOnlineBookingAbandonRecord(bookingFlowId));
          onClose();
        },
      });
    };

    return (
      <OnlineBookingRequestModalView
        className={className}
        visible={!!bookingFlowId}
        onClose={onClose}
        title="Abandoned booking request"
        width="595px"
        height="calc(100% - 248px)"
      >
        {ifNeedDiff && (
          <ReviewUpdateTips
            onClick={() => {
              onReviewUpdate({
                customerId: onlineBookingAbandonRecord.customerId,
              });
            }}
          />
        )}
        <OnlineBookingRequestContentView>
          <BookingInfo infoList={abandonBookingInfoList} />
          <ClientInfo
            infoList={abandonClientInfoList}
            isCollapsed={!(business.isMobileGrooming() && isValidAddress(onlineBookingAbandonRecord.address))}
            recordId={bookingFlowId}
          />
          <PetInfo petInfoList={abandonPetInfoList} />
        </OnlineBookingRequestContentView>
        <FooterView>
          <Button btnType="danger" disabled={ifNeedDiff} fill={false} buttonRadius="circle" onClick={handleDelete}>
            Delete
          </Button>
          {btnText ? (
            <Button
              buttonRadius="circle"
              disabled={ifNeedDiff}
              btnType="primary"
              onClick={() => {
                btnOnClick({ getAbandonDetailFirst: true });
                onClose();
              }}
            >
              {btnText}
            </Button>
          ) : null}
        </FooterView>
      </OnlineBookingRequestModalView>
    );
  },
);
