import { type BoardingSplitLodgingScheduleDef } from '@moego/api-web/moego/models/appointment/v1/boarding_split_lodging_defs';
import { type PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import {
  type ServiceItemType,
  type ServiceOverrideType,
  type ServicePriceUnit,
} from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type ServiceOperationEntry } from './serviceOperation';

export interface ServiceEntry {
  /** map中作key使用 */
  id: string;
  serviceId: number;
  servicePrice: number;
  serviceTime: number;
  serviceType: number;
  serviceItemType?: ServiceItemType;
  scopeTypePrice: number;
  scopeTypeTime: number;
  priceOverrideType?: ServiceOverrideType;
  durationOverrideType?: ServiceOverrideType;
  staffId: number;
  enableOperation: boolean;
  operationList: ServiceOperationEntry[];
  workMode: number;
  isPriceModified?: boolean;
  isDurationModified?: boolean;
  isSavePrice?: boolean;
  isSaveDuration?: boolean;
  serviceName?: string;
  priceUnit?: ServicePriceUnit;
  // 以下 hybrid service
  associatedId?: string;
  requireDedicatedStaff?: boolean;
  startDate?: string;
  endDate?: string;
  startTime?: number;
  endTime?: number;
  lodgingId?: string;
  lodgingName?: string;
  splitLodgings?: BoardingSplitLodgingScheduleDef[];
  isApplySplitLodgingsToAllPets?: boolean;
  specificDates?: string[];
  dateType?: PetDetailDateType;
  serviceItemEnum?: string;
  maxDuration?: number;
  quantityPerDay?: number;
  bundleServiceIds?: string[];
  isAdditionalService?: boolean;
  orderLineItemId?: string;
}

export type PetIdsServiceList = { petId: number; serviceList: ServiceEntry[] }[];
