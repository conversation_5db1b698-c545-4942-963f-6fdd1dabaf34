import { Form, useFieldArray, useForm } from '@moego/ui';
import { QuestionCard } from './common/QuestionCard';
import React, { forwardRef, useEffect, useImperativeHandle } from 'react';
import { Feedback } from '../../../../../components/Feedback/Feedback';
import { validateFeedback } from '../../../../../components/Feedback/utils/validate';
import { type FeedbackItemType } from '../../../../../store/groomingReport/groomingReportEdit.actions';
import { type QuestionRef } from '../GroomingReportEdit.options';
import { QuestionType } from '@moego/api-web/moego/models/appointment/v1/daily_report_enums';
import { type FeedbackValue } from '../../../../../components/Feedback/types';

const enum QuestionTypeString {
  UNSPECIFIED = 'unspecified',
  SINGLE_CHOICE = 'single_choice',
  MULTI_CHOICE = 'multi_choice',
  TEXT_INPUT = 'text_input',
  SHORT_TEXT_INPUT = 'short_text_input',
  TAG_CHOICE = 'tag_choice',
}

function typeStrToNum(type: string) {
  switch (type) {
    case QuestionTypeString.SINGLE_CHOICE:
      return QuestionType.SINGLE_CHOICE;
    case QuestionTypeString.MULTI_CHOICE:
      return QuestionType.MULTI_CHOICE;
    case QuestionTypeString.TEXT_INPUT:
      return QuestionType.TEXT_INPUT;
    case QuestionTypeString.SHORT_TEXT_INPUT:
      return QuestionType.SHORT_TEXT_INPUT;
    case QuestionTypeString.TAG_CHOICE:
      return QuestionType.TAG_CHOICE;
    default:
      return QuestionType.UNSPECIFIED;
  }
}

function typeNumToStr(type: QuestionType): string {
  switch (type) {
    case QuestionType.SINGLE_CHOICE:
      return QuestionTypeString.SINGLE_CHOICE;
    case QuestionType.MULTI_CHOICE:
      return QuestionTypeString.MULTI_CHOICE;
    case QuestionType.TEXT_INPUT:
      return QuestionTypeString.TEXT_INPUT;
    case QuestionType.SHORT_TEXT_INPUT:
      return QuestionTypeString.SHORT_TEXT_INPUT;
    case QuestionType.TAG_CHOICE:
      return QuestionTypeString.TAG_CHOICE;
    default:
      return QuestionTypeString.UNSPECIFIED;
  }
}

function normalizeFeedbackField(feedback: FeedbackItemType): { value: FeedbackValue } {
  return {
    value: {
      ...feedback,
      type: typeStrToNum(feedback.type),
    },
  };
}

export const CustomizedFeedback = forwardRef<
  QuestionRef<FeedbackItemType[]>,
  { customizedFeedbacks: FeedbackItemType[]; onDirty?: () => void }
>(({ customizedFeedbacks, onDirty }, forwardedRef) => {
  const form = useForm<{ customizedFeedbacks: { value: FeedbackValue }[] }>({
    defaultValues: {
      customizedFeedbacks: customizedFeedbacks.map(normalizeFeedbackField),
    },
    mode: 'onChange',
  });

  useEffect(() => {
    form.setValue('customizedFeedbacks', customizedFeedbacks.map(normalizeFeedbackField));
  }, [customizedFeedbacks]);

  useEffect(() => {
    if (form.formState.isDirty) {
      onDirty?.();
    }
  }, [form.formState.isDirty]);

  const { fields } = useFieldArray({
    control: form.control,
    name: 'customizedFeedbacks',
  });

  useImperativeHandle(forwardedRef, () => ({
    getData: () =>
      (form.getValues().customizedFeedbacks ?? []).map((field) => {
        const feedback = field.value;
        return {
          ...feedback,
          type: typeNumToStr(feedback.type),
        } as FeedbackItemType;
      }),
    validate: () => {
      const error = form.formState.errors.customizedFeedbacks?.[0];
      if (error?.message) {
        return {
          isValid: false,
          message: error?.message,
        };
      }
      return { isValid: true };
    },
  }));

  return (
    <QuestionCard title="Customized feedback">
      <Form form={form} footer={null}>
        {fields.map((field, index) => {
          return (
            <Form.Item
              key={field.id}
              name={`customizedFeedbacks.${index}.value`}
              rules={{
                validate: validateFeedback,
              }}
            >
              <Feedback />
            </Form.Item>
          );
        })}
      </Form>
    </QuestionCard>
  );
});
