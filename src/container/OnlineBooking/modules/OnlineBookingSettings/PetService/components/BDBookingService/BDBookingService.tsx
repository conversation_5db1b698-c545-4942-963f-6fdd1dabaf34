import React, { memo, useState } from 'react';
import { CareTypeSortableList } from './components/CareTypeSortableList/CareTypeSortableList';
import { useMount } from 'react-use';
import { CareTypeServiceList } from './components/CareTypeServiceList/CareTypeServiceList';
import { isNormal } from '../../../../../../../store/utils/identifier';
import { useDispatch } from 'amos';
import { listBookingCareTypes } from '../../../../../../../store/onlineBooking/actions/private/services.actions';
import { Spin } from '@moego/ui';
import { useSerialCallback } from '@moego/tools';

export const BDBookingService = memo(() => {
  const [selectedCareTypeId, setSelectedCareTypeId] = useState('');
  const dispatch = useDispatch();

  const handleSelectCareType = (careTypeId: string) => {
    setSelectedCareTypeId(careTypeId);
  };

  const getBookingCareTypes = useSerialCallback(async () => {
    const res = await dispatch(listBookingCareTypes());
    setSelectedCareTypeId(res[0]?.id ?? '');
  });

  useMount(getBookingCareTypes);

  if (getBookingCareTypes.isBusy()) {
    return <Spin isLoading className="moe-w-full" />;
  }

  return (
    <div className="moe-flex moe-border moe-border-divider moe-rounded-spacing-xs moe-overflow-hidden">
      <CareTypeSortableList careTypeId={selectedCareTypeId} onChange={handleSelectCareType} />
      {isNormal(selectedCareTypeId) && <CareTypeServiceList careTypeId={selectedCareTypeId} />}
    </div>
  );
});
