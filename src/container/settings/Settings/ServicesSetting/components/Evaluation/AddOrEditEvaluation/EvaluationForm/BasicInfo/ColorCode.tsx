import { ColorPicker, Form } from '@moego/ui';
import React, { memo } from 'react';
import { DEFAULT_PRESET_COLORS } from '../../../../../../../../../components/ColorPicker/ColorCodePicker';
import { useServiceStaffPermissions } from '../../../../AddOrEditComponents/hooks/useServiceStaffPermissions';
import { useEvaluationFormContext } from '../EvaluationFormContext';

export const ColorCode = memo(() => {
  const { isEdit } = useEvaluationFormContext();
  const { isDisabledForRemains } = useServiceStaffPermissions(isEdit);

  return (
    <Form.Item name="colorCode" label="Color code">
      <ColorPicker isDisabled={isDisabledForRemains} isRequired presetList={DEFAULT_PRESET_COLORS} />
    </Form.Item>
  );
});
