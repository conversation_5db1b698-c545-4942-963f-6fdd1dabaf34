import React, { memo } from 'react';
import { FormSection } from '../../../../AddOrEditComponents/FormSection/FormSection';
import { ServiceFormSections } from '../../../../AddOrEditComponents/FormSection/formSectionConfig';
import { EvaluationBusiness } from './EvaluationBusiness';
import { EvaluationStaffs } from './EvaluationStaffs';

export const BusinessAndStaff = memo(() => {
  return (
    <FormSection section={ServiceFormSections.AvailableBusinessAndStaff} title="Business and staff">
      <EvaluationBusiness />
      <EvaluationStaffs />
    </FormSection>
  );
});
