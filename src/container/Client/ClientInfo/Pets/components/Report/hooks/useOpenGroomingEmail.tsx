import { Modal } from '@moego/ui';
import React, { memo } from 'react';
import { useModal } from '../../../../../../../components/Modal/useModal';
import { GroomingReportEmail } from '../../../../../../../components/ReportEmail/GroomingReportEmail/GroomingReportEmail';
import { type OpenApiModels } from '../../../../../../../openApi/schema';

export interface GroomingEmailPreviewProps {
  onClose?: () => void;
  value: OpenApiModels['GET/grooming/grooming-report/summaryInfo']['Res'];
}

const GroomingEmailPreview = memo((props: GroomingEmailPreviewProps) => {
  const { onClose, value } = props;

  return (
    <Modal isOpen footer={null} onClose={onClose}>
      <GroomingReportEmail {...value} />
    </Modal>
  );
});

export const useOpenGroomingEmail = () => {
  return useModal(GroomingEmailPreview);
};
