import {
  type IGeneralOrderDetailV2,
  OrderPaymentModelStatus,
  RealmType,
  RefundOrderPaymentModelStatus,
  assertModel,
} from '@moego/finance-web-kit';
import { T_SECOND } from '@moego/reporting';
import { Text, cn } from '@moego/ui';
import { useSelector } from 'amos';
import React from 'react';
import { type FinanceKit } from '../../../../../service/finance-kit';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';

export const PaymentsInfo = (props: {
  className?: string;
  payments: IGeneralOrderDetailV2<typeof FinanceKit.model>['payments'];
}) => {
  const { payments, className } = props;
  const [business] = useSelector(selectCurrentBusiness);
  return (
    <div className={cn('moe-flex moe-flex-col moe-gap-y-s', className)}>
      {payments.map((orderPayment) => {
        const isGeneral = assertModel(orderPayment, RealmType.OrderPayment);

        return (
          <div key={orderPayment.id} className="moe-flex moe-flex-col">
            <div className="moe-flex moe-justify-between moe-text-primary">
              <Text variant="regular-short">
                {isGeneral
                  ? `${OrderPaymentModelStatus.mapLabels[orderPayment.paymentStatus]?.label} by ${orderPayment.paymentMethodDisplayName}`
                  : `${RefundOrderPaymentModelStatus.mapLabels[orderPayment.refundOrderPaymentStatus]?.label} to ${orderPayment.refundPaymentMethodDisplayName}`}
              </Text>
              <Text variant="regular-short">
                {business.formatAmount(
                  isGeneral ? orderPayment.totalAmount?.valueOf() : -orderPayment.refundTotalAmount?.valueOf(),
                )}
              </Text>
            </div>
            <Text variant="small" className="moe-text-tertiary moe-mt-xxs">
              {business.formatDateTime(Number(orderPayment.createTime) * T_SECOND)}
            </Text>
          </div>
        );
      })}
    </div>
  );
};
