import { useDispatch, useSelector } from 'amos';
import { Badge } from 'antd';
import React, { memo } from 'react';
import { useHistory } from 'react-router';
import SvgIconPlusSvg from '../../../../../assets/svg/icon-plus.svg';
import { Avatar } from '../../../../../components/Avatar/Avatar';
import { SvgIcon } from '../../../../../components/Icon/Icon';
import { CustomerNameView } from '../../../../../components/SmartClientList/SmartClientListTable.style';
import { PATH_CUSTOMER_OVERVIEW } from '../../../../../router/paths';
import { selectCurrentPermissions } from '../../../../../store/business/role.selectors';
import { type OnlineBookingAbandonRecord } from '../../../../../store/onlineBooking/abandonBooking.boxes';
import {
  AbandonRecordAction,
  getOnlineBookingAbandonRecord,
} from '../../../../../store/onlineBooking/actions/private/abandonBooking.actions';
import { useBool } from '../../../../../utils/hooks/useBool';
import { ReportActionName } from '../../../../../utils/reportType';
import { reportData } from '../../../../../utils/tracker';
import { TableCellCapsule, type TableCellCapsuleProps } from '../../../components/TableCellCapsule/TableCellCapsule';
import { useAbandonAddClient } from '../AbandonBookingRequestModal/hooks/useAbandonAddClient';
import { type ColumnsEventsType } from '../hooks/useTableColumns';

export interface AbandonClientNameCellProps {
  data: OnlineBookingAbandonRecord;
  tableCellCapsuleProps: TableCellCapsuleProps;
  events?: ColumnsEventsType;
}

export const AbandonClientNameCell = memo(({ data, tableCellCapsuleProps, events }: AbandonClientNameCellProps) => {
  const client = data.customer;
  const customerId = client?.customerId;
  const isInteracting = useBool(false);
  const history = useHistory();
  const dispatch = useDispatch();
  const abandonAddClient = useAbandonAddClient();
  const [permissions] = useSelector(selectCurrentPermissions());

  const canViewClientProfile = permissions.has('viewIndividualClientProfile');

  const ifNeedDiff = data.hasRequestUpdate;

  const onClickCustomerName = async (record: OnlineBookingAbandonRecord) => {
    if (ifNeedDiff) {
      return events?.onOpenReviewUpdate(record);
    }
    const customerId = record?.customer?.customerId;
    if (customerId) {
      if (!canViewClientProfile) return;
      history.push(PATH_CUSTOMER_OVERVIEW.queried({ goBack: true }, { customerId }));
      return;
    }
    if (abandonAddClient) {
      const res = await dispatch(getOnlineBookingAbandonRecord(record.bookingFlowId, AbandonRecordAction.CreateClient));
      abandonAddClient(res);
      reportData(ReportActionName.ObAbandonListAddClientName);
    }
  };

  const avatar = <Avatar type="user" src={(client as any)?.avatarPath} size="32px" info={client} />;

  const AvatarOrPlus =
    // 在两种情况下展示 Avatar：existing 用户、new 用户且不在交互中
    customerId || (!customerId && !isInteracting.value) ? (
      avatar
    ) : (
      <div className="!moe-bg-[#fff] !moe-rounded-full !moe-w-[32px] !moe-h-[32px] !moe-flex !moe-justify-center !moe-items-center !moe-mr-[12px] moe-flex-shrink-0">
        <SvgIcon src={SvgIconPlusSvg} size={22}></SvgIcon>
      </div>
    );

  return (
    <TableCellCapsule onEnter={isInteracting.open} onLeave={isInteracting.close} {...tableCellCapsuleProps}>
      <CustomerNameView
        onClick={(e) => {
          e.stopPropagation();
          onClickCustomerName(data);
        }}
        className={'!moe-cursor-pointer !moe-mr-0'} // here is to override CustomerNameView's margin-right
      >
        {ifNeedDiff ? (
          <Badge dot offset={[-4, 4]}>
            {AvatarOrPlus}
          </Badge>
        ) : (
          AvatarOrPlus
        )}

        <div className="name">
          {client.firstName} {client.lastName}
        </div>
      </CustomerNameView>
    </TableCellCapsule>
  );
});
