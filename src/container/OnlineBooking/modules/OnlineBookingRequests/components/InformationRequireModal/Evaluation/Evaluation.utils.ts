import { type EvaluationTestDetailModel } from '@moego/api-web/moego/models/online_booking/v1/evaluation_test_detail_models';
import { KEY_FIELD_DIVIDER } from '../InformationRequireModal.utils';
import { type BoardingServiceDetailModel } from '@moego/api-web/moego/models/online_booking/v1/boarding_service_detail_models';
import { type DaycareServiceDetailModel } from '@moego/api-web/moego/models/online_booking/v1/daycare_service_detail_models';
import { isNormal } from '../../../../../../../store/utils/identifier';

export const EvaluationIdPrefix = 'evaluationId';
export const EvaluationStaffIdPrefix = 'evaluationStaffId';

export const getEvaluationIdKey = (itemKey: string) => {
  return [EvaluationIdPrefix, itemKey].join(KEY_FIELD_DIVIDER);
};

export const getEvaluationStaffIdKey = (itemKey: string) => {
  return [EvaluationStaffIdPrefix, itemKey].join(KEY_FIELD_DIVIDER);
};

export const CreateEvaluationInfoPrefix = 'createEvaluationInfo';

export const getCreateEvaluationInfoKey = (itemKey: string) => {
  return [CreateEvaluationInfoPrefix, itemKey].join(KEY_FIELD_DIVIDER);
};

export const isEvaluationServiceDetail = (
  serviceDetail: EvaluationTestDetailModel | BoardingServiceDetailModel | DaycareServiceDetailModel,
): serviceDetail is EvaluationTestDetailModel => {
  return isNormal((serviceDetail as EvaluationTestDetailModel).evaluationId);
};

export const hasDaycareSpecificDates = (
  serviceDetail: EvaluationTestDetailModel | BoardingServiceDetailModel | DaycareServiceDetailModel,
): serviceDetail is DaycareServiceDetailModel => {
  return (
    (serviceDetail as DaycareServiceDetailModel).specificDates !== undefined &&
    (serviceDetail as DaycareServiceDetailModel).specificDates.length > 0
  );
};
