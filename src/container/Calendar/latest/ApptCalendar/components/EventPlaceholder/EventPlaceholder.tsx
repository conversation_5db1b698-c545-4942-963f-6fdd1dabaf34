import { Tooltip } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React from 'react';
import { DEFAULT_BLOCK_PRESET_COLORS } from '../../../../../../components/ColorPicker/ColorCodePickerFlat';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { quickAddPlaceholderBox } from '../../../../../../store/calendarLatest/calendar.boxes';
import {
  type CalendarCardComponent,
  CalendarCardComponentType,
} from '../../../../../../store/calendarLatest/card.types';
import { openNewEvaluationDrawer } from '../../../../../../store/evaluation/evaluation.actions';
import { EvaluationQuickAddFrom } from '../../../../../../store/evaluation/evaluation.boxes';
import { getDefaultEvaluationInfo } from '../../../../../../store/evaluation/evaluation.types';
import { isNoAssignedStaffId } from '../../../../../../store/staff/no-assigned-staff-helper';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../utils/DateTimeUtil';
import { useCloseAllDrawer } from '../../../../../../utils/hooks/useCloseAllDrawer';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { openGlobalQuickAddDrawer } from '../../../../../Appt/store/appt.actions';
import { BlockDefaultDuration } from '../../../AwesomeCalendar.utils';
import { withRewriteProps } from '../../ApptCalendar.utils';
import { useBlockTimeConfig } from '../BlockTimeDrawer/hooks/useBlockTimeConfig';
import { PlaceholderCardApptBlock } from '../NewCardPlaceholder/PlaceholderCardApptBlock';
import { apptReporter } from '../../../../../../utils/reportData/reporter/apptReporter';

export const EventPlaceholder = withRewriteProps(
  () => ({}),
  function CalendarCardPlaceholder(props) {
    const dispatch = useDispatch();
    const [placeholderInfo, business] = useSelector(quickAddPlaceholderBox, selectCurrentBusiness());
    const { closeAllDrawer } = useCloseAllDrawer();
    const [, setBlockTimeDrawerConfig] = useBlockTimeConfig();
    const { staffId: placeholderStaffId, apptDateTime, apptEndDateTime } = placeholderInfo;
    const isNoAssignStaff = isNoAssignedStaffId(placeholderStaffId);
    const onAddBlock = useLatestCallback(() => {
      closeAllDrawer();
      setBlockTimeDrawerConfig({
        visibleAddBlockDrawer: true,
        preset: {
          staffId: placeholderStaffId,
          blockStartDate: apptDateTime,
          duration: BlockDefaultDuration,
        },
        previewColorCode: DEFAULT_BLOCK_PRESET_COLORS[0],
      });
    });

    const onAddAppt = useLatestCallback(() => {
      closeAllDrawer();
      apptReporter.setCreateApptDrawerOpenTime();
      apptReporter.setCreateApptFrom('calendar');

      if (isNoAssignStaff) {
        const startDate = apptDateTime.format(DATE_FORMAT_EXCHANGE);
        const startTime = apptDateTime.getMinutes();
        const endDate = apptEndDateTime.format(DATE_FORMAT_EXCHANGE);
        const endTime = apptEndDateTime.getMinutes();
        dispatch(
          openNewEvaluationDrawer(
            business.id,
            {
              evaluationInfo: {
                ...getDefaultEvaluationInfo(),
                startDate,
                startTime,
                endDate,
                endTime,
              },
            },
            EvaluationQuickAddFrom.Calendar,
          ),
        );
        return;
      }
      dispatch(openGlobalQuickAddDrawer({ staffId: placeholderStaffId, appointmentStart: apptDateTime }));
    });

    return (
      <Tooltip content={business.formatDateTime(apptDateTime)}>
        <div {...props} className="moe-pl-[1px] moe-h-full">
          <PlaceholderCardApptBlock
            className="moe-overflow-hidden"
            onAddAppt={onAddAppt}
            onAddBlock={onAddBlock}
            showAddBlock={!isNoAssignStaff}
          />
        </div>
      </Tooltip>
    );
  },
) as CalendarCardComponent;

EventPlaceholder.cardType = CalendarCardComponentType.Placeholder;
