import { useDispatch, useSelector } from 'amos';
import { isNil } from 'lodash';
import { useCallback, useEffect, useMemo } from 'react';
import { useAsync } from 'react-use';
import { getServiceApplicableStaff } from '../../../container/Appt/store/appt.api';
import { type ApptInfoPetServiceInfo } from '../../../store/calendarLatest/calendar.types';
import { getStaffList } from '../../../store/staff/staff.actions';
import { staffMapBox } from '../../../store/staff/staff.boxes';
import { selectBusinessStaffs } from '../../../store/staff/staff.selectors';
import { ID_ANONYMOUS, isNormal } from '../../../store/utils/identifier';
import { type StaffWithServicePriceDurationView, filterStaffList, isSelectedStaff } from '../utils/filterStaffList';

export interface UseServiceApplicableStaffListOptions {
  petId?: string;
  serviceId?: number;
  selectedStaffId?: number;
  filterStaffIds?: number[];
  onlyAvailableStaff?: boolean;
  enableServiceAvailableStaff?: boolean;
}

export type ServiceVariation = Partial<
  Pick<ApptInfoPetServiceInfo, 'servicePrice' | 'serviceTime' | 'priceOverrideType' | 'durationOverrideType'>
>;

export interface UseServiceApplicableStaffListReturn {
  loading: boolean;
  availableStaffList: StaffWithServicePriceDurationView[];
  unavailableStaffList: StaffWithServicePriceDurationView[];
  getServiceVariation: (staffId?: number) => ServiceVariation | undefined;
}

export const useServiceApplicableStaffList = (
  options: UseServiceApplicableStaffListOptions,
): UseServiceApplicableStaffListReturn => {
  const {
    serviceId = ID_ANONYMOUS,
    petId,
    filterStaffIds,
    selectedStaffId,
    onlyAvailableStaff = false,
    enableServiceAvailableStaff = true,
  } = options;

  const [businessStaffs, staffMap] = useSelector(selectBusinessStaffs, staffMapBox);
  const dispatch = useDispatch();

  const generateDefaultStaff = (
    staffId?: number,
    options?: Partial<StaffWithServicePriceDurationView>,
  ): StaffWithServicePriceDurationView => {
    const staff = staffMap.mustGetItem(staffId ?? ID_ANONYMOUS);
    return {
      staffId: staffId?.toString() ?? '',
      firstName: staff.firstName,
      lastName: staff.lastName,
      avatarPath: '',
      isAvailable: true,
      ...options,
    };
  };

  const { value: applicableStaffResult, loading } = useAsync<
    () => Promise<{ staffs: StaffWithServicePriceDurationView[] }>
  >(async () => {
    if (!isNormal(serviceId) || !enableServiceAvailableStaff) {
      return {
        staffs: businessStaffs.map((staffId) => generateDefaultStaff(staffId)).toArray(),
      };
    }

    return await dispatch(
      getServiceApplicableStaff({
        serviceId: serviceId.toString(),
        petId,
      }),
    );
  }, [serviceId, petId]);

  useEffect(() => {
    if (!businessStaffs.size && !isNormal(serviceId)) {
      dispatch(getStaffList());
    }
  }, [businessStaffs.size]);

  const { staffs } = applicableStaffResult || {};

  const originalAvailableStaffList = useMemo(
    () => filterStaffList({ staffList: staffs, isAvailable: true, filterStaffIds }),
    [staffs, filterStaffIds],
  );
  const originalUnavailableStaffList = useMemo(
    () => filterStaffList({ staffList: staffs, isAvailable: false, filterStaffIds }),
    [staffs, filterStaffIds],
  );

  const isDeletedStaff =
    isNormal(selectedStaffId) && staffs?.every((staff) => !isSelectedStaff(staff, selectedStaffId));
  const isSelectedStaffUnavailable =
    isNormal(selectedStaffId) && originalUnavailableStaffList.some((staff) => isSelectedStaff(staff, selectedStaffId));

  const selectedStaffWithPriceDurationList = isDeletedStaff
    ? [generateDefaultStaff(selectedStaffId, { isDisabled: true, isAvailable: false })]
    : (staffs?.filter((staff) => isSelectedStaff(staff, selectedStaffId)) ?? []);

  let unavailableStaffList: StaffWithServicePriceDurationView[] = [];
  if (onlyAvailableStaff) {
    // put selected staff to unavailable group, when only show available staff but selected staff is unavailable or deleted
    if (isDeletedStaff || isSelectedStaffUnavailable) {
      unavailableStaffList = selectedStaffWithPriceDurationList;
    }
  } else {
    unavailableStaffList = isDeletedStaff
      ? originalUnavailableStaffList.concat(selectedStaffWithPriceDurationList)
      : originalUnavailableStaffList;
  }

  /**
   * Get service price and duration variation from applicable staff list
   * Case: when staff is selected, get the service price and duration from the selected staff
   */
  const getServiceVariation = useCallback(
    (staffId?: number): ServiceVariation | undefined => {
      const staffInfo = staffs?.find((staff) => staff.staffId === staffId?.toString());
      const { servicePrice, serviceDuration } = staffInfo || {};
      if (isNil(servicePrice) || isNil(serviceDuration)) {
        return;
      }
      return {
        servicePrice: staffInfo?.servicePrice,
        serviceTime: staffInfo?.serviceDuration,
        priceOverrideType: staffInfo?.priceOverrideType,
        durationOverrideType: staffInfo?.durationOverrideType,
      };
    },
    [staffs],
  );

  return {
    loading,
    availableStaffList: originalAvailableStaffList,
    unavailableStaffList,
    getServiceVariation,
  };
};
