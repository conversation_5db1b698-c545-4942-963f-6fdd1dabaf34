import { DateRangePicker, Form, Radio, RadioGroup, type RangeValue, useFormState } from '@moego/ui';
import { type Dayjs } from 'dayjs';
import React from 'react';
import { type DepositRuleVM } from '../../../../../../../store/payment/actions/private/deposit.actions';

interface FieldDateRangeProps {
  value?: DepositRuleVM['dateRange'];
  onChange?: (value: DepositRuleVM['dateRange']) => void;
}

const VALUE_NO_EXPIRATION = 1;
const VALUE_SPECIFIC_DATE_RANGE = 2;

export const FieldDateRange = React.forwardRef(function FieldDateRange(
  { value, onChange }: FieldDateRangeProps,
  ref: React.Ref<HTMLDivElement>,
) {
  const errMsg = useFormState().errors?.dateRange;
  const lastDateRangeRef = React.useRef<DepositRuleVM['dateRange'] | null>([null, null]);

  const handleTypeChange = (newType: number | null) => {
    // 临时保存值，万一用户反悔了不至于要重新选择
    if (newType === VALUE_NO_EXPIRATION) {
      lastDateRangeRef.current = value || null;
    }
    onChange?.(newType === VALUE_NO_EXPIRATION ? null : lastDateRangeRef.current);
  };

  const handleRangeChange = (newValue: RangeValue<Dayjs> | null) => {
    onChange?.(newValue);
  };

  const renderDateRange = (value: DepositRuleVM['dateRange']) => {
    if (value) {
      return (
        <div className="moe-flex moe-flex-col moe-w-[416px] moe-ml-[28px] moe-gap-[4px]">
          <DateRangePicker isRequired value={value} onChange={handleRangeChange} />
          {errMsg ? <Form.HelpText errorMessage={errMsg.message} /> : null}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="moe-flex moe-flex-col moe-gap-[16px]" ref={ref}>
      <Form.Label label="Date range" isRequired />
      <RadioGroup
        isRequired
        value={value === null ? VALUE_NO_EXPIRATION : VALUE_SPECIFIC_DATE_RANGE}
        onChange={handleTypeChange}
      >
        <Radio value={VALUE_NO_EXPIRATION}>No expiration</Radio>
        <Radio value={VALUE_SPECIFIC_DATE_RANGE}>Specific date range</Radio>
      </RadioGroup>
      {renderDateRange(value || null)}
    </div>
  );
});

export const FieldDateRangeValidator = (param: DepositRuleVM['dateRange']) => {
  if (!param) return true;
  const [startDate, endDate] = param;
  if (!startDate || !endDate) {
    return 'Date range is required';
  }
  return true;
};
