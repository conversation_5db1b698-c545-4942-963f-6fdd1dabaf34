import React from 'react';
import { BookingRangeEnd } from './components/BookingRangeEnd';
import { BookingRangeStart } from './components/BookingRangeStart';
import { ConfigGroup } from './components/ConfigGroup';
import { DisplayStaffSelection } from './components/DisplayStaffSelection';
import { FakeIt } from './components/FakeIt';
import { ShowOneAvailableTime } from './components/ShowOneAvailableTime';
import { BySlotShowOneAvailableTime } from './components/BySlotShowOneAvailableTime';
import { TimeSlotFormat } from './components/TimeSlotFormat';
import { TimeSlotInterval } from './components/TimeSlotInterval';
import cn from 'classnames';
import { useSelector } from 'amos';
import { selectDraftAvailabilitySettings } from '../../../../../../../../../store/onlineBooking/settings/availabilityGrooming.selectors';
import { GrowthBookFeatureList } from '../../../../../../../../../utils/growthBook/growthBook.config';
import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { Switch } from '../../../../../../../../../components/SwitchCase';

export function TimePreference({ className }: { className?: string }) {
  const [draftSettings] = useSelector(selectDraftAvailabilitySettings());
  const isEnableMultiPetBySlot = useFeatureIsOn(GrowthBookFeatureList.EnableMultiPetBySlot);

  return (
    <div className={cn('moe-flex moe-flex-col moe-gap-y-xl', className)}>
      <ConfigGroup title="Staff selection">
        <DisplayStaffSelection draftSettings={draftSettings} />
      </ConfigGroup>
      <ConfigGroup title="Time slot" className="moe-flex-gap">
        <TimeSlotFormat />
        <TimeSlotInterval draftSettings={draftSettings} />
        <Switch shortCircuit>
          <Switch.Case if={draftSettings.isByWorkingHour}>
            <ShowOneAvailableTime draftSettings={draftSettings} />
          </Switch.Case>
          <Switch.Case if={draftSettings.isBySlot && isEnableMultiPetBySlot}>
            <BySlotShowOneAvailableTime draftSettings={draftSettings} />
          </Switch.Case>
        </Switch>
        <FakeIt draftSettings={draftSettings} />
      </ConfigGroup>
      <ConfigGroup title="Advanced booking">
        <BookingRangeStart />
        <BookingRangeEnd />
      </ConfigGroup>
    </div>
  );
}
