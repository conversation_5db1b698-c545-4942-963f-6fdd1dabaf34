import React from 'react';
import { type CombinedPetEvaluationPayload } from '../../../../store/calendarLatest/calendar.types';
import { EditEvaluationInfoPanel } from '../../components/Evaluation/EditEvaluationInfoPanel';
import { EvaluationResultCell } from '../../components/Evaluation/EvaluationResultCell';
import { toNumber } from '../../../../store/utils/identifier';
import { toastApi } from '../../../../components/Toast/Toast';
import { useDispatch, useSelector } from 'amos';
import { updatePetEvaluation } from '../../../../store/evaluation/evaluation.actions';
import { selectApptPetInfo } from '../../store/appt.selectors';
import { PetEvaluationHistoryModelActionType } from '@moego/api-web/moego/models/business_customer/v1/business_pet_evaluation_models';
import { useTicketActions } from './hooks/useTicketActions';
import { setApptDetailDrawerEdit } from '../../../../store/calendarLatest/actions/private/calendar.actions';
import { ApptNavigationTabsV3 } from '../../../Calendar/latest/AwesomeCalendar.utils';
import { CommentNoteType } from '../../../../store/calendarLatest/calendar.utils';
import { apptPetMapBox } from '../../store/appt.boxes';
import { Heading } from '@moego/ui';
import { apptDetailDrawerEditEvaluationBox } from '../../../../store/calendarLatest/calendar.boxes';
import { addApptPetEvaluations } from '../../store/appt.actions';
import { useTicketDrawerDetail } from './hooks/useTicketDrawerDetail';

export interface EditEvaluationInfoPanelProps {
  isSubmitLoading?: boolean;
  payload: CombinedPetEvaluationPayload;
}

export const ApptDetailEvaluationInfoPanel = (props: EditEvaluationInfoPanelProps) => {
  const { petId, appointmentId } = props.payload;
  const dispatch = useDispatch();
  const [petInfo] = useSelector(selectApptPetInfo(appointmentId));
  const activePet = petInfo.pets.find((pet) => pet.petId === petId);
  const activeEvaluationResult = activePet?.evaluations?.[0];
  const activePetCodes = activePet?.petCodes;
  const { refreshTicket } = useTicketActions();
  const { isFinishedOrCancelled } = useTicketDrawerDetail();

  const handleStatusChange = async (value: string) => {
    if (activeEvaluationResult) {
      await Promise.all([
        dispatch(
          updatePetEvaluation({
            evaluationStatus: Number(value),
            petId,
            evaluationId: activeEvaluationResult?.evaluationId.toString(),
            actionType: PetEvaluationHistoryModelActionType.UPDATE_BY_EVALUATION_APPOINTMENT,
          }),
        ),
        dispatch(
          addApptPetEvaluations(
            petId,
            [
              {
                ...activeEvaluationResult,
                evaluationStatus: Number(value),
              },
            ],
            appointmentId,
          ),
        ),
      ]);
    }
    await refreshTicket();
    toastApi.success('Evaluation result saved successfully.');
  };

  const handleCodeListChange = async (value: number[] | undefined) => {
    activeEvaluationResult &&
      dispatch(
        apptPetMapBox.mergeItem(appointmentId, {
          pets: petInfo.pets.map((pet) => {
            if (pet.petId === petId) {
              return {
                ...pet,
                petCodes: value || [],
              };
            }
            return pet;
          }),
        }),
      );
    toastApi.success('Pet codes saved successfully.');
  };

  const handleAddPetCode = async () => {
    refreshTicket();
  };

  return (
    <EditEvaluationInfoPanel
      {...props}
      isFinishedOrCancelled={isFinishedOrCancelled}
      CustomBlock={
        <div className="moe-flex moe-flex-col moe-gap-y-[20px]">
          <div className="moe-border-t-[1px] moe-border-divider moe-border-dashed moe-mt-[24px]" />
          <Heading size="4">Pet evaluation result</Heading>
          <EvaluationResultCell
            evaluationId={activeEvaluationResult?.evaluationId}
            petId={toNumber(petId)}
            petCodeIdList={activePetCodes || []}
            evaluationStatus={activeEvaluationResult?.evaluationStatus}
            onAddPetCode={handleAddPetCode}
            onStatusChange={handleStatusChange}
            onCodeListChange={handleCodeListChange}
            onAddPetNote={() => {
              dispatch(apptDetailDrawerEditEvaluationBox.setState(apptDetailDrawerEditEvaluationBox.initialState));
              dispatch(
                setApptDetailDrawerEdit({
                  activeTab: ApptNavigationTabsV3.TICKET_COMMENT,
                  ticketCommentsActiveTab: CommentNoteType.PetNotes,
                  petNotesDefaultPetId: toNumber(petId),
                }),
              );
            }}
          />
        </div>
      }
    />
  );
};
