import { Heading, Text, cn } from '@moego/ui';
import React, { type PropsWithChildren, memo } from 'react';
import { Condition } from '../../../../../../../components/Condition';
import { type EnumValues } from '../../../../../../../store/utils/createEnum';
import { ServiceFormSections } from './formSectionConfig';

export interface FormSectionProps {
  section: EnumValues<typeof ServiceFormSections>;
  title?: string;
  description?: string;
  titleWrapperClassName?: string;
}

export const FormSection = memo<PropsWithChildren<FormSectionProps>>((props) => {
  const { section, title, children, titleWrapperClassName, description } = props;

  const { title: configTitle, description: configDescription } = ServiceFormSections.mapLabels[section];

  const displayedDescription = description || configDescription;

  return (
    <div className="moe-flex moe-flex-col moe-gap-m" id={section}>
      <div className={cn(titleWrapperClassName)}>
        <Heading size="3">{title || configTitle}</Heading>
        <Condition if={displayedDescription}>
          {typeof displayedDescription === 'string' ? (
            <Text variant="small" className="moe-text-tertiary moe-mt-xxs">
              {displayedDescription}
            </Text>
          ) : (
            displayedDescription
          )}
        </Condition>
      </div>
      {children}
    </div>
  );
});
