import { useSelector } from 'amos';
import { useMemo } from 'react';
import { selectBDFeatureEnable } from '../../../../../store/company/company.selectors';
import { selectCompanyCareTypeNameMap } from '../../../../../store/careType/careType.selectors';
import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { AddonItemType } from '../../../../../store/service/category.boxes';
import { createEnum } from '../../../../../store/utils/createEnum';

export const DiscountApplicableServiceTabs = createEnum({
  Services: [
    1,
    {
      title: 'Services',
      serviceType: ServiceType.SERVICE,
      serviceItemType: ServiceItemType.GROOMING,
    },
  ],
  Addon: [
    2,
    {
      title: 'Add-ons',
      serviceType: ServiceType.ADDON,
      serviceItemType: AddonItemType.serviceAddon,
    },
  ],
});

export const useDiscountServiceTabs = () => {
  const [enableBD, companyCareTypeMap] = useSelector(selectBDFeatureEnable, selectCompanyCareTypeNameMap);
  return useMemo(() => {
    const tabs = enableBD
      ? createEnum({
          Boarding: [
            1,
            {
              title: companyCareTypeMap.Boarding,
              serviceType: ServiceType.SERVICE,
              serviceItemType: ServiceItemType.BOARDING,
            },
          ],
          Daycare: [
            2,
            {
              title: companyCareTypeMap.Daycare,
              serviceType: ServiceType.SERVICE,
              serviceItemType: ServiceItemType.DAYCARE,
            },
          ],
          Grooming: [
            3,
            {
              title: companyCareTypeMap.Grooming,
              serviceType: ServiceType.SERVICE,
              serviceItemType: ServiceItemType.GROOMING,
            },
          ],
          Addon: [
            4,
            {
              title: 'Add-ons',
              serviceType: ServiceType.ADDON,
              serviceItemType: AddonItemType.serviceAddon,
            },
          ],
        })
      : DiscountApplicableServiceTabs;

    return {
      serviceTabs: tabs,
      defaultActiveTab: tabs.values[0],
    };
  }, [enableBD, companyCareTypeMap]);
};
