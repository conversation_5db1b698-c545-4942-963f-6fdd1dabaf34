import React, { useMemo } from 'react';
import { ServiceSelect } from './ServiceSelect';
import { type AllServicesItemType } from '../../../../store/service/category.boxes';
import { Form, Input, LegacySelect as Select, type useForm } from '@moego/ui';
import { DiscountUnit } from '@moego/api-web/moego/models/membership/v1/redeem_models';
import { type MembershipModel, type DiscountItem } from '../../../../store/membership/membership.boxes';
import { ProductSelect } from './ProductSelect';
import { PackageSelect } from './PackageSelect';
import { MembershipDiscountType } from '../../types';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { useSelector } from 'amos';

interface BenefitFormItemProps {
  form: ReturnType<typeof useForm<MembershipModel>>;
  index: number;
  isDisabled: boolean;
  item: DiscountItem;
  type: MembershipDiscountType;
  serviceType: (typeof AllServicesItemType)[keyof typeof AllServicesItemType][];
  selectAllLabel?: string;
  disabledIds?: number[];
}

export const BenifitFormItem = ({
  item,
  form,
  index,
  isDisabled,
  type,
  serviceType,
  selectAllLabel,
  disabledIds,
}: BenefitFormItemProps) => {
  const [business] = useSelector(selectCurrentBusiness);

  const unitOptions = useMemo(() => {
    const currencySymbol = business.currencySymbol;
    return [
      {
        label: '%',
        value: DiscountUnit.PERCENT,
      },
      {
        label: currencySymbol,
        value: DiscountUnit.NUMERICAL,
      },
    ];
  }, [business.currencySymbol]);
  return (
    <div className="moe-flex moe-shrink-0 moe-pr-4 moe-gap-4">
      {(type === MembershipDiscountType.ServiceDiscount || type === MembershipDiscountType.AddOnDiscount) && (
        <ServiceSelect
          showTabs
          form={form}
          multiple
          disabled={isDisabled}
          formItemName={`${type}.${index}.ids`}
          formItemSelectAllName={`${type}.${index}.isAll`}
          acceptAllServices
          selectAllLabel={selectAllLabel}
          serviceType={serviceType}
          rules={{
            required: true,
          }}
          disabledServiceIds={disabledIds}
        />
      )}
      {type === MembershipDiscountType.ProductDiscount && (
        <ProductSelect
          form={form}
          disabled={isDisabled}
          formItemName={`${type}.${index}.ids`}
          formItemSelectAllName={`${type}.${index}.isAll`}
          acceptAll
          rules={{
            required: true,
          }}
          disabledIds={disabledIds}
        />
      )}

      {type === MembershipDiscountType.PackageDiscount && (
        <PackageSelect
          form={form}
          disabled={isDisabled}
          formItemName={`${type}.${index}.ids`}
          formItemSelectAllName={`${type}.${index}.isAll`}
          acceptAll
          rules={{
            required: true,
          }}
          disabledIds={disabledIds}
        />
      )}
      <Form.Item
        name={`${type}.${index}.count`}
        rules={{
          required: true,
          max:
            item?.unit === DiscountUnit.PERCENT
              ? {
                  value: 100,
                  message: `The maximum value is 100`,
                }
              : undefined,
        }}
      >
        <Input.Number
          className="moe-w-[124px]"
          placeholder="Amount"
          isDisabled={isDisabled}
          minValue={1}
          maxValue={item?.unit === DiscountUnit.PERCENT ? 100 : undefined}
        />
      </Form.Item>
      <Form.Item name={`${type}.${index}.unit`} rules={{ required: true }}>
        <Select
          className="moe-w-[100px]"
          isSearchable={false}
          isDisabled={isDisabled}
          options={unitOptions}
          placeholder="Unit"
        />
      </Form.Item>
    </div>
  );
};
