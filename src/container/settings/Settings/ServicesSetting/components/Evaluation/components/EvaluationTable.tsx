import { Empty } from '@moego/ui';
import React, { memo, useMemo, useRef } from 'react';
import { useVisibleEffect } from '../../../../../../../utils/hooks/hooks';

import { useSelector } from 'amos';
import { Condition } from '../../../../../../../components/Condition';
import { type EvaluationRecord } from '../../../../../../../store/evaluation/evaluation.boxes';
import { selectEvaluationList } from '../../../../../../../store/evaluation/evaluation.selectors';
import { BasedTable } from '../../BasedTable';
import { useGetEvaluationColumns } from '../hooks/useGetEvaluationColumns';
import {
  ALL_LOCATIONS,
  type ALL_LOCATIONS_TYPE,
} from '../../../../../../../components/Business/SingleLocationSelector';

export interface ServiceTableProps {
  className?: string;
  onEdit: (service: EvaluationRecord) => void;
  onDelete: (service: EvaluationRecord) => void;
  onDuplicate: (service: EvaluationRecord) => void;
  isInactive: boolean;
  selectedBusinessId: string | ALL_LOCATIONS_TYPE;
}

export const EvaluationTable = memo(function EvaluationTable(props: ServiceTableProps) {
  const { className, selectedBusinessId, isInactive, onDelete, onEdit, onDuplicate } = props;
  const ref = useRef<HTMLDivElement>(null);
  const basedTableRef = useRef<HTMLDivElement>(null);
  const [services] = useSelector(selectEvaluationList);
  const columns = useGetEvaluationColumns({
    onDelete,
    onEdit,
    onDuplicate,
  });

  const filterServices = useMemo(() => {
    return services.filter((service) => {
      const isBusinessMatch =
        selectedBusinessId === ALL_LOCATIONS || service.availableBusinessIds?.includes(selectedBusinessId);
      const isActiveMatch = isInactive ? !service.isActive : service.isActive;
      return isBusinessMatch && isActiveMatch;
    });
  }, [selectedBusinessId, isInactive, services]);

  const visible = useVisibleEffect(ref, [services.length]);

  return (
    <div className="moe-full">
      <div ref={ref}>
        <Condition if={visible && filterServices.length}>
          <BasedTable
            ref={basedTableRef}
            className={className}
            data={filterServices}
            columns={columns}
            onRowClick={onEdit}
          />
        </Condition>
        <Condition if={!filterServices.length}>
          <Empty
            className="moe-mt-[118px]"
            title=""
            description={`${isInactive ? 'No inactive' : 'No'} evaluation service available.`}
          />
        </Condition>
      </div>
    </div>
  );
});
