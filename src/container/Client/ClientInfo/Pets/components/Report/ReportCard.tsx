import { Tabs } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { Card } from '../../../../../../components/Card/Card';
import { selectBDFeatureEnable } from '../../../../../../store/company/company.selectors';
import { ReportDaily } from './ReportDaily';
import { ReportGrooming } from './ReportGrooming';
import { type ReportCardProps } from './type';

export const ReportCard = memo((props: ReportCardProps) => {
  const { petId } = props;
  const [isSupportBd] = useSelector(selectBDFeatureEnable());

  if (isSupportBd) {
    return (
      <Card title="Report cards">
        <Tabs disableAnimation>
          <Tabs.Item label="Daily report">
            <ReportDaily petId={petId} />
          </Tabs.Item>
          <Tabs.Item label="Grooming report">
            <ReportGrooming petId={petId} />
          </Tabs.Item>
        </Tabs>
      </Card>
    );
  }

  return (
    <Card title="Report cards">
      <ReportGrooming petId={petId} />
    </Card>
  );
});
