import React, { forwardRef } from 'react';
import cn from 'classnames';
import IconCalendarCheck from '../../../../../../../assets/svg/calendar-schedule-check.svg';
import { SvgIcon } from '../../../../../../../components/Icon/Icon';

interface NotWorkingCellProps {
  isDisabled?: boolean;
}

export const NotWorkingCell = forwardRef((props: NotWorkingCellProps, ref: React.Ref<HTMLDivElement>) => {
  const { isDisabled, ...restProps } = props;

  return (
    <div
      className={cn(
        '!moe-pt-[8px] !moe-pb-[12px] moe-h-full moe-min-h-[62px]',
        isDisabled ? '!moe-cursor-not-allowed moe-opacity-50' : 'moe-group !moe-cursor-pointer',
      )}
      ref={ref}
      {...restProps}
    >
      <div
        className={cn(
          '!moe-font-medium !moe-text-[12px] moe-w-full moe-py-[12px]',
          'moe-relative !moe-px-[6px] !moe-rounded-[8px] !moe-flex !moe-items-center !moe-justify-center moe-whitespace-nowrap',
          'moe-border-[1px] moe-border-solid moe-border-[#E5E5E5]',
          '!moe-text-center !moe-text-[12px] !moe-leading-[18px] !moe-text-[#333]',
        )}
      >
        <SvgIcon
          src={IconCalendarCheck}
          color="#3D414B"
          size={12}
          className="moe-absolute moe-right-[6px] moe-top-[6px]"
        />
        Not working
      </div>
    </div>
  );
});
