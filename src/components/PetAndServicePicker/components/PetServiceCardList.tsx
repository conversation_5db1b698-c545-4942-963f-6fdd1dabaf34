import { cn } from '@moego/ui';
import React, { memo } from 'react';
import { type QuickAddPetServicePayload } from '../../../store/calendarLatest/calendar.types';
import { optionalFunction } from '../../../utils/utils';
import { type ServiceEntry } from '../../ServiceApplicablePicker/types/serviceEntry';
import { PetServiceCard, type PetServiceCardProps } from './PetServiceCard';

export interface PetServiceCardListProps
  extends Pick<PetServiceCardProps, 'prefix' | 'suffix' | 'disabled' | 'appointmentDate' | 'appointmentEndDate'> {
  className?: string;
  currentPetIds: string[];
  onEdit?: (petId: string) => void;
  onDelete?: (petId: string) => void;
  onAdd?: (payload: QuickAddPetServicePayload) => void;
  onGo2PetNotes?: (petId: string) => void;
  getServiceListByPet?: (petId: string) => ServiceEntry[];
  children?: (petId: string) => React.ReactNode;
  priceContent?: (petId: string, serviceId: string, defaultContent: React.ReactNode) => React.ReactNode;
  renderExtraTips?: (petId: string) => React.ReactNode;
}

export const PetServiceCardList = memo<PetServiceCardListProps>((props) => {
  const {
    className,
    currentPetIds,
    onAdd,
    onDelete,
    onEdit,
    onGo2PetNotes,
    getServiceListByPet,
    children,
    priceContent,
    ...rest
  } = props;

  return (
    <div className={cn('moe-flex moe-flex-col moe-gap-s', className)}>
      {currentPetIds.map((petId) => {
        const { disabled } = rest;
        const go2Edit = optionalFunction(() => {
          onEdit?.(petId);
        }, onEdit && !disabled);

        return (
          <PetServiceCard
            {...rest}
            serviceList={getServiceListByPet?.(petId) || []}
            priceContent={
              priceContent ? (serviceId, defaultContent) => priceContent(petId, serviceId, defaultContent) : undefined
            }
            key={petId}
            petId={petId}
            onDelete={optionalFunction(() => onDelete?.(petId), Boolean(onDelete))}
            onEdit={go2Edit}
            onClick={go2Edit}
            onGo2PetNotes={optionalFunction(() => {
              onGo2PetNotes?.(petId);
            }, onGo2PetNotes && !disabled)}
          >
            {children?.(petId)}
          </PetServiceCard>
        );
      })}
    </div>
  );
});
