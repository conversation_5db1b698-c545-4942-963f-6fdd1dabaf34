import React, { Fragment, useMemo } from 'react';
import { Heading, Text } from '@moego/ui';
import { ServiceType } from '../../../../../../store/service/category.boxes';
import { Service } from '../Service/Service';
import { type ServiceProps } from '../Service/Service.type';
import { Addon } from '../Addon/Addon';
import { AdditionalServiceListProvider } from './AdditionalServiceList.context';
import { useServiceDetailDataContext } from '../../contexts/ServiceDetailData.context';
import { withPl } from '../../../../../../utils/calculator';
import { getPriceUnitText, PriceUnitEnum } from '../../../../../../components/ServiceApplicablePicker/utils/priceUnit';
import { isAdditionalServiceRuleHit } from './AdditionalService.utils';

export interface AdditionalServiceListProps extends ServiceProps {
  hideTopDivider?: boolean;
}

export const AdditionalServiceList: React.FC<AdditionalServiceListProps> = (props) => {
  const { petId, appointmentId, form, hideTopDivider } = props;

  const { additionalPetServiceList, petServiceListWithMain, stayNights, onRemoveServiceDetail } =
    useServiceDetailDataContext();

  const { additionalServiceList, minStayLength, priceUnit, isRuleHit } = useMemo(() => {
    const additionalServiceList = additionalPetServiceList
      .filter((p) => p.petId === petId)
      .map((p) => p.serviceList)
      .flat();
    const petMainServiceInfo = petServiceListWithMain.find((p) => p.petId === petId)?.mainServiceInfo;
    const minStayLength = petMainServiceInfo?.additionalServiceRule?.minStayLength ?? Number.MAX_SAFE_INTEGER;
    const currentStayLength = petMainServiceInfo?.priceUnit === PriceUnitEnum.PER_DAY ? stayNights + 1 : stayNights;
    const isRuleHit = isAdditionalServiceRuleHit(petMainServiceInfo?.additionalServiceRule, currentStayLength);
    return {
      additionalServiceList,
      minStayLength,
      priceUnit: petMainServiceInfo?.priceUnit,
      isRuleHit,
    };
  }, [additionalPetServiceList, petId, petServiceListWithMain, stayNights]);

  if (!additionalServiceList.length || !isRuleHit) {
    return null;
  }

  return (
    <AdditionalServiceListProvider>
      {!hideTopDivider && <div className="moe-border-t moe-border-t-divider moe-border-dashed" />}
      <div className="moe-flex moe-flex-col moe-gap-xs">
        <Heading size="6">Default service(s)/add-on(s)</Heading>
        <div className="moe-p-s moe-rounded-s moe-bg-neutral-sunken-0 moe-flex moe-flex-col moe-gap-s">
          <Text variant="small" className="moe-text-tertiary">
            These items are auto-added for stays of{' '}
            {withPl(minStayLength, priceUnit ? getPriceUnitText(priceUnit) : '')} or more:
          </Text>
          <div className="moe-flex moe-flex-col">
            {additionalServiceList.map((service, index) => {
              return (
                <Fragment key={service.id}>
                  {service.serviceType === ServiceType.Service ? (
                    <Service
                      item={service}
                      key={service.id}
                      petId={petId}
                      appointmentId={appointmentId}
                      form={form}
                      canRemove
                      onRemove={() => onRemoveServiceDetail(service.id)}
                    />
                  ) : (
                    <Addon
                      item={service}
                      key={service.id}
                      petId={petId}
                      appointmentId={appointmentId}
                      canRemove
                      onRemove={() => onRemoveServiceDetail(service.id)}
                    />
                  )}
                  {index !== additionalServiceList.length - 1 && (
                    <div className="moe-my-s moe-border-t-[1px] moe-border-divider moe-border-dashed" />
                  )}
                </Fragment>
              );
            })}
          </div>
        </div>
      </div>
    </AdditionalServiceListProvider>
  );
};
