import { IconButton, Radio, RadioGroup } from '@moego/ui';
import { Space } from 'antd';
import React from 'react';

const steps = [1, 5, 10, 20];

const useCounter = (initialValue = 0, initialStep = 1) => {
  const [value, setValue] = React.useState(initialValue);
  const [step, setStep] = React.useState(initialStep);

  const add = () => setValue((prev) => Math.min(prev + Number(step), 100));
  const sub = () => setValue((prev) => Math.max(prev - Number(step), 0));

  return {
    value,
    add,
    sub,
    setStep,
  };
};

export const Rex: React.FC = () => {
  const { value, add, sub, setStep } = useCounter(0, 1);

  return (
    <Space direction="vertical" align="center" className="moe-w-[100%]">
      <h1>Counter by Rex</h1>
      <Space>
        <IconButton icon={<span>-</span>} isDisabled={value <= 0} onPress={sub} />
        <div>{value}</div>
        <IconButton icon={<span>+</span>} isDisabled={value >= 100} onPress={add} />
      </Space>
      <Space className="moe-p-spacing-m">
        <span>Step</span>
        <RadioGroup orientation="horizontal" defaultValue={1} onChange={setStep}>
          {steps.map((step) => (
            <Radio key={step} value={step}>
              {step}
            </Radio>
          ))}
        </RadioGroup>
      </Space>
    </Space>
  );
};
