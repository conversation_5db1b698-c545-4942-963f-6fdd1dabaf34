import { Table } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { type ApptListPrintCardInfo } from '../../../../store/printCard/appointmentList/appointmentList.actions';
import { selectApptListSettings } from '../../../../store/printCard/appointmentList/appointmentList.selectors';
import { useApptListContentColumns } from '../../hooks/useApptListContentColumns';
import { useAppointmentListCardCompareFn } from '../../hooks/useAppointmentListCardCompareFn';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';

export const AppointmentListCardTable = memo<ApptListPrintCardInfo>((props) => {
  const { serviceItemType, petDetailsByCareType, lodgingUnits, lodgingTypes } = props;
  const [settings] = useSelector(selectApptListSettings);
  const {
    commentsAndNotes: { showPetNotes, showAlertNotes, showTicketComments },
    petCodeFilter: { petCodeIdList },
  } = settings;

  const showCommentsNotes = showPetNotes || showAlertNotes || showTicketComments;
  const columns = useApptListContentColumns(serviceItemType, showCommentsNotes);

  const showLodging = [ServiceItemType.BOARDING, ServiceItemType.DAYCARE, ServiceItemType.EVALUATION].includes(
    serviceItemType,
  );
  const appointmentListCardCompareFn = useAppointmentListCardCompareFn();

  const filterPetDetails = useMemo(() => {
    if (!petCodeIdList.length) {
      return petDetailsByCareType;
    }
    return petDetailsByCareType.filter(({ pet }) => {
      // filter by petCodeIdList
      return petCodeIdList.some((id) => pet.petCodeIds.includes(id));
    });
  }, [petCodeIdList, petDetailsByCareType]);

  const sortedData = useMemo(() => {
    if (!showLodging) {
      return [...filterPetDetails].sort(appointmentListCardCompareFn);
    }

    return filterPetDetails
      .map((item) => {
        const firstPetService = item.petServices[0];
        const lodgingUnit = lodgingUnits.find((unit) => unit.id === firstPetService.lodgingUnitId);
        const lodgingType = lodgingTypes.find((type) => type.id === firstPetService.lodgingTypeId);
        return {
          ...item,
          petServices: item.petServices.map((petService) => {
            return {
              ...petService,
              lodgingUnitSort: lodgingUnit?.sort,
              lodgingTypeSort: lodgingType?.sort,
              // 在 appointmentListCardCompareFn 中消费
              showLodging: true,
            };
          }),
        };
      })
      .sort(appointmentListCardCompareFn);
  }, [filterPetDetails, showLodging, lodgingUnits, lodgingTypes, appointmentListCardCompareFn]);

  return (
    <Table
      columns={columns}
      data={sortedData}
      getRowId={(row) => `${row.appointment.id}-${row.pet.id}`}
      stickyContainer={window}
      classNames={{
        headTable: 'moe-w-full moe-table-fixed',
        bodyTable: '!moe-w-full',
        bodyRow: 'moe-h-auto',
        headCell:
          'moe-border-l moe-border-[#cbcbcb] last:moe-border-r moe-border-t moe-px-spacing-xs moe-py-spacing-xxs moe-text-[10px] moe-text-primary',
        headSortWrapper: 'moe-block moe-w-full',
        headSortInner: 'moe-block moe-w-full moe-truncate',
        bodyCell:
          'moe-border-l moe-border-[#cbcbcb] last:moe-border-r moe-px-spacing-xs moe-py-spacing-xxs moe-text-[10px] moe-text-primary group-[[data-hovered]]/body-row:moe-bg-transparent',
      }}
    />
  );
});
