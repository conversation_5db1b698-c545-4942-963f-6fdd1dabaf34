import { type AppointmentPetFeedingScheduleDef } from '@moego/api-web/moego/models/appointment/v1/appointment_pet_feeding_schedule_defs';
import { type AppointmentPetMedicationScheduleDef } from '@moego/api-web/moego/models/appointment/v1/appointment_pet_medication_schedule_defs';
import { useValidateSplitLodgings } from '../../../../../components/SplitLodgings/hooks/useValidateSplitLodgings';
import { toastApi } from '../../../../../components/Toast/Toast';
import { DATE_FORMAT_EXCHANGE } from '../../../../../utils/DateTimeUtil';
import { getLodgingIds, useCheckConflictAlert } from '../../../hooks/useCheckConflictAlert';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { apptDisabledServiceDetailListBox, ApptServiceRecord } from '../../../store/appt.boxes';
import { type useForm } from '@moego/ui';
import { type ApptMainServiceEntry, type ApptPetServiceItem } from '../../../store/appt.types';
import dayjs from 'dayjs';
import { type ServiceDetailFormFields, type ServiceDetailFormFieldsService } from '../SelectServiceDetail.type';
import { isNil } from 'lodash';
import { validateFeedingMedicationDate } from '../../EditPetFeedMedication/utils';
import { useSerialCallback } from '@moego/tools';
import { ServiceType } from '../../../../../store/service/category.boxes';
import { useDispatch, useStore } from 'amos';
import { selectApptPetService } from '../../../store/appt.selectors';
import { deleteApptServiceAddOnsByIdList, setAddonForPet, setServiceForPet } from '../../../store/appt.actions';
import { removePlaceholder } from '../SelectServiceDetail.utils';

export interface ServiceDetailSubmitParams {
  appointmentId: string;
  serviceItemType: ServiceItemType;
  petIds?: number[];
  form: ReturnType<typeof useForm<ServiceDetailFormFields>>;
  services: ApptPetServiceItem[];
  mainService: ApptMainServiceEntry;
  onConfirm?: () => void;
}

/**
 * 处理 submit 相关部分
 */
export const useServiceDetailSubmit = (params: ServiceDetailSubmitParams) => {
  const { petIds, appointmentId, serviceItemType, form, services, mainService, onConfirm } = params;
  const store = useStore();
  const dispatch = useDispatch();

  const checkConflictAlert = useCheckConflictAlert();
  const validateSplitLodgings = useValidateSplitLodgings();
  /**
   * check start date 兜底逻辑: 不止grooming 所有的活动都不应该超过主service的日期范围的 这是mvp阶段定下来的基础逻辑
   */
  const checkStartDateValid = () => {
    if (![ServiceItemType.BOARDING, ServiceItemType.DAYCARE].includes(mainService.serviceItemType)) {
      return true;
    }
    const isValid = services.every((s) => {
      const key = ApptServiceRecord.createOwnId(appointmentId, s.id);
      const startDate = form.getValues(`${key}.startDate`);
      if (startDate?.isBefore(dayjs(mainService.startDate)) || startDate?.isAfter(dayjs(mainService.endDate))) {
        form.setValue(`${key}.startDate`, null);
        return false;
      }
      return true;
    });
    if (!isValid) {
      toastApi.error("Date exceeds the range for this pet's stay.");
    }
    return isValid;
  };

  const checkSplitLodgingsValid = () => {
    const serviceFormValues = services.map((service) => {
      const item = form.getValues?.(
        ApptServiceRecord.createOwnId(appointmentId, service.id),
      ) as ServiceDetailFormFieldsService;
      return {
        ...item,
        serviceType: service.serviceType,
        serviceItemType: service.serviceItemType,
        startDate: isNil(item.startDate) ? undefined : dayjs(item.startDate).format(DATE_FORMAT_EXCHANGE),
        endDate: isNil(item.endDate) ? undefined : dayjs(item.endDate).format(DATE_FORMAT_EXCHANGE),
        startTime: isNil(item.startTime) ? undefined : dayjs(item.startTime).getMinutes(),
        endTime: isNil(item.endTime) ? undefined : dayjs(item.endTime).getMinutes(),
        splitLodgings: item.splitLodgings?.map((lodging) => ({
          ...lodging,
          endDate: isNil(lodging.endDate) ? undefined : dayjs(lodging.endDate).format(DATE_FORMAT_EXCHANGE),
          endTime: isNil(lodging.endTime) ? undefined : dayjs(lodging.endDate).getMinutes(),
          startDate: isNil(lodging.startDate) ? undefined : dayjs(lodging.startDate).format(DATE_FORMAT_EXCHANGE),
          startTime: isNil(lodging.startTime) ? undefined : dayjs(lodging.startDate).getMinutes(),
        })),
        isAdditionalService: service.isAdditionalService,
      };
    });
    const invalidRule = validateSplitLodgings(serviceFormValues);
    if (invalidRule) {
      form.setError(invalidRule.errorName, { message: invalidRule.message });
    }
    return !invalidRule;
  };

  const checkMedicationValid = () => {
    return services.every((s) => {
      const key = ApptServiceRecord.createOwnId(appointmentId, s.id);
      const medications = form.getValues(`${key}.medications`);
      if (!medications?.length) {
        return true;
      }
      const isValid = medications?.every((item) => validateFeedingMedicationDate(item.selectedDate));
      if (!isValid) {
        toastApi.error('Please select at least one date for medication.');
      }
      return isValid;
    });
  };

  const handleSubmit = useSerialCallback(async () => {
    const submit = form.handleSubmit(async () => {
      if (!checkStartDateValid()) return;
      if (!checkSplitLodgingsValid()) return;
      if (!checkMedicationValid()) return;

      const lodgingUnitIds = services
        .map(({ id }) => {
          const key = ApptServiceRecord.createOwnId(appointmentId, id);
          const lodgingId = form.getValues(`${key}.lodgingId`);
          const splitLodgings = form.getValues(`${key}.splitLodgings`);
          return getLodgingIds(lodgingId, splitLodgings);
        })
        .flat();

      const { startDate: startDateStr, endDate: endDateStr } = mainService;

      await checkConflictAlert({
        appointmentId,
        startDateStr,
        endDateStr,
        petIds: petIds?.map(String) ?? [],
        lodgingUnitIds,
      });

      // 先执行onConfirm，避免下面actions执行，导致逻辑异常
      await onConfirm?.();

      // 提交前，干掉因为删除 / additional service 覆盖的 serviceDetail
      const disabledServiceDetailIds = store.select(apptDisabledServiceDetailListBox.getList(appointmentId)).toArray();
      await dispatch(deleteApptServiceAddOnsByIdList(appointmentId, disabledServiceDetailIds));

      const actions = services.map((s) => {
        if (s.serviceType === ServiceType.Service) {
          const petService = store.select(selectApptPetService(appointmentId, s.id));
          const { medications, startDate, serviceItemType } = petService;
          const key = ApptServiceRecord.createOwnId(appointmentId, s.id);
          const feedings = form.getValues?.(`${key}.feedings`) || [];

          const daycareEndDateTime = serviceItemType === ServiceItemType.DAYCARE ? { endDate: startDate } : {};
          return setServiceForPet(appointmentId, s.id, {
            serviceItemType,
            feedings: removePlaceholder(feedings) as AppointmentPetFeedingScheduleDef[],
            medications: removePlaceholder(medications) as AppointmentPetMedicationScheduleDef[],
            ...daycareEndDateTime,
          });
        } else {
          return setAddonForPet(appointmentId, s.id, { serviceItemType });
        }
      });
      await dispatch(actions);
    });

    await submit();
  });

  return {
    handleSubmit,
  };
};
