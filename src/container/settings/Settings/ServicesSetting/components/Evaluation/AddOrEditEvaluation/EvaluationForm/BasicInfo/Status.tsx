import { Form, Radio, RadioGroup, booleanToStringify } from '@moego/ui';
import React, { memo } from 'react';
import { useServiceStaffPermissions } from '../../../../AddOrEditComponents/hooks/useServiceStaffPermissions';
import { useEvaluationFormContext } from '../EvaluationFormContext';

export const Status = memo(() => {
  const { isEdit } = useEvaluationFormContext();
  const { isDisabledForRemains } = useServiceStaffPermissions(isEdit);
  return (
    <Form.Item name="isActive" label="Status" transformer={booleanToStringify}>
      <RadioGroup isRequired orientation="horizontal" isDisabled={isDisabledForRemains}>
        <Radio value={`true`}>Active</Radio>
        <Radio value={`false`}>Inactive</Radio>
      </RadioGroup>
    </Form.Item>
  );
});
