import { ExpiryType } from '@moego/api-web/moego/models/marketing/v1/discount_code_enums';
import { AlertDialog } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { Dropdown } from 'antd';
import dayjs from 'dayjs';
import React, { memo, useMemo } from 'react';
import { useHistory, useLocation } from 'react-router';
import { useMount } from 'react-use';
import SvgIconMoreSvg from '../../../../assets/svg/icon-more.svg';
import { Condition } from '../../../../components/Condition';
import { DropdownItem, DropdownList } from '../../../../components/DropdownList.style';
import { SvgIcon } from '../../../../components/Icon/Icon';
import { toastApi } from '../../../../components/Toast/Toast';
import { PATH_DISCOUNT, PATH_DISCOUNT_CREATE } from '../../../../router/paths';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import { changeDiscountStatus, getDiscountList } from '../../../../store/discount/discount.actions';
import { type DiscountRecord } from '../../../../store/discount/discount.boxes';
import { isNormal } from '../../../../store/utils/identifier';
import { Z_INDEX_POPOVER_UNDER_MODAL } from '../../../../style/_variables';
import { useBool } from '../../../../utils/hooks/useBool';
import { useThrottleCallback } from '../../../../utils/hooks/useThrottleCallback';
import { DiscountActionMap, DiscountActionsNeedCTA, DiscountStatusActionMap } from '../Discounts.config';
import { ActivateContent } from './ActivateContent';
import { type ExpireDateSelectValue } from './ExpireDateSelect';

export interface DiscountActionProps {
  discount: DiscountRecord;
}

export const DiscountAction = memo<DiscountActionProps>(({ discount }) => {
  const [permissions, business] = useSelector(selectCurrentPermissions, selectCurrentBusiness);
  const dispatch = useDispatch();
  const history = useHistory();
  const { pathname } = useLocation();
  const actionList = useMemo(() => DiscountStatusActionMap.mapLabels[discount.status] ?? [], [discount.status]);
  const updateExpireDateModalOpen = useBool();

  const [expireDateSelectValue, setExpireDateSelectValue] = React.useState<ExpireDateSelectValue>({
    type: discount.expiryType,
    time: discount.expiryTime ? dayjs(discount.expiryTime) : undefined,
  });

  useMount(() => {
    setExpireDateSelectValue({
      type: discount.expiryType,
      time:
        discount.expiryType === ExpiryType.NEVER
          ? undefined
          : discount.expiryTime
            ? dayjs(discount.expiryTime)
            : undefined,
    });
  });

  const handleDiscountAction = useThrottleCallback(async (action: number) => {
    const nextStatus = DiscountActionMap.mapLabels[action].status;

    // duplicate discount
    if (!isNormal(nextStatus)) {
      toastApi.success('Discount duplicated');
      history.push(PATH_DISCOUNT_CREATE.queried({ duplicateId: discount.id }));
      return;
    }

    if (DiscountActionsNeedCTA.includes(action)) {
      const actionText = DiscountActionMap.mapLabels[action].label;
      const postActionText = `${actionText}d`;
      const title = `${actionText} this discount code`;
      const content = 'It will auto remove this discount code from upcoming appointments.';
      AlertDialog.open({
        title: title,
        content: content,
        confirmText: actionText,
        confirmButtonProps: {},
        cancelButtonProps: {},
        cancelText: 'Cancel',
        async onConfirm() {
          await dispatch(changeDiscountStatus({ id: discount.id, status: nextStatus }));
          toastApi.success(`${postActionText} successfully`);
          dispatch(getDiscountList({}));
          if (action === DiscountActionMap.Delete) {
            const isDiscountDetailPage = PATH_DISCOUNT.match(pathname);
            if (isDiscountDetailPage) {
              history.goBack();
            }
          }
        },
        onClose() {
          AlertDialog.close();
        },
        onCancel() {
          AlertDialog.close();
        },
      });
      return;
    }

    if (action === DiscountActionMap.Activate) {
      if (discount.expiryType === ExpiryType.NEVER || (discount.expiryTime && dayjs().isBefore(discount.expiryTime))) {
        await dispatch(changeDiscountStatus({ id: discount.id, status: nextStatus }));
        toastApi.success(`Activated successfully`);
        dispatch(getDiscountList({}));
        return;
      }
      updateExpireDateModalOpen.open();
    }
  }, 500);

  if (!actionList.length || !permissions.has('canCreateEditArchiveDiscount')) {
    return null;
  }

  const handleActivateConfirm = async () => {
    const nextStatus = DiscountActionMap.mapLabels[DiscountActionMap.Activate].status;
    const expireTime =
      expireDateSelectValue.type === ExpiryType.NEVER
        ? undefined
        : expireDateSelectValue.time
          ? expireDateSelectValue.time.endOf('day').format('YYYY-MM-DDTHH:mm:ssZ')
          : undefined;

    await dispatch(
      changeDiscountStatus({
        id: discount.id,
        status: nextStatus,
        expiryDef: {
          type: expireDateSelectValue.type,
          time: expireTime,
        },
      }),
    );
    toastApi.success(`Activated successfully`);
    dispatch(getDiscountList({}));
    updateExpireDateModalOpen.close();
  };

  return (
    <div onClick={(e) => e.stopPropagation()} className="moe-w-full moe-h-full moe-flex moe-justify-center">
      <Dropdown
        placement="bottomLeft"
        trigger={['hover']}
        overlayStyle={{ zIndex: Z_INDEX_POPOVER_UNDER_MODAL }}
        overlay={
          <DropdownList className="moe-w-[200px]">
            {actionList.map((action) => (
              <DropdownItem
                key={action}
                hoverColor="inherit"
                hoverBackgroundColor="var(--moe-color-bg-brand-subtle)"
                danger={action === DiscountActionMap.Delete}
                onClick={() => {
                  handleDiscountAction(action);
                }}
              >
                {DiscountActionMap.mapLabels[action].label}
              </DropdownItem>
            ))}
          </DropdownList>
        }
      >
        <SvgIcon className="moe-cursor-pointer moe-rotate-90" src={SvgIconMoreSvg} color="#333" size={16} />
      </Dropdown>
      <Condition if={discount.status === DiscountStatusActionMap.Inactive}>
        <AlertDialog
          className="moe-w-[540px]"
          isOpen={updateExpireDateModalOpen.value}
          title="Activate this discount code"
          content={
            <ActivateContent
              dateFormat={business.dateFormat}
              value={expireDateSelectValue}
              onChange={setExpireDateSelectValue}
            />
          }
          confirmText="Activate"
          confirmButtonProps={{
            isDisabled: expireDateSelectValue.time && expireDateSelectValue.time.endOf('day').isBefore(dayjs()),
          }}
          onConfirm={handleActivateConfirm}
          onClose={() => {
            updateExpireDateModalOpen.close();
            setExpireDateSelectValue({
              type: discount.expiryType,
              time: discount.expiryTime ? dayjs(discount.expiryTime) : undefined,
            });
          }}
          onCancel={() => {
            updateExpireDateModalOpen.close();
            setExpireDateSelectValue({
              type: discount.expiryType,
              time: discount.expiryTime ? dayjs(discount.expiryTime) : undefined,
            });
          }}
        />
      </Condition>
    </div>
  );
});
