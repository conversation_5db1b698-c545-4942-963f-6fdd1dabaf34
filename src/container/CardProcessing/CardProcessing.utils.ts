import { PATH_BUNDLE_SALE_FLEXIBLE } from '../../router/paths';
import { KEY_BUSINESS_ID, KEY_COMPANY_ID } from '../../utils/querySessionContextConst';

const ENT_HARDWARE_HREF = 'https://www.moego.shop/collections/hardware';

export const getPurchaseHardwareLink = (isEnterpriseRelatedStaff: boolean) => {
  if (isEnterpriseRelatedStaff) return ENT_HARDWARE_HREF;
  const query = new URLSearchParams(window.location.search);
  return PATH_BUNDLE_SALE_FLEXIBLE.queried({
    [KEY_COMPANY_ID]: query.get(KEY_COMPANY_ID) ?? '',
    [KEY_BUSINESS_ID]: query.get(KEY_BUSINESS_ID) ?? '',
  });
};
