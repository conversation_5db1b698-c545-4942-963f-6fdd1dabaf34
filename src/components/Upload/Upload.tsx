import { compressImageJs, toWebpJs } from '@sourcebug/image-lib';
import { arrayEqual } from 'amos';
import { appendQuery } from 'monofile-utilities/lib/query-string';
import React, {
  type ChangeEvent,
  createElement,
  type DragEvent,
  type ReactNode,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { createPortal } from 'react-dom';
import { useSetState } from 'react-use';
import { UploadLoggerKeys } from '../../config/logger/upload';
import { http } from '../../middleware/api';
import { ByteUnit } from '../../utils/common';
import { useBool } from '../../utils/hooks/useBool';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import type { SerialCallback } from '../../utils/hooks/useSerialCallback';
import { LoggerModule, logger } from '../../utils/logger';
import { displaySize } from '../../utils/misc';
import { checkIfAcceptFiles } from '../../utils/utils';
import { alertApi } from '../Alert/AlertApi';
import { toastApi } from '../Toast/Toast';
import type { UploadHandlerFactoryOptions } from './AvatarUpload';
import { DropUploadArea } from './Upload.style';
import { getImgMeta } from './utils/getImgMeta';

export type UploadHandler = (
  id: number,
  file: File,
  onProgress: (progress: number, id: number) => void,
  onSucceeded: (url: string, id: number) => void,
  onFailed: (reason: any, id: number) => void,
) => AbortController;

const heicList = ['image/heic', 'image/heif'];

function fileToBlob(file: File): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const blob = new Blob([e.target ? e.target.result! : ''].filter(Boolean), { type: file.type });
      resolve(blob);
    };
    reader.onerror = reject;
    reader.readAsArrayBuffer(file);
  });
}

export async function convertFile(file: File): Promise<File> {
  if (heicList.includes(file.type)) {
    try {
      const blob = await fileToBlob(file);
      const { default: heic2any } = await import('heic2any');
      const result = (await heic2any({ blob })) as Blob;
      const convertToPng = new File([result], file.name.replace(/\.(heic|heif)$/, '.png'), { type: 'image/png' });

      return convertToPng;
    } catch {
      return file;
    }
  }
  return file;
}

type CreateUploadHandlerProps = {
  compressStrategy?: UploadHandlerFactoryOptions['compressStrategy'];
};
const createUploadHandler = (props?: CreateUploadHandlerProps) => {
  const { compressStrategy = 'none' } = props || {};
  const fn: UploadHandler = (id, file, onProgress, onSucceeded, onFailed) => {
    const controller = new AbortController();
    const data = new FormData();
    convertFile(file)
      .then((file) => {
        if (['webp', 'any'].includes(compressStrategy)) {
          const isToWebp = compressStrategy === 'webp';
          const compress = isToWebp ? toWebpJs : compressImageJs;
          return compress(file, { type: isToWebp ? 'image/webp' : file.type })
            .then((r) => {
              const diff = file.size - r.size;
              logger.get(LoggerModule.UPLOAD).info(UploadLoggerKeys.CONVERT_IMAGE_SUCCEEDED, {
                diff,
                preSize: file.size,
                nowSize: r.size,
                type: file.type,
              });
              return r;
            })
            .catch((e) => {
              logger.get(LoggerModule.UPLOAD).error(UploadLoggerKeys.CONVERT_IMAGE_FAILED, {
                error: e,
                type: file.type,
                name: file.name,
              });
              return file;
            });
        }
        return file;
      })
      .then((file) => {
        data.append('file', file);
        http
          .open('POST/business/upload/uploadFile', void 0, {
            formData: data,
            signal: controller.signal,
            onUploadProgress: (e) => {
              onProgress(e.total ? e.loaded / e.total : 0, id);
            },
          })
          .then(
            async (res) => {
              const query: Record<string, number | string> = {
                name: file.name,
                fileSize: file.size,
              };
              if (file.type.startsWith('image/')) {
                const meta = await getImgMeta(file);
                query.width = meta.width;
                query.height = meta.height;
              }
              onSucceeded(appendQuery(res.data.url, query), id);
            },
            (err) => {
              onFailed(err, id);
            },
          );
      });
    return controller;
  };
  return fn;
};

const defaultUploadHandler: UploadHandler = createUploadHandler();
const upload2WebpHandler: UploadHandler = createUploadHandler({
  compressStrategy: 'webp',
});

export type UploadStatus = 'pending' | 'uploading' | 'succeeded' | 'failed';

export interface UploadedItem {
  type: 'url';
  id: number;
  url: string;
  base64: string | undefined;
}

export interface SelectedItem {
  type: 'file';
  id: number;
  file: File;
  status: UploadStatus;
  url: string | undefined;
  base64: string | undefined;
  // [0, 1]
  progress: number;
  reason: any;
  cancel: () => void;
}

export type UploadItem = UploadedItem | SelectedItem;

export interface UploadState {
  latest: UploadItem | undefined;
  latestStatus: UploadStatus | undefined;
  /**
   * 如果是单张模式, 未上传时, 此数组中只有一个 url, 上传中, 第一个为上传中的 file, 第二个为已上传的 url
   * 如果是多张模式, 数组中包含上传后的 url 和上传中的 file, 上传文件的添加模式(unshift/push)决定了新选择的文件所在的位置
   */
  items: UploadItem[];
  /**
   * 触发选择文件系统弹窗
   */
  select: () => void;
  /**
   * 删除 items 中的记录, 如果类型是 url, 会直接删除并且会触发 onChange, 如果是 file, 会先取消上传再删除
   * @param index
   */
  deleteItem: (id: number) => void;
  /**
   * 重试, 只对 file 类型生效
   * @param index
   */
  retry: (id: number) => void;

  readonly: boolean;
  disabled: boolean;
}

export interface BaseUploadProps {
  /**
   * 可以选择的文件类型, 比如: ".doc,.pdf,image/*"
   */
  accept: string;
  /**
   * 单文件还是多文件模式
   */
  multiple?: boolean;
  /**
   * input 的 name
   */
  name?: string;
  /**
   * 文件最大尺寸
   */
  maxSize?: number;
  /**
   * 最大文件数量, 默认 20
   */
  maxCount?: number;
  /**
   * UI 渲染函数
   * @param state
   */
  children: (state: UploadState) => ReactNode;
  /**
   * 是否加载文件 base64 内容
   * @param file
   */
  loadFileContent?: (file: File) => boolean;
  /**
   * 上传函数
   */
  upload?: UploadHandler;
  /**
   * 禁用状态
   */
  disabled?: boolean;
  /**
   * 上传追加模式
   */
  addMode?: 'push' | 'unshift';

  readonly?: boolean;
  /**
   * 是否允许拖拽上传
   */
  dndUploadEnable?: boolean;
  /**
   * 拖拽上传区域的提示文字，默认是“Drop the image here”
   */
  dropAreaTips?: string;
  onStart?: (item: SelectedItem) => void;
  onSuccess?: (item: SelectedItem) => void;
  onFail?: (e: any, item: SelectedItem) => void;
  onDrag?: () => void;
  formCallback?: SerialCallback;
}

/**
 * 单文件上传
 */
export interface SingleUploadProps extends BaseUploadProps {
  multiple?: false;
  /**
   * value 必需是字符串
   */
  value?: string;
  /**
   * 回调也会传字符串
   * @param value
   */
  onChange?: (value: string) => void;
}

/**
 * 多文件上传
 */
export interface MultipleUploadProps extends BaseUploadProps {
  multiple: true;
  /**
   * value 必需是数组, 并且值唯一
   */
  value?: string[];
  /**
   * 回调也是数组
   * @param value
   */
  onChange?: (value: string[]) => void;
}

export type UploadProps = SingleUploadProps | MultipleUploadProps;

export const defaultLoadFileContent = (file: File) => /^image\//.test(file.type);

interface UploadInternalState {
  items: UploadItem[];
  values: string[];
}

function urls(items: UploadItem[]) {
  return items.filter((i): i is UploadedItem => i.type === 'url').map((i) => i.url);
}

let uid = 0;

/**
 * 上传逻辑组件, UI 由 children 渲染, 提供了一个默认的 UI 渲染组件 {@see PhotoView}.
 *
 * 支持多文件上传 (multiple = true)
 */
function InnerUpload({
  name,
  value,
  onChange,
  accept,
  multiple,
  maxSize = 2 * ByteUnit.MB,
  children,
  loadFileContent = defaultLoadFileContent,
  upload = defaultUploadHandler,
  disabled = false,
  addMode = 'unshift',
  readonly = false,
  dndUploadEnable = false,
  dropAreaTips = 'Drop the image here',
  onStart,
  onFail,
  onSuccess,
  onDrag,
  formCallback,
}: UploadProps) {
  if (!multiple) {
    addMode = 'unshift';
  }
  const [state, setState] = useSetState<UploadInternalState>({
    items: [],
    values: [],
  });
  const input = useRef<HTMLInputElement>(null);
  const isDragOver = useBool();
  /**
   * value 同步逻辑
   *
   * 如果外部传入的 value 和 本地 values 不同, 则触发同步
   *
   * 如果是单张模式, 则直接使用外部值
   *
   * 如果是多张模式, 如果本地已有, 则使用本地的, 如果本地没有, 则 push 到本地, 如果 readonly, 则使用外部值
   */
  useEffect(() => {
    if (multiple) {
      if (!value) {
        setState({
          items: state.items.filter((v) => v.type === 'file'),
          values: [],
        });
        return;
      }
      if (!Array.isArray(value)) {
        console.error('You are passing a non-array to multiple uploader', value);
        return;
      }
      if (arrayEqual(value, state.values)) {
        return;
      }
      const pending = value.filter((v) => !state.items.some((i) => i.url === v));
      const items = state.items
        .filter((i) => i.type === 'file' || value.includes(i.url))
        .concat(
          pending.map((url) => ({
            type: 'url',
            id: uid++,
            base64: void 0,
            url,
          })),
        );
      const values = items.filter((i): i is UploadedItem => i.type === 'url').map((i) => i.url);
      setState({ items, values });
    } else {
      if (Array.isArray(value)) {
        console.error('You are passing an array to single uploader', value);
        return;
      }
      if (!value) {
        setState({
          items: state.items.filter((v) => v.type === 'file'),
          values: [],
        });
      } else if (state.values[0] === value) {
        return;
      } else {
        setState({
          items: [
            ...state.items.filter((v) => v.type === 'file'),
            { type: 'url', id: uid++, url: value, base64: void 0 },
          ],
          values: [value],
        });
      }
    }
  }, [value, readonly]);
  const handleLoadFileContent = useLatestCallback((base64: string, id: number) => {
    setState({
      items: state.items.map((item): UploadItem => {
        return item.type === 'file' && item.id === id ? { ...item, base64 } : item;
      }),
    });
  });
  const handleProgress = useLatestCallback((progress: number, id: number) => {
    setState({
      items: state.items.map((item): UploadItem => {
        return item.type === 'file' && item.id === id ? { ...item, progress, status: 'uploading' } : item;
      }),
    });
  });
  const handleFail = useLatestCallback((reason: any, id: number) => {
    const item = state.items.find((item) => item.id === id);
    if (item?.type === 'file') {
      onFail?.(reason, item);
      formCallback?.del();
    }
    setState({
      items: state.items.map((item): UploadItem => {
        return item.type === 'file' && item.id === id ? { ...item, reason, status: 'failed' } : item;
      }),
    });
  });
  const handleSuccess = useLatestCallback((url: string, id: number) => {
    const index = state.items.findIndex((v) => v.type === 'file' && v.id === id);
    const item = state.items[index];
    if (item?.type !== 'file') {
      return;
    }
    item.url = url;
    onSuccess?.(item);
    formCallback?.del();
    const items = state.items.slice();
    items[index] = {
      type: 'url',
      id: items[index].id,
      base64: items[index].base64,
      url,
    };
    let values: string[];
    if (multiple) {
      values = urls(items);
      setState({ values, items });
      onChange?.(values as any);
    } else {
      values = [url];
      setState({ values, items: [items[index]] });
      onChange?.(url as any);
    }
  });
  const handleInputChange = useLatestCallback((e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) {
      return;
    }
    handleUploadFiles(files);
    e.target.value = '';
  });

  const handleUploadFiles = useLatestCallback((files: FileList) => {
    const items = state.items.slice();
    for (let i = 0; i < files.length; i++) {
      const file = files[addMode === 'push' ? i : files.length - i - 1];
      if (file.size > maxSize) {
        toastApi.error(`The file size should be less than ${displaySize(maxSize)}!`);
        continue;
      }
      const id = Date.now() + uid++;
      // TODO limit uploading item at the same time.
      const item: SelectedItem = {
        type: 'file',
        id,
        file,
        url: void 0,
        base64: void 0,
        progress: 0,
        status: 'uploading',
        reason: void 0,
        cancel: upload(id, file, handleProgress, handleSuccess, handleFail).abort,
      };
      items[addMode](item);
      onStart?.(item);
      formCallback?.add();
      if (loadFileContent(file)) {
        const reader = new FileReader();
        reader.addEventListener('load', () => {
          handleLoadFileContent(reader.result as string, id);
        });
        reader.readAsDataURL(file);
      }
    }
    setState({ items });
  });

  const handleSelect = useLatestCallback(() => {
    if (!disabled && !readonly) {
      input.current?.click();
    }
  });
  const handleRetry = useLatestCallback((id: number) => {
    const index = state.items.findIndex((i) => i.id === id);
    if (index === -1) {
      return;
    }
    const item = state.items[index];
    if (item.type !== 'file') {
      return;
    }
    const items = state.items.slice();
    items[index] = {
      ...item,
      status: 'uploading',
      cancel: upload(item.id, item.file, handleProgress, handleSuccess, handleFail).abort,
    };
    setState({ items });
  });
  const handleDeleteItem = useLatestCallback((id: number) => {
    const index = state.items.findIndex((i) => i.id === id);
    if (index === -1) {
      return;
    }
    const item = state.items[index];
    const items = state.items.slice();
    items.splice(index, 1);
    if (item.type === 'file') {
      item.cancel();
      setState({ items });
    } else {
      const values = urls(items);
      setState({ values, items });
      onChange?.(multiple ? values : (values[0] as any));
    }
  });

  const handleDrag = useCallback(() => {
    onDrag?.();
  }, []);

  const dndEvents = {
    onDrop: (e: DragEvent<HTMLDivElement>) => {
      // prevent default action (open as link for some elements)
      e.preventDefault();
      isDragOver.close();
      const files = e.dataTransfer.files;
      if (!files.length) {
        return;
      }
      // check if the files are accepted (spread operator here is to convert FileList to File array)
      if (!checkIfAcceptFiles([...files], accept)) {
        alertApi.warn('Please upload files in accepted formats only.');
        return;
      }
      handleUploadFiles(files);
    },
    onDragOver: (e: DragEvent<HTMLDivElement>) => {
      // prevent default to allow drop
      e.preventDefault();
      // only for drag files
      if (e.dataTransfer.types.includes('Files')) {
        isDragOver.open();
        handleDrag();
      }
    },
    onDragLeave: () => {
      isDragOver.close();
      handleDrag();
    },
  };

  const [container] = useState(() => document.createElement('div'));
  useEffect(() => {
    document.body.appendChild(container);
    return () => {
      document.body.removeChild(container);
    };
  }, []);
  const uploadState: UploadState = {
    latest: state.items[0],
    latestStatus: (state.items[0] as SelectedItem)?.status,
    items: state.items,
    select: handleSelect,
    deleteItem: handleDeleteItem,
    retry: handleRetry,
    readonly,
    disabled,
  };

  const renderChildren = () =>
    typeof children === 'function' ? children(uploadState) : createElement(children, uploadState);

  return (
    <>
      {createPortal(
        <input
          name={name}
          type="file"
          ref={input}
          style={inputStyle}
          onChange={handleInputChange}
          className="moego-upload-input"
          accept={accept}
          multiple={multiple}
        />,
        container,
      )}
      {dndUploadEnable ? (
        <div className="!moe-relative" {...dndEvents}>
          <DropUploadArea className={isDragOver.value ? '!moe-flex' : '!moe-hidden'}>{dropAreaTips}</DropUploadArea>
          {renderChildren()}
        </div>
      ) : (
        renderChildren()
      )}
    </>
  );
}

export const Upload = Object.assign(InnerUpload, {
  /**
   * @deprecated Don't use <Upload/> component, use `@moego/ui` instead. So, this handler is not recommended.
   */
  defaultUploadHandler,
  /**
   * @deprecated Don't use <Upload/> component, use `@moego/ui` instead. So, this handler is not recommended.
   */
  upload2WebpHandler,
});

const inputStyle = { display: 'none' };
