import { Button } from '@moego/ui';
import { PlusOutlined } from '@ant-design/icons';
import React from 'react';

interface AddItemContainerProps {
  isDisabled: boolean;
  onAdd: () => void;
  label: string;
}

export const AddItemContainer = ({ isDisabled, onAdd, label }: AddItemContainerProps) => {
  if (isDisabled) {
    return null;
  }
  return (
    <Button className="-moe-ml-2 -moe-mt-[8px]" icon={<PlusOutlined />} onPress={onAdd} variant="tertiary">
      {label}
    </Button>
  );
};
