/**
 * @since 2023-06-07
 * <AUTHOR>
 * @description alert提示卡片，多条消息展示为无序列表
 */
import { type AlertProps } from 'antd/lib/alert';
import classNames from 'classnames';
import React, { memo, type ReactNode } from 'react';
import SvgIconAlertSvg from '../../assets/svg/icon-alert.svg';
import SvgIconCloseSvg from '../../assets/svg/icon-close.svg';
import { useBool } from '../../utils/hooks/useBool';
import { SvgIcon } from '../Icon/Icon';
import { AlertCardView } from './AlertCard.style';

type AlertType = Exclude<AlertProps['type'], undefined | 'info'>;

export interface AlertCardProps {
  type?: AlertType;
  icon?: ReactNode;
  iconSize?: number;
  narrow?: boolean;
  showIcon?: boolean;
  iconClassName?: string;
  closable?: boolean;
  dotClassName?: string;
  contentClassName?: string;
  className?: string;
  message?: ReactNode | ReactNode[];
  children?: ReactNode;
  onClose?: () => void;
  backgroundColor?: string;
}

const ALERT_ICONS: Record<
  AlertType,
  {
    color: string;
    bgColor: string;
  }
> = {
  success: {
    color: '#F96B18',
    bgColor: '#f6ffed',
  },
  warning: {
    color: '#faad14',
    bgColor: '#fff7e8',
  },
  error: {
    color: '#f5222d',
    bgColor: '#FAE6E8',
  },
};

export const AlertCard = memo<AlertCardProps>(
  ({
    type = 'warning',
    message,
    showIcon = true,
    icon,
    iconSize,
    className,
    contentClassName,
    backgroundColor,
    children,
    iconClassName,
    dotClassName,
    closable,
    onClose,
    narrow,
  }) => {
    const visible = useBool(true);
    const list = Array.isArray(message) ? message.filter(Boolean) : [message];
    const config = ALERT_ICONS[type];
    if (!visible.value) {
      return null;
    }

    const handleClose = () => {
      visible.close();
      onClose?.();
    };

    return (
      <AlertCardView
        className={classNames([type, narrow ? 'narrow' : '', className])}
        backgroundColor={backgroundColor || config.bgColor}
      >
        {showIcon
          ? (icon ?? (
              <SvgIcon
                src={SvgIconAlertSvg}
                size={iconSize}
                color={config.color}
                className={classNames(['alert-icon', iconClassName])}
              />
            ))
          : null}
        <div className={classNames([contentClassName])}>
          {list.map((content, index) => {
            return (
              <div key={index} className="!moe-flex !moe-items-start">
                {list.length > 1 ? (
                  <span className={classNames(['!moe-text-[36px] !moe-pr-[4px]', dotClassName])}>·</span>
                ) : null}
                {content}
              </div>
            );
          })}
        </div>
        {children}
        {closable ? <SvgIcon src={SvgIconCloseSvg} onClick={handleClose} size={16} color="#333" /> : null}
      </AlertCardView>
    );
  },
);
