import React, { useState } from 'react';
import { <PERSON>dal, Alert, type ModalProps, Text } from '@moego/ui';

import { useModal } from '../../../../../components/Modal/useModal';

export const ModalNotMatchCheckInModal = (props: ModalProps) => {
  const [isLoading, setIsLoading] = useState(false);

  return (
    <Modal
      isOpen
      title="Check-in date mismatch"
      cancelText="Cancel check in"
      confirmText="Continue to check in"
      {...props}
      confirmButtonProps={{
        isLoading,
      }}
      onConfirm={async (e) => {
        setIsLoading(true);
        await props.onConfirm?.(e);
        setIsLoading(false);
      }}
      size="s"
    >
      <Alert isCloseable={false} isBordered={true} color="warning">
        <Text variant="small" className="moe-text-primary">
          <Text as="span" className="moe-font-bold" variant="small">
            Warning
          </Text>
          : The start date for the selected appointment is
          <Text as="span" className="moe-font-bold" variant="small">
            {' '}
            not today
          </Text>
          . Please verify the correct check-in date before proceeding
        </Text>
      </Alert>
    </Modal>
  );
};

export function useNotMatchCheckInModal() {
  return useModal(ModalNotMatchCheckInModal);
}
