import React from 'react';
import { ServiceBriefView } from '@moego/api-web/moego/models/offering/v1/service_models';
import { BoardingServiceDetailModel } from '@moego/api-web/moego/models/online_booking/v1/boarding_service_detail_models';
import { DaycareServiceDetailModel } from '@moego/api-web/moego/models/online_booking/v1/daycare_service_detail_models';
import { useServiceDateTimeText } from '../hooks/useServiceDateTimeText';
import { Markup, Text } from '@moego/ui';

interface ServiceDateTimeTextProps {
  service: ServiceBriefView;
  serviceDetail: BoardingServiceDetailModel | DaycareServiceDetailModel;
}
export const ServiceDateTimeText = (props: ServiceDateTimeTextProps) => {
  const { service, serviceDetail } = props;
  const dateTimeText = useServiceDateTimeText(service, serviceDetail);
  return (
    <div className="moe-text-primary">
      <Markup as="span" variant="small">
        {service.name}:&nbsp;
      </Markup>
      <Text as="span" variant="small">
        {dateTimeText}
      </Text>
    </div>
  );
};
