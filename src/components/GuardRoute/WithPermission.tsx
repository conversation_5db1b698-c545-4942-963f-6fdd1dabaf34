/*
 * @since 2020-09-29 11:33:19
 * <AUTHOR> <<EMAIL>>
 */

import { useSelector } from 'amos';
import type { FormItemProps } from 'antd/es/form';
import FormItem from 'antd/es/form/FormItem';
import type { Set } from 'immutable';
import { intersection } from 'lodash';
import React, { cloneElement, type FC, type ReactElement, type ReactNode } from 'react';
import type { PermissionKinds } from '../../store/business/role.boxes';
import { selectCurrentPermissions, selectOwnerPermissions } from '../../store/business/role.selectors';
import type { StaffRecord } from '../../store/staff/staff.boxes';
import { selectCurrentStaff } from '../../store/staff/staff.selectors';

export interface JudgePermissionProps {
  permissions: PermissionKinds | PermissionKinds[];
  currentUserPermission: PermissionKinds[];
  /**
   * 条件判断模式
   * @default and
   */
  conditionOperator?: 'and' | 'or';
}

/**
 * 判断permission是否包含在当前user里面
 * 支持and|or两种判断模式
 */
export function judgePermission(params: JudgePermissionProps) {
  const { currentUserPermission, conditionOperator = 'and' } = params;
  const permissions = Array.isArray(params.permissions) ? params.permissions : [params.permissions];

  // 尝试将两端的差异抹平,不然心智负担大
  // 使用lodash保证web和mobile逻辑处理一致,方便搬移
  if (conditionOperator === 'and') {
    return intersection(permissions, currentUserPermission).length === permissions.length;
  }
  if (conditionOperator === 'or') {
    return intersection(permissions, currentUserPermission).length > 0;
  }
  return false;
}

export interface UsePermissionCheckProps {
  permissions:
    | PermissionKinds
    | PermissionKinds[]
    | ((permissions: Set<PermissionKinds>, staff: StaffRecord) => boolean);
  ignoreDev?: boolean;
  conditionOperator?: JudgePermissionProps['conditionOperator'];
  /**
   * 检查当前角色的权限还是owner的权限
   * @default current
   */
  checkCurrentRoleOrOwner?: 'current' | 'owner';
}

export const usePermissionCheck = (props: UsePermissionCheckProps) => {
  const { permissions, ignoreDev, conditionOperator, checkCurrentRoleOrOwner = 'current' } = props;
  const [staff, currentRolePermissions, ownerPermissions] = useSelector(
    selectCurrentStaff,
    selectCurrentPermissions,
    selectOwnerPermissions,
  );
  const owned = checkCurrentRoleOrOwner === 'owner' ? ownerPermissions : currentRolePermissions;
  const hasPermission =
    ignoreDev && __DEV__
      ? true
      : typeof permissions === 'function'
        ? permissions(owned, staff)
        : judgePermission({
            permissions,
            currentUserPermission: owned.toArray(),
            conditionOperator,
          });
  return hasPermission;
};

export interface WithPermissionProps extends UsePermissionCheckProps {
  children: ReactNode | null | ((hasPermission: boolean) => ReactNode | null);
}

export const WithPermission: React.FC<WithPermissionProps> = (
  props: WithPermissionProps,
): React.ReactElement | null => {
  const { children, ...rest } = props;
  const hasPermission = usePermissionCheck(rest);

  return typeof children === 'function'
    ? (children(hasPermission) as ReactElement | null)
    : hasPermission
      ? (children as ReactElement | null)
      : null;
};

export interface PermFormItemProps extends FormItemProps {
  permissions: PermissionKinds | PermissionKinds[];
  children: ReactElement<{ disabled?: boolean }>;
  disabled?: boolean;
}

export const PermFormItem: FC<PermFormItemProps> = ({
  permissions,
  name,
  rules,
  children,
  disabled = false,
  ...props
}) => (
  <WithPermission permissions={permissions}>
    {(hasPermission) => (
      <FormItem name={hasPermission ? name : '$' + name} rules={hasPermission ? rules : []} {...props}>
        {cloneElement(children, { disabled: !hasPermission || disabled })}
      </FormItem>
    )}
  </WithPermission>
);
