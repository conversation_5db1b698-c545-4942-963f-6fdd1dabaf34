import { type GetApplicableServiceListParams } from '@moego/api-web/moego/api/offering/v1/service_api';
import { type Dispatch, useDispatch, useSelector } from 'amos';
import { curry } from 'lodash/fp';
import { useMemo, useRef } from 'react';
import { useAsync } from 'react-use';
import { type getAppointmentService, getMultiplePetsAppointmentService } from '../../../container/Appt/store/appt.api';
import { currentBusinessIdBox } from '../../../store/business/business.boxes';
import { getSavedPriceList } from '../../../store/pet/petSavedPrice.actions';
import { getPetLastServices } from '../../../store/pet/petServices.actions';
import { selectPetLastService } from '../../../store/pet/petServices.selectors';
import { getAllBusinessBasicServiceInfoList } from '../../../store/service/actions/public/service.actions';
import { ServiceType, serviceCategoryMapBox } from '../../../store/service/category.boxes';
import { selectBusinessServiceCategories } from '../../../store/service/category.selectors';
import { selectAllActiveServiceIdList } from '../../../store/service/service.selectors';
import { type EnumValues } from '../../../store/utils/createEnum';
import { ID_ANONYMOUS, isNormal } from '../../../store/utils/identifier';
import { type RequireKeys, type ReturnTypeAmosAction } from '../../../types/common';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import { useImperativeServicePriceDurationInfo } from './useServiceInfo';

interface Params extends Omit<GetApplicableServiceListParams, 'petIds' | 'petId'> {
  staffId?: number;
  petIds: string[];
}

export interface MixedService {
  categoryId: number;
  categoryName: string;
  serviceId: number;
  serviceType: number;
  applicable: boolean;
  serviceName?: string;
}

export interface CategoryItem {
  categoryId: number;
  categoryName: string;
  services: MixedService[];
}

export type GetApplicablePetServiceResultMulti =
  | Awaited<ReturnTypeAmosAction<typeof getMultiplePetsAppointmentService>>
  | undefined;

export const reduceByCategory = (acc: CategoryItem[], cur: MixedService) => {
  const { categoryId, categoryName } = cur;
  const hasCategory = acc.find((item) => item.categoryId === categoryId);
  if (hasCategory) {
    hasCategory.services.push(cur);
  } else {
    acc.push({
      categoryId,
      categoryName,
      services: [cur],
    });
  }
  return acc;
};

const mapApplicable = curry((applicable: boolean, item: CategoryItem) => {
  const nextItem: CategoryItem = {
    ...item,
    services: item.services.filter((i) => (applicable ? i.applicable : true)),
  };
  return nextItem;
});

const filterLessOneServices = (category: CategoryItem) => category.services.length > 0;

export function useApplicablePetServices(params: Params) {
  const { serviceType, serviceItemType, staffId, petIds } = params;
  // 对于只选一个 pet 和 edit 模式下，都需要使用 petId 去获取 last service saved 信息等
  const petId = Number(petIds[0] || ID_ANONYMOUS);
  const applicable = params.onlyAvailable;
  const cacheRef = useRef<Record<string, ReturnTypeAmosAction<typeof getAppointmentService> | undefined>>({});

  const dispatch = useDispatch();
  const [categoryMap, lastServiceList, lastAddonList, activeServiceIds, businessId, businessServiceCategoriesList] =
    useSelector(
      serviceCategoryMapBox,
      selectPetLastService(petId, ServiceType.Service),
      selectPetLastService(petId, ServiceType.Addon),
      selectAllActiveServiceIdList,
      currentBusinessIdBox,
      selectBusinessServiceCategories(serviceType),
    );
  const getServicePriceDurationInfo = useImperativeServicePriceDurationInfo();

  const { loading, value } = useAsync(async () => {
    const result = await getApplicablePetService(
      { ...params, petIds, businessId: String(businessId) },
      dispatch,
      cacheRef,
    );
    return result;
  }, [petId, serviceType, serviceItemType, applicable, petIds?.length]);

  const { loading: extraLoading } = useAsync(async () => {
    if (!isNormal(petId)) {
      return;
    }

    await Promise.all([
      dispatch(getPetLastServices(petId)),
      petIds ? dispatch(petIds.map((id) => getSavedPriceList(Number(id)))) : dispatch(getSavedPriceList(petId)),
      !businessServiceCategoriesList.size && dispatch(getAllBusinessBasicServiceInfoList({ inactive: false })), // 仅需获取一次 biz 下的全部 service
    ]);
  }, [petId, petIds?.length]);

  const { serviceCategories, addonCategories, categories } = useMemo(() => {
    const { serviceCategories, addonCategories, categories } = groupByCategory(value);
    return {
      serviceCategories,
      addonCategories,
      categories,
    };
  }, [value, categoryMap]);

  const applicableServices = useMemo(
    () => getApplicableByCategory(serviceCategories, applicable),
    [serviceCategories, applicable],
  );

  const applicableAddons = useMemo(
    () => getApplicableByCategory(addonCategories, applicable),
    [addonCategories, applicable],
  );

  const applicableServiceIds = useMemo(() => getApplicableIdsByCategory(applicableServices), [applicableServices]);

  const applicableAddonIds = useMemo(() => getApplicableIdsByCategory(applicableAddons), [applicableAddons]);

  /** 获取saved / by staff service信息 */
  const getServiceInfo = useLatestCallback((serviceId: number) =>
    getServicePriceDurationInfo({ petId, serviceId, staffId }),
  );

  const lastActiveServiceIds = useMemo(
    () => lastServiceList.filter((serviceId) => activeServiceIds.includes(serviceId)),
    [lastServiceList, activeServiceIds],
  );

  const lastActiveAddonIds = useMemo(
    () => lastAddonList.filter((serviceId) => activeServiceIds.includes(serviceId)),
    [lastAddonList, activeServiceIds],
  );

  const lastActiveServiceList = useMemo(() => {
    return categories.filter(
      (item) => item.serviceType === ServiceType.Service && lastActiveServiceIds.includes(item.serviceId),
    );
  }, [lastActiveServiceIds, categories]);

  const lastActiveAddonList = useMemo(() => {
    return categories.filter(
      (item) => item.serviceType === ServiceType.Addon && lastActiveAddonIds.includes(item.serviceId),
    );
  }, [lastActiveAddonIds, categories]);

  /** 包括services和add-ons */
  const lastActiveServices = useMemo(() => {
    return categories.filter(
      (item) => lastActiveAddonIds.includes(item.serviceId) || lastActiveServiceIds.includes(item.serviceId),
    );
  }, [lastActiveAddonIds, lastActiveServiceIds, categories]);

  const getRenderListInfo = (serviceType: EnumValues<typeof ServiceType>) => {
    const isAddons = serviceType === ServiceType.Addon;
    const list = isAddons ? applicableAddons : applicableServices;
    const lastActiveIds = (isAddons ? lastActiveAddonList : lastActiveServiceList).map((item) => item.serviceId);

    const isEmpty = !list.length && !lastActiveIds.length;

    return {
      list,
      lastActiveIds,
      isEmpty,
    };
  };

  return {
    loading: loading || extraLoading,
    applicableServices,
    applicableAddons,
    applicableServiceIds,
    applicableAddonIds,
    lastActiveServiceList,
    lastActiveAddonList,
    lastActiveServices,
    getServiceInfo,
    getRenderListInfo,
  };
}

export async function getApplicablePetService(
  partialParams: RequireKeys<
    Partial<Omit<Params, 'petId'>>,
    'serviceType' | 'selectedServiceIds' | 'onlyAvailable' | 'businessId' | 'petIds'
  >,
  dispatch: Dispatch,
  cacheRef?: React.MutableRefObject<
    Record<
      string,
      ReturnTypeAmosAction<typeof getAppointmentService | typeof getMultiplePetsAppointmentService> | undefined
    >
  >,
): Promise<GetApplicablePetServiceResultMulti> {
  const {
    serviceType,
    selectedServiceIds = [],
    serviceItemType,
    businessId,
    onlyAvailable,
    selectedLodgingUnitId,
    petIds,
  } = partialParams;
  const validatePetId = petIds.every(isNormal);
  if (validatePetId) {
    // addon 不传serviceItemType
    const serviceItemTypeParams = serviceType === ServiceType.Service ? { serviceItemType } : undefined;
    const params = {
      serviceType,
      selectedServiceIds,
      businessId,
      onlyAvailable,
      selectedLodgingUnitId,
      selectedServiceItemType: serviceItemType,
      inactive: false,
      petIds,
      ...serviceItemTypeParams,
    };
    const cacheKey = JSON.stringify(params);
    if (cacheRef && cacheRef.current[cacheKey]) {
      return (await cacheRef?.current[cacheKey]) as GetApplicablePetServiceResultMulti;
    }
    const fetcher = dispatch(getMultiplePetsAppointmentService(params));
    try {
      const res = await fetcher;
      if (cacheRef) {
        cacheRef.current[cacheKey] = fetcher;
      }
      return res;
    } catch {}
  }
  return {
    categoryList: [],
    commonCategories: [],
    petServices: [],
  };
}

export function groupByCategory(value: GetApplicablePetServiceResultMulti) {
  const list = value?.categoryList || [];
  const categories = list.map((item) => {
    const { id, type, categoryId, categoryName } = item;

    return {
      categoryId: Number(categoryId),
      categoryName,
      serviceId: Number(id),
      serviceType: type,
      applicable: true,
    } as MixedService;
  });
  const serviceCategories = categories
    .filter((i) => i.serviceType === ServiceType.Service)
    .reduce(reduceByCategory, [] as CategoryItem[]);
  const addonCategories = categories
    .filter((i) => i.serviceType === ServiceType.Addon)
    .reduce(reduceByCategory, [] as CategoryItem[]);

  return {
    serviceCategories,
    addonCategories,
    categories,
  };
}

export function getApplicableIdsByCategory(categories: CategoryItem[]) {
  return categories
    .map((category) => {
      return category.services.map((service) => service.serviceId);
    })
    .flat();
}

export function getApplicableByCategory(categories: CategoryItem[], applicable: boolean) {
  return categories.map(mapApplicable(!!applicable)).filter(filterLessOneServices);
}

export function getApplicableIdsByValue(
  value: GetApplicablePetServiceResultMulti,
  serviceType: EnumValues<typeof ServiceType>,
  applicable: boolean,
) {
  const { serviceCategories, addonCategories } = groupByCategory(value);
  if (serviceType === ServiceType.Service) {
    return getApplicableIdsByCategory(getApplicableByCategory(serviceCategories, applicable));
  }
  return getApplicableIdsByCategory(getApplicableByCategory(addonCategories, applicable));
}
