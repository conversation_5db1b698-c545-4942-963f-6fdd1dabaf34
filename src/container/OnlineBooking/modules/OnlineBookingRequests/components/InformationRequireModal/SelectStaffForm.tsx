import { Form, Heading, LegacySelect as Select, Text, TimePicker, type useForm } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { memo, useEffect, useMemo } from 'react';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { printFullName } from '../../../../../../store/customer/customer.boxes';
import { ServiceType } from '../../../../../../store/service/category.boxes';
import { staffMapBox } from '../../../../../../store/staff/staff.boxes';
import { selectBusinessStaffs } from '../../../../../../store/staff/staff.selectors';
import { type InformationRequireFormRecord, type StaffTimeDetail } from './InformationRequireModal.types';
import { KEY_FIELD_DIVIDER } from './InformationRequireModal.utils';
import { DateTypeWordingMap } from '../../../../../../components/DateType/DateType.utils';

interface SelectStaffFormProps {
  itemKey: string;
  serviceDetail: StaffTimeDetail;
  form: ReturnType<typeof useForm<InformationRequireFormRecord>>;
}

export const StaffFiledPrefix = 'staffId';
export const TimeFiledPrefix = 'stateTime';

export const SelectStaffForm = memo<SelectStaffFormProps>((props) => {
  const { itemKey, serviceDetail, form } = props;
  const [business, bizStaffs, staffMap] = useSelector(selectCurrentBusiness, selectBusinessStaffs, staffMapBox);

  const staffOptions = useMemo(() => {
    const list = bizStaffs.toArray().map((id) => {
      const item = staffMap.mustGetItem(id);
      return {
        value: id,
        label: printFullName(item.firstName, item.lastName),
      };
    });
    return list;
  }, [bizStaffs, staffMap]);

  const staffKey = useMemo(() => [StaffFiledPrefix, itemKey].join(KEY_FIELD_DIVIDER), [itemKey]);
  const timeKey = useMemo(() => [TimeFiledPrefix, itemKey].join(KEY_FIELD_DIVIDER), [itemKey]);

  // 两种模式：单天和多天
  const serviceDateList = (
    serviceDetail?.specificDates?.length ? serviceDetail.specificDates : [serviceDetail.startDate]
  ).filter(Boolean);
  const serviceDateText = serviceDateList?.map((date) => dayjs(date).format(business.dateFormatMD))?.join(', ');
  const dateTypeText = serviceDetail?.dateType ? DateTypeWordingMap[serviceDetail.dateType] : '';
  const isAddOn = serviceDetail?.serviceType === ServiceType.Addon;

  // 默认值的初始化
  useEffect(() => {
    const { defaultStaffId, defaultStartTime } = serviceDetail;
    if (defaultStaffId) {
      form.setValue(staffKey, +defaultStaffId);
    }
    if (defaultStartTime) {
      form.setValue(timeKey, dayjs().setMinutes(defaultStartTime));
    }
  }, [serviceDetail]);

  return (
    <div className="moe-gap-2">
      <div className="moe-flex moe-items-center moe-mb-2">
        <Heading size="6">{isAddOn ? 'Add on' : 'Extra service'}:</Heading>
        <Text variant="small">&nbsp;{serviceDetail?.serviceName}</Text>
      </div>
      <div className="moe-flex moe-items-center moe-mb-2">
        <Heading size="6">Service date:</Heading>
        <Text variant="small">&nbsp;{serviceDateText || dateTypeText || ''}</Text>
      </div>
      <div className="moe-grid moe-grid-cols-2 moe-gap-s">
        <Form.Item
          key={staffKey}
          name={staffKey}
          label="Staff"
          rules={{
            required: 'Please select a staff',
          }}
        >
          <Select isRequired options={staffOptions} placeholder="Select staff" />
        </Form.Item>
        <Form.Item
          key={timeKey}
          name={timeKey}
          label="Time"
          rules={{
            required: 'Please select a time',
          }}
        >
          <TimePicker isRequired placeholder="Select time" format={business.timeFormat()} />
        </Form.Item>
      </div>
    </div>
  );
});
