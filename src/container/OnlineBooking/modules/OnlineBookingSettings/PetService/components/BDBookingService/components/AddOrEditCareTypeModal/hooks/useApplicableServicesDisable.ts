import { useSelector, useStore } from 'amos';
import { selectBusinessCareTypeConfigList } from '../../../../../../../../../../store/onlineBooking/onlineBookingServices.selectors';
import { type ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { selectAllCategoriesOfAllServices } from '../../../../../../../../../../store/service/category.selectors';
import { currentBusinessIdBox } from '../../../../../../../../../../store/business/business.boxes';

export interface UseApplicableServicesDisableParams {
  id?: string;
}

export const useApplicableServicesDisable = (params: UseApplicableServicesDisableParams) => {
  const store = useStore();
  const [businessId] = useSelector(currentBusinessIdBox);

  const [bookingCareTypeConfigs] = useSelector(selectBusinessCareTypeConfigList);

  const getIsDisabled = (careType: ServiceItemType) => {
    const type = `${ServiceType.SERVICE}-${careType}`;
    const categoryServiceList = store.select(selectAllCategoriesOfAllServices(businessId, true, [type]));
    const careTypeServiceOptions = categoryServiceList[type].flatMap((category) => category.options);

    const sameServiceItemTypeCareTypeConfigs = bookingCareTypeConfigs.filter(
      ({ serviceItemType, serviceType, id }) =>
        serviceItemType === careType && serviceType === ServiceType.SERVICE && id !== params.id,
    );

    const sameServiceItemTypeServiceOptions = sameServiceItemTypeCareTypeConfigs.flatMap(
      ({ applicableServices }) => applicableServices.selectedServices,
    );

    // case 1: all service applicable
    // case 2: all services has been selected by several care types
    const isDisabledCareType =
      sameServiceItemTypeCareTypeConfigs.some(({ applicableServices }) => applicableServices.isAllServiceApplicable) ||
      (!!careTypeServiceOptions.length &&
        careTypeServiceOptions.every((option) => sameServiceItemTypeServiceOptions.includes(String(option.value))));

    const isDisabledCareTypeAllService = sameServiceItemTypeCareTypeConfigs.some(
      ({ applicableServices }) =>
        applicableServices.isAllServiceApplicable || applicableServices.selectedServices.length > 0,
    );

    return {
      isDisabledCareType,
      isDisabledCareTypeAllService,
    };
  };

  return getIsDisabled;
};
