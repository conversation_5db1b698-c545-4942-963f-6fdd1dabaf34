import { TimeSlot } from '@moego/api-web/moego/models/smart_scheduler/v1/time_slot_models';
import React, { memo } from 'react';
import { TimeSlotItem } from './TimeSlotItem';
import { BusinessRecord } from '../../../../../../../store/business/business.boxes';
import { useSerialCallback } from '@moego/tools';
import { AlertDialog, Markup, cn } from '@moego/ui';

export interface TimeSlotPickerProps {
  slots: TimeSlot[];
  business: BusinessRecord;
  onSelect: (time: number) => void;
  className?: string;
  value: number;
}

export const TimeSlotPicker = memo<TimeSlotPickerProps>((props) => {
  const { slots, business, onSelect, className, value } = props;

  const handleClick = useSerialCallback(async (isFullyBooked: boolean, time: number) => {
    if (isFullyBooked) {
      const isConfirm = await new Promise<boolean>((resolve) => {
        AlertDialog.open({
          title: 'Override',
          classNames: { container: 'moe-w-[540px]' },
          content: 'This time slot is currently at capacity. Would you like to override and book anyway?',
          onConfirm: () => {
            resolve(true);
          },
          onClose: () => {
            resolve(false);
          },
        });
      });

      if (!isConfirm) {
        return;
      }
    }

    onSelect(time);
  });

  if (!slots.length) {
    return (
      <div className={cn('moe-w-full', className)}>
        <Markup variant="regular-short" className="moe-text-tertiary moe-text-center">
          No available slots
        </Markup>
      </div>
    );
  }

  return (
    <div className={cn('moe-grid moe-grid-cols-4 moe-gap-s moe-w-full', className)}>
      {slots.map((slot, index) => {
        return <TimeSlotItem key={index} slot={slot} onClick={handleClick} business={business} value={value} />;
      })}
    </div>
  );
});

TimeSlotPicker.displayName = 'TimeSlotPicker';
