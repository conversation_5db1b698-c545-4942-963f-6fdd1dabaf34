import React, { useEffect, useImperativeHandle } from 'react';
import { useSetState } from 'react-use';
import {
  MoodQuestion,
  type MoodQuestionType,
  type QuestionRef,
  type ValidateResult,
} from '../../container/settings/GroomingReport/GroomingReportEdit/GroomingReportEdit.options';
import { useQuestionList } from '../../container/settings/Settings/GroomingReportSetting/hooks/useQuestionList';
import { type FeedbackItemType } from '../../store/groomingReport/groomingReportEdit.actions';
import { useBool } from '../../utils/hooks/useBool';
import { useControllableValue } from '../../utils/hooks/useControlledValue';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { memoForwardRef } from '../../utils/react';
import { MoodItem } from './components/MoodItem';
import { type QuestionDef } from '@moego/api-web/moego/models/appointment/v1/daily_report_defs';
import { AddTagModal } from '../Feedback/components/AddTagModal';
import { IconButton } from '@moego/ui';
import { MinorCloseOutlined, MinorPlusOutlined } from '@moego/icons-react';

export interface MoodProps {
  value: string[];
  optionList: string[];
  customList: string[];
  onChange: (value: string[], type: MoodQuestionType) => void;
}

const MoodFC = (props: MoodProps) => {
  const addMoodModalVisible = useBool(false);
  const { customList, optionList } = props;
  const [value, setValue] = useControllableValue<string[]>(props);
  const moodOptionList = useQuestionList(optionList);
  const customOptionList = useQuestionList(customList);

  const handleChange = useLatestCallback((v: string, isActive: boolean) => {
    if (isActive) {
      setValue(
        value.filter((item) => item !== v),
        MoodQuestion.Choices,
      );
    } else {
      setValue([...value, v], MoodQuestion.Choices);
    }
  });

  const handleCustomOptionsChange = useLatestCallback((v?: string) => {
    if (v) {
      setValue([...customList, v], MoodQuestion.CustomOptions);
    }
  });

  const handleCustomOptionsRemove = useLatestCallback((index: number) => {
    const v = customList[index];
    setValue(
      value.filter((item) => item !== v),
      MoodQuestion.Choices,
    );
    setValue(
      customList.filter((_, i) => i !== index),
      MoodQuestion.CustomOptions,
    );
  });

  const showAddCustomOptions = props.customList.length < 5;
  return (
    <div className="!moe-flex !moe-flex-wrap !moe-gap-[8px]">
      {moodOptionList.map((item, index) => {
        const isActive = value.includes(item.value);
        return (
          <MoodItem
            key={index}
            isActive={isActive}
            label={item.label}
            value={item.value}
            onClick={() => handleChange(item.value, isActive)}
          />
        );
      })}
      {customOptionList.map((item, index) => (
        <MoodItem
          key={index}
          isActive={true}
          label={
            <div className="moe-flex moe-items-center moe-justify-between moe-w-full moe-text-primary">
              <div>{item.label}</div>
              <IconButton
                className="moe-w-[16px] moe-h-[16px]"
                icon={<MinorCloseOutlined className="!moe-text-[16px]" />}
                variant="primary"
                color="transparent"
                size="xs"
                onPress={() => handleCustomOptionsRemove(index)}
              />
            </div>
          }
          value={item.value}
        />
      ))}
      {showAddCustomOptions && (
        <MoodItem
          isActive={false}
          onClick={addMoodModalVisible.open}
          label={
            <div className="!moe-flex !moe-items-center !moe-text-[#333]">
              <MinorPlusOutlined className="!moe-text-[16px]" /> Add new
            </div>
          }
        />
      )}
      {addMoodModalVisible.value && (
        <AddTagModal
          isOpen
          existingTags={new Set([...optionList, ...customList])}
          onConfirm={handleCustomOptionsChange}
          onClose={addMoodModalVisible.close}
        />
      )}
    </div>
  );
};

export interface MoodQuestionProps {
  question?: FeedbackItemType | QuestionDef;
  onDirty: () => void;
}

export type MoodQuestionRef = QuestionRef<FeedbackItemType | QuestionDef>;

export const Mood = memoForwardRef<MoodQuestionRef, MoodQuestionProps>(
  ({ question, onDirty }: MoodQuestionProps, ref) => {
    const [moodValue, setMoodValue] = useSetState({
      choices: question?.choices || [],
      customOptions: question?.customOptions || [],
    });

    const handleSetMoodValue = useLatestCallback((v: string[], type: MoodQuestionType) => {
      setMoodValue({
        [type]: v,
      });

      setTimeout(() => {
        onDirty();
      }, 0);
    });

    useEffect(() => {
      if (question) {
        setMoodValue({
          choices: question.choices || [],
          customOptions: question.customOptions || [],
        });
      }
    }, [question]);

    const getData = useLatestCallback(() => {
      return {
        ...question!,
        choices: [...new Set([...moodValue.choices, ...moodValue.customOptions])],
        customOptions: moodValue.customOptions,
      };
    });

    const validate = useLatestCallback((): ValidateResult => {
      // always return true
      return {
        isValid: true,
      };
    });

    useImperativeHandle(ref, () => ({
      getData,
      validate,
    }));

    return (
      <MoodFC
        value={moodValue.choices || []}
        optionList={question?.options || []}
        customList={moodValue.customOptions || []}
        onChange={handleSetMoodValue}
      />
    );
  },
);
