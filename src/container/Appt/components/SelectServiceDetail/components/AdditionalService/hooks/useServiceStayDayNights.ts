import { useSelector } from 'amos';
import { selectMainServiceInAppt } from '../../../../../store/appt.selectors';
import { useWatch, type useForm } from '@moego/ui';
import { ApptServiceRecord } from '../../../../../store/appt.boxes';
import { ServiceType } from '../../../../../../../store/service/category.boxes';
import { useMemo } from 'react';
import dayjs from 'dayjs';
import { type ApptPetIdsServiceList } from '../../../../../store/appt.types';

/**
 * 获取当前 appt 的 stay length，
 * 注意 stay length 在 days 和 nights 场景下单位有差异
 */
export const useServiceStayNights = (params: {
  appointmentId: string;
  // 最初的 petId 和选择 service 的列表
  petIdsServiceList?: ApptPetIdsServiceList;
  form: ReturnType<typeof useForm>;
}) => {
  const { appointmentId, petIdsServiceList, form } = params;
  const [mainService] = useSelector(selectMainServiceInAppt(appointmentId));
  const formKey = petIdsServiceList
    ?.map((pet) => {
      const { petId, serviceList } = pet;
      if (+mainService.petId !== petId) {
        return null;
      }
      const res = serviceList.find(
        (s) => s.serviceType === ServiceType.Service && s.serviceId === mainService.serviceId,
      );
      if (!res) {
        return null;
      }
      return ApptServiceRecord.createOwnId(appointmentId, res.id);
    })
    .find(Boolean) as string | undefined;

  const [formStartDate, formEndDate] = useWatch({
    control: form.control,
    name: [`${formKey}.startDate`, `${formKey}.endDate`],
  });

  const nights = useMemo(() => {
    const startDate = formKey ? formStartDate : mainService.startDate;
    const endDate = formKey ? formEndDate : mainService.endDate;
    if (!startDate || !endDate) {
      return -1;
    }
    return dayjs(endDate).diff(startDate, 'days');
  }, [formStartDate, formEndDate, mainService, formKey]);

  return nights;
};
