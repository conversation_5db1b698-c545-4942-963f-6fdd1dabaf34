/* eslint-disable sonarjs/no-nested-functions */
import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type ApptPetServiceItem, type ApptPetIdsServiceList } from '../../../store/appt.types';
import { useAdditionalPetServiceList } from '../components/AdditionalService/hooks/useAdditionalPetServiceList';
import { useMergedPetServiceList } from '../components/AdditionalService/hooks/useMergedPetServiceList';
import { useServiceStayNights } from '../components/AdditionalService/hooks/useServiceStayDayNights';
import { type useForm } from '@moego/ui';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { ServiceType } from '../../../../../store/service/category.boxes';
import { useDispatch } from 'amos';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { apptDisabledServiceDetailListBox } from '../../../store/appt.boxes';

export interface ServiceDetailDataParams {
  form: ReturnType<typeof useForm>;
  petIds?: number[];
  appointmentId: string;
  serviceItemType: ServiceItemType;
  originPetIdsServiceList?: ApptPetIdsServiceList;
  services: ApptPetServiceItem[];
}

/**
 * 统一处理 Service Detail 编辑用到的表单数据，需要考虑
 * 1. additional 相互合并的场景
 * 2. 标记删除、过滤的支持
 */
export const useServiceDetailData = (params: ServiceDetailDataParams) => {
  const { form, appointmentId, serviceItemType, originPetIdsServiceList, services, petIds } = params;
  const dispatch = useDispatch();

  // 支持删除的标记
  const [removedServiceDetailIds, setRemovedServiceDetailIds] = useState<string[]>([]);

  const onRemoveServiceDetail = useCallback((id: string) => {
    setRemovedServiceDetailIds((prev) => [...prev, id]);
  }, []);

  const { additionalPetServiceList, petServiceListWithMain } = useAdditionalPetServiceList({
    petIds,
    appointmentId,
    originPetIdsServiceList,
    serviceItemType,
  });

  const stayNights = useServiceStayNights({
    appointmentId,
    petIdsServiceList: originPetIdsServiceList,
    form,
  });

  const { finalPetIdsServiceList } = useMergedPetServiceList({
    stayNights,
    originPetIdsServiceList,
    additionalPetServiceList,
    petServiceListWithMain,
  });

  const normalPetIdsServiceAddOnList = useMemo(() => {
    return finalPetIdsServiceList.map((petItem) => ({
      ...petItem,
      serviceList: petItem.serviceList.filter((s) => !s.isAdditionalService),
    }));
  }, [finalPetIdsServiceList]);

  // 优先取petIdsServiceList，case 是grooming / daycare 再添加boarding
  const fullServiceAddOnList = useMemo(() => {
    const origFullServiceAddOnList = finalPetIdsServiceList?.flatMap((item) => item.serviceList) ?? services;
    return origFullServiceAddOnList.filter((s) => !removedServiceDetailIds.includes(s.id));
  }, [finalPetIdsServiceList, removedServiceDetailIds, services]);

  const finalAdditionalPetServiceList = useMemo(() => {
    return additionalPetServiceList.map((petItem) => ({
      ...petItem,
      serviceList: petItem.serviceList.filter((s) => !removedServiceDetailIds.includes(s.id)),
    }));
  }, [additionalPetServiceList, removedServiceDetailIds]);

  /**
   * 展示的服务列表
   */
  const normalServiceAddOnList = useMemo(
    () => fullServiceAddOnList.filter((s) => !s.isAdditionalService),
    [fullServiceAddOnList],
  );
  const normalServiceList = normalServiceAddOnList.filter((s) => s.serviceType === ServiceType.Service);
  const normalAddOnList = normalServiceAddOnList.filter((s) => s.serviceType === ServiceType.Addon);

  const serviceDetailDataContextValue = useMemo(
    () => ({
      additionalPetServiceList: finalAdditionalPetServiceList,
      stayNights,
      petServiceListWithMain,
      onRemoveServiceDetail,
    }),
    [finalAdditionalPetServiceList, stayNights, petServiceListWithMain, onRemoveServiceDetail],
  );

  const serviceDetailIdsShouldBeDeleted = useMemo(() => {
    const ids = new Set<string>(removedServiceDetailIds);
    services.forEach((s) => {
      if (!fullServiceAddOnList.find((fs) => fs.id === s.id)) {
        ids.add(s.id);
      }
    });
    finalAdditionalPetServiceList.forEach((petItem) => {
      const { serviceList } = petItem;
      serviceList.forEach((s) => {
        if (!fullServiceAddOnList.find((fs) => fs.id === s.id)) {
          ids.add(s.id);
        }
      });
    });
    return [...ids];
  }, [finalAdditionalPetServiceList, fullServiceAddOnList, removedServiceDetailIds, services]);

  const handleSyncDisabledServiceDetailList = useLatestCallback(async (detailIdList: string[]) => {
    // 将要删除的 id 同步至 store，供 calendar 场景去消费 + submit 场景去过滤
    dispatch(apptDisabledServiceDetailListBox.setList(appointmentId, detailIdList));
  });

  useEffect(() => {
    handleSyncDisabledServiceDetailList(serviceDetailIdsShouldBeDeleted);
  }, [serviceDetailIdsShouldBeDeleted]);

  return {
    fullServiceAddOnList,
    normalServiceAddOnList,
    normalServiceList,
    normalAddOnList,
    normalPetIdsServiceAddOnList,
    serviceDetailDataContextValue,
    serviceDetailIdsShouldBeDeleted,
  };
};
