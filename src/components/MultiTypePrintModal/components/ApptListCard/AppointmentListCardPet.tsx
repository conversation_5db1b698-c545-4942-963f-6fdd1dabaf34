import { type ListAppointmentCardResultPetView } from '@moego/api-web/moego/api/appointment/v1/print_card_api';
import { useSelector } from 'amos';
import React from 'react';
import { selectApptListSettings } from '../../../../store/printCard/appointmentList/appointmentList.selectors';
import { ListCardPet } from '../common/ListCard/ListCardPet';

export interface AppointmentListCardPetProps {
  pet: ListAppointmentCardResultPetView;
}

export const AppointmentListCardPet = ({ pet }: AppointmentListCardPetProps) => {
  const [settings] = useSelector(selectApptListSettings);

  const { showPetNotes, showAlertNotes, showTicketComments } = settings?.commentsAndNotes ?? {};
  const { showUniqueComment } = settings?.petCodeFilter ?? {};

  const showAdditionalInfo = showPetNotes || showAlertNotes || showTicketComments;

  return <ListCardPet pet={pet} verticalLayout={showAdditionalInfo} showUniqueComment={showUniqueComment} />;
};
