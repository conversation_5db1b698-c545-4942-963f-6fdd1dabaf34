import { useQuery } from '../../../../../store/utils/useQuery';
import { getGroomingServiceAvailability } from '../../../../../store/onlineBooking/actions/private/onlineBookingSettings.actions';

import { currentBusinessIdBox } from '../../../../../store/business/business.boxes';
import { TimeAvailabilityType } from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_enums';
import { useStore } from 'amos';

export function useGroomingAvailability() {
  const store = useStore();
  const businessId = store.select(currentBusinessIdBox);

  const { loading, value } = useQuery(getGroomingServiceAvailability({ businessId: businessId.toString() }));

  return {
    isOBBySlot: value?.timeAvailabilityType === TimeAvailabilityType.TIME_SLOT,
    isShiftSync: value?.enableShiftSync,
    isLoading: loading,
  };
}
