/**
 * @since 2024-02-21 18:31:46
 * <AUTHOR>
 * @description filter item state and actions
 */
import { useDispatch } from 'amos';
import { isEqual } from 'lodash';
import { useMemo } from 'react';
import { toastApi } from '../../../../../components/Toast/Toast';
import {
  ComMoegoCommonParamsFilterParamsProperty as FilterParamsProperty,
  type ComMoegoCommonParamsFilterParamsOperator as FilterParamsOperator,
} from '../../../../../openApi/customer-schema';
import {
  addClientFilter,
  removeClientFilter,
  setEditingFilter,
} from '../../../../../store/customer/clientFilters.action';
import {
  ClientFiltersInitState,
  ClientFiltersRecord,
  type FilterEventPropsModel,
} from '../../../../../store/customer/clientFilters.boxes';
import { markViewFilterChanged } from '../../../../../store/customer/clientView.actions';
import { uniq } from '../../../../../store/utils/utils';
import { useDebounceCallback } from '../../../../../utils/hooks/useDebounceCallback';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { isBothPetTypeBreed, isValidFilterValue } from '../FilterItems/FilterProperty.utils';
import { type ClientViewContextModel, useClientViewContext } from './useClientViewContext';
import { useFilterActions } from './useFilterActions';

export function useFilterProperty(property: FilterParamsProperty, clientViewContext?: ClientViewContextModel) {
  const {
    viewId,
    source,
    rawClientFilterList,
    filtersMaxCount,
    clientFilterListWithEditing,
    clientFilterMap,
    filterConfigMap,
  } = clientViewContext ?? useClientViewContext();
  const filterActions = useFilterActions(clientViewContext);
  const dispatch = useDispatch();
  const propertyKey = ClientFiltersRecord.ownKey(source, property, viewId);
  // Forbid access to filters that do not exist in the list
  const clientFilter = rawClientFilterList.includes(propertyKey)
    ? clientFilterMap.mustGetItem(propertyKey)
    : ClientFiltersInitState;
  const filter = clientFilter as Omit<ClientFiltersRecord, 'operator'> & {
    operator?: FilterParamsOperator;
  };

  // config and state
  const { isReadonly, isRequired, propertyConfig } = useMemo(() => {
    const config = filterConfigMap[property];
    return {
      isReadonly: !!config?.isReadonly,
      isRequired: !!config?.isRequired,
      propertyConfig: config,
    };
  }, [filterConfigMap, property]);
  const isEmpty = useMemo(() => {
    return !isValidFilterValue(filter.value) && !isValidFilterValue(filter.values?.[0]);
  }, [filter]);
  const isExistBefore = useMemo(() => {
    return rawClientFilterList.includes(propertyKey);
  }, [rawClientFilterList, propertyKey]);

  // actions
  const debounceChange = useDebounceCallback(async (prevFilters: ReturnType<typeof filterActions.getFilterParams>) => {
    const filterListParams = filterActions.getFilterParams(false);
    const isChanged = !isEqual(filterListParams, prevFilters);
    if (!isChanged) {
      return;
    }

    dispatch(markViewFilterChanged(viewId, true));
    await filterActions.fetch('init');
  }, 300);

  // handlers
  const handleChange = useLatestCallback(
    (filter: FilterEventPropsModel, option?: { force?: boolean; isEditing?: boolean }) => {
      if (isReadonly && !option?.force) {
        return;
      }

      // for calc max count: add property to list temporarily
      // change the property that existed in list : uniq
      const tempClientFiltersList = uniq(
        clientFilterListWithEditing.concat(ClientFiltersRecord.ownKey(source, property, viewId)),
      );

      // pet type & breed show as 1 filer, but data in store are 2 separate filters
      const bothPetTypeBreed = isBothPetTypeBreed(tempClientFiltersList, source, viewId);
      let maxRequestFilterCount = bothPetTypeBreed ? filtersMaxCount + 1 : filtersMaxCount;
      // 这个筛选器是用来区分customer 和 lead 的，区分不同场景添加，用户无法修改，所以这里不计算在内
      let filterLengthCount = tempClientFiltersList.size;
      if (tempClientFiltersList.some((item) => item.includes(FilterParamsProperty.customer_type))) {
        maxRequestFilterCount -= 1;
        filterLengthCount -= 1;
      }
      // warning if more than max count
      if (filterLengthCount > maxRequestFilterCount) {
        toastApi.neutral(`Does not support more than ${maxRequestFilterCount} filters`);
        return;
      }

      const prevFilters = filterActions.getFilterParams(false);
      dispatch(
        addClientFilter({
          ...filter,
          property,
          source,
          viewId,
        }),
      );
      dispatch(setEditingFilter(option?.isEditing ? property : undefined));
      debounceChange(prevFilters);
    },
  );

  const handleRemove = useLatestCallback((force?: boolean) => {
    if (isReadonly && !force) {
      return;
    }

    const prevFilters = filterActions.getFilterParams(false);
    dispatch(removeClientFilter(source, property, viewId));
    debounceChange(prevFilters);
  });

  return { isEmpty, isRequired, isReadonly, isExistBefore, filter, propertyConfig, handleChange, handleRemove };
}
