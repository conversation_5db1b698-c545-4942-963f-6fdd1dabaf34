import { useEffect, useState } from 'react';
import { useGetAdditionalServiceList } from './useGetAdditionalServiceList';
import { useLatestCallback } from '../../../../../../../utils/hooks/useLatestCallback';
import { type PetServiceListWithMainService, type ApptPetIdsServiceList } from '../../../../../store/appt.types';
import { quickServiceToServices } from '../../../SelectServiceDetail.utils';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useGetMergeServiceList } from '../../../../../hooks/useGetMergeServiceList';
import { useDispatch, useStore } from 'amos';
import { addApptPetService, deleteApptAdditionalServiceAddOns } from '../../../../../store/appt.actions';
import { type ServiceEntry } from '../../../../../../../components/ServiceApplicablePicker/types/serviceEntry';
import { getDefaultService } from '../../../../../../../components/ServiceApplicablePicker/utils/getDefaultService';
import { selectMainServiceInAppt } from '../../../../../store/appt.selectors';
import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';

export interface AdditionalServiceListDataParams {
  petIds?: number[];
  // 选择的 careType
  serviceItemType: ServiceItemType;
  // 总的 appointmentId
  appointmentId: string;
  // petId 和选择的 service 列表
  originPetIdsServiceList?: ApptPetIdsServiceList;
}

export const useAdditionalPetServiceList = (params: AdditionalServiceListDataParams) => {
  const { serviceItemType, appointmentId, originPetIdsServiceList, petIds } = params;
  const dispatch = useDispatch();
  const store = useStore();
  const [additionalPetServiceList, setAdditionalPetServiceList] = useState<ApptPetIdsServiceList>([]);
  const [petServiceListWithMain, setMainPetServiceList] = useState<PetServiceListWithMainService[]>([]);

  const getAdditionalServiceList = useGetAdditionalServiceList();
  const getMergeServiceList = useGetMergeServiceList();
  const onEffectCallback = useLatestCallback(async (currentPetIdsServiceList: ApptPetIdsServiceList) => {
    // remove 之前加过的 additional service 的列表
    dispatch(deleteApptAdditionalServiceAddOns(appointmentId));
    // 开始新增
    const { petAdditionalServiceList, petServiceListWithMainService } = await getAdditionalServiceList({
      petIds,
      appointmentId,
      petIdsServiceList: currentPetIdsServiceList,
    });
    const newBundlePetServiceList = petAdditionalServiceList.map((item) => {
      const mainServiceDetailId = petServiceListWithMainService.find((petItem) => petItem.petId === item.petId)
        ?.mainServiceDetail?.id;
      return {
        petId: item.petId,
        serviceList: quickServiceToServices(item.serviceList, serviceItemType, mainServiceDetailId, true),
      };
    });

    /**
     * 因为 getMergeServiceList 需要传入带 mainService 的数据结构，才能建立正确的 associateId 关联关系，
     * 所以这里 mock 一个，后续的消费不会再依赖这个 mainServiceEntry，所以没有副作用
     */
    const petAdditionalServiceListWithMainServiceEntry = petAdditionalServiceList.map((petItem) => {
      const mainItem = petServiceListWithMainService.find((item) => item.petId === petItem.petId);
      if (!mainItem) return petItem;
      const { mainServiceDetail, mainServiceInfo } = mainItem;
      const mainServiceEntry: ServiceEntry = getDefaultService({
        ...mainServiceInfo.toJSON(),
        id: mainServiceDetail.id,
        serviceType: mainServiceInfo.type,
        serviceItemType: mainServiceDetail.serviceItemType,
        isAdditionalService: false,
      });
      return {
        ...petItem,
        serviceList: [mainServiceEntry, ...petItem.serviceList],
      };
    });

    const { newServiceList: mergedServiceList } = getMergeServiceList({
      petIdsServiceList: petAdditionalServiceListWithMainServiceEntry,
      serviceItemType,
      appointmentId,
    });

    const mainService = store.select(selectMainServiceInAppt(appointmentId));
    await Promise.all(
      petAdditionalServiceList.map(async ({ petId, serviceList }) => {
        // 只取 additional list 新增的 serviceEntry，其他的都过滤掉
        const petServiceList = mergedServiceList
          .filter(({ id }) =>
            // eslint-disable-next-line sonarjs/no-nested-functions
            serviceList?.some(({ id: origId }) => origId === id),
          )
          .map((item) => {
            // additional service / add-on 不预填充 startTime
            return {
              ...item,
              startTime: undefined,
            };
          })
          .map((item) => {
            if (item.dateType === PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY) {
              return {
                ...item,
                startDate: mainService.startDate,
                endDate: undefined,
                endTime: undefined,
                isAdditionalService: true,
              };
            }
            if (item.dateType === PetDetailDateType.PET_DETAIL_DATE_LAST_DAY) {
              return {
                ...item,
                startDate: mainService.endDate,
                endDate: undefined,
                endTime: undefined,
                isAdditionalService: true,
              };
            }
            return {
              ...item,
              isAdditionalService: true,
            };
          });
        if (!petServiceList.length) return;
        await dispatch(addApptPetService(petId, petServiceList, appointmentId));
      }),
    );
    setAdditionalPetServiceList(newBundlePetServiceList);
    setMainPetServiceList(petServiceListWithMainService);
  });

  useEffect(() => {
    // 理论上只触发一次 + 每次触发都会清理掉所有的
    if (originPetIdsServiceList && serviceItemType === ServiceItemType.BOARDING) {
      onEffectCallback(originPetIdsServiceList);
    }
  }, [originPetIdsServiceList, serviceItemType]);

  return {
    additionalPetServiceList,
    petServiceListWithMain,
  };
};
