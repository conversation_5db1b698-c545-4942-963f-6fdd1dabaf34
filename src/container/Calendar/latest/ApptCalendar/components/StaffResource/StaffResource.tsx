import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { memo, useEffect, useMemo, useRef } from 'react';
import { staffServiceAreaBox } from '../../../../../../store/calendarLatest/calendar.boxes';
import { selectBusinessCalendarConfig } from '../../../../../../store/calendarLatest/calendar.selectors';
import { type MoeResourceInfo, type ViewType } from '../../../../../../store/calendarLatest/calendar.types';
import { apptEventCardMapBox } from '../../../../../../store/calendarLatest/card.boxes';
import { type MoeEventInfo } from '../../../../../../store/calendarLatest/card.types';
import { isNoAssignedStaffId } from '../../../../../../store/staff/no-assigned-staff-helper';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../utils/DateTimeUtil';
import { type StaffVisibleChange } from '../../ApptCalendar.card';
import { EstimatedRevenue } from './EstimatedRevenue';
import { StaffHeader } from './StaffHeader';
import { StaffServiceArea } from './StaffServiceArea';

export interface StaffResourceProps {
  viewType: ViewType;
  date: Date;
  resourceInfo: MoeResourceInfo;
  eventCards: MoeEventInfo[];
  onVisibleChange?: StaffVisibleChange;
}

export const StaffResource = memo(function StaffResource(props: StaffResourceProps) {
  const { date, eventCards, resourceInfo, onVisibleChange } = props;
  const { staffId, isWorkingMap } = resourceInfo;
  const [apptEvent, staffServiceArea, { showStaffEstimatedRevenue, showSlotLocation }] = useSelector(
    apptEventCardMapBox,
    staffServiceAreaBox,
    selectBusinessCalendarConfig,
  );
  const dateStr = useMemo(() => dayjs(date).format(DATE_FORMAT_EXCHANGE), [date]);
  const ref = useRef(null);

  const { apptsNum, petsNum } = useMemo(() => {
    const cards = eventCards.map((e) => apptEvent.mustGetItem(e.cardId));
    const ticketPets = cards
      .map((ticket) => ticket.pets.map((pet) => ({ ...pet, appointmentId: ticket.appointmentId })))
      .flat()
      .map((target) => `${target.petId}-${target.appointmentId}`); // 去重要根据一个 petId + appointmentId 来去重，同步后端逻辑，一个 appointment 维度下来计算 pet 数量

    return {
      apptsNum: new Set(cards.map((card) => card.appointmentId)).size,
      petsNum: new Set(ticketPets).size,
      cards,
    };
  }, [eventCards, apptEvent]);

  const serviceAreaId = useMemo(() => {
    const dateToServiceAreaListMap = staffServiceArea.find((item) => item.staffId === staffId)?.workingAreaRange || {};
    const list = dateToServiceAreaListMap?.[dateStr] || [];
    // 理论上某 staff 某个日期只有一个 service area，所以列表取第一个即可；area id 不会是 falsy 值，所以可以或 null
    return list?.[0]?.areaId || null;
  }, [staffServiceArea, staffId, dateStr]);
  const isWorking = isWorkingMap?.[dateStr];

  useEffect(() => {
    if (!ref.current || !onVisibleChange) return;
    const observer = new IntersectionObserver((entries) => {
      const isShow = entries.some((entry) => entry.isIntersecting || entry.intersectionRatio > 0);
      onVisibleChange({
        visible: isShow,
        value: {
          staffId: staffId,
          date: dayjs(date).format(DATE_FORMAT_EXCHANGE),
        },
      });
    });
    observer.observe(ref.current);
    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <div ref={ref}>
      <StaffHeader
        staffId={staffId}
        apptsNum={apptsNum}
        petsNum={petsNum}
        data-date={dateStr}
        date={dateStr}
        showSlotLocation={showSlotLocation}
        className={!isNoAssignedStaffId(staffId) ? 'moe-cursor-pointer' : 'moe-cursor-default'}
      />
      {showStaffEstimatedRevenue && <EstimatedRevenue staffId={staffId} date={dateStr} />}
      <StaffServiceArea data-date={dateStr} staffId={staffId} serviceAreaId={serviceAreaId} isWorking={isWorking} />
    </div>
  );
});
