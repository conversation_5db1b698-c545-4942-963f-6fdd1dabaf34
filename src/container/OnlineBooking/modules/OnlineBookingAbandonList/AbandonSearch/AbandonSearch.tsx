import { useDispatch } from 'amos';
import classNames from 'classnames';
import React, { memo, useCallback, useRef } from 'react';
import { useDebounce } from 'react-use';
import SvgIconRemoveSvg from '../../../../../assets/svg/icon-remove.svg';
import SvgIconSearchSvg from '../../../../../assets/svg/icon-search.svg';
import { SvgIcon } from '../../../../../components/Icon/Icon';
import { SearchInput } from '../../../../../components/SmartClientList/SmartClientListTable.style';
import { getOnlineBookingAbandonList } from '../../../../../store/onlineBooking/actions/private/abandonBooking.actions';
import { usePropsState } from '../../../../../utils/hooks/usePropsState';

export interface AbandonSearchProps {
  defaultKeyword?: string;
  className?: string;
}

export const AbandonSearch = memo(({ defaultKeyword = '', className }: AbandonSearchProps) => {
  const [keyword, setKeyword] = usePropsState(defaultKeyword);
  const runCount = useRef(0);
  const dispatch = useDispatch();

  const onKeywordChange = useCallback((keyword: string) => {
    dispatch(getOnlineBookingAbandonList({ query: { keyword }, clear: true, pageNum: 1 }));
  }, []);

  useDebounce(
    () => {
      if (runCount.current++) {
        onKeywordChange(keyword);
      }
    },
    500,
    [keyword],
  );

  return (
    <SearchInput
      prefix={<SvgIcon color="#666" src={SvgIconSearchSvg} size={16} />}
      value={keyword}
      onChange={(e) => setKeyword(e.target.value)}
      suffix={
        keyword.length > 0 ? (
          <SvgIcon color="#666" size={16} src={SvgIconRemoveSvg} onClick={() => setKeyword('')} />
        ) : null
      }
      // @text-lint ignore
      placeholder={'name, address, number, pet, email...'}
      className={classNames('!moe-mr-0', className)}
    />
  );
});
