import { type ColumnDef, Empty, Heading, Table } from '@moego/ui';
import React from 'react';
import { type DepositRuleVM } from '../../../../../../store/payment/actions/private/deposit.actions';

interface DepositTableProps {
  title: React.ReactNode;
  dataSource: DepositRuleVM[];
  columns: ColumnDef<DepositRuleVM, string>[];
  loading: boolean;
  active: boolean;
}

export const DepositTable = ({ title, dataSource, columns, loading, active }: DepositTableProps) => {
  const renderTableWithSkeleton = () => {
    if (loading || dataSource.length) {
      return (
        <div data-testid={`deposit-table-${active ? 'active' : 'inactive'}`}>
          <Table isLoading={loading} columns={columns} data={dataSource} getRowId={(row) => row.id} scrollX="100%" />
        </div>
      );
    }
    return (
      <Empty
        className="moe-flex moe-items-center moe-justify-center moe-h-[132px] moe-bg-neutral-sunken-0 moe-rounded-l"
        classNames={{
          description: 'moe-text-gray-700',
        }}
        illustration={null}
        title="No data"
        description={`There are currently no ${active ? 'active' : 'inactive'} deposit rules.`}
      />
    );
  };

  return (
    <>
      <Heading as="h3" size="3" className="moe-mt-[40px] moe-mb-[24px]">
        {title}
      </Heading>
      {renderTableWithSkeleton()}
    </>
  );
};
