import React, { type FC, useMemo, useRef } from 'react';
import {
  EvaluationActionType,
  EvaluationSlot,
  type EvaluationSlotRef,
} from '../../../../../../Appt/components/EvaluationCombined/EvaluationSlot';
import { type EvaluationBriefView } from '@moego/api-web/moego/models/offering/v1/evaluation_models';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type BoardingServiceDetailModel } from '@moego/api-web/moego/models/online_booking/v1/boarding_service_detail_models';
import { type DaycareServiceDetailModel } from '@moego/api-web/moego/models/online_booking/v1/daycare_service_detail_models';
import { type useForm } from '@moego/ui';
import { getCreateEvaluationInfoKey, hasDaycareSpecificDates } from './Evaluation.utils';
import { type InformationRequireFormRecord } from '../InformationRequireModal.types';
import { type ApptPetEvaluationInfo } from '../../../../../../Appt/store/appt.types';
import { stringToDateMessage } from '../../../../../../../utils/utils';
import { type ServiceBriefView } from '@moego/api-web/moego/models/offering/v1/service_models';
import { type AcceptBookingRequestV2RequestCreateEvaluationRequest } from '@moego/api-web/moego/service/online_booking/v1/booking_request_service';

interface MissingEvaluationFormProps {
  itemKey: string;
  form: ReturnType<typeof useForm<InformationRequireFormRecord>>;
  petId: string;
  careType: ServiceItemType;
  evaluation: EvaluationBriefView;
  service: ServiceBriefView;
  serviceDetail: BoardingServiceDetailModel | DaycareServiceDetailModel;
}
export const MissingEvaluationForm: FC<MissingEvaluationFormProps> = (props) => {
  const { form, itemKey, petId, service, evaluation, serviceDetail, careType } = props;
  const evaluationSlotRef = useRef<EvaluationSlotRef>(null);
  const createEvaluationInfoKey = getCreateEvaluationInfoKey(itemKey);
  const isBoarding = careType === ServiceItemType.BOARDING;
  const hasSpecificDates = hasDaycareSpecificDates(serviceDetail);

  const startDate = useMemo(() => {
    if (hasSpecificDates) {
      const [firstDate] = serviceDetail.specificDates;
      return firstDate;
    }
    return serviceDetail.startDate;
  }, [hasSpecificDates, serviceDetail]);

  const addEvaluationInfo = () => {
    const values = evaluationSlotRef.current?.getEvaluationFields?.();
    form.setValue(createEvaluationInfoKey, {
      ...values,
      petId,
      evaluationId: evaluation.id,
      startDate: stringToDateMessage(startDate),
      startTime: values?.startTime?.getMinutes() ?? serviceDetail.startTime,
    });
  };

  const editEvaluationInfo = (updateInfo: Partial<ApptPetEvaluationInfo> | undefined) => {
    const currentValue = form.getValues(
      createEvaluationInfoKey,
    ) as AcceptBookingRequestV2RequestCreateEvaluationRequest;
    form.setValue(createEvaluationInfoKey, { ...currentValue, ...updateInfo });
  };

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-s">
      <EvaluationSlot
        ref={evaluationSlotRef}
        petId={petId}
        isBoarding={isBoarding}
        defaultEvaluationId={evaluation.id}
        startTime={serviceDetail.startTime}
        associatedServiceIds={[service.id]}
        startDate={startDate}
        updateEvaluationInfo={(actionType, updateInfo) => {
          switch (actionType) {
            case EvaluationActionType.ADD:
              addEvaluationInfo();
              break;
            case EvaluationActionType.EDIT:
              editEvaluationInfo(updateInfo);
              break;
            case EvaluationActionType.REMOVE:
              form.setValue(createEvaluationInfoKey, undefined);
              break;
          }
        }}
      />
    </div>
  );
};
