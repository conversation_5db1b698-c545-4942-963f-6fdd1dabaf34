import { selectWorkingLocationIdList } from '../../store/business/location.selectors';
import { useLatestCallback } from '../../utils/hooks/useLatestCallback';
import { useLazySelectors } from '../../utils/unstable/createSelectAccessor';

export function useIsWorkingLocation() {
  const [locationIds] = useLazySelectors(selectWorkingLocationIdList());

  return useLatestCallback((nextBizId: number | string) => {
    return locationIds().includes('' + nextBizId);
  });
}
