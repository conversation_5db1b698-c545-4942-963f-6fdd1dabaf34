import { type BoardingSplitLodgingScheduleDef } from '@moego/api-web/moego/models/appointment/v1/boarding_split_lodging_defs';
import { type ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { isUndefined } from 'lodash';
import { matchApptServiceScene } from '../../../container/Appt/store/appt.options';
import { ApptServiceScene } from '../../../container/Appt/store/appt.types';
import { createEnum } from '../../../store/utils/createEnum';
import { isNormal } from '../../../store/utils/identifier';
import {
  SplitLodgingError,
  type SplitLodgingItemValue,
  type SplitLodgingServiceRange,
} from '../utils/SplitLodgings.types';
import { validateSplitLodgings } from '../utils/SplitLodgings.utils';
import { useFormatServiceSplitLodgings } from './useFormatServiceSplitLodgings';

interface ValidateSplitLodgingsParams {
  lodgingId?: string;
  lodgingName?: string;
  startDate?: string;
  endDate?: string;
  startTime?: number;
  endTime?: number;
  splitLodgings?: Partial<BoardingSplitLodgingScheduleDef>[];
  serviceType?: ServiceType;
  serviceItemType?: ServiceItemType;
  isAdditionalService?: boolean;
}

interface IMatchIsValidParams {
  lodgingId?: string;
  lodgingName?: string;
  splitLodgings?: SplitLodgingItemValue[];
  serviceRange?: SplitLodgingServiceRange;
}

const SplitLodgingsRules = createEnum({
  LodgingRequired: [
    1,
    {
      matchIsValid: (params: IMatchIsValidParams) => {
        if (!isNormal(params.lodgingId) && !isNormal(params.lodgingName)) {
          return 'Lodging cannot be empty.';
        }
        return undefined;
      },
    },
  ],
  SplitLodgingsRequired: [
    2,
    {
      matchIsValid: (params: IMatchIsValidParams) => {
        const { splitLodgings, serviceRange, lodgingId, lodgingName } = params;
        if (splitLodgings && splitLodgings.length > 1 && serviceRange) {
          return validateSplitLodgings({ splitLodgings, serviceRange });
        }
        if (!isNormal(lodgingId) && !isNormal(lodgingName)) {
          return 'Lodging cannot be empty.';
        }
        return undefined;
      },
    },
  ],
});

export const useValidateSplitLodgings = () => {
  const formatServiceSplitLodgings = useFormatServiceSplitLodgings();

  /**
   * 构建校验参数
   * @param service
   * @returns
   */
  const buildValidationParams = (service: ValidateSplitLodgingsParams): IMatchIsValidParams => {
    return {
      lodgingId: service.lodgingId,
      lodgingName: service.lodgingName,
      serviceRange: {
        startDate: service.startDate,
        startTime: service.startTime,
        endDate: service.endDate,
        endTime: service.endTime,
      },
      splitLodgings: formatServiceSplitLodgings({
        splitLodgings: service.splitLodgings,
        lodgingId: service.lodgingId,
        lodgingName: service.lodgingName,
      }),
    };
  };

  const validateService = (service: ValidateSplitLodgingsParams) => {
    if (service.serviceType !== ServiceType.SERVICE || service.isAdditionalService) {
      return undefined;
    }

    const serviceScenes = [
      {
        scene: ApptServiceScene.ServiceSplitLodgings,
        rule: SplitLodgingsRules.SplitLodgingsRequired,
      },
      {
        scene: ApptServiceScene.LodgingAssignmentRequired,
        rule: SplitLodgingsRules.LodgingRequired,
      },
    ];

    const matchedScene = serviceScenes.find(({ scene }) =>
      matchApptServiceScene(scene, { serviceItemType: service.serviceItemType }),
    );
    if (!matchedScene) {
      return undefined;
    }

    const { matchIsValid } = SplitLodgingsRules.mapLabels[matchedScene.rule];
    const message = matchIsValid(buildValidationParams(service));
    return message ? { rule: matchedScene.rule, message, errorName: SplitLodgingError } : undefined;
  };

  return (services: Array<ValidateSplitLodgingsParams>) => {
    for (const service of services) {
      const result = validateService(service);
      if (!isUndefined(result)) {
        return result;
      }
    }
    return undefined;
  };
};
