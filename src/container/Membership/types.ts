import { CalendarPeriod } from '@moego/api-web/google/type/calendar_period';
import { DayOfWeek } from '@moego/api-web/google/type/dayofweek';
import { MembershipModelStatus } from '@moego/api-web/moego/models/membership/v1/membership_models';
import { createEnum } from '../../store/utils/createEnum';

export const MembershipStatus = createEnum({
  Active: [MembershipModelStatus.ACTIVE, 'Active'],
  Inactive: [MembershipModelStatus.INACTIVE, 'Inactive'],
});

export const BillingCycle = createEnum({
  Daily: [CalendarPeriod.DAY, 'Day(s)'],
  Weekly: [CalendarPeriod.WEEK, 'Week(s)'],
  Monthly: [CalendarPeriod.MONTH, 'Month(s)'],
  Annually: [CalendarPeriod.YEAR, 'Year(s)'],
} as const);

export const WeekDay = createEnum({
  Monday: [DayOfWeek.MONDAY, 'Monday'],
  Tuesday: [DayOfWeek.TUESDAY, 'Tuesday'],
  Wednesday: [DayOfWeek.WEDNESDAY, 'Wednesday'],
  Thursday: [DayOfWeek.THURSDAY, 'Thursday'],
  Friday: [DayOfWeek.FRIDAY, 'Friday'],
  Saturday: [DayOfWeek.SATURDAY, 'Saturday'],
  Sunday: [DayOfWeek.SUNDAY, 'Sunday'],
} as const);

export const MEMBERSHIP_LIST_CLASS_NAME = 'membership-list-layout';

export enum MembershipDiscountType {
  ServiceDiscount = 'serviceDiscounts',
  AddOnDiscount = 'addOnDiscounts',
  ProductDiscount = 'productDiscounts',
  PackageDiscount = 'packageDiscounts',
}
