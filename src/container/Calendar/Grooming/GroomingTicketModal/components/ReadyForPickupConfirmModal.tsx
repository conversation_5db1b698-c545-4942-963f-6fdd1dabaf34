import { Checkbox, Radio, Space, Spin } from 'antd';
import { type CheckboxChangeEvent } from 'antd/es/checkbox';
import React, { memo, useEffect, useMemo, useState } from 'react';
import { Condition } from '../../../../../components/Condition';
import { ConfirmModalWithClose } from '../../../../../components/Modal/ConfirmModalWithClose';
import { toastApi } from '../../../../../components/Toast/Toast';
import { ApptTestIds } from '../../../../../config/testIds/apptDrawer';
import { useHasBrandedApp } from '../../../../../store/branded/branded.hooks';
import { ReadyForPickupConfirmModalMode } from '../../../../../store/calendarLatest/calendar.boxes';
import { type MessageDetailMethod } from '../../../../../store/message/message.boxes';
import { useEnableFeature } from '../../../../../store/metadata/featureEnable.hooks';
import { META_DATA_KEY_LIST } from '../../../../../store/metadata/metadata.config';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { AppointmentStatus } from '../../../../TicketDetail/AppointmentStatus';
import { ReadyForPickupMessageSendMethodOptions } from '../../../../TicketDetail/interfaces.latest';

export interface ReadyForPickupConfirmModalProps {
  mode: ReadyForPickupConfirmModalMode;
  visible: boolean;
  onClose: () => void;
  onConfirm: (sendMethod?: MessageDetailMethod) => Promise<void>;
  onCancel: () => void;
  canSendAutoMessage: boolean;
  canSendAutoEmail: boolean;
  canSendAppAutoMessage: boolean;
  ticketStatus?: number;
  loading?: boolean;
  isAutoMessageEnable?: boolean;
}

export const ReadyForPickupConfirmModal = memo(
  ({
    mode,
    visible,
    onClose,
    onCancel,
    onConfirm,
    canSendAutoMessage,
    canSendAutoEmail,
    canSendAppAutoMessage,
    ticketStatus,
    loading = false,
    isAutoMessageEnable,
  }: ReadyForPickupConfirmModalProps) => {
    const isManualMode = mode === ReadyForPickupConfirmModalMode.ManualSendMessage;
    const title = isManualMode ? 'Mark this appointment as ready?' : 'Send ready for pickup message';
    const { enable: canSendMessage, toggle: setCanSendMessage } = useEnableFeature(
      META_DATA_KEY_LIST.ReadyForPickupMSGSetting,
    );
    const { has: hasBrandedApp } = useHasBrandedApp();

    const multipleSendEnable = [canSendAutoMessage, canSendAutoEmail, canSendAppAutoMessage].filter(Boolean).length > 1;

    const [customSendMethod, setCustomSendMethod] = useState(ReadyForPickupMessageSendMethodOptions.SMS);

    const showRadio = useMemo(() => {
      if (isManualMode) {
        return canSendMessage && isAutoMessageEnable && multipleSendEnable;
      }
      return multipleSendEnable;
    }, [multipleSendEnable, canSendMessage, isManualMode, isAutoMessageEnable]);

    const sendMethod = useMemo(() => {
      if (isManualMode && !isAutoMessageEnable) {
        return undefined;
      }
      if (showRadio) {
        return customSendMethod;
      }

      // If no radio, then only one method is available
      if (canSendAutoMessage) return ReadyForPickupMessageSendMethodOptions.SMS;
      if (canSendAutoEmail) return ReadyForPickupMessageSendMethodOptions.Email;
      if (canSendAppAutoMessage) return ReadyForPickupMessageSendMethodOptions.PPA;
      return undefined;
    }, [
      isManualMode,
      isAutoMessageEnable,
      showRadio,
      canSendAutoMessage,
      canSendAutoEmail,
      canSendAppAutoMessage,
      customSendMethod,
    ]);

    const handleConfirm = useLatestCallback(async () => {
      // 手动模式下，如果不选择发送消息，那么就 pass，如果选择发送消息，就跟自动发送逻辑一致
      // 自动模式下，如果 auto message & email 都开了，那么就二选一；否则只选其中一个，不用展示 radio
      if (!(hasBrandedApp && canSendAppAutoMessage) && !canSendAutoMessage && !canSendAutoEmail) {
        toastApi.error('All channels for sending auto messages to this client are disabled.');
      }
      if (isManualMode) {
        await onConfirm(canSendMessage ? sendMethod : undefined);
      } else {
        await onConfirm(sendMethod);
      }
    });

    const handleCheckboxChange = useLatestCallback((e: CheckboxChangeEvent) => {
      setCanSendMessage(e.target.checked ? 1 : 0);
    });

    const renderRadioOptions = () => {
      const result: React.ReactNode[] = [];
      if (hasBrandedApp && canSendAppAutoMessage) {
        result.push(
          <Radio value={ReadyForPickupMessageSendMethodOptions.PPA} key="PPA">
            Send by Pet Parent App
          </Radio>,
        );
      }

      if (canSendAutoMessage) {
        result.push(
          <Radio value={ReadyForPickupMessageSendMethodOptions.SMS} key="SMS">
            Send by SMS
          </Radio>,
        );
      }

      if (canSendAutoEmail) {
        result.push(
          <Radio value={ReadyForPickupMessageSendMethodOptions.Email} key="Email">
            Send by Email
          </Radio>,
        );
      }

      return result;
    };

    useEffect(() => {
      if (visible) {
        setCustomSendMethod(
          hasBrandedApp && canSendAppAutoMessage
            ? ReadyForPickupMessageSendMethodOptions.PPA
            : ReadyForPickupMessageSendMethodOptions.SMS,
        );
      }
    }, [visible, hasBrandedApp, canSendAppAutoMessage]);

    return (
      <ConfirmModalWithClose
        title={title}
        visible={visible}
        onCancel={onCancel}
        onClose={onClose}
        onConfirm={handleConfirm}
        confirmBtnText={isManualMode ? 'Confirm' : 'Send'}
        content={
          <Spin spinning={loading}>
            <div className="moe-w-full">
              <Condition if={mode === ReadyForPickupConfirmModalMode.ManualSendMessage}>
                <Condition if={(canSendAutoEmail || canSendAutoMessage) && isAutoMessageEnable}>
                  <div>
                    <Checkbox
                      checked={canSendMessage}
                      onChange={handleCheckboxChange}
                      data-testid={ApptTestIds.ApptPopupSmsSendCb}
                    >
                      Also send ready for pickup message{' '}
                      {!multipleSendEnable
                        ? `via ${ReadyForPickupMessageSendMethodOptions.mapLabels[sendMethod!]}`
                        : ''}
                    </Checkbox>
                  </div>
                </Condition>
              </Condition>
              <Condition
                if={
                  mode === ReadyForPickupConfirmModalMode.AutoSendMessage && ticketStatus !== AppointmentStatus.FINISHED
                }
              >
                <div>Note: Appointment will also be marked as ready</div>
              </Condition>
              <Condition if={showRadio}>
                <div className={`moe-mt-[10px] ${isManualMode ? 'moe-pl-[24px]' : ''}`}>
                  <Radio.Group value={customSendMethod} onChange={(e) => setCustomSendMethod(e.target.value)}>
                    <Space direction="vertical">{renderRadioOptions()}</Space>
                  </Radio.Group>
                </div>
              </Condition>
            </div>
          </Spin>
        }
      />
    );
  },
);
