import { type StringDateRange } from '@moego/api-web/moego/utils/v2/condition_messages';
import { MinorPlusOutlined, MinorTrashOutlined } from '@moego/icons-react';
import { DateRangePicker, Form, type RangeValue, useFieldArray, cn } from '@moego/ui';
import dayjs, { type Dayjs } from 'dayjs';
import React, { memo } from 'react';
import { Condition } from '../../../../../../../../../components/Condition';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../../../../utils/DateTimeUtil';
import { useLatestCallback } from '../../../../../../../../../utils/hooks/useLatestCallback';
import { usePricingRuleSettingContext } from '../../PricingRuleSettingContext';
import { useDisabledDate } from '../../hooks/useDisabledDate';
import { pricingRuleFormHelperFactory } from '../../utils/PricingRuleFormHelperFactory';
import { FormItemLabel } from '@moego/ui/dist/esm/components/Form/FormItemLabel';
import { MultipleRuleTips } from './MultipleRuleTips';

const ApplicableDateFormName = `ruleConfiguration.conditionGroups.0.conditions`;

export const SpecificDate = memo(() => {
  const { form, ruleType } = usePricingRuleSettingContext();
  const helper = pricingRuleFormHelperFactory.getHelper(Number(ruleType));
  const {
    fields: conditions = [],
    append,
    remove,
  } = useFieldArray({
    control: form?.control,
    name: ApplicableDateFormName,
  });
  const getDisabledDate = useDisabledDate(conditions);
  const handleUpdateDateRange = useLatestCallback((index: number, value: RangeValue<Dayjs>) => {
    const [start, end] = value ?? [];
    form?.setValue(`${ApplicableDateFormName}.${index}.value.dateRange`, {
      startDate: start?.format(DATE_FORMAT_EXCHANGE) ?? '',
      endDate: end?.format(DATE_FORMAT_EXCHANGE) ?? '',
    });
  });
  return (
    <div className="moe-p-8px-200 moe-bg-neutral-sunken-0 moe-rounded-8px-200 moe-flex moe-flex-col moe-gap-[8px]">
      <FormItemLabel>{'Applicable date(s)'}</FormItemLabel>
      {conditions.map((item, index) => {
        const isLast = index === conditions.length - 1;
        const dateRangePropsName = `${ApplicableDateFormName}.${index}.value.dateRange`;
        return (
          <div className="moe-flex moe-items-start moe-gap-[16px]" key={item.id}>
            <Form.Item<StringDateRange, RangeValue<Dayjs>>
              name={dateRangePropsName}
              rules={{
                validate: (currentDateRange) => {
                  if (!currentDateRange?.startDate) {
                    return 'Applicable date is required!';
                  }
                  if (!currentDateRange?.endDate) {
                    return 'Applicable date is required!';
                  }
                  return true;
                },
              }}
              transformer={{
                input: (value) => {
                  return [
                    value?.startDate ? dayjs(value?.startDate) : null,
                    value?.endDate ? dayjs(value?.endDate) : null,
                  ];
                },
              }}
            >
              <DateRangePicker
                mode="date"
                className="moe-flex-1"
                disabledDate={(date: Dayjs) => getDisabledDate(date, index)}
                onChange={(value) => {
                  form?.clearErrors(`${ApplicableDateFormName}.${index}.value.dateRange`);
                  handleUpdateDateRange(index, value);
                }}
              />
            </Form.Item>
            <Condition if={conditions.length > 1}>
              <MinorTrashOutlined
                onClick={() => remove(index)}
                className={cn('moe-mt-[10px] moe-cursor-pointer', {
                  'moe-mr-[36px]': !isLast,
                })}
              />
            </Condition>
            <Condition if={isLast}>
              <MinorPlusOutlined
                className="moe-mt-[10px] moe-cursor-pointer"
                onClick={() => append(helper.createCondition())}
              />
            </Condition>
          </div>
        );
      })}
      <MultipleRuleTips />
    </div>
  );
});
