import { Markup, LegacySelect as Select } from '@moego/ui';
import { useSelector } from 'amos';
import React, { useMemo } from 'react';
import { QuestionTooltip } from '../../../../../../../../components/Popup/QuestionTooltip';
import { WithPricingEnableUpgrade } from '../../../../../../../../components/Pricing/WithPricingComponents';
import { OBTestIds } from '../../../../../../../../config/testIds/onlineBooking';
import { selectCurrentBusiness } from '../../../../../../../../store/business/business.selectors';
import { selectPricingPermission } from '../../../../../../../../store/company/company.selectors';
import { AvailableTimeTypeKind } from '../../../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { selectOnlineBookingPreference } from '../../../../../../../../store/onlineBooking/onlineBookingPreference.selectors';
import { type AvailabilitySettings } from '../../../../../../../../store/onlineBooking/settings/availabilityGrooming.boxes';
import { selectDraftAvailabilitySettings } from '../../../../../../../../store/onlineBooking/settings/availabilityGrooming.selectors';
import cn from 'classnames';
import { AvailabilityType } from '../../../../../../../../store/staffSchedule/staffSchedule.types';
import { type EnumValues } from '../../../../../../../../store/utils/createEnum';

interface AvailableByWhichTypeProps {
  className?: string;
  isDisabled?: boolean;
  availableType?: number;
  onAvailabilityTypeChange?: (availableType: number) => void;
}

export const GENERAL_RESET_KEY: (keyof AvailabilitySettings)[] = [
  'fakeIt',
  'showOneAvailableTime',
  'bySlotShowOneAvailableTime',
  'displayStaffSelectionPage',
  'arrivalWindowAfterMin',
  'arrivalWindowBeforeMin',
  'bookingRangeStartOffset',
  'bookingRangeEndType',
  'bookingRangeEndOffset',
  'bookingRangeEndDate',
];
export const BY_SLOT_RESET_KEY: (keyof AvailabilitySettings)[] = ['timeslotMins', 'timeslotFormat'];
export const BY_WORKING_HOUR_RESET_KEY: (keyof AvailabilitySettings)[] = ['bySlotTimeslotMins', 'bySlotTimeslotFormat'];

export function AvailableByWhichType({
  className,
  isDisabled,
  availableType,
  onAvailabilityTypeChange,
}: AvailableByWhichTypeProps) {
  const [preference, pricingPermission, draftSettings, business] = useSelector(
    selectOnlineBookingPreference,
    selectPricingPermission(),
    selectDraftAvailabilitySettings(),
    selectCurrentBusiness(),
  );
  // 原则上来说 mobile 商家不可以选择 by slot 模式，早期开启的商家除外
  const canViewBySlot = pricingPermission.enable.has('obBySlot') && !business.isPureMobileGrooming();
  const serviceType = preference.availableTimeType;
  const serviceTypeIsBySlot = serviceType === AvailableTimeTypeKind.BySlot;

  const optionList = useMemo(
    () =>
      AvailableTimeTypeKind.values
        .filter((item) => {
          // 兼容存量用户，如果目前不可见 bySlot 且 服务端不是 by slot 设置，则不显示。
          return !(item === AvailableTimeTypeKind.BySlot && !canViewBySlot && !serviceTypeIsBySlot);
        })
        .map((item) => {
          return {
            label: AvailableTimeTypeKind.mapLabels[item],
            value: item,
          };
        }),
    [canViewBySlot, serviceTypeIsBySlot],
  );

  return (
    <div className={cn('moe-flex moe-flex-col', className)}>
      <Markup variant="small" className="moe-mb-s moe-font-bold">
        Availability type
      </Markup>
      <div className="moe-flex moe-items-center">
        <WithPricingEnableUpgrade permission="obBySlot">
          {(onCapture) => {
            return (
              <Select
                value={availableType}
                options={optionList}
                className="moe-w-[352px]"
                isSearchable={false}
                isDisabled={isDisabled}
                onChange={(type) => {
                  // 如果选择了 by slot，则需要判断是否有权限 且 是否服务端是 by slot 设置。如果都不是，则需要弹窗。
                  if (type === AvailableTimeTypeKind.BySlot && onCapture && !serviceTypeIsBySlot) {
                    onCapture();
                    return;
                  }

                  onAvailabilityTypeChange?.(type);
                }}
                data-testid={OBTestIds.SettingAvailabilityTypeSelect}
              ></Select>
            );
          }}
        </WithPricingEnableUpgrade>
        {draftSettings.isDisableSelectTime && (
          <QuestionTooltip
            content={`Once "Disable select time option" applied, clients are not allowed to select booking time online.`}
          />
        )}
      </div>
    </div>
  );
}

export function SMAvailabilityTypeToOBAvailabilityType(smAvailabilityType: EnumValues<typeof AvailabilityType>) {
  switch (smAvailabilityType) {
    case AvailabilityType.BY_SLOT:
      return AvailableTimeTypeKind.BySlot;
    case AvailabilityType.BY_TIME:
      return AvailableTimeTypeKind.ByWorkingHour;
    default:
      return AvailableTimeTypeKind.DisableSelectTime;
  }
}
