import { useSelector } from 'amos';
import { Popconfirm, Switch } from 'antd';
import React, { memo } from 'react';
import IconIconDeleteSvg from '../../../../../../assets/icon/icon-delete.svg';
import IconIconEditSvg from '../../../../../../assets/icon/icon-edit.svg';
import IconIconSortSvg from '../../../../../../assets/icon/icon-sort.svg';
import { ImgIcon } from '../../../../../../components/Icon/Icon';
import {
  type PaymentMethodRecord,
  PaymentMethodType,
  paymentMethodMapBox,
} from '../../../../../../store/payment/payment.boxes';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useDragDropList } from '../../../components/DraggableList/utils';
import { PaymentMethodItemView } from '../../PaymentSetting.style';

interface PaymentMethodItemProps {
  id: number;
  index: number;
  onEdit: (item: PaymentMethodRecord) => void;
  onDelete: (item: PaymentMethodRecord) => void;
  onEnable: (item: PaymentMethodRecord, enabled: boolean) => void;
  onMove: (dragIndex: number, hoverIndex: number) => void;
  onDrop: () => void;
  isDisabled?: boolean;
}

const DRAG_ITEM = 'SETTING/PAYMENT/METHOD';

export const PaymentMethodItem = memo<PaymentMethodItemProps>(
  ({ index, onEdit, onDelete, onMove, onDrop, id, onEnable, isDisabled }) => {
    const [dragRef, itemRef, { opacity }] = useDragDropList<HTMLDivElement>(DRAG_ITEM, index, onMove, onDrop);
    const [item] = useSelector(paymentMethodMapBox.mustGetItem(id));
    const handleEdit = useLatestCallback(() => onEdit(item));
    const handleDelete = useLatestCallback(() => onDelete(item));
    const handleEnable = useLatestCallback((checked: boolean) => onEnable(item, checked));

    return (
      <PaymentMethodItemView ref={itemRef} style={{ opacity }}>
        <Switch checked={!item.inactive} onChange={handleEnable} disabled={isDisabled} />
        <div className="name">{item.name === 'Credit card' ? item.name + ' (MoeGo Pay)' : item.name}</div>
        <div className="right">
          {item.type === PaymentMethodType.Custom && (
            <>
              <ImgIcon src={IconIconEditSvg} onClick={handleEdit} />

              <Popconfirm
                title="Are you sure to delete this payment method?"
                okText="Yes"
                okType="danger"
                cancelText="No"
                onConfirm={handleDelete}
                placement="topRight"
              >
                <ImgIcon src={IconIconDeleteSvg} />
              </Popconfirm>
            </>
          )}
          <ImgIcon cursor="move" src={IconIconSortSvg} ref={dragRef} />
        </div>
      </PaymentMethodItemView>
    );
  },
);
