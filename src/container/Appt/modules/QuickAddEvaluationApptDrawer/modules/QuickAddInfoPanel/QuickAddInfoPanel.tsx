import { Text } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import React from 'react';
import { useAsync } from 'react-use';
import { Condition } from '../../../../../../components/Condition';
import { AddPetAndServices } from '../../../../../../components/PetAndServicePicker/components/AddPetAndServices';
import { ScrollerProvider } from '../../../../../../layout/components/ScrollerProvider';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { getAllClientInfo } from '../../../../../../store/customer/customer.actions';
import { EvaluationQuickAddStep } from '../../../../../../store/evaluation/evaluation.types';
import { getLodgingUnitList } from '../../../../../../store/lodging/actions/public/lodgingUnit.actions';
import { getCompanyStaffList } from '../../../../../../store/staff/staff.actions';
import { ID_ANONYMOUS, isNormal } from '../../../../../../store/utils/identifier';
import { useQuery } from '../../../../../../store/utils/useQuery';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { ApptDateTime } from '../../../../components/ApptDateTime';
import { SectionInfo } from '../../../../components/SectionInfo';
import { useEvaluationCustomerOverview } from '../../../../hooks/useEvaluationCustomerOverview';
import { useAutoSelectSinglePet } from '../../hooks/useAutoSelectSinglePet';
import { useEvaluationQuickAdd } from '../../hooks/useEvaluationQuickAdd';
import { EvaluationClientAlertNotes } from './EvaluationClientAlertNotes';
import { EvaluationClientInfo } from './EvaluationClientInfo';
import { EvaluationColorCode } from './EvaluationColorCode';
import { EvaluationPetInfoCardList } from './EvaluationPetInfoCardList';
import { isNumber } from 'lodash';

export interface QuickAddInfoPanelProps {
  footer?: React.ReactNode;
}

export const QuickAddInfoPanel: React.FC<QuickAddInfoPanelProps> = (props) => {
  const { footer } = props;
  const dispatch = useDispatch();
  const {
    customerId,
    stepInfo: { type: currentStep },
    evaluationInfo,
    setQuickAddFields,
    jumpToStep,
  } = useEvaluationQuickAdd();
  const [business] = useSelector(selectCurrentBusiness);
  const apptStartDate = dayjs(evaluationInfo.startDate);
  useEvaluationCustomerOverview();
  useQuery(getLodgingUnitList({ businessId: business.id?.toString() }));
  useQuery(getCompanyStaffList());

  const handleClearClient = useLatestCallback(() => {
    setQuickAddFields({
      customerId: ID_ANONYMOUS,
      petIds: [],
      isDirty: false,
    });
    jumpToStep(EvaluationQuickAddStep.Init);
  });

  const handleSelectClient = useLatestCallback((newClientId: number) => {
    setQuickAddFields({
      customerId: newClientId,
      petIds: [],
      evaluationInfo: {
        ...evaluationInfo,
      },
      isDirty: true,
    });
    jumpToStep(EvaluationQuickAddStep.SelectPetMultipleMode);
  });

  const handleSelectPetMultiple = useLatestCallback(() => {
    jumpToStep(EvaluationQuickAddStep.SelectPetAndEvaluation);
  });

  const handleEditDateTime = useLatestCallback(() => {
    jumpToStep(EvaluationQuickAddStep.RescheduleDateAndTime);
  });

  const { loading: loadingClient } = useAsync(async () => {
    if (isNormal(customerId)) {
      await dispatch(getAllClientInfo(customerId));
    }
  }, [customerId]);

  useAutoSelectSinglePet();

  return (
    <ScrollerProvider className="moe-flex-1 moe-min-w-0 moe-flex moe-flex-col">
      <div className="moe-bg-[#F3F3F3] moe-px-m moe-py-m moe-text-[24px] moe-text-[#333] moe-leading-[40px] moe-font-bold moe-sticky moe-top-0 moe-z-[1]">
        New evaluation
      </div>
      <div
        className="moe-px-m moe-mb-m"
        style={{ background: 'linear-gradient(to bottom, #F3F3F3, #F3F3F3 40px, #fff 40px, #fff)' }}
      >
        <EvaluationClientInfo
          clientId={customerId}
          onSelectClient={handleSelectClient}
          onClearClient={handleClearClient}
          loadingClient={loadingClient}
        />
      </div>
      <ScrollerProvider className="moe-flex-1 moe-px-m moe-pb-s">
        <Condition if={currentStep === EvaluationQuickAddStep.SelectPetMultipleMode}>
          <div className="moe-mb-8px-300 moe-flex moe-flex-col moe-gap-y-m">
            <EvaluationClientAlertNotes />
            <AddPetAndServices onClick={handleSelectPetMultiple} disabledOverlay />
          </div>
        </Condition>
        <Condition
          if={[EvaluationQuickAddStep.AfterSelectPetAndEvaluation, EvaluationQuickAddStep.SelectPetSingleMode].includes(
            currentStep,
          )}
        >
          <div className="moe-mb-8px-300 moe-flex moe-flex-col moe-gap-y-m">
            <ApptDateTime
              className="moe-px-xs"
              onEdit={handleEditDateTime}
              hiddenRepeatDetailText
              customApptTimeTxtBlock={
                <Text variant="regular" className="moe-text-primary">
                  {apptStartDate.format('dddd')}, {business.formatDate(apptStartDate)}{' '}
                  {isNumber(evaluationInfo.startTime)
                    ? business.formatTime(apptStartDate.add(evaluationInfo.startTime, 'minute'))
                    : null}
                </Text>
              }
            />
            <EvaluationClientAlertNotes />
          </div>
        </Condition>
        <Condition if={currentStep === EvaluationQuickAddStep.AfterSelectPetAndEvaluation}>
          <EvaluationPetInfoCardList />
        </Condition>
        <Condition
          if={[EvaluationQuickAddStep.AfterSelectPetAndEvaluation, EvaluationQuickAddStep.SelectPetSingleMode].includes(
            currentStep,
          )}
        >
          <SectionInfo className="moe-mt-m" label="Color code" flexDirection="row">
            <EvaluationColorCode />
          </SectionInfo>
        </Condition>
      </ScrollerProvider>
      {footer}
    </ScrollerProvider>
  );
};
