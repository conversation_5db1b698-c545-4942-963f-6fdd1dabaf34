import { datadogRum } from '@datadog/browser-rum';
import { cn, Heading, Text } from '@moego/ui';
import { useSelector } from 'amos';
import { Popover } from 'antd';
import React, { memo, useMemo, useRef } from 'react';
import { CompressedAvatar } from '../../../../../components/CompressedAvatar/CompressedAvatar';
import { Condition } from '../../../../../components/Condition';
import { VaccineIcon } from '../../../../../components/VaccineIcon/VaccineIcon';
import {
  selectLodgingApptCard,
  selectLodgingCalendarConfig,
} from '../../../../../store/calendarLatest/lodgingCalendar.selectors';
import { DATE_FORMAT_EXCHANGE } from '../../../../../utils/DateTimeUtil';
import { useBool } from '../../../../../utils/hooks/useBool';
import { useOpenApptDetailDrawer } from '../../../../../utils/hooks/useOpenApptDetailDrawer';
import { DataDogActionName } from '../../../../../utils/logger';
import { ReportActionName } from '../../../../../utils/reportType';
import { reportData } from '../../../../../utils/tracker';
import { useLodgingPlaceHolderCard } from '../../hooks/useLodgingPlaceholderCard';
import { LodgingAppointmentCardView } from '../LodgingCalendar.style';
import { LodgingPopoverContent } from './LodgingPopoverContent';
import { PetAvatarPreview } from '../../../../../components/PetInfo/PetAvatarPreview';
export interface LodgingAppointmentCardProps {
  cardId: string;
  isDragging: boolean;
}

export const LodgingAppointmentCard = memo<LodgingAppointmentCardProps>(({ cardId, isDragging }) => {
  const [lodgingApptCard, lodgingCalendarConfig] = useSelector(
    selectLodgingApptCard(cardId),
    selectLodgingCalendarConfig,
  );
  const {
    petAvatarList,
    petList,
    appointmentId,
    startDate: appointmentStartDate,
    endDate: appointmentEndDate,
    colorCode,
    serviceItemTypes,
  } = lodgingApptCard;

  const { startDate: calendarStartDate } = lodgingCalendarConfig.getStartEndDate();

  const { openApptDetailDrawer } = useOpenApptDetailDrawer();
  const { removePlaceholderCard } = useLodgingPlaceHolderCard();

  const forcePopOverInVisible = useBool();
  const hasActiveNestedTooltip = useBool();

  const disablePopOverProps = useMemo(() => {
    if (isDragging || forcePopOverInVisible.value) {
      return { visible: false };
    }
    return undefined;
  }, [isDragging, forcePopOverInVisible.value]);

  const isAppointmentInCurrentWeek = useMemo(() => {
    return appointmentStartDate.isSameOrAfter(calendarStartDate, 'day');
  }, [appointmentStartDate, calendarStartDate]);

  const isDownRef = useRef<boolean>(false);
  const isClickRef = useRef<boolean>(true);
  const viewRef = useRef<HTMLDivElement>(null);
  const mouseDownEvent = useRef<React.MouseEvent<HTMLDivElement, MouseEvent> | null>(null);

  return (
    <Popover
      arrowPointAtCenter
      mouseEnterDelay={0.5}
      {...disablePopOverProps}
      {...(hasActiveNestedTooltip.value && {
        overlayClassName: 'moe-hidden',
      })}
      content={<LodgingPopoverContent cardId={cardId} appointmentId={appointmentId} />}
      overlayInnerStyle={{
        borderRadius: '16px',
      }}
    >
      <LodgingAppointmentCardView
        ref={viewRef}
        className={cn('moe-flex moe-rounded-[2px] moe-bg-[#DFF3FF]', {
          isDragging: isDragging,
        })}
        onPointerDown={() => {
          isDownRef.current = true;
        }}
        onPointerUp={() => {
          isClickRef.current = true;
          isDownRef.current = false;
        }}
        // HACK: popover自带的显示隐藏机制，在卡片拖拽的时候，有时候不会关闭，鼠标检测到mousedown的时候强制关闭下; 而且因为down之后就是click查看详情了，关闭popover，刚好不冲突
        onMouseDownCapture={(e) => {
          mouseDownEvent.current = e;
          if (isClickRef.current) {
            e.stopPropagation();
          }
          forcePopOverInVisible.open();
        }}
        onPointerMove={() => {
          if (isDownRef.current) {
            isClickRef.current = false;
            mouseDownEvent.current && viewRef.current?.dispatchEvent(mouseDownEvent.current.nativeEvent);
            mouseDownEvent.current = null;
          }
        }}
        onMouseEnter={forcePopOverInVisible.close}
        onClick={() => {
          datadogRum.startDurationVital(DataDogActionName.OPEN_APPT_DRAWER);
          removePlaceholderCard();
          openApptDetailDrawer({ ticketId: Number(appointmentId) });
          reportData(ReportActionName.lodgingViewOpenApptDetail);
        }}
      >
        <Condition if={isAppointmentInCurrentWeek}>
          <div
            className="moe-w-[4px] moe-flex-shrink-0 moe-rounded-tl-[2px] moe-rounded-bl-[2px] moe-bg-information-mild"
            style={{ backgroundColor: colorCode }}
          />
        </Condition>
        <div className="moe-flex moe-items-center moe-pl-8px-150 moe-py-spacing-xs  moe-overflow-hidden">
          {petAvatarList.map(({ petAvatar, petBriefType }, index) => (
            <PetAvatarPreview
              className={isDragging ? 'moe-hidden' : ''}
              petAvatar={petAvatar}
              key={index}
              onTooltipOpenChange={hasActiveNestedTooltip.as}
            >
              <CompressedAvatar.Pet
                color="neutral"
                type={petBriefType}
                showLoading={false}
                size="xs"
                compressedSize="W_24"
                src={petAvatar}
                className={cn('moe-flex-shrink-0 moe-relative', { 'moe-ml-[-8px]': index })}
              />
            </PetAvatarPreview>
          ))}
          <Heading size="6" className="moe-ml-xs moe-text-secondary moe-whitespace-nowrap moe-flex moe-gap-x-xs">
            {petList.map(({ petId, petName }, index) => (
              <span className="moe-inline-flex moe-items-center moe-gap-x-xxs" key={petId}>
                <Text variant="small" className="moe-font-bold" as="span">
                  {petName}
                </Text>
                <VaccineIcon
                  key={petId}
                  petId={+petId}
                  appointmentId={+appointmentId}
                  appointmentDate={appointmentEndDate.format(DATE_FORMAT_EXCHANGE)}
                  serviceItemTypes={serviceItemTypes}
                  onTooltipOpenChange={hasActiveNestedTooltip.as}
                />
                <Condition if={index !== petList.length - 1}>
                  <Text variant="small" className="moe-font-bold" as="span">
                    {','}
                  </Text>
                </Condition>
              </span>
            ))}
          </Heading>
        </div>
      </LodgingAppointmentCardView>
    </Popover>
  );
});
LodgingAppointmentCard.displayName = 'LodgingAppointmentCard';
