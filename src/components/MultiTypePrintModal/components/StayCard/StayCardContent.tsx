import { MajorBoardingOutlined, MajorDaycareOutlined } from '@moego/icons-react';
import { T_MINUTE } from '@moego/reporting';
import { Heading, Text, cn } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { useMemo } from 'react';
import {
  ComMoegoServerGroomingDtoPrintcardStayCardAppointmentMainServiceItemType,
  ComMoegoServerGroomingDtoPrintcardStayCardAppointmentMainServiceItemType as MainServiceItemType,
} from '../../../../openApi/grooming-schema';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { type StayPrintCardInfo } from '../../../../store/printCard/stayCard/stayCard.actions';
import { stayPrintCardSettingsBox } from '../../../../store/printCard/stayCard/stayCard.boxes';
import { getPetAvatarType } from '../../../../utils/BusinessUtil';
import { printAge } from '../../../../utils/DateTimeUtil';
import { withPl } from '../../../../utils/calculator';
import { useWeightSuffix } from '../../../../utils/hooks/useWeightSuffix';
import { CompressedAvatar } from '../../../CompressedAvatar/CompressedAvatar';
import { Condition } from '../../../Condition';
import { Switch } from '../../../SwitchCase';
import { getFeedingIntro, getFullDateTime, getMedicationIntro } from '../../MultiTypePrintModal.options';
import { PrintCardAppointmentInfoType } from '../../hooks/useAppointmentInfoRows';
import { useFeedingCheckTable } from '../../hooks/useFeedingCheckTable';
import { useFeedingSummaryCheckTable } from '../../hooks/useFeedingSummaryCheckTable';
import { useMedicationCheckTable, useMedicationCompactViewCheckTable } from '../../hooks/useMedicationCheckTable';
import { PrintCardAppointmentInfo } from '../common/PrintCardAppointmentInfo/PrintCardAppointmentInfo';
import { type CheckTableColumn, CheckTableGroup, type RecordData } from './CheckTables';
import { StayCardAddOnsContent } from './StayCardAddOnsContent';

export const StayCardContent = (props: { data: StayPrintCardInfo[] }) => {
  const { data } = props;
  return (
    // can not add class print:moe-gap-y-0, or it will impact print layout
    <div className="moe-flex moe-w-full moe-flex-col moe-gap-y-m">
      {/* petId 可能重复 */}
      {data.map((card, index) => (
        <StayCard key={`${card.petId}-${index}`} data={card} />
      ))}
    </div>
  );
};

const StayCard = (props: { data: StayPrintCardInfo }) => {
  const {
    data: { appointment, pet, customer, groomingId, addOns, extraServiceDetails, petBelongings },
  } = props;
  const [
    {
      appointmentInfo,
      extraServicesAndAddOns: { showExtraServicesAndAddOns, showScheduleTable },
      feedingAndMedication: {
        feedingSummaryCheckbox,
        feedingTableCheckbox,
        medicationSummaryCheckbox,
        medicationTableCheckbox,
        medicationCompactView,
      },
    },
  ] = useSelector(stayPrintCardSettingsBox);

  return (
    <div className="moe-w-full moe-min-h-[792px] moe-flex moe-flex-col moe-bg-white moe-relative moe-p-m moe-shadow-elevated print:moe-shadow-none moe-break-before-page">
      <div className="moe-flex-1">
        {/* header */}
        <Header data={props.data} />
        {/* appt info */}
        <PrintCardAppointmentInfo
          appointmentInfo={appointment}
          customerInfo={customer}
          petInfo={pet}
          groomingId={groomingId}
          settings={appointmentInfo}
          addOns={addOns}
          extraServiceDetails={extraServiceDetails}
          petBelongings={petBelongings}
          type={PrintCardAppointmentInfoType.STAY_CARD}
        />
        {/* extra services & add-ons */}
        <Condition if={showExtraServicesAndAddOns}>
          <ExtraServicesAndAddOns data={props.data} showScheduleTable={showScheduleTable} />
        </Condition>
        {/* feeding */}
        <Condition if={feedingSummaryCheckbox || feedingTableCheckbox}>
          <Feedings data={props.data} showSummary={feedingSummaryCheckbox} showTable={feedingTableCheckbox} />
        </Condition>
        {/* medication */}
        <Condition if={medicationSummaryCheckbox || medicationTableCheckbox}>
          <Medications
            data={props.data}
            showSummary={medicationSummaryCheckbox}
            showTable={medicationTableCheckbox}
            showCompactView={medicationCompactView}
          />
        </Condition>
      </div>
      {/* <div className="moe-w-full moe-flex moe-justify-center moe-mt-m">
        <img src={ImageCareContractLogoPng} className="moe-h-[16px]" />
      </div> */}
    </div>
  );
};

const Header = (props: { data: StayPrintCardInfo }) => {
  const {
    data: { pet, appointment, customer },
  } = props;
  const [{ general }, business] = useSelector(stayPrintCardSettingsBox, selectCurrentBusiness);
  const { petGender, petWeight, petPhoto, petAppearance, petAge, petFixedInfo } = general;
  const unit = useWeightSuffix();
  const customerName = customer.lastName || '';

  const petIntro = useMemo(() => {
    const intro = [];
    intro.push(pet.breed);
    if (petGender) {
      intro.push(pet.gender);
    }
    if (petWeight) {
      intro.push(`${pet.weight} ${unit}`);
    }
    if (petAge && pet.birthday) {
      intro.push(printAge(dayjs(pet.birthday)));
    }
    if (petFixedInfo) {
      intro.push(pet.petFixed);
    }
    if (petAppearance) {
      intro.push([pet.petAppearanceColor, pet.petAppearanceNotes].filter(Boolean).join('/'));
    }
    return intro.filter(Boolean).join(' · ');
  }, [
    petGender,
    petWeight,
    pet.breed,
    pet.gender,
    pet.weight,
    petAge,
    pet.birthday,
    petFixedInfo,
    pet.petFixed,
    unit,
    petAppearance,
    pet.petAppearanceColor,
    pet.petAppearanceNotes,
  ]);

  const isBoarding = appointment.mainServiceItemType === MainServiceItemType.BOARDING;

  const apptStartTime = getFullDateTime(
    appointment.appointmentStartDate,
    appointment.appointmentStartTime,
    business.dateFormat,
    business.timeFormat(),
  );

  // show end date only if it's different from start date
  const endDate =
    appointment.appointmentStartDate === appointment.appointmentEndDate ? '' : appointment.appointmentEndDate;
  const apptEndTime = getFullDateTime(
    endDate,
    appointment.appointmentEndTime,
    business.dateFormat,
    business.timeFormat(),
  );
  const name = customerName ? `${pet.petName} (${customerName})` : pet.petName;

  const lodgingRoomText = useMemo(() => {
    // 注意这里 lodging room 的概念不是 room / area
    const { splitLodgingInfos, lodgingRoom } = appointment;
    if (splitLodgingInfos && splitLodgingInfos.length > 0) {
      return withPl(splitLodgingInfos.length, 'room');
    }
    return lodgingRoom;
  }, [appointment]);

  return (
    <div className="moe-flex moe-gap-x-s moe-items-start moe-justify-between">
      <Condition if={petPhoto}>
        <div className="moe-flex moe-items-center">
          <CompressedAvatar.Pet
            src={pet.avatarPath}
            compressedSize="W_256"
            classNames={{
              icon: 'moe-text-[48px]',
              image: 'moe-rounded-none moe-object-contain ',
            }}
            type={getPetAvatarType(pet.petTypeId)}
            className="moe-h-[160px] moe-w-[160px] moe-rounded-[8px] moe-bg-neutral-sunken-0 moe-overflow-hidden"
          />
        </div>
      </Condition>
      <div className="pet-info-header moe-flex-1">
        <Heading size="4" className="moe-flex moe-items-center moe-gap-[8px] moe-flex-wrap">
          <span>{name}</span>
          <Condition if={lodgingRoomText}>
            <div className="moe-w-[1px] moe-h-[24px] moe-bg-[#202020]"></div>
            <span className="moe-font-normal">{lodgingRoomText}</span>
          </Condition>
        </Heading>
        <div className="moe-mt-[16px] moe-flex moe-flex-col moe-gap-[8px]">
          <div className="moe-text-[#000] moe-text-[14px] moe-font-normal">{petIntro}</div>
          <div className="moe-text-[#000] moe-text-[14px] moe-font-normal">
            <div>{appointment.mainServiceName}</div>
            <div>{`${apptStartTime} - ${apptEndTime}`}</div>
          </div>
          <Condition if={appointment.splitLodgingInfos.length}>
            <div>
              {appointment.splitLodgingInfos.map((lodgingInfo, index) => (
                <div
                  key={`${lodgingInfo.lodgingUnitId}-${index}`}
                  className="moe-text-[#000] moe-text-[14px] moe-h-[20px] moe-font-normal moe-flex moe-items-center"
                >
                  <div className="moe-w-[110px]">
                    {`${dayjs(lodgingInfo.startDate).format(business.dateFormatMD)} - ${dayjs(lodgingInfo.endDate).format(business.dateFormatMD)}`}
                  </div>
                  <div>{lodgingInfo.lodgingUnitName}</div>
                </div>
              ))}
            </div>
          </Condition>
        </div>
      </div>

      <div className="moe-min-w-[40px] moe-min-h-[40px] moe-w-[40px] moe-h-[40px] moe-bg-neutral-sunken-0 moe-rounded-2 moe-flex moe-items-center moe-justify-center">
        {isBoarding ? <MajorBoardingOutlined /> : <MajorDaycareOutlined />}
      </div>
    </div>
  );
};

enum SummaryShowType {
  TABLE = 'table',
  TEXT = 'text',
}

const CheckedTablesContainer = (props: {
  title: string;
  noDataDesc: string;
  descriptions?: string[];
  columnsGroup: CheckTableColumn[][];
  dataGroup: RecordData[][];
  summaryDataGroup?: RecordData[][];
  summaryColumnsGroup?: CheckTableColumn[][];
  summaryShowType?: SummaryShowType;
  className?: string;
  tableClassName?: string;
  showSummary?: boolean;
  showTable?: boolean;
}) => {
  const {
    title,
    noDataDesc,
    descriptions = [],
    columnsGroup,
    dataGroup,
    className,
    tableClassName,
    showSummary,
    showTable,
    summaryDataGroup = [],
    summaryColumnsGroup = [],
    summaryShowType = SummaryShowType.TEXT,
  } = props;

  return (
    <div className="moe-flex moe-flex-col moe-mt-m moe-pt-s moe-border-t-divider moe-border-t">
      <Heading size="6" className="moe-mb-xs">
        {title}
      </Heading>
      <div>
        <Switch shortCircuit>
          <Switch.Case if={dataGroup.length === 0}>
            <Text variant="small" className="moe-text-[12px]">
              {noDataDesc}
            </Text>
          </Switch.Case>
          <Switch.Case else>
            {showSummary && summaryShowType === SummaryShowType.TEXT
              ? descriptions.map((des) => {
                  return (
                    <Text key={des} variant="small" className="moe-text-[12px]">
                      {des}
                    </Text>
                  );
                })
              : null}
            {showSummary && summaryShowType === SummaryShowType.TABLE ? (
              <CheckTableGroup
                dataGroup={summaryDataGroup}
                columnsGroup={summaryColumnsGroup}
                className={cn('moe-mt-s', className)}
                tableClassName={cn(tableClassName, 'moe-w-full')}
              />
            ) : null}
            {showTable ? (
              <CheckTableGroup
                dataGroup={dataGroup}
                columnsGroup={columnsGroup}
                className={cn({ 'moe-mt-s': showSummary }, className)}
                tableClassName={tableClassName}
              />
            ) : null}
          </Switch.Case>
        </Switch>
      </div>
    </div>
  );
};

const ExtraServicesAndAddOns = ({
  data,
  showScheduleTable,
}: { data: StayPrintCardInfo; showScheduleTable?: boolean }) => {
  const { extraServiceDetails, appointment } = data;

  return (
    <div className="moe-flex moe-flex-col moe-mt-m moe-pt-s moe-border-t-divider moe-border-t">
      <Heading size="6" className="moe-mb-xs">
        Extra services & add-ons
      </Heading>
      <StayCardAddOnsContent
        extraServiceDetails={extraServiceDetails}
        mainServiceItemType={appointment.mainServiceItemType}
        showScheduleTable={showScheduleTable}
      />
    </div>
  );
};

const Feedings = (props: { data: StayPrintCardInfo; showSummary?: boolean; showTable?: boolean }) => {
  const [business] = useSelector(selectCurrentBusiness());
  const {
    data: { feedingInstructions, appointment },
    showSummary,
    showTable,
  } = props;

  const days = dayjs(appointment.appointmentEndDate).diff(dayjs(appointment.appointmentStartDate), 'day') + 1;

  const { columnsGroup, dataGroup } = useFeedingCheckTable({
    days,
    startDate: appointment.appointmentStartDate,
    feedingInstructions,
  });

  const feedingDescriptionsMap = useMemo(() => {
    return feedingInstructions.reduce(
      (acc, curr) => {
        const { timeLabelList, timeList } = curr;
        const intro = getFeedingIntro(curr, business, { showTime: false });
        timeLabelList.forEach((label, index) => {
          const timeStr = business.formatFixedTime(timeList[index] * T_MINUTE);
          const key = `${label}(${timeStr})`;

          acc[key] ||= {
            intro: [],
            time: timeList[index],
          };
          acc[key].intro.push(intro);
        });

        return acc;
      },
      {} as Record<string, { intro: string[]; time: number }>,
    );
  }, [feedingInstructions, business]);

  const feedingDescriptions = useMemo(
    () =>
      Object.entries(feedingDescriptionsMap)
        .sort((a, b) => a[1].time - b[1].time)
        .map(([key, { intro }]) => `${key}: ${intro.join('; ')}`),
    [feedingDescriptionsMap],
  );

  const { dataGroup: summaryDataGroup, columnsGroup: summaryColumnsGroup } = useFeedingSummaryCheckTable({
    feedingInstructions,
  });

  return (
    <CheckedTablesContainer
      title="Feeding"
      noDataDesc="No feeding schedule"
      descriptions={feedingDescriptions}
      columnsGroup={columnsGroup}
      dataGroup={dataGroup}
      className="[&_td]:moe-h-[32px]"
      showSummary={showSummary}
      showTable={showTable}
      summaryDataGroup={summaryDataGroup}
      summaryColumnsGroup={summaryColumnsGroup}
      summaryShowType={SummaryShowType.TEXT}
    />
  );
};

const Medications = (props: {
  data: StayPrintCardInfo;
  showSummary?: boolean;
  showTable?: boolean;
  showCompactView?: boolean;
}) => {
  const [business] = useSelector(selectCurrentBusiness());

  const {
    data: { medicationInstructions, appointment },
    showSummary,
    showTable,
    showCompactView,
  } = props;

  const days = dayjs(appointment.appointmentEndDate).diff(dayjs(appointment.appointmentStartDate), 'day') + 1;

  const { columnsGroup, dataGroup } = useMedicationCheckTable({
    days,
    startDate: appointment.appointmentStartDate,
    medicationInstructions,
  });

  const { columnsGroup: compactColumnsGroup, dataGroup: compactDataGroup } = useMedicationCompactViewCheckTable({
    days,
    startDate: appointment.appointmentStartDate,
    medicationInstructions,
  });

  const medicationDescriptions = useMemo(
    () =>
      medicationInstructions.map((medication) =>
        getMedicationIntro(medication, business, {
          showTime: true,
          showDate:
            appointment.mainServiceItemType ===
            ComMoegoServerGroomingDtoPrintcardStayCardAppointmentMainServiceItemType.BOARDING,
        }),
      ),
    [medicationInstructions, appointment.mainServiceItemType],
  );
  return (
    <CheckedTablesContainer
      columnsGroup={showCompactView ? compactColumnsGroup : columnsGroup}
      dataGroup={showCompactView ? compactDataGroup : dataGroup}
      descriptions={medicationDescriptions}
      title="Medications"
      noDataDesc="No medication schedule"
      tableClassName={cn({ 'moe-w-full': !showCompactView })}
      showSummary={showSummary}
      showTable={showTable}
    />
  );
};
