import { useDispatch, useSelector, useStore } from 'amos';
import { selectApptPetAddon, selectApptPetService, selectMainServiceInAppt } from '../../../store/appt.selectors';
import { useEffect } from 'react';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { type ApptPetServiceItem, type ApptMainServiceEntry } from '../../../store/appt.types';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { ServiceType } from '../../../../../store/service/category.boxes';
import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';
import dayjs from 'dayjs';
import { setAddonForPet, setServiceForPet } from '../../../store/appt.actions';
import { DATE_FORMAT_EXCHANGE } from '@moego/reporting';
import { type ApptAddOnRecord, type ApptServiceRecord } from '../../../store/appt.boxes';
import { isUndefined } from 'lodash';

export interface BoardingServiceScheduleSyncParams {
  appointmentId: string;
  petServiceAddOnList: ApptPetServiceItem[];
}

type MainScheduleInfo = Pick<ApptMainServiceEntry, 'startDate' | 'startTime' | 'endDate' | 'endTime'>;

type ServiceScheduleInfo = Pick<
  ApptServiceRecord | ApptAddOnRecord,
  'dateType' | 'startDate' | 'startTime' | 'endDate' | 'endTime' | 'serviceTime'
>;

/**
 * 将 Boarding Main Service 的 schedule 变化同步到各个 service / addOn，避免隐藏字段影响到动态的 First Day / Last Day 的 startDate / endDate 的参数
 */
export const useBoardingServiceScheduleSync = (params: BoardingServiceScheduleSyncParams) => {
  const { appointmentId, petServiceAddOnList } = params;
  const store = useStore();
  const dispatch = useDispatch();
  const [mainService] = useSelector(selectMainServiceInAppt(appointmentId));

  const getFirstDayDateTime = useLatestCallback(
    (mainScheduleInfo: MainScheduleInfo, serviceScheduleInfo: ServiceScheduleInfo) => {
      const newStartDate = mainScheduleInfo.startDate;
      const newStartTime =
        !isUndefined(serviceScheduleInfo.startTime) &&
        dayjs(serviceScheduleInfo.startDate)
          .setMinutes(serviceScheduleInfo.startTime || 0)
          .isBefore(dayjs(serviceScheduleInfo.startDate).setMinutes(mainScheduleInfo.startTime || 0), 'minute')
          ? mainScheduleInfo.startTime
          : serviceScheduleInfo.startTime;
      const newEndDateTime = dayjs(newStartDate)
        .setMinutes(newStartTime || 0)
        .add(serviceScheduleInfo.serviceTime || 0, 'minute');

      return {
        dateType: serviceScheduleInfo.dateType,
        startDate: newStartDate,
        startTime: newStartTime,
        endDate: newEndDateTime.format(DATE_FORMAT_EXCHANGE),
        endTime: newEndDateTime.getMinutes(),
      };
    },
  );

  const getLastDayDateTime = useLatestCallback(
    (mainScheduleInfo: MainScheduleInfo, serviceScheduleInfo: ServiceScheduleInfo) => {
      const scheduleEndDateTime = dayjs(serviceScheduleInfo.endDate).setMinutes(serviceScheduleInfo.endTime || 0);
      const newStartDate = mainScheduleInfo.endDate;
      const newStartTime =
        !isUndefined(serviceScheduleInfo.startTime) &&
        dayjs(serviceScheduleInfo.startDate)
          .setMinutes(serviceScheduleInfo.startTime || 0)
          .add(serviceScheduleInfo.serviceTime || 0, 'minute')
          .isAfter(scheduleEndDateTime)
          ? (mainScheduleInfo.endTime || 0) - (serviceScheduleInfo.serviceTime || 0)
          : serviceScheduleInfo.startTime;
      const newEndDateTime = dayjs(newStartDate)
        .setMinutes(newStartTime || 0)
        .add(serviceScheduleInfo.serviceTime || 0, 'minute');
      return {
        startDate: newStartDate,
        startTime: newStartTime,
        endDate: newEndDateTime.format(DATE_FORMAT_EXCHANGE),
        endTime: newEndDateTime.getMinutes(),
      };
    },
  );

  const onSyncScheduleEffect = useLatestCallback((scheduleInfo: MainScheduleInfo) => {
    petServiceAddOnList
      .filter((s) => {
        // 由于当前 serviceItemType 都统一为主 service，无法分辩出非 main service 的 service
        // 临时通过 isAdditionalService 来判断是否为主 service（因为非 main service 目前只有 additional 一条添加路径）
        const isMainService =
          s.serviceType === ServiceType.Service &&
          s.serviceItemType === ServiceItemType.BOARDING &&
          !s.isAdditionalService;
        return !isMainService;
      })
      .forEach((s) => {
        if (s.serviceType === ServiceType.Service) {
          const service = store.select(selectApptPetService(appointmentId, s.id));
          const { dateType } = service;
          if (dateType === PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY) {
            dispatch(setServiceForPet(appointmentId, s.id, getFirstDayDateTime(scheduleInfo, service)));
          } else if (dateType === PetDetailDateType.PET_DETAIL_DATE_LAST_DAY) {
            dispatch(setServiceForPet(appointmentId, s.id, getLastDayDateTime(scheduleInfo, service)));
          }
        } else {
          const addOn = store.select(selectApptPetAddon(appointmentId, s.id));
          const { dateType } = addOn;
          if (dateType === PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY) {
            dispatch(setAddonForPet(appointmentId, s.id, getFirstDayDateTime(scheduleInfo, addOn)));
          } else if (dateType === PetDetailDateType.PET_DETAIL_DATE_LAST_DAY) {
            dispatch(setAddonForPet(appointmentId, s.id, getLastDayDateTime(scheduleInfo, addOn)));
          }
        }
      });
  });

  // listen for change
  const { startDate, startTime, endDate, endTime, serviceItemType } = mainService;
  useEffect(() => {
    if (serviceItemType !== ServiceItemType.BOARDING) return;
    onSyncScheduleEffect({ startDate, startTime, endDate, endTime });
  }, [startDate, startTime, endDate, endTime, serviceItemType]);
};
