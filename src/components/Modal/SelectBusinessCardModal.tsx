import { Avatar, Checkbox, Heading, Modal, Text, cn } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo, useState } from 'react';
import IconMobileGroomingSvg from '../../assets/icon/icon-mobile-grooming.svg';
import IconSalonSvg from '../../assets/icon/icon-salon.svg';
import { type BusinessRecord, businessMapBox } from '../../store/business/business.boxes';
import { type RecordMap } from '../../store/utils/RecordMap';

interface IBusinessSelectorProps {
  onCancel?: () => void;
  onConfirm?: (businessIds: number[]) => void;
  onClose?: () => void;
  confirmText?: string;
  cancelText?: string;
  /**
   * Filter business by business id
   * return false to hide the business
   */
  businessFilters?: (businessId: number) => boolean;
}

const SelectableBusinessCard = memo<{
  business: BusinessRecord;
  selected: boolean;
  onSelect: (businessId: number, selected: boolean) => void;
}>((props) => {
  const { business, selected, onSelect } = props;
  return (
    <section
      aria-selected={selected}
      className={cn([
        'moe-flex moe-items-center moe-p-s moe-cursor-pointer',
        'moe-border-divider moe-rounded-m moe-border',
        'aria-selected:moe-bg-brand-subtle aria-selected:moe-border-brand',
      ])}
      onClick={() => onSelect(business.id, !selected)}
    >
      <Avatar className="moe-w-8px-600 moe-h-8px-600" src={business.avatarPath} />
      <section className="moe-ml-s moe-flex moe-flex-col moe-gap-xxs moe-flex-1">
        <Text variant="regular" className="moe-font-bold moe-line-clamp-2" title={business.businessName}>
          {business.businessName}
        </Text>
        <Text variant="small" className="moe-text-tertiary moe-line-clamp-2" title={business.address1}>
          {business.address1}
        </Text>
      </section>
      <Checkbox className="moe-ml-s" isSelected={selected} />
    </section>
  );
});

const CardList = memo<{
  businesses: number[];
  businessMap: RecordMap<BusinessRecord, 'id'>;
  selectedBusiness: Set<number>;
  onSelect: (businessId: number, selected: boolean) => void;
}>((props) => {
  const { businesses, businessMap, selectedBusiness, onSelect } = props;
  return (
    <section className="moe-grid moe-grid-cols-2 moe-gap-s moe-mt-s">
      {businesses.map((business) => {
        const businessRecord = businessMap.getItem(Number(business));
        if (!businessRecord) return null;
        return (
          <SelectableBusinessCard
            key={business}
            business={businessRecord}
            selected={selectedBusiness.has(business)}
            onSelect={onSelect}
          />
        );
      })}
    </section>
  );
});

export const SelectBusinessCardModal = memo<IBusinessSelectorProps>((props) => {
  const { onConfirm, onCancel, onClose, cancelText, confirmText, businessFilters = () => true } = props;
  const [selectedBusiness, setSelectedBusiness] = useState<Set<number>>(new Set());
  const [businessMap] = useSelector(businessMapBox);
  const { salonBusiness, mobileBusiness } = useMemo(() => {
    const map = businessMap.toJSON();
    const businessIds = Object.keys(map);
    const salonBusiness = businessIds
      .filter((key) => !map[key].isMobileGrooming())
      .map(Number)
      .filter(businessFilters);
    const mobileBusiness = businessIds
      .filter((key) => map[key].isMobileGrooming())
      .map(Number)
      .filter(businessFilters);
    return { salonBusiness, mobileBusiness };
  }, [businessMap, businessFilters]);
  const isAllSelected = salonBusiness.length + mobileBusiness.length === selectedBusiness.size;

  const handleSelect = (businessId: number, selected: boolean) => {
    setSelectedBusiness((prev) => {
      const next = new Set(prev);
      if (selected) {
        next.add(businessId);
      } else {
        next.delete(businessId);
      }
      return next;
    });
  };

  const handleSelectAll = (isSelected: boolean) => {
    if (!isSelected) {
      setSelectedBusiness(new Set());
    } else {
      setSelectedBusiness(new Set([...salonBusiness, ...mobileBusiness]));
    }
  };
  return (
    <Modal
      isOpen
      title="Select Business"
      cancelText={cancelText}
      confirmText={confirmText}
      onCancel={onCancel}
      onConfirm={() => onConfirm?.([...selectedBusiness])}
      onClose={onClose}
      confirmButtonProps={{
        isDisabled: selectedBusiness.size === 0,
      }}
    >
      <Checkbox isSelected={isAllSelected} onChange={handleSelectAll}>
        Select all
      </Checkbox>
      {!!salonBusiness.length && (
        <>
          <section className="moe-flex moe-mt-[32px]">
            <img src={IconSalonSvg} alt="salon icon" width={28} height={28}></img>
            <Heading size="3" className="moe-ml-[8px]">
              Salon
            </Heading>
          </section>
          <CardList
            businesses={salonBusiness}
            businessMap={businessMap}
            selectedBusiness={selectedBusiness}
            onSelect={handleSelect}
          />
        </>
      )}
      {!!mobileBusiness.length && (
        <>
          <section className="moe-flex moe-mt-[32px]">
            <img src={IconMobileGroomingSvg} alt="mobile grooming icon" width={28} height={28}></img>
            <Heading size="3" className="moe-ml-[8px]">
              Mobile
            </Heading>
          </section>
          <CardList
            businesses={mobileBusiness}
            businessMap={businessMap}
            selectedBusiness={selectedBusiness}
            onSelect={handleSelect}
          />
        </>
      )}
    </Modal>
  );
});
