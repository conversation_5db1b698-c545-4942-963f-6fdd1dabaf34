package main

import (
	"context"
	"fmt"
	"log"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/protobuf/proto"

	offeringModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringModelsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	offeringV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v2"
)

func main() {
	// 连接到 gRPC 服务
	conn, err := grpc.Dial("localhost:9090", grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("Failed to connect: %v", err)
	}
	defer conn.Close()

	// 创建客户端
	client := offeringV2.NewPricingRuleServiceClient(conn)

	// 构建请求
	request := &offeringV2.CalculatePricingRuleRequest{
		CompanyId: 103931,
		PetDetails: []*offeringModelsV2.PetDetailCalculateDef{
			{
				PetId:           18992681,
				ServiceId:       1143751,
				ServicePrice:    111,
				LodgingUnitId:   proto.Int64(3531),
				ScopeTypePrice:  offeringModelsV1.ServiceScopeType_DO_NOT_SAVE.Enum(),
				ServiceDate:     proto.String("2025-07-31"),
				IsSplitLodging:  false,
			},
			{
				PetId:           22555699,
				ServiceId:       1143751,
				ServicePrice:    111,
				LodgingUnitId:   proto.Int64(3531),
				ScopeTypePrice:  offeringModelsV1.ServiceScopeType_DO_NOT_SAVE.Enum(),
				ServiceDate:     proto.String("2025-07-31"),
				IsSplitLodging:  false,
			},
			{
				PetId:           22555700,
				ServiceId:       1211479,
				ServicePrice:    1120,
				LodgingUnitId:   proto.Int64(3531),
				ScopeTypePrice:  &offeringModelsV1.ServiceScopeType_DO_NOT_SAVE,
				ServiceDate:     proto.String("2025-07-31"),
				IsSplitLodging:  false,
			},
		},
	}

	// 调用 API
	fmt.Printf("Calling CalculatePricingRule with:\n")
	fmt.Printf("Company ID: %d\n", request.CompanyId)
	fmt.Printf("Pet Details:\n")
	for i, pet := range request.PetDetails {
		fmt.Printf("  Pet %d: ID=%d, ServiceID=%d, Price=%.2f, LodgingUnit=%d\n",
			i+1, pet.PetId, pet.ServiceId, pet.ServicePrice, pet.GetLodgingUnitId())
	}
	fmt.Println()

	response, err := client.CalculatePricingRule(context.Background(), request)
	if err != nil {
		log.Fatalf("Failed to call CalculatePricingRule: %v", err)
	}

	// 打印结果
	fmt.Printf("Response received:\n")
	fmt.Printf("Number of pet details: %d\n", len(response.PetDetails))
	
	for i, pet := range response.PetDetails {
		fmt.Printf("Pet %d:\n", i+1)
		fmt.Printf("  Pet ID: %d\n", pet.PetId)
		fmt.Printf("  Service ID: %d\n", pet.ServiceId)
		fmt.Printf("  Adjusted Price: %.2f\n", pet.AdjustedPrice)
		fmt.Printf("  Applied Rule IDs: %v\n", pet.AppliedRuleIds)
		fmt.Printf("  Service Date: %s\n", pet.GetServiceDate())
		
		if len(pet.AppliedRuleIds) > 0 {
			fmt.Printf("  ✓ Pricing rule applied!\n")
		} else {
			fmt.Printf("  ✗ No pricing rule applied\n")
		}
		fmt.Println()
	}

	// 分析结果
	totalAppliedRules := 0
	for _, pet := range response.PetDetails {
		totalAppliedRules += len(pet.AppliedRuleIds)
	}

	if totalAppliedRules > 0 {
		fmt.Printf("✅ SUCCESS: Pricing rules were applied to pets\n")
		fmt.Printf("Total applied rules: %d\n", totalAppliedRules)
	} else {
		fmt.Printf("❌ ISSUE: No pricing rules were applied\n")
		fmt.Printf("This might indicate:\n")
		fmt.Printf("1. No multi-pet rules exist for company 103931\n")
		fmt.Printf("2. The rules don't cover services 1143751 or 1211479\n")
		fmt.Printf("3. The rules have conditions that aren't met (e.g., minimum pet count)\n")
		fmt.Printf("4. There's still a bug in the pricing rule logic\n")
	}
}
