import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { matchApptServiceScene } from '../../../container/Appt/store/appt.options';
import { ApptServiceScene } from '../../../container/Appt/store/appt.types';

export interface ShouldHideServiceDateParams {
  serviceItemType?: ServiceItemType;
  mainServiceItemType?: ServiceItemType;
  serviceType?: ServiceType;
}

export const shouldHideServiceDate = ({
  serviceItemType,
  mainServiceItemType,
  serviceType,
}: ShouldHideServiceDateParams) => {
  const isDaycareAddon = mainServiceItemType === ServiceItemType.DAYCARE && serviceType === ServiceType.ADDON;
  const serviceShouldHideDate = matchApptServiceScene(ApptServiceScene.HiddenServiceDateWhenSingleDayAppt, {
    serviceItemType,
    mainServiceItemType,
  });

  return isDaycareAddon || serviceShouldHideDate;
};
