import FullCalendar, {
  type CalendarOptions,
  type ClassNamesGenerator,
  type CustomContentGenerator,
  type EventContentArg,
  type SlotLabelContentArg,
  type SlotLaneContentArg,
} from '@fullcalendar/react';
import { useSelector } from 'amos';
import React, { forwardRef, memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useUnmount } from 'react-use';
import { Condition } from '../../../../components/Condition';
import { selectLodgingPlaceholderCards } from '../../../../store/calendarLatest/card.selectors';
import {
  selectCalendarBusinessLodgingUnitList,
  selectLodgingCalendarConfig,
} from '../../../../store/calendarLatest/lodgingCalendar.selectors';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { CreateApptId } from '../../../Appt/store/appt.types';
import { useObserveCalendarResize } from '../../latest/ApptCalendar/hooks/useObserveCalendarResize';
import { useSetFullCalendarRef } from '../../latest/AwesomeCalendarProvider';
import { useBizClosedTimeAndDate } from '../hooks/useBizClosedTimeAndDate';
import { useBizSchedule } from '../hooks/useBizSchedule';
import { useFullCalendarDropResize } from '../hooks/useFullCalendarCallback';
import { useFullCalendarDateClick } from '../hooks/useFullCalendarDateClick';
import { useFullCalendarEventAllow } from '../hooks/useFullCalendarEventAllow';
import { useFullCalendarOnBlur } from '../hooks/useFullCalendarOnBlur';
import { useLodgingCalendarApptEvents } from '../hooks/useLodgingCalendarEvents';
import { useLodgingPlaceHolderCard } from '../hooks/useLodgingPlaceholderCard';
import { useLodgingResource } from '../hooks/useLodgingResource';
import { usePreviewLodgingCards } from '../hooks/usePreviewLodgingCards';
import { LodgingCalendarDefaultProps, LodgingCalendarHeight } from './LodgingCalendar.config';
import { LodgingCalendarView } from './LodgingCalendar.style';
import { LodgingAppointmentEvent } from './components/LodgingAppointmentEvent';
import { LodgingCalendarEmpty, LodgingCalendarEmptySceneMap } from './components/LodgingCalendarEmpty';
import { LodgingPlaceholderCard } from './components/LodgingPlaceholderCard';
import { RescheduleLodgingApptModal } from './components/RescheduleLodgingApptModal';
import { SlotLabelContent } from './components/SlotLabelContent';
import { SlotLaneContent } from './components/SlotLaneContent';
import { isSafari } from '../../../../utils/device';

export interface LodgingCalendarProps {
  isLoading?: boolean;
  scrollerProviderRef?: React.RefObject<HTMLDivElement>;
}

const CALENDAR_RESOURCE_SLOT_HEIGHT = 64;
const LAYOUT_PADDING = 20;

const LodgingFullCalendar = memo(
  forwardRef<FullCalendar, CalendarOptions>((props, ref) => {
    return <FullCalendar {...props} ref={ref} />;
  }),
);
LodgingFullCalendar.displayName = 'LodgingFullCalendar';

export const LodgingCalendar = memo<LodgingCalendarProps>((props) => {
  const { scrollerProviderRef } = props;
  const boxRef = useRef(null);
  const tableElementRef = useRef<HTMLTableElement | null>(null);
  const [businessLodgingUnitList, placeholderCards, lodgingCalendarConfig] = useSelector(
    selectCalendarBusinessLodgingUnitList,
    selectLodgingPlaceholderCards,
    selectLodgingCalendarConfig,
  );
  const [scrollHeight, setScrollHeight] = useState(0);
  const {
    isEmptyResource,
    resources,
    renderResourceLabelContent,
    resourceGroupLaneClassNames,
    resourceGroupLabelClassNames,
    renderResourceGroupLabelContent,
    renderResourceGroupLaneContent,
    resourceGroupLaneDidMount,
    resourceGroupLaneWillUnmount,
    resourceGroupLabelDidMount,
    isAllResourceGroupNotExpanded,
    resourceAreaHeaderContent,
  } = useLodgingResource(tableElementRef);
  const setFullCalendarRef = useSetFullCalendarRef();
  const eventList = useLodgingCalendarApptEvents();
  const handleDateClick = useFullCalendarDateClick();
  const { removePlaceholderCard } = useLodgingPlaceHolderCard();
  const { event, rescheduleModal, rescheduleInfo } = useFullCalendarDropResize();
  const eventAllow = useFullCalendarEventAllow();
  const { timeData } = useBizSchedule();
  const { getClosedData } = useBizClosedTimeAndDate({ timeData });

  const isEmptyBusinessLodgingUnit = !businessLodgingUnitList.size;
  const showEmpty = isEmptyResource || isEmptyBusinessLodgingUnit;

  const calendarHeight = useMemo(() => (showEmpty ? LodgingCalendarHeight.Empty : '100%'), [showEmpty]);

  const renderSlotLabelContent = useCallback(
    (slotLabelContentArg: SlotLabelContentArg) => {
      const { showCloseDate } = getClosedData(slotLabelContentArg);
      return <SlotLabelContent slotLabelContentArg={slotLabelContentArg} showCloseDate={showCloseDate} />;
    },
    [getClosedData],
  );

  const renderEventContent = useCallback((eventInfo: EventContentArg) => {
    const { event, isDragging, isResizing } = eventInfo;
    const { extendedProps, id } = event;
    const { appointmentId } = extendedProps;
    if (appointmentId === CreateApptId || !appointmentId) {
      return <LodgingPlaceholderCard event={event} isResizing={isResizing} isDragging={isDragging} />;
    }
    return <LodgingAppointmentEvent cardId={id} isDragging={isDragging} />;
  }, []);

  const renderSlotLaneContent: CustomContentGenerator<SlotLaneContentArg> = useLatestCallback((event) => {
    const { showCloseBg } = getClosedData(event);
    return <SlotLaneContent {...event} showCloseBg={showCloseBg} />;
  });

  const renderEventClassNames: ClassNamesGenerator<EventContentArg> = useLatestCallback((eventInfo) => {
    const { event } = eventInfo;
    const { extendedProps } = event;
    const { appointmentId } = extendedProps;
    if (!appointmentId) {
      return 'moe-top-[6px] moe-fc-event-add-lodging';
    }
    return 'moe-top-[6px]';
  });

  usePreviewLodgingCards(eventList, placeholderCards);
  useFullCalendarOnBlur(removePlaceholderCard, []);
  useObserveCalendarResize(boxRef);
  useUnmount(() => {
    removePlaceholderCard();
  });

  useEffect(() => {
    const tableElement = tableElementRef.current;

    if (!tableElement) return;

    const resizeObserver = new ResizeObserver((entries) => {
      // offsetHeight 获取的值是整数，而 contentRect 获取的值是浮点数，所以这里需要取整
      const tableElementHeight =
        Math.round(entries[0].contentRect.height) + CALENDAR_RESOURCE_SLOT_HEIGHT + LAYOUT_PADDING;

      setScrollHeight(tableElementHeight);
    });

    resizeObserver.observe(tableElement);
    return () => resizeObserver.disconnect();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tableElementRef.current]);

  useEffect(() => {
    const eventHandler = (event: WheelEvent) => {
      if (!scrollerProviderRef?.current) return;
      const ref = scrollerProviderRef.current;
      // 如果到了页面顶部或者底部，就不再滚动
      if (
        (ref.scrollTop === 0 && event.deltaY < 0) ||
        (ref.scrollTop + ref.clientHeight >= ref.scrollHeight && event.deltaY > 0)
      ) {
        return;
      }
      ref.scrollBy({
        top: event.deltaY,
      });
    };
    // safari 原生支持拖拽时鼠标滚动和接触页面顶部或者底部的时候触发滚动
    // 加上这段逻辑反而 safari 会有问题
    if (!isSafari()) {
      scrollerProviderRef?.current?.addEventListener('wheel', eventHandler);
    }

    return () => {
      if (!isSafari()) {
        scrollerProviderRef?.current?.removeEventListener('wheel', eventHandler);
      }
    };
  }, []);

  return (
    <LodgingCalendarView
      style={{
        ...(scrollHeight ? { height: scrollHeight } : {}),
        paddingBottom: LAYOUT_PADDING,
      }}
      ref={boxRef}
      isAllResourceGroupNotExpanded={isAllResourceGroupNotExpanded}
    >
      <Condition if={!showEmpty}>
        <LodgingFullCalendar
          {...LodgingCalendarDefaultProps}
          initialView={lodgingCalendarConfig.calendarViewType}
          height={calendarHeight}
          slotLaneContent={renderSlotLaneContent}
          slotLaneClassNames="moe-relative moe-z-0"
          ref={setFullCalendarRef}
          resources={resources}
          resourceLabelContent={renderResourceLabelContent}
          slotLabelContent={renderSlotLabelContent}
          eventContent={renderEventContent}
          resourceGroupLaneClassNames={resourceGroupLaneClassNames}
          resourceGroupLabelClassNames={resourceGroupLabelClassNames}
          resourceGroupLabelContent={renderResourceGroupLabelContent}
          resourceGroupLaneContent={renderResourceGroupLaneContent}
          resourceGroupLaneDidMount={resourceGroupLaneDidMount}
          resourceGroupLaneWillUnmount={resourceGroupLaneWillUnmount}
          resourceGroupLabelDidMount={resourceGroupLabelDidMount}
          eventClassNames={renderEventClassNames}
          dateClick={handleDateClick}
          eventDrop={event.handleEventDropResize}
          eventResize={event.handleEventDropResize}
          eventAllow={eventAllow}
          resourceAreaHeaderContent={resourceAreaHeaderContent}
        />
      </Condition>
      <Condition if={showEmpty}>
        <LodgingCalendarEmpty
          scene={
            isEmptyBusinessLodgingUnit
              ? LodgingCalendarEmptySceneMap.SetupLodgingUnits
              : LodgingCalendarEmptySceneMap.NoMatchResult
          }
        />
      </Condition>
      <RescheduleLodgingApptModal
        rescheduleInfo={rescheduleInfo}
        visible={rescheduleModal.visible}
        onClose={rescheduleModal.handleClose}
        dateIsChange={rescheduleModal.dateIsChange}
        onCancel={event.handleRevertEvent}
      />
    </LodgingCalendarView>
  );
});
LodgingCalendar.displayName = 'LodgingCalendar';
