import { SegmentControl } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { selectLodgingCalendarConfig } from '../../../../store/calendarLatest/lodgingCalendar.selectors';
import { ReportActionName } from '../../../../utils/reportType';
import { reportData } from '../../../../utils/tracker';
import { LodgingCalendarViewsMap, type LodgingCalendarViewsType } from '../LodgingCalendar/LodgingCalendar.config';
import { useSwitchLodgingCalendarView } from '../hooks/LodgingFullCalendarApi';
import { LODGING_CALENDAR_VIEW_TYPE_KEY } from '../../../../store/calendarLatest/lodgingCalendar.boxes';

export interface LodgingCalendarViewTypeProps {
  onChange?: (viewType: LodgingCalendarViewsType) => void;
}

export const LodgingCalendarViewType = memo<LodgingCalendarViewTypeProps>((props) => {
  const { onChange } = props;

  const [lodgingCalendarConfig] = useSelector(selectLodgingCalendarConfig);

  const switchLodgingCalendarView = useSwitchLodgingCalendarView();
  const handleChangeCalendarView = (view: LodgingCalendarViewsType) => {
    switchLodgingCalendarView(view);
    onChange?.(view);
    reportData(ReportActionName.lodgingViewChangeWeekType, {
      viewType: LodgingCalendarViewsMap.mapLabels[view].buttonText,
    });
    // 这个不放到 metadata 里了，否则要多请求一次，大商家的场景下可能会导致 calendar 要加载两次大数据
    localStorage.setItem(LODGING_CALENDAR_VIEW_TYPE_KEY, view);
  };

  return (
    <SegmentControl
      value={lodgingCalendarConfig.calendarViewType}
      onChange={handleChangeCalendarView}
      itemClassNames={{ base: 'moe-shrink-0' }}
    >
      {LodgingCalendarViewsMap.values.map((value) => {
        return (
          <SegmentControl.Item
            key={value}
            value={value}
            label={LodgingCalendarViewsMap.mapLabels[value].buttonText}
          ></SegmentControl.Item>
        );
      })}
    </SegmentControl>
  );
});
