import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Tabs } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo, useMemo } from 'react';
import { useHistory } from 'react-router';
import { Switch } from '../../../../../components/SwitchCase';
import { PATH_ONLINE_BOOKING_AVAILABILITY } from '../../../../../router/paths';
import { selectBDFeatureEnable } from '../../../../../store/company/company.selectors';
import { useInitBusinessApplicableEvaluation } from '../../../../../store/evaluation/evaluation.hooks';
import { useRouteQueryV2 } from '../../../../../utils/RoutePath';
import { SettingsTitle } from '../components/SettingsTitle';
import { OnlineBookingNav } from '../types';
import { usePreloadingAvailability } from './hooks/usePreloadingAvailability';
import { BoardingAndDaycareAvailability } from './modules/BoardingAndDaycareAvailability/BoardingAndDaycareAvailability';
import { GroomingAvailability } from './modules/GroomingAvailability/GroomingAvailability';
import { type AvailabilityTabKeyType, AvailabilityTabType } from './type';
import { selectCompanyCareTypeNameMap } from '../../../../../store/careType/careType.selectors';

const TabsComponent = {
  [AvailabilityTabType.Grooming]: <GroomingAvailability />,
  [AvailabilityTabType.Boarding]: <BoardingAndDaycareAvailability serviceItemType={ServiceItemType.BOARDING} />,
  [AvailabilityTabType.Daycare]: <BoardingAndDaycareAvailability serviceItemType={ServiceItemType.DAYCARE} />,
  [AvailabilityTabType.Evaluation]: <BoardingAndDaycareAvailability serviceItemType={ServiceItemType.EVALUATION} />,
};

export const Availability = memo(function Availability() {
  const history = useHistory();
  const { panel = AvailabilityTabType.mapLabels[AvailabilityTabType.Grooming].panel } = useRouteQueryV2(
    PATH_ONLINE_BOOKING_AVAILABILITY,
  );
  const [enableBD, companyCareTypeNameMap] = useSelector(selectBDFeatureEnable, selectCompanyCareTypeNameMap);
  const evaluation = useInitBusinessApplicableEvaluation(true);
  const enableEvaluation = !evaluation.isLoading && !!evaluation.currentEvaluation?.isActive;

  const tabList = useMemo(() => {
    return AvailabilityTabType.values
      .filter((value) => {
        if (value === ServiceItemType.EVALUATION) {
          return enableEvaluation;
        }
        return true;
      })
      .filter((key) => {
        return enableBD || !AvailabilityTabType.mapLabels[key].needBDEnable;
      })
      .map((value) => {
        return {
          key: AvailabilityTabType.mapLabels[value].panel,
          label: companyCareTypeNameMap.getName(value),
          component: TabsComponent[value],
        };
      });
  }, [enableBD, enableEvaluation, companyCareTypeNameMap]);

  /**
   * 如果只有一个 tab，直接展示内容，tab 可以隐藏。
   * 当 enableBD === false，则只有 grooming。
   */
  const firstTabContent = tabList.length === 1 ? tabList[0].component : undefined;

  usePreloadingAvailability();

  return (
    <div className="moe-w-full moe-font-manrope">
      <SettingsTitle title={OnlineBookingNav.Availability} />
      <div className="moe-flex moe-flex-col moe-gap-y-xl">
        <Switch>
          <Switch.Case if={tabList.length > 1}>
            <Tabs
              selectedKey={panel}
              onChange={(key) => {
                history.push(
                  PATH_ONLINE_BOOKING_AVAILABILITY.queried({
                    panel: key as AvailabilityTabKeyType,
                  }),
                );
              }}
            >
              {tabList.map((item) => {
                return (
                  <Tabs.Item key={item.key} label={item.label}>
                    <div className="moe-pt-m">{item.component}</div>
                  </Tabs.Item>
                );
              })}
            </Tabs>
          </Switch.Case>
          <Switch.Case else>{firstTabContent}</Switch.Case>
        </Switch>
      </div>
    </div>
  );
});
