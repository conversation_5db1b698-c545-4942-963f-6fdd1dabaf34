import { BookingRequestModelSource } from '@moego/api-web/moego/models/online_booking/v1/booking_request_models';
import { useSelector } from 'amos';
import { isNil } from 'lodash';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import { useMemo } from 'react';
import SvgDiscountSmallOutlinedSvg from '../../../../../../../assets/svg/discount-small-outlined.svg';
import SvgIconCalendarSvg from '../../../../../../../assets/svg/icon-calendar.svg';
import SvgIconClockSvg from '../../../../../../../assets/svg/icon-clock.svg';
import SvgIconMoneySvg from '../../../../../../../assets/svg/icon-money.svg';
import SvgIconUserSvg from '../../../../../../../assets/svg/icon-user.svg';
import SvgObAlertnoteSvg from '../../../../../../../assets/svg/ob-alertnote.svg';
import SvgObSourceSvg from '../../../../../../../assets/svg/ob-source.svg';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import {
  OnlineBookingRequestSource,
  onlineBookingLatestRequestMapBox,
} from '../../../../../../../store/onlineBooking/onlineBooking.boxes';
import { ID_ANONYMOUS } from '../../../../../../../store/utils/identifier';
import { appointmentDateTimeFormat } from '../../../../../../Appt/utils/appointmentDateTimeFormat';
import { TicketPaymentStatus } from '../../../../../../TicketDetail/interfaces';

export const useBookingInfoList = (bookingRequestId?: number) => {
  const [business, request] = useSelector(
    selectCurrentBusiness,
    onlineBookingLatestRequestMapBox.mustGetItem(bookingRequestId ?? ID_ANONYMOUS),
  );

  const { discountCode, isGroupClass } = request;

  return useMemo(() => {
    const submittedAt = {
      icon: SvgIconClockSvg,
      label: 'Submitted at',
      value: business.formatDateTime(Number(request.createTime) * T_SECOND),
    };
    const appointmentDateNode = request.isHybridEvaluationRequest
      ? null
      : {
          icon: SvgIconCalendarSvg,
          label: 'Appointment date',
          value: appointmentDateTimeFormat(business, request),
          valueClassName: 'moe-text-[#f15a2b]',
        };
    const { prepaidAmount } = request.prepay;

    const paymentStatus = {
      icon: SvgIconMoneySvg,
      label: 'Payment status',
      value: !isNil(prepaidAmount)
        ? `${TicketPaymentStatus.mapLabels[TicketPaymentStatus.Prepaid].label} ${business.formatAmount(prepaidAmount)}`
        : TicketPaymentStatus.mapLabels[TicketPaymentStatus.Unpaid].label,
    };

    const discount = discountCode
      ? {
          icon: SvgDiscountSmallOutlinedSvg,
          iconSize: 16,
          label: 'Discount applied',
          labelClassName: '!moe-ml-[4px]',
          value: discountCode.discountCodeName,
        }
      : null;

    const staff = request.hasEvaluation
      ? null
      : {
          icon: SvgIconUserSvg,
          label: isGroupClass ? 'Trainer' : 'Staff',
          value: request.firstStaffName(),
        };

    const alertNote = {
      icon: SvgObAlertnoteSvg,
      label: 'Additional note',
      value: request.additionalNote,
    };
    const isFromMembership = request.source === BookingRequestModelSource.MEMBERSHIP;
    const source = {
      icon: SvgObSourceSvg,
      label: 'From',
      value: isFromMembership
        ? 'Branded app'
        : OnlineBookingRequestSource.mapLabels[request.sourcePlatform] || 'Booking link',
    };

    return [submittedAt, appointmentDateNode, paymentStatus, staff, discount, source, alertNote]
      .filter(Boolean)
      .map((item) => ({
        ...item!,
        value: item?.value || '-',
      }));
  }, [business, request]);
};
