import React, { type FC } from 'react';
import { Modal } from '@moego/ui';
import { MajorLeftArrowOutlined } from '@moego/icons-react';
import { useModal } from '../../../../../../../../../components/Modal/useModal';
import { type PreviewCareTypeImage } from './types';
import ImageIphone14pmSafariPng from '../../../../../../../../../assets/image/iphone-14pm-safari.png';

export interface PreviewCareTypeImageModalProps {
  onClose: () => void;
  previewCareTypeImageList: PreviewCareTypeImage[];
}

const PreviewCareTypeImageModal: FC<PreviewCareTypeImageModalProps> = (props) => {
  const { onClose, previewCareTypeImageList } = props;

  return (
    <Modal
      isOpen
      title="Preview"
      size="m"
      onClose={onClose}
      classNames={{
        body: 'moe-p-0',
      }}
      showCancelButton={false}
      confirmText="Close"
    >
      <div className="moe-flex moe-justify-center moe-items-center moe-bg-neutral-sunken-light moe-font-[ProximaNova]">
        <div
          className="moe-w-[235px] moe-h-[478px] moe-py-[58px] moe-px-[20px] moe moe-bg-contain moe-bg-no-repeat"
          style={{
            backgroundImage: `url(${ImageIphone14pmSafariPng})`,
          }}
        >
          <div className="moe-h-full moe-flex moe-flex-col">
            <div className="moe-flex moe-items-center moe-gap-[4px] moe-py-xs">
              <MajorLeftArrowOutlined className="!moe-text-[16px]" />
              <p className="moe-text-[18px] moe-text-primary moe-font-bold">I want to book ...</p>
            </div>
            <div className="moe-flex-1 moe-flex moe-flex-col moe-gap-y-xs moe-overflow-y-auto">
              {previewCareTypeImageList.map(({ id, name, description, image }) => (
                <div
                  key={id}
                  className="moe-flex moe-justify-between moe-items-center moe-border moe-border-divider moe-px-xs moe-py-[6px] moe-rounded-s"
                >
                  <div>
                    <p className="moe-text-[12px] moe-text-primary moe-font-bold">{name}</p>
                    {description && <p className="moe-text-[10px] moe-mt-xxs moe-text-tertiary">{description}</p>}
                  </div>
                  {image && <img src={image} alt={name} className="moe-w-[54px] moe-h-[54px]" />}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export const usePreviewCareTypeImageModal = () => useModal(PreviewCareTypeImageModal);
