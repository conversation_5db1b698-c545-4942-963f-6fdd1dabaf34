import { type Option, Text } from '@moego/ui';
import { type SelectInstance } from '@moego/ui/dist/esm/components/LegacySelect/ReactSelect';
import React from 'react';
import { isNormal } from '../../store/utils/identifier';
import { memoForwardRef } from '../../utils/react';
import { Condition } from '../Condition';
import { SelectMultiStaff, type SelectMultiStaffProps } from './components/SelectMultiStaff';
import { type ServiceVariation } from './hooks/useServiceApplicableStaffList';
import { type ServiceConflictCheckParams, useServiceConflictCheck } from './hooks/useServiceConflictCheck';
import { useShowOnCalendarStaff } from './hooks/useShowOnCalendarStaff';
import { MultipleStaffId } from './utils/multipleStaffId';

// TODO(vision,p2) 这个组件的 props 有点多，且杂，可以优化一下
// value 可以保持仍然是输入 number，这样有利于在结合 form item 的时候使用
export interface ServiceStaffPickerProps extends Omit<SelectMultiStaffProps, 'onChange'> {
  appointmentDate: string;
  serviceStartTime?: number;
  className?: string;
  appointmentId?: number;
  serviceTime?: number;
  onChange?: (value?: number, serviceVariation?: ServiceVariation) => void;
  starStaffId?: number;
  onChangeStarStaff?: (v: number) => void;
  showMultiStaff?: boolean;
  isEnableOperations?: boolean;
  disableConflictCheck?: boolean;
}

export const ServiceStaffPicker = memoForwardRef<SelectInstance<Option<number>, false>, ServiceStaffPickerProps>(
  (props, ref) => {
    const {
      className,
      appointmentId,
      appointmentDate,
      serviceStartTime,
      showMultiStaff = true,
      isRequired = true,
      serviceTime,
      value,
      isEnableOperations,
      disableConflictCheck,
      onChange,
      ...restProps
    } = props;

    const checkParams: ServiceConflictCheckParams = {
      appointmentDate,
      ticketId: appointmentId,
      startTime: serviceStartTime,
      staffId: value,
      serviceTime: serviceTime ?? 0,
      isMultiple: isEnableOperations,
    };
    const { showConflictErrorTip, conflictErrorMsg, conflictCheckLoading } = useServiceConflictCheck(
      checkParams,
      disableConflictCheck,
    );
    const optionStaffIds = useShowOnCalendarStaff();

    const val = isEnableOperations && showMultiStaff ? MultipleStaffId : isNormal(value) ? Number(value) : undefined;

    return (
      <div className={className}>
        <SelectMultiStaff
          ref={ref}
          {...restProps}
          loading={conflictCheckLoading}
          value={val}
          filterStaffIds={optionStaffIds}
          isRequired={isRequired}
          showMultiStaff={showMultiStaff}
          onChange={onChange}
        />
        <Condition if={showConflictErrorTip}>
          <Text variant="small" className="moe-mt-[4px] moe-text-[#FAAD14]">
            {conflictErrorMsg}
          </Text>
        </Condition>
      </div>
    );
  },
);
