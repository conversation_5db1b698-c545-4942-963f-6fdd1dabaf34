import { FNK_OrderItemType, type IGeneralOrderDetailV2 } from '@moego/finance-web-kit';
import { MinorDollarOutlined, MinorPawsOutlined } from '@moego/icons-react';
import { Heading } from '@moego/ui';
import { groupBy, keyBy } from 'lodash';
import React from 'react';
import SvgIconAnimalPrintBagSvg from '../../../../../assets/svg/icon-animal-print-bag.svg';
import { Condition } from '../../../../../components/Condition';
import { SvgIcon } from '../../../../../components/Icon/Icon';
import { type FinanceKit } from '../../../../../service/finance-kit';
import { SectionTitle } from '../../../CartMixinOrderDrawer/components/Layout/SectionTitle';
import { OrderItems } from './OrderItems';
import { ProductSubtotal } from './ProductSubtotal';
import { ServiceSubtotal } from './ServiceSubtotal';
import { CompressedAvatarPet } from '@moego/business-components';

// 需要考虑几种 type 的 item：service、product、package、discount
export const OrderItemsInfo = (props: {
  className?: string;
  orderDetail: IGeneralOrderDetailV2<typeof FinanceKit.model>;
}) => {
  const { orderDetail, className } = props;
  const { items, petBriefs } = orderDetail;
  const petMap = keyBy(petBriefs, 'id');
  const services = items.filter((item) => item.type === FNK_OrderItemType.Service);
  const servicesGroupByPet = groupBy(services, 'petId');
  const petIds = Object.keys(servicesGroupByPet);
  const products = items.filter((item) => item.type === FNK_OrderItemType.Product);
  const deposits = items.filter((item) => item.type === FNK_OrderItemType.Deposit);
  const noShows = items.filter((item) => item.type === FNK_OrderItemType.NoShow);
  const serviceCharges = items.filter((item) => item.type === FNK_OrderItemType.ServiceCharge);

  return (
    <div className={className}>
      {/* 渲染 service */}
      <Condition if={!!services.length}>
        <SectionTitle
          data-slot="service-addons-header"
          icon={<MinorPawsOutlined className="moe-text-icon-tertiary" />}
          title="Services & add-ons"
        />
        <div className="moe-mt-s moe-flex moe-flex-col moe-gap-y-xs">
          {petIds.map((petId) => {
            return (
              <div key={petId}>
                <div className="moe-flex moe-items-center moe-gap-x-xxs">
                  <CompressedAvatarPet type={'dog'} size="xs" className="moe-bg-neutral-sunken-0" />
                  <Heading size="5">
                    {petMap[petId].name}({petMap[petId].breed})
                  </Heading>
                </div>
                <OrderItems
                  orderDetail={orderDetail}
                  items={servicesGroupByPet[petId]}
                  type={FNK_OrderItemType.Service}
                  className="moe-mt-xs"
                />
              </div>
            );
          })}
        </div>
      </Condition>

      {/* 渲染 service charge */}
      <Condition if={!!serviceCharges.length}>
        <SectionTitle icon={<MinorDollarOutlined className="moe-text-tertiary" />} title="Fees" className="moe-mt-xs" />
        <OrderItems
          orderDetail={orderDetail}
          items={serviceCharges}
          type={FNK_OrderItemType.ServiceCharge}
          className="moe-mt-[12px]"
        />
      </Condition>

      {/* 渲染 service subtotal = service + service charge */}
      <Condition if={!!services.length || !!serviceCharges.length}>
        <ServiceSubtotal orderDetail={orderDetail} className="moe-mt-xs" />
      </Condition>

      {/* 渲染 products */}
      <Condition if={!!products.length}>
        <div className="moe-border-divider moe-border-t moe-border-solid moe-my-m" />
        <SectionTitle
          icon={<SvgIcon src={SvgIconAnimalPrintBagSvg} className="moe-text-tertiary" size={20} />}
          title="Products"
        />
        <OrderItems
          orderDetail={orderDetail}
          items={products}
          type={FNK_OrderItemType.Product}
          className="moe-mt-[12px]"
        />
        <ProductSubtotal orderDetail={orderDetail} className="moe-mt-xs" />
      </Condition>

      {/* 渲染 deposits */}
      <Condition if={!!deposits.length}>
        <OrderItems
          orderDetail={orderDetail}
          items={deposits}
          type={FNK_OrderItemType.Deposit}
          className="moe-mt-[12px]"
        />
      </Condition>

      {/* 渲染 no shows */}
      <Condition if={!!noShows.length}>
        <OrderItems
          orderDetail={orderDetail}
          items={noShows}
          type={FNK_OrderItemType.NoShow}
          className="moe-mt-[12px]"
        />
      </Condition>
    </div>
  );
};
