import { Checkbox, Heading, Radio, RadioGroup, LegacySelect as Select } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { T_MINUTE } from 'monofile-utilities/lib/consts';
import React from 'react';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { type ActivityPrintCardSettingsType } from '../../../../store/printCard/activityCard/activityCard.boxes';
import {
  selectActivityPrintCardExtra,
  selectActivityPrintCardSettings,
} from '../../../../store/printCard/activityCard/activityCard.selectors';
import { setPrintCardSettings } from '../../../../store/printCard/printCard.actions';
import { Condition } from '../../../Condition';
import { StaticPrintSettingItem } from '../common/StaticPrintSettingItem';
import { ActivityCardSortEnum } from './ActivityCard.config';
import { FormItemLabel } from '@moego/ui/dist/esm/components/Form/FormItemLabel';
import { PetCodeSelector } from '../common/PetCodes/PetCodeSelector';
import { UniqueCommentCheckbox } from '../common/PetCodes/UniqueCommentCheckbox';

export const ActivityPrintCardSettings = () => {
  const dispatch = useDispatch();
  const [settings, business, { addOnOptions, feedingOptions, medicationOptions }] = useSelector(
    selectActivityPrintCardSettings(),
    selectCurrentBusiness(),
    selectActivityPrintCardExtra(),
  );

  const handleChange = <
    PK extends Exclude<keyof ActivityPrintCardSettingsType, 'version'>,
    CK extends keyof ActivityPrintCardSettingsType[PK],
  >(
    parentKey: PK,
    childrenKey: CK,
    value: ActivityPrintCardSettingsType[PK][CK],
  ) => {
    dispatch(setPrintCardSettings({ parentKey, childrenKey, value }));
  };

  const formatItemTime = (item: { label: string; value: string }) => {
    return `${item.label} ${business.formatFixedTime(Number(item.value) * T_MINUTE)}`;
  };

  return (
    <div className="moe-m-m">
      <Heading size="4">Settings</Heading>
      <div className="moe-mt-m moe-flex moe-flex-col moe-gap-y-m">
        <StaticPrintSettingItem title="Activity type" description="Select which tasks to display on the card">
          <Checkbox
            isSelected={settings.type.feeding}
            onChange={(isSelect) => {
              handleChange('type', 'feeding', isSelect);
            }}
          >
            Feeding
          </Checkbox>
          <Condition if={settings.type.feeding}>
            <Select
              className="moe-ml-[28px]"
              isMultiple
              options={feedingOptions}
              value={settings.type.feedingItems}
              formatOptionTitle={formatItemTime}
              onChange={(value) => {
                handleChange('type', 'feedingItems', value);
              }}
            />
          </Condition>
          <Checkbox
            isSelected={settings.type.medication}
            onChange={(isSelect) => {
              handleChange('type', 'medication', isSelect);
            }}
          >
            Medication
          </Checkbox>
          <Condition if={settings.type.medication}>
            <Select
              className="moe-ml-[28px]"
              isMultiple
              options={medicationOptions}
              value={settings.type.medicationItems}
              formatOptionTitle={formatItemTime}
              onChange={(value) => {
                handleChange('type', 'medicationItems', value);
              }}
            />
          </Condition>
          <Checkbox
            isSelected={settings.type.addOns}
            onChange={(isSelect) => {
              handleChange('type', 'addOns', isSelect);
            }}
          >
            Add-ons
          </Checkbox>
          <Condition if={settings.type.addOns}>
            <Select
              isMultiple
              className="moe-ml-[28px]"
              value={settings.type.addOnItems}
              onChange={(value) => {
                handleChange('type', 'addOnItems', value);
              }}
              options={addOnOptions}
            />
            <Checkbox
              className="moe-ml-[28px]"
              isSelected={settings.type.showTicketComments}
              onChange={(isSelect) => {
                handleChange('type', 'showTicketComments', isSelect);
              }}
            >
              Ticket comments
            </Checkbox>
          </Condition>
        </StaticPrintSettingItem>
        <StaticPrintSettingItem title="Sort by" note="Activities will always be grouped by schedule first.">
          <RadioGroup value={settings.sort.sortBy} onChange={(value) => handleChange('sort', 'sortBy', value)}>
            {ActivityCardSortEnum.values.map((value) => (
              <Radio key={value} value={value}>
                {ActivityCardSortEnum.mapLabels[value]}
              </Radio>
            ))}
          </RadioGroup>
        </StaticPrintSettingItem>
        <StaticPrintSettingItem title="Pet code">
          <div>
            <FormItemLabel>Select the pet code(s) to show</FormItemLabel>
            <PetCodeSelector
              value={settings.petCode.petCodeIdList}
              onChange={(value) => handleChange('petCode', 'petCodeIdList', value)}
            />
          </div>
          <UniqueCommentCheckbox
            isSelected={settings.petCode.showUniqueComment}
            onChange={(isSelect) => {
              handleChange('petCode', 'showUniqueComment', isSelect);
            }}
          />
        </StaticPrintSettingItem>
      </div>
    </div>
  );
};
