/*
 * @Author: <PERSON>
 * @Date: 2022-06-20 10:32:53
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2022-06-20 15:09:57
 */

import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'amos';
import { Empty } from 'antd';
import React, { memo } from 'react';
import { useAsync } from 'react-use';
import ImagePaymentSmartReaderPng from '../../../assets/image/payment-smart-reader.png';
import { Button } from '../../../components/Button/Button';
import { type ModalRequiredProps, modalApi } from '../../../components/Modal/Modal';
import { toastApi } from '../../../components/Toast/Toast';
import { deleteStripeSmartReaderById, getStripeSmartReaderList } from '../../../store/stripe/stripeTerminal.actions';
import { type StripeSmartReader, stripeSmartReaderMapBox } from '../../../store/stripe/stripeTerminal.boxes';
import { selectBusinessStripeSmartReaderList } from '../../../store/stripe/stripeTerminal.selectors';
import { c_text_secondary_color } from '../../../style/_variables';
import { useBool } from '../../../utils/hooks/useBool';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { getPurchaseHardwareLink } from '../CardProcessing.utils';
import { StripeAddSmartReaderModal } from './StripeAddSmartReaderModal';
import { StripeSmartReaderListModalView } from './StripeSmartReaderListModal.style';
import { selectCurrentStaff } from '../../../store/staff/staff.selectors';

export const URL_STRIPE_TERMINAL_SHOP = 'https://dashboard.stripe.com/terminal/shop';

export interface StripeSmartReaderListModalProps extends ModalRequiredProps {
  className?: string;
  locationId: string;
}

export const StripeSmartReaderListModal = memo<StripeSmartReaderListModalProps>(
  ({ className, visible, locationId, onClose }) => {
    const [readerList, readerMap, staff] = useSelector(
      selectBusinessStripeSmartReaderList(),
      stripeSmartReaderMapBox,
      selectCurrentStaff(),
    );

    const addTerminalVisible = useBool();
    const dispatch = useDispatch();
    const loaded = useBool();

    useAsync(async () => {
      if (visible && locationId) {
        await dispatch(getStripeSmartReaderList(locationId));
        loaded.open();
      }
    }, [visible, locationId]);

    const handleDeleteReader = useSerialCallback(async (reader: StripeSmartReader) => {
      return new Promise((resolve, reject) => {
        modalApi.confirm({
          type: 'success',
          icon: <ExclamationCircleOutlined style={{ color: '#d0021b' }} />,
          okType: 'danger',
          content: (
            <div>
              <p>The smart reader will be deleted</p>
              <p>SN: {reader.serialNumber}</p>
              <p>name: {reader.label}</p>
            </div>
          ),
          okText: 'Confirm',
          onOk: async () => {
            try {
              await dispatch(deleteStripeSmartReaderById(reader.id));
              toastApi.success('delete success');
              resolve('success');
            } catch (error) {
              reject();
            }
          },
        });
      });
    });

    return (
      <StripeSmartReaderListModalView
        width="600px"
        className={className}
        title="Smart readers"
        visible={visible}
        onClose={onClose}
        loading={!loaded.value}
      >
        {addTerminalVisible.value && (
          <StripeAddSmartReaderModal locationId={locationId} onClose={addTerminalVisible.close} />
        )}
        <div className="list">
          {readerList.map((id) => {
            const device = readerMap.mustGetItem(id);

            return (
              <div className="item" key={id}>
                <img src={ImagePaymentSmartReaderPng} />
                <div className="info">
                  <div className="name">{device.label}</div>
                  <div className="sn">{device.serialNumber}</div>
                </div>
                <div
                  className="code"
                  onClick={() => {
                    if (handleDeleteReader.isBusy()) return;

                    handleDeleteReader(device);
                  }}
                >
                  Delete
                </div>
              </div>
            );
          })}
          {!readerList.size && <Empty style={{ color: c_text_secondary_color }} />}
        </div>

        <div className="actions">
          {!readerList.size && (
            <div className="actions-link-wrap">
              <Button
                className="actions-link"
                btnType="link"
                target="_blank"
                href={getPurchaseHardwareLink(staff.isEnterpriseRelatedStaff())}
              >
                Purchase hardware
              </Button>
            </div>
          )}

          <Button onClick={addTerminalVisible.open} buttonRadius="circle" btnType="primary">
            Pair a reader
          </Button>
        </div>
      </StripeSmartReaderListModalView>
    );
  },
);
