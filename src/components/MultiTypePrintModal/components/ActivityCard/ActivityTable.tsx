import { Heading } from '@moego/ui';

import { CompressedAvatar } from '@moego/business-components';
import { T_MINUTE } from '@moego/reporting';
import { cn } from '@moego/ui';
import { useSelector } from 'amos';
import React from 'react';
import { getMergedLodgingNameFromLodgingInfos } from '../../../../container/Appt/utils/lodgingDisplayHelpers';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { printFullName } from '../../../../store/customer/customer.boxes';
import { getPetAvatarType } from '../../../../utils/BusinessUtil';
import { Condition } from '../../../Condition';
import { Switch } from '../../../SwitchCase';
import { useGetPetIntro } from '../../hooks/useGetPetIntro';
import { TableTitle } from '../common/TableTitle';
import { type ActivityTableDataItem } from './Activity.type';
import { selectActivityPrintCardSettings } from '../../../../store/printCard/activityCard/activityCard.selectors';
import { useActivityAddonsTableColsWidthMap } from '../../hooks/useActivityAddonsTableColsWidthMap';
import { PetCodes } from '../common/PetCodes/PetCodes';

export const ActivityTable = (props: {
  title: string;
  data: ActivityTableDataItem[];
  className?: string;
  instructionAlias?: string;
  noDataDesc?: string;
  isAddon?: boolean;
}) => {
  const [business, settings] = useSelector(selectCurrentBusiness(), selectActivityPrintCardSettings);
  const { title, data, className, noDataDesc, instructionAlias, isAddon } = props;
  const { type, petCode } = settings;
  const { petCodeIdList, showUniqueComment } = petCode;

  const getPetIntro = useGetPetIntro();
  const colsWidthMap = useActivityAddonsTableColsWidthMap(isAddon);
  const borderClass = 'moe-border moe-border-[#cbcbcb] moe-border-collapse';
  const paddingClass = 'moe-px-spacing-xs moe-py-spacing-xxs';

  const showTicketComments = type.showTicketComments && isAddon;

  return (
    <div className={cn(className)}>
      <TableTitle title={title} />
      <table className={cn('moe-w-full', borderClass)}>
        <Switch shortCircuit>
          <Switch.Case if={data.length === 0}>
            <tbody>
              <tr>
                <td colSpan={5} className="moe-text-center moe-py-m moe-font-normal">
                  {noDataDesc ?? 'No data'}
                </td>
              </tr>
            </tbody>
          </Switch.Case>
          <Switch.Case else>
            <>
              <thead className="moe-font-bold moe-text-[10px] moe-text-secondary moe-tracking-[0.2px]">
                <tr>
                  <td className={cn(borderClass, paddingClass, colsWidthMap.pet)}>Pet</td>
                  <td className={cn(borderClass, paddingClass, colsWidthMap.lodging)}>Lodging</td>
                  <td className={cn(borderClass, paddingClass, colsWidthMap.service)}>Service</td>
                  <td className={cn(borderClass, paddingClass, colsWidthMap.instruction)}>
                    {instructionAlias ?? 'Instruction'}
                  </td>
                  {showTicketComments && (
                    <td className={cn(borderClass, paddingClass, colsWidthMap.ticketComments)}>Ticket comments</td>
                  )}
                  <td className={cn(borderClass, paddingClass, colsWidthMap.time)}>Time</td>
                  <td className={cn(borderClass, paddingClass, colsWidthMap.staff)}>Staff</td>
                  <td className={cn(borderClass, paddingClass, colsWidthMap.action)}></td>
                </tr>
              </thead>
              <tbody>
                {data.map((item, index) => {
                  return (
                    <tr key={index}>
                      <td className={cn(borderClass, paddingClass)}>
                        <div className="moe-flex moe-flex-col moe-gap-y-xxs">
                          <div className="moe-flex moe-items-center">
                            <CompressedAvatar.Pet
                              src={item.avatarPath}
                              type={getPetAvatarType(item.petTypeId)}
                              size="xs"
                              className="moe-mr-xxs moe-bg-neutral-sunken-0"
                            />
                            <div className="moe-flex-1">
                              <Heading size="5" className="moe-text-[10px] moe-leading-[12px]">
                                {item.petName} ({printFullName(item.clientFirstName, item.clientLastName)})
                              </Heading>
                              <div className="moe-text-[#000] moe-text-[10px] moe-font-normal moe-leading-[12px]">
                                {getPetIntro({
                                  breed: item.breed,
                                  gender: item.gender,
                                  weight: item.weight,
                                })}
                              </div>
                            </div>
                          </div>
                          <PetCodes
                            petCodeBindings={item.petCodeBindings}
                            petCodeIds={petCodeIdList}
                            showUniqueComment={showUniqueComment}
                          />
                        </div>
                      </td>
                      <td className={cn(borderClass, paddingClass, 'moe-leading-[12px]')}>
                        <span className="moe-text-[10px] moe-font-normal">
                          {getMergedLodgingNameFromLodgingInfos(item)}
                        </span>
                      </td>
                      <td className={cn(borderClass, paddingClass, 'moe-leading-[12px]')}>
                        <span className="moe-text-[10px] moe-font-normal">{item.serviceName}</span>
                      </td>
                      <td className={cn(borderClass, paddingClass)}>
                        <div className="moe-text-[10px] moe-font-normal">
                          {/* can't use intro as key because some instructions are the same */}
                          {item.instruction.map((intro, index) => {
                            return <p key={index}>{intro}</p>;
                          })}
                        </div>
                      </td>
                      {showTicketComments && (
                        <td className={cn(borderClass, paddingClass)}>
                          <div className="moe-text-[10px] moe-font-normal">{item.ticketComments}</div>
                        </td>
                      )}
                      <td className={cn(borderClass, paddingClass)}>
                        <span className="moe-text-[10px] moe-font-normal moe-whitespace-nowrap">
                          {typeof item.time !== 'number' ? '' : business.formatFixedTime(item.time * T_MINUTE)}
                        </span>
                      </td>
                      <td className={cn(borderClass, paddingClass)}>
                        <Condition if={item.staffName}>
                          <span className="moe-text-[10px] moe-font-normal">{item.staffName}</span>
                        </Condition>
                      </td>
                      <td className={cn(borderClass, paddingClass)}></td>
                    </tr>
                  );
                })}
              </tbody>
            </>
          </Switch.Case>
        </Switch>
      </table>
    </div>
  );
};
