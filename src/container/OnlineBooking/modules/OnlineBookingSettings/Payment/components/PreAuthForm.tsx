import { Form, Switch } from 'antd';
import TextArea from 'antd/lib/input/TextArea';
import classNames from 'classnames';
import { type F0 } from 'monofile-utilities/lib/types';
import React, { forwardRef, useEffect, useImperativeHandle } from 'react';
import { type OutputModelOfInput, formInput } from '../../../../../../utils/form';
import { useFormRef } from '../../../../../../utils/hooks/hooks';
import { useBool } from '../../../../../../utils/hooks/useBool';
import { useInvoiceReinvent } from '../../../../../PaymentFlow/hooks/useInvoiceReinvent';
import { PrepaymentFormContainer } from './Form.style';
import { usePaymentOptions } from './PaymentOptionsContext';
import { PrepaymentPreviewModal } from './PrepaymentPreviewModal';

export interface PreAuthFormProps {
  className?: string;
  disabled?: boolean;
  onValuesChange: F0;
}

export interface PreAuthModel {
  preAuthTipEnable: number;
  preAuthPolicy: string;
}

export interface PreAuthFormRef {
  validate: () => Promise<OutputModelOfInput<typeof preAuthInput>>;
}

const preAuthInput = formInput<PreAuthModel>().copy('preAuthTipEnable', 'preAuthPolicy');

export const PreAuthForm = forwardRef<PreAuthFormRef, PreAuthFormProps>(
  ({ className, disabled, onValuesChange }, ref) => {
    const preference = usePaymentOptions(true);
    const form = useFormRef();
    const previewModalVisible = useBool(false);
    const { isNewOrderV4Flow } = useInvoiceReinvent();

    useImperativeHandle(ref, () => ({
      validate: () => preAuthInput.validate(form),
    }));

    useEffect(() => {
      preAuthInput.attach(form, {
        preAuthTipEnable: preference.preAuthTipEnable,
        preAuthPolicy: preference.preAuthPolicy,
      });
    }, [preference]);

    return (
      <PrepaymentFormContainer className={classNames(className, { disabled })}>
        {/* <p className="desc-section">123</p> */}
        <Form ref={form} className="form-container" onValuesChange={onValuesChange}>
          {isNewOrderV4Flow ? null : (
            <Form.Item name="preAuthTipEnable" label="Enable tipping" valuePropName="checked">
              <Switch disabled={disabled} />
            </Form.Item>
          )}
          <Form.Item label="Cancellation policy" required className="prepayment-policy-row">
            <div className="prepayment-policy-textarea">
              <p className="preview" onClick={previewModalVisible.open}>
                Preview
              </p>
              <Form.Item
                name="preAuthPolicy"
                rules={[{ required: true, message: 'Please input cancellation policy.' }]}
                noStyle
              >
                <TextArea
                  style={{ height: '100px' }}
                  disabled={disabled}
                  placeholder="24 hour notice is required to cancel a confirmed grooming or bath appointment without penalty. Cancellations made within 24 hours will be subject to the full charge of the service."
                />
              </Form.Item>
            </div>
          </Form.Item>
          <p className="!moe-text-[#999] !moe-text-sm !moe-font-medium">
            Please note that standard card processing fees apply and will be deducted from the total amount.
          </p>
        </Form>

        <PrepaymentPreviewModal
          visible={previewModalVisible.value}
          onClose={previewModalVisible.close}
          prepaymentPolicy={form.current?.getFieldValue('preAuthPolicy')}
        />
      </PrepaymentFormContainer>
    );
  },
);
