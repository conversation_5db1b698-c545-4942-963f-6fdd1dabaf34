import { captureException } from '@sentry/browser';

/**
 * Capture an error thrown during MoeGo Pay flow.
 * If {@param e} is an instance of Error, the title will be e.name (defaults to "Error").
 * If {@param e} is a plain object, the title will be e.name (defaults to "MoePayError").
 * @param e
 */
export const captureMoePayError = (e: any) => {
  if (e instanceof Error) {
    captureException(e);
  } else {
    captureException(Object.assign(new Error(), { name: 'MoePayError' }, e));
  }
};
