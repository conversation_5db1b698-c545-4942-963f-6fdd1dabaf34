import { MinorTrashOutlined } from '@moego/icons-react';
import { DatePicker, Form, IconButton, Input, Text, TimePicker, cn, type useForm } from '@moego/ui';
import { FormItemLabel } from '@moego/ui/dist/esm/components/Form/FormItemLabel';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { isNil } from 'lodash';
import React, { memo } from 'react';
import { ADPetsServicesTestIds } from '../../../config/testIds/apptDrawer';
import { SelectRoom } from '../../../container/Appt/components/SelectServiceDetail/components/SelectRoom/SelectRoom';
import { BD_TIME_PICKER_STEP_MINUTES } from '../../../container/Appt/store/appt.utils';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { lodgingUnitMapBox } from '../../../store/lodging/lodgingUnit.boxes';
import { isNormal } from '../../../store/utils/identifier';
import { Condition } from '../../Condition';
import { useGetTimeIsDisabled } from '../hooks/useGetTimeIsDisabled';
import {
  type ISplitLodgingItemChangePayload,
  SplitLodgingChangeType,
  type SplitLodgingItemValue,
  type SplitLodgingServiceRange,
} from '../utils/SplitLodgings.types';
import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { GrowthBookFeatureList } from '../../../utils/growthBook/growthBook.config';

interface SplitLodgingItemProps {
  form?: ReturnType<typeof useForm>;
  formPrefix: string;
  petId: string | number;
  serviceId: string | number;
  index: number;
  total: number;
  serviceRange: SplitLodgingServiceRange;
  value: SplitLodgingItemValue;
  isDisabled?: boolean;
  onChange: (payload: ISplitLodgingItemChangePayload) => void;
  onDelete: (index: number) => void;
  getDisabledRange: (date: dayjs.Dayjs) => boolean;
  getIsApplicableLodging: (lodgingId: string | undefined) => boolean;
}
export const SplitLodgingItem = memo<SplitLodgingItemProps>((props) => {
  const {
    form,
    formPrefix,
    petId,
    serviceId,
    index,
    total,
    value,
    serviceRange,
    isDisabled,
    onChange,
    onDelete,
    getDisabledRange,
    getIsApplicableLodging,
  } = props;
  const disableSplitLodgingEditPrice = useFeatureIsOn(GrowthBookFeatureList.DisableSplitLodgingEditPrice);
  const [business, lodgingUnitMap] = useSelector(selectCurrentBusiness, lodgingUnitMapBox);
  const lodging = isNormal(value.lodgingId) ? lodgingUnitMap.mustGetItem(value.lodgingId) : undefined;
  const getTimeIsDisabled = useGetTimeIsDisabled(serviceRange);
  const isApplicableLodging = getIsApplicableLodging(value.lodgingId);
  const isSingleLodging = total === 1;
  const isLastLodging = index === total - 1;
  return (
    <div
      data-testid={`${ADPetsServicesTestIds.SplitLodgingItem}-${index}`}
      className={cn('moe-flex moe-justify-between moe-gap-x-8px', {
        'moe-mb-8px-200 moe-pb-8px-200 moe-border-b moe-border-b-divider': !isLastLodging,
      })}
    >
      <div className="moe-flex moe-flex-col moe-flex-1 moe-gap-y-8px-200">
        <Form.Item
          name={isSingleLodging ? `${formPrefix}lodgingId` : `${formPrefix}splitLodgings.${index}.lodgingId`}
          rules={{ required: true }}
        >
          <SelectRoom
            containerClassName="moe-h-auto"
            serviceId={serviceId}
            petId={petId}
            isEvaluation={false}
            isDisabled={isDisabled}
            placeholder="Select room"
            value={value.lodgingId}
            onChange={(lodgingId, item) => {
              if (!isNil(lodgingId) && !isNil(item)) {
                onChange({
                  index,
                  type: SplitLodgingChangeType.LodgingId,
                  value: {
                    lodgingId,
                    lodgingName: item.label,
                    isApplicable: getIsApplicableLodging(lodgingId),
                  },
                });
              }
            }}
            range={{
              startDate: dayjs(serviceRange.startDate),
              endDate: dayjs(serviceRange.endDate),
            }}
          />
        </Form.Item>
        <Condition if={!isSingleLodging}>
          <div className="moe-flex moe-w-full moe-gap-[16px] moe-justify-between">
            <Form.Item name={`${formPrefix}splitLodgings.${index}.endDate`} rules={{ required: true }}>
              <DatePicker
                className="moe-w-full"
                placeholder="Select end date"
                format={business.dateFormat}
                isDisabled={isDisabled}
                isClearable={false}
                defaultViewDate={dayjs(value.endDate ?? serviceRange.startDate)}
                disabledDate={getDisabledRange}
                onChange={(date) => {
                  form?.clearErrors(`${formPrefix}splitLodgings.${index}.endDate`);
                  onChange({
                    index,
                    type: SplitLodgingChangeType.Date,
                    value: { endDate: date },
                  });
                }}
              />
            </Form.Item>
            <Form.Item name={`${formPrefix}splitLodgings.${index}.endTime`} rules={{ required: true }}>
              <TimePicker
                className="moe-w-[188px]"
                placeholder="Select end time"
                isClearable={false}
                isDisabled={isDisabled || getTimeIsDisabled(value.endDate)}
                format={business.timeFormat()}
                minuteStep={BD_TIME_PICKER_STEP_MINUTES}
                onChange={(time) => {
                  onChange({
                    index,
                    type: SplitLodgingChangeType.Time,
                    value: { endTime: time },
                  });
                }}
              />
            </Form.Item>
          </div>
        </Condition>
        <Condition if={!isSingleLodging && !isApplicableLodging}>
          <div>
            <FormItemLabel isRequired>Price for {lodging?.name}</FormItemLabel>
            <Input.Number
              suffix="per night"
              prefix={business.currencySymbol}
              value={value.price}
              minValue={0}
              isDisabled={disableSplitLodgingEditPrice || isDisabled}
              onChange={(price) => {
                onChange({
                  index,
                  type: SplitLodgingChangeType.Price,
                  value: { price: Number(price) },
                });
              }}
            />
            <Text variant="small" className="moe-text-tertiary">
              This is not an eligible lodging for your selected service, please confirm price.
            </Text>
          </div>
        </Condition>
      </div>
      <Condition if={!isSingleLodging}>
        <div className="moe-h-[40px] moe-w-[48px] moe-flex moe-items-center moe-justify-end">
          <IconButton
            size="m"
            isDisabled={isDisabled}
            onPress={() => onDelete(index)}
            icon={<MinorTrashOutlined />}
            data-testid={`${ADPetsServicesTestIds.SplitLodgingDeleteBtn}-${index}`}
          />
        </div>
      </Condition>
    </div>
  );
});
