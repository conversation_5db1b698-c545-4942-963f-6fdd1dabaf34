import { Form, Input } from '@moego/ui';
import React, { memo } from 'react';
import { useServiceStaffPermissions } from '../../../../AddOrEditComponents/hooks/useServiceStaffPermissions';
import { useEvaluationFormContext } from '../EvaluationFormContext';

export const OnlineBookingServiceName = memo(() => {
  const { isEdit } = useEvaluationFormContext();
  const { isDisabledForRemains } = useServiceStaffPermissions(isEdit);

  return (
    <Form.Item
      name="aliasForOnlineBooking"
      label="Service name in online booking"
      rules={{
        required: 'Service name is required',
      }}
    >
      <Input
        isRequired
        isDisabled={isDisabledForRemains}
        className="moe-w-full"
        placeholder="Meet & greet"
        maxLength={20}
        description="This will show on the customer side when they book online. "
      />
    </Form.Item>
  );
});
