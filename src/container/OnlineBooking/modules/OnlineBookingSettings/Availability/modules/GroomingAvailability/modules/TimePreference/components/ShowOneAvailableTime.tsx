import { Switch } from '@moego/ui';
import { useDispatch } from 'amos';
import React, { memo } from 'react';
import { OBTestIds } from '../../../../../../../../../../config/testIds/onlineBooking';
import { setAvailabilityDraftSettings } from '../../../../../../../../../../store/onlineBooking/actions/private/availabilityGrooming.actions';
import { type DraftAvailableSlotRecord } from '../../../../../../../../../../store/onlineBooking/settings/availabilityGrooming.boxes';
import { TimePreferenceSettingItem } from './TimePreferenceSettingItem';

export const ShowOneAvailableTime = memo<{ draftSettings: DraftAvailableSlotRecord }>((props) => {
  const { draftSettings } = props;
  const dispatch = useDispatch();

  return (
    <TimePreferenceSettingItem label="Next available slot" labelClassName="moe-mb-[16px]">
      <Switch
        isSelected={draftSettings.showOneAvailableTime > 0}
        onChange={(checked) => {
          dispatch(setAvailabilityDraftSettings({ showOneAvailableTime: +checked }));
        }}
        data-testid={OBTestIds.SettingNextAvailableSlotSwitch}
      >
        Only show the next available slot
      </Switch>
    </TimePreferenceSettingItem>
  );
});
