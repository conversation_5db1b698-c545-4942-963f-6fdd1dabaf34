import { AmountUtil } from '@moego/finance-utils';
import { Condition, cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { Divider } from 'antd';
import React, { useEffect } from 'react';
import { Loading } from '../../../../../../components/Loading/Loading';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { getAllClientInfo } from '../../../../../../store/customer/customer.actions';
import { customerMapBox } from '../../../../../../store/customer/customer.boxes';
import { selectPrimaryAddress } from '../../../../../../store/customer/customerAddress.selectors';
import { isNormal } from '../../../../../../store/utils/identifier';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { useLazySelectors } from '../../../../../../utils/unstable/createSelectAccessor';
import { getAppointment } from '../../../../../Appt/store/appt.api';
import { useInvoiceReinvent } from '../../../../../PaymentFlow/hooks/useInvoiceReinvent';
import { OrderPaymentStatus } from '../../../../../PaymentFlow/shared/OrderPaymentStatus';
import { WithInvoiceReinvent } from '../../../../../PaymentFlow/shared/WithInvoiceReinvent';
import { useEventPopoverState } from '../../hooks/useEventPopoverState';
import { ApptAlerts } from '../ApptEvent/ApptAlerts';
import { ApptPreAuthStatus } from '../ApptEvent/ApptPreAuthStatus';
import { useApptEventsDefaultColors } from '../ApptEvent/hooks/useApptEventsThemeColor';
import { ApptPayStatus } from '../ApptPayStatus';
import { ApptPopAlertNote } from './ApptPopAlertNote';
import { ApptPopClient } from './ApptPopClient';
import { ApptPopDateTime } from './ApptPopDateTime';
import { ApptPopEstimatedTotal } from './ApptPopEstimatedTotal';
import { ApptPopLastUpdatedBy } from './ApptPopLastUpdatedBy';
import { ApptPopPetServices } from './ApptPopPetServices';
import { ApptPopProgress } from './ApptPopProgress';
import { ApptPopServiceArea } from './ApptPopServiceArea';
import { ApptPopStaff } from './ApptPopStaff';
import { ApptPopStatus } from './ApptPopStatus';
import { ApptPopTicketComment } from './ApptPopTicketComment';
import { PartialServiceTip } from './PartialServiceTip';

export interface ApptPopoverContentProps {
  isPartial: boolean;
}

export function ApptPopoverContent(props: ApptPopoverContentProps) {
  const { isPartial } = props;
  const dispatch = useDispatch();
  const { apptPayStatus, orderInfo, appointmentId, clientInfo, isLodgingView } = useEventPopoverState();
  const { alertsClass } = useApptEventsDefaultColors();
  const [client, primaryAddress] = useLazySelectors(
    customerMapBox.mustGetItem(Number(clientInfo.clientId)),
    selectPrimaryAddress(Number(clientInfo.clientId)),
  );
  const { isNewOrderV4Flow } = useInvoiceReinvent();
  const [business] = useSelector(selectCurrentBusiness());

  const isValidateClientId = () => isNormal(client().customerId) && isNormal(primaryAddress().customerId);
  const getDetailInfoNew = useSerialCallback(async (appointmentId: string) => {
    if (appointmentId) {
      const res = await dispatch(getAppointment({ appointmentId }));
      const {
        customer: { customerProfile: customer },
      } = res;
      if (!isValidateClientId() && !customer.deleted) {
        await dispatch(getAllClientInfo(+customer.id));
      }
    }
  });

  useEffect(() => {
    getDetailInfoNew(appointmentId);
  }, [appointmentId]);

  return (
    // 第一次没有数据loading，有数据的时候不展示loading，但是会拉最新数据，来覆盖缓存的数据
    <Loading loading={getDetailInfoNew.isBusy()}>
      <div
        className={cn(
          'moe-flex moe-flex-col moe-gap-y-[16px] moe-text-[#333] moe-w-[480px] moe-py-[8px] moe-px-[4px] moe-overflow-y-auto',
          isLodgingView ? 'moe-max-h-[400px]' : 'moe-max-h-[600px]',
        )}
      >
        <div className="moe-flex moe-items-center moe-justify-between">
          <div className="moe-flex moe-items-center moe-gap-x-[16px]">
            <ApptPopStatus />
            <WithInvoiceReinvent fallback={<ApptPayStatus payStatus={apptPayStatus} />}>
              {!isNewOrderV4Flow && (
                <OrderPaymentStatus
                  status={orderInfo.paymentStatus}
                  isOverPaid={AmountUtil.checkIsCompatibleNegative(orderInfo.outstandingBalance)}
                  overPaidAmountString={business.formatAmount(orderInfo.outstandingBalance ?? 0)}
                  paidAmountString={business.formatAmount(orderInfo.paidAmount ?? 0)}
                />
              )}
            </WithInvoiceReinvent>
            <ApptPreAuthStatus size={20} />
          </div>
          <ApptAlerts showVaccination showAgreementNotSigned className={alertsClass} />
        </div>
        <Divider className="!moe-my-0 moe-border-divider" />
        <PartialServiceTip isPartial={isPartial} />
        <ApptPopClient />
        <ApptPopDateTime />
        <ApptPopPetServices />
        <ApptPopServiceArea />
        <ApptPopEstimatedTotal />
        <ApptPopStaff />
        <ApptPopAlertNote />
        <ApptPopTicketComment />
        <Condition if={!isLodgingView}>
          <ApptPopProgress />
          <ApptPopLastUpdatedBy />
        </Condition>
      </div>
    </Loading>
  );
}
