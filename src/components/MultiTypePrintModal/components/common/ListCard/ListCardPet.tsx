import type { ListAppointmentCardResultPetView } from '@moego/api-web/moego/api/appointment/v1/print_card_api';
import { CompressedAvatar } from '@moego/business-components';
import { cn } from '@moego/ui';
import React, { memo } from 'react';
import { PetGender } from '../../../../../store/pet/pet.boxes';
import { getPetAvatarType } from '../../../../../utils/BusinessUtil';
import { TextCaptionSmallBold, TextCaptionSmallRegular } from '../../../../Text/TextCaptionSmall';
import { useGetPetIntro } from '../../../hooks/useGetPetIntro';
import { PetCodes } from '../PetCodes/PetCodes';

export interface ListCardPetProps {
  pet: ListAppointmentCardResultPetView;
  verticalLayout: boolean;
  showUniqueComment: boolean;
}

export const ListCardPet = memo<ListCardPetProps>(({ pet, verticalLayout, showUniqueComment }) => {
  const {
    avatarPath,
    petType,
    name,
    breed,
    gender,
    weight,
    petAppearanceNotes,
    petAppearanceColor,
    ownerName,
    petFixed,
    petCodeBindings,
  } = pet;

  const getPetIntro = useGetPetIntro({ showAppearance: true });

  return (
    <div
      className={cn('moe-flex moe-flex-col moe-gap-y-xxs', {
        'moe-flex-row moe-items-center': !verticalLayout,
      })}
    >
      <CompressedAvatar.Pet
        src={avatarPath}
        type={getPetAvatarType(petType)}
        size="xs"
        className="moe-mr-xxs moe-bg-neutral-sunken-0"
      />
      <div className="moe-flex-1 moe-flex moe-flex-col moe-gap-xxs">
        <div className="moe-flex moe-flex-col">
          <TextCaptionSmallBold>{`${name} (${ownerName})`}</TextCaptionSmallBold>
          <TextCaptionSmallRegular>
            {getPetIntro({
              breed,
              gender: PetGender.mapLabels[gender],
              weight,
              petFixed,
              petAppearanceNotes,
              petAppearanceColor,
            })}
          </TextCaptionSmallRegular>
        </div>
        <PetCodes petCodeBindings={petCodeBindings} showUniqueComment={showUniqueComment} />
      </div>
    </div>
  );
});
