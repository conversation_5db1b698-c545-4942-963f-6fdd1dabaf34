import { datadogRum } from '@datadog/browser-rum';
import { useDispatch, useSelector } from 'amos';
import React, { memo } from 'react';
import ReactDOM from 'react-dom';
import { SwitchBusinessDropdown } from '../../../../components/Business/SwitchBusinessDropdown';
import { useCardVisibleController, useHiddenResourceController } from '../../../../components/CardVisible';
import { Condition } from '../../../../components/Condition';
import { updateLodgingCalendarConfigAndGetApptList } from '../../../../store/calendarLatest/actions/private/lodgingCalendar.actions';
import type { LodgingCalendarConfigModel } from '../../../../store/calendarLatest/lodgingCalendar.boxes';
import { selectCalendarBusinessLodgingUnitList } from '../../../../store/calendarLatest/lodgingCalendar.selectors';
import { useCancelableCallback } from '../../../../utils/hooks/useCancelableCallback';
import { DataDogActionName } from '../../../../utils/logger';
import { LodgingCalendarDate } from './LodgingCalendarDate';
import { LodgingCalendarFilters } from './LodgingCalendarFilters';
import { LodgingCalendarViewType } from './LodgingCalendarViewType';

export interface LodgingCalendarControllerProps {
  onLoading?: (isLoading: boolean) => void;
}

export const LodgingCalendarController = memo(({ onLoading }: LodgingCalendarControllerProps) => {
  const [businessLodgingUnitList] = useSelector(selectCalendarBusinessLodgingUnitList);

  const dispatch = useDispatch();
  const setHiddenResource = useHiddenResourceController();
  const setVisibleCards = useCardVisibleController();

  const handleUpdateConfigAndGetApptList = useCancelableCallback(
    async (signal, config: Partial<LodgingCalendarConfigModel>) => {
      ReactDOM.unstable_batchedUpdates(() => {
        onLoading?.(true);
        setHiddenResource(true);
        setVisibleCards(undefined);
      });
      try {
        await dispatch(updateLodgingCalendarConfigAndGetApptList(config, signal));
      } finally {
        ReactDOM.unstable_batchedUpdates(() => {
          onLoading?.(false);
          setHiddenResource(false);
          Promise.resolve().then(() => {
            datadogRum.stopDurationVital(DataDogActionName.CHANGE_CALENDAR_DATE);
          });
        });
      }
    },
  );

  return (
    <div className="moe-flex moe-gap-y-s moe-flex-wrap moe-items-center moe-justify-between moe-mb-l">
      <div className="moe-flex moe-gap-x-s">
        <Condition if={businessLodgingUnitList.size}>
          <LodgingCalendarViewType
            onChange={(viewType) => handleUpdateConfigAndGetApptList({ calendarViewType: viewType })}
          />
        </Condition>
        <LodgingCalendarDate onChange={(date) => handleUpdateConfigAndGetApptList({ calendarDate: date })} />
        <SwitchBusinessDropdown scene="working" />
      </div>
      <Condition if={businessLodgingUnitList.size}>
        <LodgingCalendarFilters onChange={(filter) => handleUpdateConfigAndGetApptList(filter)} />
      </Condition>
    </div>
  );
});
