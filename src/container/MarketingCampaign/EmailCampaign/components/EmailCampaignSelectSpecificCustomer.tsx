import { useDispatch, useSelector } from 'amos';
import classNames from 'classnames';
import React, { forwardRef, memo, useEffect, useImperativeHandle, useMemo } from 'react';
import { useSetState, useUnmount } from 'react-use';
import { alertApi } from '../../../../components/Alert/AlertApi';
import { Loading } from '../../../../components/Loading/Loading';
import { PATH_MARKETING_CAMPAIGN_PANEL } from '../../../../router/paths';
import { selectEmailBilling } from '../../../../store/company/company.selectors';
import {
  calculateEmailCampaignCredit,
  getEmailCampaignAvailableCount,
} from '../../../../store/marketingCampaign/emailCampaign.action';
import { useRouteQuery } from '../../../../utils/RoutePath';
import { useBool } from '../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { PurchaseEmailCreditModal } from './PurchaseCredit';
import { type ColumnDef, Heading, Table } from '@moego/ui';
import { selectChosenCustomersWithoutFilter } from '../../../../store/customer/chosenCustomers.selectors';
import { type CustomerRecord } from '../../../../store/customer/customer.boxes';

export interface EmailCampaignSelectRecipientRef {
  checkRecipientAndCredit: () => { validate: boolean; autoReloadTimes?: number };
}

export interface EmailCampaignSelectRecipientProps {
  templateId: string;
  recipientLoading: boolean;
  setRecipientLoading: (v: boolean) => void;
}

/** 一次买多少 credit */
export const BASE_CREDIT_NUM = 10;

export const EmailCampaignSelectSpecificCustomer = memo(
  forwardRef<EmailCampaignSelectRecipientRef, EmailCampaignSelectRecipientProps>(
    ({ recipientLoading, setRecipientLoading }, ref) => {
      const { specificCustomer } = useRouteQuery(PATH_MARKETING_CAMPAIGN_PANEL);
      const dispatch = useDispatch();
      const [countInfo, setCountInfo] = useSetState({
        availableEmailCredit: 0,
        /** 本次send，需要发送的credit */
        usedEmailCredit: 0,
      });
      const [emailBilling, chosenCustomersWithoutFilter] = useSelector(
        selectEmailBilling,
        selectChosenCustomersWithoutFilter(specificCustomer),
      );
      const clientClist = useMemo(() => {
        return chosenCustomersWithoutFilter.toArray().filter((customer) => !!customer.email);
      }, [chosenCustomersWithoutFilter]);
      const purchaseVisible = useBool();
      const isAutoReload = emailBilling.autoReload > 0;

      const handleCalculateEmailCampaignCredit = useSerialCallback(async () => {
        setRecipientLoading(true);
        if (clientClist.length === 0) {
          setCountInfo({
            usedEmailCredit: 0,
          });
          setRecipientLoading(false);
          return;
        }
        const res = await dispatch(calculateEmailCampaignCredit(clientClist.length));
        setCountInfo({
          usedEmailCredit: res,
        });
        handleGetAvailableCount();
      });
      // 是否需要额外购买credit
      const needBuyCredit = countInfo.usedEmailCredit > countInfo.availableEmailCredit;
      // 需要额外购买多少credit
      const extraCreditNum = needBuyCredit ? countInfo.usedEmailCredit - countInfo.availableEmailCredit : 0;
      // 是否自动购买
      const enableAutoBuyCredit = isAutoReload && needBuyCredit;
      // 需要额外购买多少次，因为一次只能购买10个credit
      const autoReloadTimes = useMemo(() => Math.ceil(extraCreditNum / BASE_CREDIT_NUM), [extraCreditNum]);
      // autoreload开启的问题
      const autoReloadTxt = enableAutoBuyCredit
        ? `, will auto reload ${autoReloadTimes * BASE_CREDIT_NUM} credits`
        : '';
      // 没有开启autoreload，credit不足需要提示不能购买
      const isNotSufficientCredit = !recipientLoading || !enableAutoBuyCredit || needBuyCredit;

      useEffect(() => {
        handleCalculateEmailCampaignCredit();
      }, [clientClist.length]);

      const handleGetAvailableCount = useLatestCallback(async () => {
        const res = await dispatch(getEmailCampaignAvailableCount());
        setCountInfo({
          availableEmailCredit: parseInt(res.availableEmails),
        });
        setRecipientLoading(false);
      });

      const handleCheckRecipientAndCredit = useLatestCallback(() => {
        if (clientClist.length === 0) {
          alertApi.warn('No recipient found. Please choose another filter.');
          return { validate: false };
        }
        if (isNotSufficientCredit) {
          alertApi.warn('The credit on your account is not sufficient to use for this campaign.');
          return { validate: false };
        }
        if (enableAutoBuyCredit) {
          return { validate: true, autoReloadTimes };
        }
        return { validate: true };
      });

      useImperativeHandle(ref, () => ({
        checkRecipientAndCredit: handleCheckRecipientAndCredit,
      }));

      useUnmount(() => {
        setRecipientLoading(false);
      });

      const columns = useMemo(
        () =>
          [
            {
              id: 'name',
              accessorKey: 'name',
              header: 'Client',
              cell(props) {
                return props.row.original.fullName();
              },
            },
            {
              id: 'email',
              accessorKey: 'email',
              header: 'Email',
              cell(props) {
                return props.row.original.email || '/';
              },
            },
          ] as ColumnDef<CustomerRecord>[],
        [],
      );

      return (
        <>
          <div className="!moe-w-[100%] !moe-flex !moe-flex-col !moe-items-center !moe-bg-[#fff] !moe-py-[40px] !moe-rounded-[8px] !moe-self-stretch !moe-min-h-[calc(100%-82px)]">
            <div className="!moe-w-[700px]">
              <Heading size={4}>Review recipients</Heading>
              <Loading loading={recipientLoading}>
                <div
                  className={`!moe-mt-8 !moe-p-[16px] !moe-flex !moe-items-center !moe-rounded-[8px]  ${
                    isNotSufficientCredit ? '!moe-bg-[#FEF0E8] !moe-mb-[4px]' : '!moe-bg-[#FFF7F0]'
                  }`}
                >
                  <div>Recipient count:</div>
                  <div className="!moe-font-bold !moe-mr-[24px] !moe-ml-[8px]">{clientClist.length}</div>
                  <div>Campaign credits used:</div>
                  <div className="!moe-font-bold !moe-ml-[8px]">{countInfo.usedEmailCredit}</div>
                  {enableAutoBuyCredit ? (
                    <div>&nbsp;{`(${countInfo.availableEmailCredit} available${autoReloadTxt})`}</div>
                  ) : (
                    <div>
                      &nbsp;(
                      <span
                        className={classNames(isNotSufficientCredit ? '!moe-text-[#d0021b]' : '')}
                      >{`${countInfo.availableEmailCredit} available`}</span>
                      )
                    </div>
                  )}
                  {isNotSufficientCredit && (
                    <div
                      className="!moe-ml-auto !moe-text-brand !moe-text-sm !moe-font-bold !moe-cursor-pointer hover:!moe-text-brand-hover"
                      onClick={purchaseVisible.open}
                    >
                      Purchase
                    </div>
                  )}
                </div>
              </Loading>
              <Table
                classNames={{
                  table: 'moe-border-black',
                  base: 'moe-border-black moe-mt-8',
                }}
                data={clientClist}
                columns={columns}
                getRowId={(record) => record.customerId.toString()}
              />
            </div>
          </div>
          <PurchaseEmailCreditModal
            visible={purchaseVisible.value}
            onClose={purchaseVisible.close}
            onPurchaseSuccess={() => {
              purchaseVisible.close();
              handleGetAvailableCount();
            }}
          />
        </>
      );
    },
  ),
);
