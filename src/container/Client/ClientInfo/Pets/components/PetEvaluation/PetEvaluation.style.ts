import styled from 'styled-components';
import { Card } from '../../../../../../components/Card/Card';

export const EvaluationCardView = styled(Card)`
  .card-container {
    padding-top: 16px;
    th {
      color: #9b9b9b;
      background-color: transparent;
      padding: 12px 6px 12px 0;
    }
    td {
      padding-left: 0;
    }
    tr:last-child > td {
      border-bottom: 0;
    }
    tr:hover > td {
      background: transparent;
    }
  }
`;
