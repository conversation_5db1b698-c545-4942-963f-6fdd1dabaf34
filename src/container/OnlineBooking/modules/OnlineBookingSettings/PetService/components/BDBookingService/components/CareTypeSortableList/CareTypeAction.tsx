import { MajorMoreOutlined } from '@moego/icons-react';
import { AlertDialog, Dropdown, IconButton } from '@moego/ui';
import React, { memo } from 'react';
import { useAddOrEditCareTypeModal } from '../AddOrEditCareTypeModal/AddOrEditCareTypeModal';
import { useEditAddonsModal } from '../EditAddonsModal/EditAddonsModal';
import { ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { useDispatch, useSelector } from 'amos';
import { deleteBookingCareType } from '../../../../../../../../../store/onlineBooking/actions/private/services.actions';
import { toastApi } from '../../../../../../../../../components/Toast/Toast';
import { selectBusinessCareTypeConfig } from '../../../../../../../../../store/onlineBooking/onlineBookingServices.selectors';

export interface CareTypeActionProps {
  className?: string;
  careTypeId: string;
}

export const CareTypeAction = memo<CareTypeActionProps>((props) => {
  const { className, careTypeId } = props;

  const [careTypeConfig] = useSelector(selectBusinessCareTypeConfig(careTypeId));

  const dispatch = useDispatch();
  const openEditModal = useAddOrEditCareTypeModal();
  const openEditAddonModal = useEditAddonsModal();
  const isAddon = careTypeConfig?.serviceType === ServiceType.ADDON;

  const handleEditCareType = () => {
    isAddon ? openEditAddonModal({ careTypeId }) : openEditModal({ careTypeId });
  };

  const handleDeleteCareType = () => {
    AlertDialog.open({
      title: 'Delete care type',
      content: 'Are you sure you want to delete this care type?',
      confirmText: 'Delete',
      onConfirm: async () => {
        await dispatch(deleteBookingCareType(careTypeConfig.id));
        toastApi.success('Care type deleted successfully!');
      },
    });
  };

  return (
    <Dropdown>
      <Dropdown.Trigger>
        <IconButton size="s" className={className} variant="primary" icon={<MajorMoreOutlined />} />
      </Dropdown.Trigger>
      <Dropdown.Menu>
        <Dropdown.Item title="Edit" onAction={handleEditCareType} />
        {!isAddon ? <Dropdown.Item title="Delete" isDestructive onAction={handleDeleteCareType} /> : <Dropdown.Item />}
      </Dropdown.Menu>
    </Dropdown>
  );
});
