import React, { memo } from 'react';
import { PetEvaluationHistory } from './PetEvaluationHistory';
import { listPetEvaluation } from '../../../../../../store/evaluation/evaluation.actions';
import { Link, Spin, Text } from '@moego/ui';
import { Switch } from '../../../../../../components/SwitchCase';
import { PATH_SERVICE_SETTING } from '../../../../../../router/paths';
import { ServicesNav } from '../../../../../settings/Settings/ServicesSetting/types';
import { PetEvaluationTable } from './PetEvaluationTable';
import { useRequest } from 'ahooks';
import { useDispatch, useSelector } from 'amos';
import { selectBDFeatureEnable } from '../../../../../../store/company/company.selectors';
import { Condition } from '../../../../../../components/Condition';
import { isNormal } from '@moego/finance-utils';
import { EvaluationCardView } from './PetEvaluation.style';

export interface PetEvaluationProps {
  petId: number;
}

export const PetEvaluation = memo<PetEvaluationProps>((props) => {
  const { petId } = props;
  const [enableBD] = useSelector(selectBDFeatureEnable);
  const dispatch = useDispatch();

  const {
    data: petEvaluationList,
    loading,
    run,
  } = useRequest(async () => await dispatch(listPetEvaluation({ petId: String(petId) })), {
    refreshDeps: [petId],
    ready: isNormal(petId),
  });

  return (
    <Condition if={enableBD}>
      <EvaluationCardView title="Pet's Evaluation" extra={<PetEvaluationHistory petId={petId} />}>
        <Spin isLoading={loading && !petEvaluationList}>
          <Switch>
            <Switch.Case if={!petEvaluationList?.length}>
              <Text variant="small" className="moe-my-xl moe-text-secondary moe-text-center">
                No active evaluation service available, please configure at
                <Link
                  variant="small"
                  href={PATH_SERVICE_SETTING.build({ panel: ServicesNav.Evaluation })}
                  target="_blank"
                  className="moe-ml-[2px] moe-text-secondary"
                >
                  {'Settings > Services > Evaluation'}
                </Link>
              </Text>
            </Switch.Case>
            <Switch.Case else>
              <PetEvaluationTable petEvaluationList={petEvaluationList} afterChangePetEvaluationStatus={run} />
            </Switch.Case>
          </Switch>
        </Spin>
      </EvaluationCardView>
    </Condition>
  );
});
