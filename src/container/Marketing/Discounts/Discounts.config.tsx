import { DiscountCodeStatus, RedeemType } from '@moego/api-web/moego/models/marketing/v1/discount_code_enums';
import React from 'react';
import { WithPricingEnableUpgrade } from '../../../components/Pricing/WithPricingComponents';
import { PATH_DISCOUNTS, PATH_DISCOUNT_CREATE } from '../../../router/paths';
import { DiscountStatusMap } from '../../../store/discount/discount.boxes';
import { createEnum } from '../../../store/utils/createEnum';

export const ApplyToMap = createEnum({
  Everything: ['everything', 'No limitation'],
  LimitToSelected: ['limitToSelected', 'Limit to selected options'],
});

export const LimitToSelectedItemMap = createEnum({
  ServicesAddons: [
    'services',
    {
      title: 'Services & add-ons',
      checkboxFormItemName: 'applyToService',
      inputFormItemName: 'serviceIds',
      placeholder: 'Select service',
    },
  ],
  Products: [
    'products',
    {
      title: 'Products',
      checkboxFormItemName: 'applyToProduct',
      inputFormItemName: 'productIds',
      placeholder: 'Select product',
    },
  ],
});

export const ClientEligibilityMap = createEnum({
  Everyone: ['everyone', 'Everyone'],
  SpecificGroup: ['specificGroup', 'Specific group'],
});

export const DiscountUsageMap = createEnum({
  LimitTotal: [
    1,
    {
      formItemName: 'chooseLimitUsage',
      label: 'Limit number of times this discount can be redeemed in total',
    },
  ],
  LimitToOneUser: [
    2,
    {
      formItemName: 'chooseLimitNumberPerClient',
      label: 'Limit to one use per client',
    },
  ],
});

export const SpecificClientGroupMap = createEnum({
  NewClients: [
    'new clients',
    {
      title: 'New clients (has 0 finished appointment)',
      checkboxFormItemName: 'allowedNewClients',
    },
  ],
  ClientGroups: [
    'clientGroups',
    {
      title: 'Client groups',
      placeholder: 'Select client groups',
      checkboxFormItemName: 'chooseClientsGroup',
      inputFormItemName: 'clientsGroup',
    },
  ],
  SpecificClients: [
    'specificClients',
    {
      title: 'Specific clients',
      placeholder: 'Select clients',
      checkboxFormItemName: 'chooseSpecificClients',
      inputFormItemName: 'clientIds',
    },
  ],
});

export const DiscountControlsMap = createEnum({
  AutoApply: [
    'autoApplyAssociation',
    {
      title: "Automatically apply this discount for new appointments when it's applicable",
      description: 'You will see this discount in appointment detail and invoice.',
    },
  ],
  EnableOB: [
    'enableOnlineBooking',
    {
      title: 'Enable this discount for online booking',
      description: 'Your clients can use this discount code when booking online.',
    },
  ],
});

export const DiscountActionMap = createEnum({
  Activate: [
    1,
    {
      status: DiscountCodeStatus.ACTIVE,
      label: 'Activate',
    },
  ],
  Deactivate: [
    2,
    {
      status: DiscountCodeStatus.INACTIVE,
      label: 'Deactivate',
    },
  ],
  Duplicate: [
    3,
    {
      status: DiscountCodeStatus.UNSPECIFIED,
      label: 'Duplicate',
    },
  ],
  Archive: [
    4,
    {
      status: DiscountCodeStatus.ARCHIVED,
      label: 'Archive',
    },
  ],
  Delete: [
    5,
    {
      status: DiscountCodeStatus.DELETED,
      label: 'Delete',
    },
  ],
});

export const DiscountStatusActionMap = createEnum({
  Active: [
    DiscountStatusMap.Active,
    [DiscountActionMap.Deactivate, DiscountActionMap.Duplicate, DiscountActionMap.Archive, DiscountActionMap.Delete],
  ],
  Inactive: [
    DiscountStatusMap.Inactive,
    [DiscountActionMap.Activate, DiscountActionMap.Duplicate, DiscountActionMap.Archive, DiscountActionMap.Delete],
  ],
  Archived: [DiscountStatusMap.Archived, [DiscountActionMap.Delete]],
});

// delete/archive/deactivated action 需要 CTA
export const DiscountActionsNeedCTA = [
  DiscountActionMap.Deactivate,
  DiscountActionMap.Archive,
  DiscountActionMap.Delete,
];

export const RedeemTypeMap = createEnum({
  Appointment: [RedeemType.APPOINTMENT, { enableClick: true, label: 'Appointment' }],
  OnlineBooking: [RedeemType.ONLINE_BOOKING, { enableClick: false, label: 'Online booking' }],
  ProductSale: [RedeemType.PRODUCT, { enableClick: true, label: 'Product sale' }],
  PackageSale: [RedeemType.PACKAGE, { enableClick: true, label: 'Package sale' }],
});

export const DiscountEmptyStateMap = createEnum({
  DiscountList: [
    1,
    {
      title: 'No discount',
      subTitle: 'Set up discount codes for various scenarios',
      action: 'Create discount',
      path: PATH_DISCOUNT_CREATE.build(),
    },
  ],
  DiscountListNoAuth: [
    2,
    {
      title: (
        <span>
          <WithPricingEnableUpgrade permission="discountCode">
            <span className="moe-text-[#f96b18] moe-cursor-pointer">Upgrade your subscription</span>
          </WithPricingEnableUpgrade>{' '}
          to create discounts for various scenarios!
        </span>
      ),
      subTitle: '',
      action: 'Take a sneak peak',
      path: PATH_DISCOUNT_CREATE.build(),
    },
  ],
  Checkout: [
    3,
    {
      title: 'No available discount',
      subTitle: 'Set up discount codes for various scenarios',
      action: 'Visit discount page',
      path: PATH_DISCOUNTS.build(),
      target: '_blank',
    },
  ],
  CheckoutSearch: [
    4,
    {
      title: 'No results',
      subTitle: "Don't see what you need?",
      action: 'Create a new discount',
      path: PATH_DISCOUNT_CREATE.build(),
    },
  ],
  NewSale: [
    5,
    {
      title: 'No available discount',
      action: 'Create new discount ',
      path: PATH_DISCOUNTS.build(),
      target: '_blank',
    },
  ],
});
