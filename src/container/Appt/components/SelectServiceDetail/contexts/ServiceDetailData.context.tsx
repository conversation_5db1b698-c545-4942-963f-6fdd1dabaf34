import React, { createContext, useContext } from 'react';
import { type PetServiceListWithMainService, type ApptPetIdsServiceList } from '../../../store/appt.types';

export interface ServiceDetailData {
  additionalPetServiceList: ApptPetIdsServiceList;
  petServiceListWithMain: PetServiceListWithMainService[];
  stayNights: number;
  onRemoveServiceDetail: (id: string) => void;
}

export const ServiceDetailDataContext = createContext<ServiceDetailData>({
  additionalPetServiceList: [],
  petServiceListWithMain: [],
  stayNights: -1,
  onRemoveServiceDetail: () => {},
});

export const ServiceDetailDataProvider: React.FC<{ value: ServiceDetailData }> = ({ value, children }) => {
  return <ServiceDetailDataContext.Provider value={value}>{children}</ServiceDetailDataContext.Provider>;
};

export const useServiceDetailDataContext = () => {
  return useContext(ServiceDetailDataContext);
};
