import { Heading, Text } from '@moego/ui';
import { useSelector } from 'amos';
import React, { memo } from 'react';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { printFullName } from '../../../../../store/customer/customer.boxes';
import { ClientAvatar } from '../../../../Client/ClientList/components/ClientTable/Columns/ClientAvatar';
import { type CustomerDetailType } from '../type';

interface ClientInfoProps {
  customer: CustomerDetailType;
}

export const ClientInfo = memo<ClientInfoProps>(({ customer }) => {
  const [business] = useSelector(selectCurrentBusiness);

  const { phoneNumber, lastName, firstName } = customer;

  const name = printFullName(firstName, lastName);
  const phone = business.formatPhoneNumber(phoneNumber);

  return (
    <div className="moe-flex moe-items-center moe-gap-s">
      <ClientAvatar client={customer} size="s" />
      <div className="moe-flex moe-flex-col moe-gap-[4px]">
        <Heading size={5}>{name}</Heading>
        <Text variant="regular" className="moe-text-tertiary">
          {phone}
        </Text>
      </div>
    </div>
  );
});
