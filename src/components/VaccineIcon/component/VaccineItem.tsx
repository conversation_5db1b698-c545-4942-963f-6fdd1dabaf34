import { Button, Heading, cn } from '@moego/ui';
import { useSelector } from 'amos';
import React, { type FC, useMemo } from 'react';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { type VaccineExpiryInfo } from '../../../store/pet/petVaccineBinding.selectors';
import { useVaccineFormModal } from '../../AddClientWithPets/components/VaccineFormModal/useVaccineFormModal';
import { getVaccineTag } from '../../../store/pet/petVaccine.utils';
import { VaccineStatusText } from './VaccineStatusText';

interface VaccineItemProps {
  petId: number;
  vaccineInfo: VaccineExpiryInfo;
}
export const VaccineItem: FC<VaccineItemProps> = ({ petId, vaccineInfo }) => {
  const { vaccineName, expirationDate, isMissing, hasExpired, willExpired, willExpireIn30Days, vaccineId } =
    vaccineInfo;
  const [business] = useSelector(selectCurrentBusiness);
  const vaccineTag = getVaccineTag(vaccineInfo);
  const openVaccineFormModal = useVaccineFormModal();
  const shouldShowUpdateButton = !expirationDate || isMissing || hasExpired || willExpired || willExpireIn30Days;
  const expirationDateString = useMemo(
    () => (expirationDate ? business.formatDate(expirationDate) : 'N/A'),
    [business, expirationDate],
  );
  return (
    <div
      className={cn('moe-flex moe-flex-row moe-items-center moe-h-[32px] moe-justify-between', {
        'moe-w-[350px]': shouldShowUpdateButton,
      })}
    >
      <div className="moe-flex moe-gap-x-[8px]">
        <Heading size="6">{vaccineName}</Heading>
        <VaccineStatusText
          isMissing={isMissing}
          hasExpired={hasExpired}
          willExpired={willExpired}
          willExpireIn30Days={willExpireIn30Days}
          expirationDateString={expirationDateString}
        />
      </div>
      {shouldShowUpdateButton ? (
        <Button
          variant="tertiary"
          size="s"
          onPress={() =>
            openVaccineFormModal({
              petId,
              vaccineId,
              tagText: vaccineTag.tagText,
              vaccineBindingId: vaccineInfo.bindingId,
            })
          }
        >
          Update
        </Button>
      ) : null}
    </div>
  );
};
