import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Text, cn } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React, { memo, useMemo } from 'react';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { serviceMapBox } from '../../../../store/service/service.boxes';
import { Condition } from '../../../Condition';
import { type RenderService } from '../../../PetAndServicePicker/types/types';
import { TagServiceDuration } from '../TagService/TagServiceDuration';
import { TagServicePrice } from '../TagService/TagServicePrice';
import { ServiceAddOnDateTime } from './ServiceAddOnDateTime';
import { GroomingServiceStaff } from './GroomingServiceStaff';

export interface PetAddOnItemProps {
  className?: string;
  mainServiceItemType?: ServiceItemType;
  service: RenderService;
}

export const PetAddOnItem = memo<PetAddOnItemProps>(function PetAddOnItem(props) {
  const { className, service, mainServiceItemType } = props;
  const {
    serviceTime,
    operationList,
    serviceName,
    servicePrice,
    startDate,
    startTime = 0,
    staffId,
    priceOverrideType,
    durationOverrideType,
    quantityPerDay,
    serviceId,
    serviceItemType,
  } = service;
  const [business, { requireDedicatedStaff }] = useSelector(
    selectCurrentBusiness(),
    serviceMapBox.mustGetItem(serviceId),
  );
  const formattedPrice = business.formatAmount(servicePrice);
  const isMultiStaff = operationList?.length > 1;
  const staffIds = isMultiStaff ? operationList.map((op) => op.staffId) : [staffId];
  const isGrooming = serviceItemType === ServiceItemType.GROOMING;

  const order = useMemo(() => {
    return mainServiceItemType === ServiceItemType.GROOMING ? dayjs(startDate).setMinutes(startTime).unix() || 0 : 0;
  }, [mainServiceItemType, startDate, startTime]);

  // 即使是addon，我们也保持对传入数据进行判断
  // 不在乎是否是require staff。如果有startDate，我们就显示时间。有staffId就显示staff

  return (
    <div className={cn('moe-flex', className)} style={{ order }}>
      <div className="moe-flex-1 moe-min-w-0 moe-flex moe-flex-col moe-items-stretch moe-gap-y-[4px]">
        <div className="moe-text-[16px] moe-leading-[20px] moe-text-[#333] moe-font-bold">{serviceName}</div>
        <ServiceAddOnDateTime service={service} mainServiceItemType={mainServiceItemType} />
        <div className="moe-flex moe-items-center moe-gap-x-[8px] moe-text-[14px] moe-text-secondary moe-leading-[20px] moe-font-normal">
          <TagServicePrice
            hiddenIcon
            className="moe-flex-shrink-0"
            price={formattedPrice}
            overrideType={priceOverrideType}
          />
          <TagServiceDuration
            hiddenIcon
            className="moe-flex-shrink-0"
            duration={serviceTime}
            overrideType={durationOverrideType}
          />
          <Condition if={!requireDedicatedStaff && quantityPerDay && !isGrooming}>
            <Text variant="small" className="moe-text-secondary ">
              x{quantityPerDay} per day
            </Text>
          </Condition>
        </div>
      </div>
      <Condition if={staffIds.length}>
        <GroomingServiceStaff staffIds={staffIds} />
      </Condition>
    </div>
  );
});
