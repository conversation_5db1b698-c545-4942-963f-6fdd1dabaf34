import { Form, type useForm } from '@moego/ui';
import dayjs from 'dayjs';
import React, { memo, useEffect, useMemo } from 'react';
import { SelectRoom } from '../../../../../Appt/components/SelectServiceDetail/components/SelectRoom/SelectRoom';
import { type InformationRequireFormRecord, type LodgingItemsDetail } from './InformationRequireModal.types';
import { KEY_FIELD_DIVIDER } from './InformationRequireModal.utils';

interface SelectRoomFormProps {
  itemKey: string;
  serviceDetail: LodgingItemsDetail;
  petId: string;
  defaultValue?: string;
  form: ReturnType<typeof useForm<InformationRequireFormRecord>>;
}

export const RoomFiledPrefix = 'lodgingUnitId';

export const SelectRoomForm = memo<SelectRoomFormProps>((props) => {
  const { serviceDetail, itemKey, petId, defaultValue, form } = props;
  const fullKey = useMemo(() => [RoomFiledPrefix, itemKey].join(KEY_FIELD_DIVIDER), [itemKey]);

  useEffect(() => {
    if (defaultValue) {
      form.setValue(fullKey, defaultValue);
    }
  }, [defaultValue]);

  const selectRoomFormRange = useMemo(
    () => ({
      startDate: serviceDetail.startDate ? dayjs(serviceDetail.startDate) : undefined,
      endDate: serviceDetail.endDate ? dayjs(serviceDetail.endDate) : undefined,
    }),
    [serviceDetail.startDate, serviceDetail.endDate],
  );

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-s">
      <Form.Item
        key={fullKey}
        name={fullKey}
        label={'Lodging allocation'}
        rules={{
          required: 'Please select a room',
        }}
      >
        <SelectRoom range={selectRoomFormRange} isRequired serviceId={serviceDetail.serviceId} petId={petId} />
      </Form.Item>
    </div>
  );
});
