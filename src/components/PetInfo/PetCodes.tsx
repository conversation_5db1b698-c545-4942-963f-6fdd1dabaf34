import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import React from 'react';
import { Condition } from '../Condition';
import { VaccineIcon } from '../VaccineIcon/VaccineIcon';
import { PetCodeTags } from './PetCodeTags';
import { PetIncident } from './PetIncident';
import { PetInfoRender } from './PetInfoRender';
import { PetNotes } from './PetNotes';

export interface PetCodesProps {
  petId: number;
  go2PetNotes?: (petId: string) => void;
  onClick?: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
  showVaccine?: boolean;
  showNotes?: boolean;
  showIncident?: boolean;
  startDate?: string;
  endDate?: string;
  /**
   * 最多展示几个出现省略号
   * @default 3
   */
  petCodeTagsMax?: number;
  disabled?: boolean;
  appointmentId?: number;
  serviceItemTypes?: ServiceItemType[];
}

/**
 * 调用这个组件的地方，说明 pet codes 等数据已经加载过了
 */
export function PetCodes(props: PetCodesProps) {
  const {
    petId,
    go2PetNotes,
    onClick,
    showVaccine = true,
    showNotes = true,
    showIncident = false,
    startDate,
    endDate,
    petCodeTagsMax = 3,
    serviceItemTypes,
    appointmentId,
  } = props;
  return (
    <PetInfoRender petId={petId}>
      {({ codes, notes }) => {
        const noCodes = codes.length === 0;
        const noEmptyNotes = notes.filter((note) => !!note.note.trim());
        const noNotes = noEmptyNotes.length === 0;

        const showNotesFlag = showNotes && !noNotes;
        const showIncidentFlag = showIncident;

        if (noCodes && noNotes && !showIncidentFlag) {
          return null;
        }

        return (
          <>
            <Condition if={showVaccine || showNotesFlag || showIncidentFlag}>
              <div className="moe-flex moe-items-center moe-gap-x-xs empty:moe-hidden" onClick={onClick}>
                <Condition if={showIncidentFlag}>
                  <PetIncident petId={petId} />
                </Condition>
                <Condition if={showVaccine}>
                  <VaccineIcon
                    petId={petId}
                    appointmentId={appointmentId}
                    appointmentDate={endDate ?? startDate}
                    serviceItemTypes={serviceItemTypes}
                  />
                </Condition>
                <Condition if={showNotesFlag}>
                  <PetNotes notes={noEmptyNotes} petId={petId} go2PetNotes={go2PetNotes} />
                </Condition>
              </div>
            </Condition>
            <div className="moe-flex moe-items-center moe-gap-x-xs" onClick={onClick}>
              <Condition if={!noCodes}>
                <PetCodeTags className="moe-flex-nowrap" petId={petId} codes={codes} max={petCodeTagsMax} tip />
              </Condition>
            </div>
          </>
        );
      }}
    </PetInfoRender>
  );
}
