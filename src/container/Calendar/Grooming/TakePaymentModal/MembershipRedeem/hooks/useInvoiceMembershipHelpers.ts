import { useStore } from 'amos';
import { useCallback } from 'react';
import { selectGroomingTicketInvoice } from '../../../../../../store/grooming/grooming.selectors';
import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { GrowthBookFeatureList } from '../../../../../../utils/growthBook/growthBook.config';

export function useInvoiceMembershipHelpers() {
  const store = useStore();
  // redeem membership 暂不对 staff 权限做控制
  const enableMembership = useFeatureIsOn(GrowthBookFeatureList.EnableMembership);

  const membershipApplied2Invoice = useCallback(
    (invoiceId: number, membershipId: string) => {
      const invoice = store.select(selectGroomingTicketInvoice(invoiceId));
      const isApplied = !!invoice.membershipList?.some((item) => String(item.id) === membershipId);
      return isApplied;
    },
    [store],
  );

  return {
    membershipApplied2Invoice,
    isMembershipEnabled: enableMembership,
  };
}
