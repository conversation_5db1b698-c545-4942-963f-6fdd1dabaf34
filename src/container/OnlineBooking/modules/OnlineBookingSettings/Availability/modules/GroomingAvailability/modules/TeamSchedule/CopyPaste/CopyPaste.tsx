import { MinorCopyOutlined } from '@moego/icons-react';
import { Button, cn } from '@moego/ui';
import { Checkbox, Divider, Popover, Row } from 'antd';
import React, { useState } from 'react';
import { dayOfWeeks } from '../../../../../../../../../../store/business/business.boxes';
import {
  type FullWeekDay,
  FullWeekDayList,
} from '../../../../../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { useSerialCallback } from '../../../../../../../../../../utils/hooks/useSerialCallback';
import { CheckboxRow, WeekdaysContainerStyle, WorkdaysPopoverContent } from './CopyPaste.style';

interface CopyPasteProps {
  className?: string;
  disabled?: boolean;
  onApply?: (days: FullWeekDay[]) => Promise<void>;
  day: FullWeekDay;
  classNameIcon?: string;
  title?: React.ReactNode;
}

export const CopyPaste = ({ onApply, day, title, disabled, className }: CopyPasteProps) => {
  const [days, setDays] = useState(FullWeekDayList);
  const [visible, setVisible] = useState(false);

  const checkedAll = days.length === FullWeekDayList.length;
  const handleApply = useSerialCallback(() => onApply?.(days).then(() => setVisible(false)));

  return (
    <div className={cn(className, 'moe-flex moe-items-center')}>
      <WeekdaysContainerStyle />
      <Popover
        arrowPointAtCenter
        placement="bottomLeft"
        trigger="click"
        onVisibleChange={(visible) => {
          if (disabled) return;
          if (visible) setDays(FullWeekDayList);
          setVisible(visible);
        }}
        visible={disabled ? false : visible}
        overlayClassName="weekdays-popover-container"
        overlayStyle={{ width: 200 }}
        content={
          <WorkdaysPopoverContent>
            {title && <Row style={{ paddingLeft: 16, color: '#999999' }}>{title}</Row>}
            {dayOfWeeks.sundayFirst.map((d) => {
              const weekday = FullWeekDayList[d];
              return (
                <CheckboxRow align="middle" key={weekday}>
                  <Checkbox
                    key={weekday}
                    disabled={day === weekday}
                    className={day === weekday ? 'disable-checked-checkbox' : ''}
                    checked={days.includes(weekday)}
                    onChange={() => {
                      setDays(() => {
                        const index = days.indexOf(weekday);
                        const nextDays = days.slice();
                        if (index === -1) {
                          nextDays.push(weekday);
                          return nextDays;
                        } else {
                          nextDays.splice(index, 1);
                          return nextDays;
                        }
                      });
                    }}
                  >
                    {weekday}
                  </Checkbox>
                </CheckboxRow>
              );
            })}
            <CheckboxRow align="middle">
              <Checkbox
                checked={checkedAll}
                onChange={(e) => {
                  setDays(() => {
                    if (e.target.checked) {
                      return FullWeekDayList.slice();
                    } else {
                      const nextDays = [];
                      nextDays.push(day);
                      return nextDays;
                    }
                  });
                }}
              >
                All days
              </Checkbox>
            </CheckboxRow>
            <Divider style={{ marginTop: 0, marginBottom: 12 }} />
            <Row justify="center" align="middle">
              <Button isDisabled={disabled} size="s" onPress={handleApply} isLoading={handleApply.isBusy()}>
                Apply
              </Button>
            </Row>
          </WorkdaysPopoverContent>
        }
      >
        <MinorCopyOutlined
          className={cn('moe-text-primary moe-w-[15px] moe-h-[15px] moe-cursor-pointer', {
            'moe-opacity-50 moe-cursor-not-allowed': disabled,
          })}
        />
      </Popover>
    </div>
  );
};
