import { useDispatch, useSelector } from 'amos';
import classNames from 'classnames';
import React, { forwardRef, memo, useEffect, useImperativeHandle, useMemo } from 'react';
import { useSetState, useUnmount } from 'react-use';
import { alertApi } from '../../../../components/Alert/AlertApi';
import { Loading } from '../../../../components/Loading/Loading';
import { PATH_MARKETING_CAMPAIGN_PANEL } from '../../../../router/paths';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { selectEmailBilling } from '../../../../store/company/company.selectors';
import { ClientFilterListSourceMap } from '../../../../store/customer/clientFilters.boxes';
import { selectClientFiltersList } from '../../../../store/customer/clientFilters.selectors';
import { getCustomerList } from '../../../../store/customer/customer.actions';
import { selectBusinessCustomers } from '../../../../store/customer/customer.selectors';
import {
  calculateEmailCampaignCredit,
  getEmailCampaignAvailableCount,
} from '../../../../store/marketingCampaign/emailCampaign.action';
import { isNormal } from '../../../../store/utils/identifier';
import { useRouteQuery, useRouteState } from '../../../../utils/RoutePath';
import { useBool } from '../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import {
  AllFilterList,
  abandonedPropertyList,
  leadPropertyList,
} from '../../../Client/ClientList/componentsLegacy/ClientFilters/AllFilterList';
import { FilterPropertyConfigMap } from '../../../Client/ClientList/componentsLegacy/ClientFilters/ClientFilters.config';
import {
  ClientFiltersContext,
  useClientFilterListParams,
} from '../../../Client/ClientList/componentsLegacy/ClientFilters/ClientFilters.hooks';
import { useEmailClientListFilters, useEmailClientListKeyword } from '../hooks/useEmailClientListFilters';
import { EmailCampaignRecipientListWithFilter } from './EmailCampaignRecipientListWithFilter';
import { MoreFilterView } from './EmailCampaignSelectRecipient.style';
import { PurchaseEmailCreditModal } from './PurchaseCredit';
import { useFeatureIsOn } from '@growthbook/growthbook-react';
import { GrowthBookFeatureList } from '../../../../utils/growthBook/growthBook.config';

export interface EmailCampaignSelectRecipientRef {
  checkRecipientAndCredit: () => { validate: boolean; autoReloadTimes?: number };
}

export interface EmailCampaignSelectRecipientProps {
  templateId: string;
  recipientLoading: boolean;
  setRecipientLoading: (v: boolean) => void;
}

/** 一次买多少 credit */
export const BASE_CREDIT_NUM = 10;

const { MassEmail } = ClientFilterListSourceMap;

export const EmailCampaignSelectRecipient = memo(
  forwardRef<EmailCampaignSelectRecipientRef, EmailCampaignSelectRecipientProps>(
    ({ templateId, recipientLoading, setRecipientLoading }, ref) => {
      const { needAbandoned } = useRouteQuery(PATH_MARKETING_CAMPAIGN_PANEL);
      const showFilters = useBool(false);
      const dispatch = useDispatch();
      const [countInfo, setCountInfo] = useSetState({
        availableEmailCredit: 0,
        /** 本次send，需要发送的credit */
        usedEmailCredit: 0,
      });
      const [clientFiltersList, customerList, business, emailBilling] = useSelector(
        selectClientFiltersList(MassEmail),
        selectBusinessCustomers(MassEmail),
        selectCurrentBusiness,
        selectEmailBilling,
      );
      const purchaseVisible = useBool();
      const isAutoReload = emailBilling.autoReload > 0;
      const customerListKeyword = useEmailClientListKeyword();
      const filterListParams = useClientFilterListParams(MassEmail);
      const routeState = useRouteState(PATH_MARKETING_CAMPAIGN_PANEL);
      const { hasExtraFilter } = useEmailClientListFilters(templateId, routeState?.customerType);
      const enableLeadManagement = useFeatureIsOn(GrowthBookFeatureList.EnableLeadManagement);
      const extraPropertyFilterList = useMemo(() => {
        const extra = needAbandoned ? abandonedPropertyList : [];
        if (enableLeadManagement) {
          extra.unshift(...leadPropertyList);
        }
        return extra;
      }, [enableLeadManagement, needAbandoned]);

      const handleFilterChange = async () => {
        setRecipientLoading(true);
        await dispatch(
          getCustomerList({
            source: MassEmail,
            filters: filterListParams,
            pageSize: 20,
            queries: {
              keyword: customerListKeyword,
            },
            clear: true,
            pageNum: 1,
          }),
        );
        await handleCalculateEmailCampaignCredit();
      };

      const handleCalculateEmailCampaignCredit = useSerialCallback(async () => {
        setRecipientLoading(true);
        if (customerList.total === 0) {
          setCountInfo({
            usedEmailCredit: 0,
          });
          setRecipientLoading(false);
          return;
        }
        const res = await dispatch(calculateEmailCampaignCredit(customerList.total));
        setCountInfo({
          usedEmailCredit: res,
        });
        handleGetAvailableCount();
      });
      // 是否需要额外购买credit
      const needBuyCredit = countInfo.usedEmailCredit > countInfo.availableEmailCredit;
      // 需要额外购买多少credit
      const extraCreditNum = needBuyCredit ? countInfo.usedEmailCredit - countInfo.availableEmailCredit : 0;
      // 是否自动购买
      const enableAutoBuyCredit = isAutoReload && needBuyCredit;
      // 需要额外购买多少次，因为一次只能购买10个credit
      const autoReloadTimes = useMemo(() => Math.ceil(extraCreditNum / BASE_CREDIT_NUM), [extraCreditNum]);
      // autoreload开启的问题
      const autoReloadTxt = enableAutoBuyCredit
        ? `, will auto reload ${autoReloadTimes * BASE_CREDIT_NUM} credits`
        : '';
      // 没有开启autoreload，credit不足需要提示不能购买
      const isNotSufficientCredit = recipientLoading ? false : enableAutoBuyCredit ? false : needBuyCredit;

      // default filter
      useEffect(() => {
        if (isNormal(business.id)) {
          handleFilterChange();
        }
      }, [business.id]);

      useEffect(() => {
        handleCalculateEmailCampaignCredit();
      }, [customerList.total]);

      const handleGetAvailableCount = async () => {
        const res = await dispatch(getEmailCampaignAvailableCount());
        setCountInfo({
          availableEmailCredit: parseInt(res.availableEmails),
        });
        setRecipientLoading(false);
      };

      const handleCheckRecipientAndCredit = useLatestCallback(() => {
        if (customerList.total === 0) {
          alertApi.warn('No recipient found. Please choose another filter.');
          return { validate: false };
        }
        if (isNotSufficientCredit) {
          alertApi.warn('The credit on your account is not sufficient to use for this campaign.');
          return { validate: false };
        }
        if (enableAutoBuyCredit) {
          return { validate: true, autoReloadTimes };
        }
        return { validate: true };
      });

      useImperativeHandle(ref, () => ({
        checkRecipientAndCredit: handleCheckRecipientAndCredit,
      }));

      useUnmount(() => {
        setRecipientLoading(false);
      });

      return (
        <>
          <div className="!moe-w-[100%] !moe-flex !moe-flex-col !moe-items-center !moe-bg-[#fff] !moe-py-[40px] !moe-rounded-[8px] !moe-self-stretch !moe-min-h-[calc(100%-82px)]">
            <div className="!moe-w-[700px]">
              <div className="!moe-text-[20px] !moe-text-[#333] !moe-mb-[4px] !moe-font-bold">Select recipients</div>
              <div className="!moe-text-[16px] !moe-text-[#666] !moe-mb-[32px]">
                Choose who to send this email campaign to.
              </div>
              <Loading loading={recipientLoading}>
                <div
                  className={`!moe-p-[16px] !moe-flex !moe-items-center !moe-rounded-[8px]  ${
                    isNotSufficientCredit ? '!moe-bg-[#FEF0E8] !moe-mb-[4px]' : '!moe-bg-[#FFF7F0]'
                  }`}
                >
                  <div>Recipient count:</div>
                  <div className="!moe-font-bold !moe-mr-[24px] !moe-ml-[8px]">{customerList.total}</div>
                  <div>Campaign credits used:</div>
                  <div className="!moe-font-bold !moe-ml-[8px]">{countInfo.usedEmailCredit}</div>
                  {enableAutoBuyCredit ? (
                    <div>&nbsp;{`(${countInfo.availableEmailCredit} available${autoReloadTxt})`}</div>
                  ) : (
                    <div>
                      &nbsp;(
                      <span
                        className={classNames(isNotSufficientCredit ? '!moe-text-[#d0021b]' : '')}
                      >{`${countInfo.availableEmailCredit} available`}</span>
                      )
                    </div>
                  )}
                  {isNotSufficientCredit && (
                    <div
                      className="!moe-ml-auto !moe-text-brand !moe-text-sm !moe-font-bold !moe-cursor-pointer hover:!moe-text-brand-hover"
                      onClick={purchaseVisible.open}
                    >
                      Purchase
                    </div>
                  )}
                </div>
              </Loading>
              <ClientFiltersContext.Provider
                value={{
                  source: MassEmail,
                  readonlyFilterList: [FilterPropertyConfigMap.EmailCnt],
                  extraPropertyFilterList: extraPropertyFilterList,
                }}
              >
                <EmailCampaignRecipientListWithFilter
                  hasExtraFilter={hasExtraFilter}
                  customerListTotal={customerList.total}
                  clientFiltersList={clientFiltersList}
                  showFilters={showFilters.value}
                  toggleFilters={showFilters.toggle}
                />
              </ClientFiltersContext.Provider>
            </div>
          </div>
          <MoreFilterView visible={showFilters.value}>
            <div className="!moe-w-[320px] !moe-h-[calc(100vh-260px)] !moe-overflow-hidden !moe-flex !moe-flex-col">
              <div className="!moe-text-[#333] !moe-font-bold !moe-text-[18px] !moe-px-[24px] !moe-flex !moe-items-center !moe-border-0 !moe-border-solid !moe-border-b-[1px] !moe-border-b-[#E6E6E6] !moe-h-[52px]">
                All filters
              </div>
              <AllFilterList
                source={MassEmail}
                filtersMaxCount={hasExtraFilter ? 8 : 7}
                onFilterChange={handleFilterChange}
                readonlyFilterList={[FilterPropertyConfigMap.EmailCnt]}
                extraPropertyFilterList={extraPropertyFilterList}
              />
            </div>
          </MoreFilterView>
          <PurchaseEmailCreditModal
            visible={purchaseVisible.value}
            onClose={purchaseVisible.close}
            onPurchaseSuccess={() => {
              purchaseVisible.close();
              handleGetAvailableCount();
            }}
          />
        </>
      );
    },
  ),
);
