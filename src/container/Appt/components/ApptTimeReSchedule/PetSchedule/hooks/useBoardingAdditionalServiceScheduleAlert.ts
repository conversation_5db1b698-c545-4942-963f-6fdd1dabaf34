import { useMemo } from 'react';
import { serviceMapBox } from '../../../../../../store/service/service.boxes';
import { type ServiceDateInfo } from '../../../../store/appt.types';
import { isAdditionalServiceRuleHit } from '../../../SelectServiceDetail/components/AdditionalService/AdditionalService.utils';
import { PriceUnitEnum } from '../../../../../../components/ServiceApplicablePicker/utils/priceUnit';
import dayjs from 'dayjs';
import { useSelector, useStore } from 'amos';

export interface BoardingAdditionalServiceScheduleAlertParams {
  ownId: string;
  serviceId: string;
  initDates: ServiceDateInfo[];
  draftDates: ServiceDateInfo[];
}

/**
 * 只看某个 startDate endDate 是否 hit rule
 */
export const useCheckSingleDateRangeMultiServiceScheduleRuleHit = (params: {
  serviceIds: number[];
  startDate?: string;
  endDate?: string;
}) => {
  const store = useStore();
  const { serviceIds, startDate, endDate } = params || {};
  if (!serviceIds.length || !startDate || !endDate) {
    return false;
  }

  const checkSingleService = (serviceId: number) => {
    const service = store.select(serviceMapBox.mustGetItem(serviceId));
    const { priceUnit, additionalServiceRule } = service;
    if (!additionalServiceRule) {
      return false;
    }

    const nights = dayjs(endDate).diff(startDate, 'day');
    const stayLength = priceUnit === PriceUnitEnum.PER_DAY ? nights + 1 : nights;
    const isRuleHit = isAdditionalServiceRuleHit(additionalServiceRule, stayLength);
    return isRuleHit;
  };

  const someServiceHitRule = serviceIds.some((serviceId) => checkSingleService(serviceId));
  return someServiceHitRule;
};

/**
 * 看 init 和 draft 两个日期段的 startDate endDate 变化后是否 hit rule
 */
export const useAdditionalServiceScheduleRuleHit = (params: {
  serviceId: string;
  initDateInfo?: ServiceDateInfo;
  draftDateInfo?: ServiceDateInfo;
}) => {
  const { serviceId, initDateInfo, draftDateInfo } = params;
  const [service, serviceMap] = useSelector(serviceMapBox.mustGetItem(+serviceId), serviceMapBox);

  const { initRuleHit, draftRuleHit } = useMemo(() => {
    if (!initDateInfo || !draftDateInfo) {
      return {
        initRuleHit: false,
        draftRuleHit: false,
      };
    }
    const { priceUnit, additionalServiceRule } = service;
    const getStayLength = (dateInfo: ServiceDateInfo) => {
      const nights = dayjs(dateInfo.endDate).diff(dateInfo.startDate, 'day');
      return priceUnit === PriceUnitEnum.PER_DAY ? nights + 1 : nights;
    };

    const initRuleHit = isAdditionalServiceRuleHit(additionalServiceRule, getStayLength(initDateInfo));
    const draftRuleHit = isAdditionalServiceRuleHit(additionalServiceRule, getStayLength(draftDateInfo));
    return {
      initRuleHit,
      draftRuleHit,
    };
  }, [draftDateInfo, initDateInfo, service]);

  const alertRuleHit = !initRuleHit && draftRuleHit;

  const additionalServiceRuleList = useMemo(() => {
    const { additionalServiceRule } = service;
    return (
      additionalServiceRule?.applyRules?.map((rule) => ({
        rule,
        service: serviceMap.mustGetItem(+rule.serviceId),
      })) || []
    );
  }, [service, serviceMap]);

  return {
    alertRuleHit,
    additionalServiceRuleList,
  };
};

export const useBoardingAdditionalServiceScheduleAlert = (params: BoardingAdditionalServiceScheduleAlertParams) => {
  const { ownId, serviceId, initDates, draftDates } = params;
  const initDateInfo = initDates.find((i) => i.ownId === ownId);
  const draftDateInfo = draftDates.find((i) => i.ownId === ownId);

  return useAdditionalServiceScheduleRuleHit({
    serviceId,
    initDateInfo,
    draftDateInfo,
  });
};
