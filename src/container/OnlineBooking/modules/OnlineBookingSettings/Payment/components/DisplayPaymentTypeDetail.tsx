import { useSelector } from 'amos';
import classNames from 'classnames';
import React, { useEffect, useMemo, useRef } from 'react';
import { useMeasure } from 'react-use';
import { Condition } from '../../../../../../components/Condition';
import { type BusinessRecord } from '../../../../../../store/business/business.boxes';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import {
  DepositType,
  NoShowProtectType,
  type OnlineBookingPreferenceRecord,
  PrepaymentType,
} from '../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { useBool } from '../../../../../../utils/hooks/useBool';
import { usePaymentType } from './PaymentOptionsContext';
import { Typography } from '@moego/ui';

const getPrepayAmountText = (
  prepayType: number,
  prepayTipEnable: boolean,
  depositType: number,
  depositAmount: number,
  depositPercentage: number,
  business: BusinessRecord,
) => {
  if (prepayType === PrepaymentType.FullPay) {
    return `Full amount${prepayTipEnable ? ' (Tipping Enabled)' : ''}`;
  }
  if (depositType === DepositType.ByPercentage) {
    return `Partial amount - ${depositPercentage}%`;
  }
  return `Partial amount - ${business.formatAmount(depositAmount)}`;
};

interface DisplayPaymentTypeDetailProps {
  paymentSetting:
    | OnlineBookingPreferenceRecord['paymentSetting']
    | OnlineBookingPreferenceRecord['paymentSetting']['certainGroupSetting'];
}

// 三行的高度，按照 line-height 计算。
const MAX_NO_ELLIPSIS_HEIGHT = 54;

export const DisplayPaymentTypeDetail = (props: DisplayPaymentTypeDetailProps) => {
  const { paymentSetting } = props;
  const { paymentType } = usePaymentType();
  const [business] = useSelector(selectCurrentBusiness());
  const [policyRef, { height }] = useMeasure<HTMLDivElement>();
  const ellipsisHandled = useBool();
  const ellipsis = useBool();
  const viewMore = useBool();

  const policy = useMemo(() => {
    if (paymentType === NoShowProtectType.CardOnFile) {
      return paymentSetting?.cancellationPolicy ?? '';
    }
    if (paymentType === NoShowProtectType.Prepayment) {
      return paymentSetting?.prepayPolicy ?? '';
    }
    if (paymentType === NoShowProtectType.PreAuth) {
      return paymentSetting?.preAuthPolicy ?? '';
    }
    return '';
  }, [paymentType, paymentSetting]);

  const heightRef = useRef<number>();

  useEffect(() => {
    heightRef.current = height;
  }, [height]);

  useEffect(() => {
    // On policy text changed, disable ellipsis and re-calculate
    ellipsis.as(false);
    ellipsisHandled.as(false);
  }, [policy]);

  useEffect(() => {
    if (!ellipsisHandled.value && height > MAX_NO_ELLIPSIS_HEIGHT) {
      ellipsisHandled.as(true);
      ellipsis.as(true);
    }
  }, [ellipsisHandled.value, height]);

  // Certain clients 会被初始化为 -1
  if (!paymentSetting || paymentType === NoShowProtectType.Closed || paymentType < 0) {
    return null;
  }

  const {
    prepayType,
    prepayTipEnable,
    depositType,
    depositAmount,
    depositPercentage,
    prepayPolicy,
    preAuthPolicy,
    cancellationPolicy,
  } = paymentSetting;

  const methodConfigsMap: Record<
    number,
    {
      policy: string;
      amountTitle?: string;
      amountText?: string;
    }
  > = {
    [NoShowProtectType.CardOnFile]: {
      policy: cancellationPolicy,
    },
    [NoShowProtectType.Prepayment]: {
      policy: prepayPolicy,
      amountTitle: 'Prepay amount:',
      amountText: getPrepayAmountText(
        prepayType,
        !!prepayTipEnable,
        depositType,
        depositAmount,
        depositPercentage,
        business,
      ),
    },
    [NoShowProtectType.PreAuth]: {
      policy: preAuthPolicy,
    },
  };

  return (
    <div className="moe-p-[16px] moe-bg-neutral-sunken-0 moe-rounded-[8px] moe-text-[16px] moe-leading-[18px] moe-font-medium moe-mt-[12px]">
      <Condition if={methodConfigsMap[paymentType]?.amountTitle}>
        <div className="moe-mb-[12px]">
          <span className="moe-font-[700] moe-text-secondary">{methodConfigsMap[paymentType].amountTitle}</span>
          <span className="moe-ml-[5px] moe-text-tertiary">{methodConfigsMap[paymentType].amountText}</span>
        </div>
      </Condition>
      <Typography.Heading size={5} className="moe-mb-[8px] moe-text-secondary">
        Cancellation Policy:
      </Typography.Heading>
      <div
        ref={policyRef}
        className={classNames(
          'moe-text-tertiary moe-whitespace-pre-line',
          ellipsisHandled.value && ellipsis.value && !viewMore.value && 'moe-line-clamp-3',
        )}
      >
        {policy}
      </div>
      <Condition if={ellipsisHandled.value && ellipsis.value}>
        <div
          className="moe-cursor-pointer moe-text-[14px] moe-leading-[18px] moe-font-medium moe-text-brand"
          onClick={viewMore.toggle}
        >
          {viewMore.value ? 'View less' : 'View more'}
        </div>
      </Condition>
    </div>
  );
};
