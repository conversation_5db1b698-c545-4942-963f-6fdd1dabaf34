/*
 * <AUTHOR> <<EMAIL>>
 * @since 2020-08-18 20:51:50
 */

import { Space } from 'antd';
import styled from 'styled-components';
import { c_danger, c_secondary, c_warning } from '../../style/_variables';

export const Line = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
`;

export const FullLine = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
`;

export const OneLine = styled.span`
  white-space: pre;
  overflow: hidden;
  text-overflow: ellipsis;
`;

export const Full = styled.div<{ minWidth?: number }>`
  flex: 1;
  min-width: ${(props) => props.minWidth ?? 0}px;
`;

export const BlockSpace = styled(Space)`
  display: flex;
`;

export const RightFirst = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow: hidden;

  > * {
    overflow: hidden;
    flex-shrink: 1;
  }

  > :last-child {
    flex-shrink: 0;
    margin-left: 12px;
  }
`;

export const AlignRight = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
`;

export const FormFooter = styled.div<{ align?: 'flex-start' | 'center' | 'flex-end' | 'space-between' }>`
  margin-top: 30px;
  display: flex;
  flex: 1;
  flex-direction: row;
  justify-content: ${(props) => props.align || 'flex-end'};
  align-items: center;
`;

export const AlignCenter = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
`;

export const AlignJustify = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;

export interface FillProps {
  width?: number;
  height?: number;
  display?: 'inline-block' | 'flex' | 'block';
}

export const Fill = styled.div<FillProps>`
  min-width: ${(props) => props.width || 0}px;
  min-height: ${(props) => props.height || 0}px;
  display: ${(props) => (props.display ? props.display : props.width ? 'inline-block' : 'block')};
`;

export const TextAnchor = styled.a`
  color: inherit;

  &:hover {
    color: inherit;
  }
`;

export const LinkAnchor = styled.a`
  color: #5e72e4;

  &:hover {
    color: #5e72e4;
  }
`;

export type ColorTheme = 'primary' | 'secondary' | 'warning' | 'danger';

const colorThemes: Record<string, string> = {
  primary: 'var(--moe-color-bg-brand-bold)',
  secondary: c_secondary,
  warning: c_warning,
  danger: c_danger,
};

export interface FontProps {
  color?: ColorTheme | string;
  weight?: 'light' | 'normal' | '600' | 'bold';
  size?: string;
}

export const Font = styled.span<FontProps>`
  color: ${(props) => (props.color ? (colorThemes[props.color] ?? props.color) : 'inherit')};
  font-weight: ${(props) => props.weight ?? 'normal'};
  font-size: ${(props) => props.size ?? 'inherit'};
  ${(props) => (props.onClick ? 'cursor: pointer' : '')};
`;

export interface DotProps {
  color?: string;
  size?: number;
}

export const Dot = styled.span<DotProps>`
  display: inline-block;
  border-radius: 50%;
  background-color: ${(props) => props.color || 'var(--moe-color-bg-brand-bold)'};
  min-width: ${(props) => props.size || 12}px;
  min-height: ${(props) => props.size || 12}px;
  align-self: center;
  vertical-align: center;
  &:not(:last-child) {
    margin-right: ${(props) => props.size || 12}px;
  }
`;
