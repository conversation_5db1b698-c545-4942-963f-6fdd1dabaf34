import { Link } from '@moego/ui';
import { useSelector } from 'amos';
import React, { type FC } from 'react';
import { useHistory } from 'react-router';
import { PATH_MEMBERSHIP } from '../../../../../router/paths';
import {
  selectActiveMembershipForClientByCustomerId,
  selectCompanyActiveMembershipListForClient,
} from '../../../../../store/membership/membership.selectors';
import { useSaleMembershipContext } from '../contexts/SaleMembershipContext';
import { MembershipItem } from './MembershipItem';

export interface MembershipListProps {
  className?: string;
}

export const MembershipList: FC<MembershipListProps> = (props) => {
  const { className } = props;
  const history = useHistory();
  const { selectedCustomerId, selectedMembershipId, setSelectedMembershipId } = useSaleMembershipContext();
  const [membershipList, customerMembershipList] = useSelector(
    selectCompanyActiveMembershipListForClient(selectedCustomerId),
    selectActiveMembershipForClientByCustomerId(selectedCustomerId),
  );
  if (membershipList.isEmpty()) {
    return (
      <div className={className}>
        <div className="moe-flex moe-flex-col moe-items-center moe-mt-[70px]">
          <div className="moe-text-primary moe-font-bold moe-text-base">No data</div>
          <div className="moe-text-secondary moe-mt-xs moe-font-normal">You haven&apos;t set up memberships yet.</div>
          <Link
            className="moe-mt-m moe-font-bold"
            onClick={() => {
              history.push(PATH_MEMBERSHIP.build({ type: 'list' }));
            }}
          >
            Add membership
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      {membershipList.toArray().map((membership) => (
        <MembershipItem
          key={membership.id}
          membership={membership}
          disabled={!!selectedMembershipId || !!customerMembershipList.find((item) => item.id === membership.id)}
          isSelected={selectedMembershipId === membership.id}
          onSelect={(id) => {
            setSelectedMembershipId(id);
          }}
        />
      ))}
    </div>
  );
};
