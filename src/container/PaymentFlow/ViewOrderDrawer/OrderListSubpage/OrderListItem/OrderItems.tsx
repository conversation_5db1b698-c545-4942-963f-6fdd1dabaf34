import { PriceDetailModelPriceItemOperator } from '@moego/api-web/moego/models/order/v1/order_line_item_models';
import { DiscountType } from '@moego/api-web/moego/models/order/v1/order_promotion_models';
import { type StrictEnumValues } from '@moego/finance-utils';
import {
  FNK_OrderItemType,
  type IGeneralOrderDetailV2,
  type IRefundOrderDetail,
  RealmType,
  assertModel,
} from '@moego/finance-web-kit';
import {
  MinorChevronDownOutlined,
  MinorChevronUpOutlined,
  MinorMembership,
  MinorPackageOutlined,
} from '@moego/icons-react';
import { Collapse, Text, cn } from '@moego/ui';
import { useSelector } from 'amos';
import React from 'react';
import { Condition } from '../../../../../components/Condition';
import { type FinanceKit } from '../../../../../service/finance-kit';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { useBool } from '../../../../../utils/hooks/useBool';

export const OrderItems = (props: {
  orderDetail: IGeneralOrderDetailV2<typeof FinanceKit.model> | IRefundOrderDetail<typeof FinanceKit.model>;
  items: IGeneralOrderDetailV2<typeof FinanceKit.model>['items'] | IRefundOrderDetail<typeof FinanceKit.model>['items'];
  type: StrictEnumValues<typeof FNK_OrderItemType>;
  className?: string;
}) => {
  const { orderDetail, items, type, className } = props;

  return (
    <div className={cn('moe-relative', className)}>
      <div className="moe-absolute moe-left-0 moe-top-0 moe-h-full moe-border-l-[3px] moe-border-solid moe-border-divider" />
      <div className="moe-flex moe-flex-col">
        {items.map((item) => {
          if (assertModel(orderDetail.order, RealmType.OrderV2)) {
            if (type === FNK_OrderItemType.Deposit || type === FNK_OrderItemType.NoShow) {
              return (
                <SimpleOrderItem
                  className="moe-py-[6px] moe-ml-[12px]"
                  item={item as IGeneralOrderDetailV2<typeof FinanceKit.model>['items'][number]}
                  orderDetail={orderDetail as IGeneralOrderDetailV2<typeof FinanceKit.model>}
                  key={item.id}
                />
              );
            }

            return (
              <NormalOrderItem
                className="moe-py-[6px] moe-ml-[12px]"
                item={item as IGeneralOrderDetailV2<typeof FinanceKit.model>['items'][number]}
                orderDetail={orderDetail as IGeneralOrderDetailV2<typeof FinanceKit.model>}
                key={item.id}
              />
            );
          }

          if (assertModel(orderDetail.order, RealmType.RefundOrder)) {
            return (
              <RefundOrderItem
                className="moe-py-[6px] moe-ml-[12px]"
                item={item as IRefundOrderDetail<typeof FinanceKit.model>['items'][number]}
                orderDetail={orderDetail as IRefundOrderDetail<typeof FinanceKit.model>}
                key={item.id}
              />
            );
          }
          return null;
        })}
      </div>
    </div>
  );
};

const NormalOrderItem = (props: {
  item: IGeneralOrderDetailV2<typeof FinanceKit.model>['items'][number];
  orderDetail: IGeneralOrderDetailV2<typeof FinanceKit.model>;
  className?: string;
}) => {
  const { item, orderDetail, className } = props;
  const { promotions } = orderDetail;
  const [business] = useSelector(selectCurrentBusiness);
  const isExpanded = useBool(false);
  const packages = promotions.filter((promotion) => !!promotion.package);
  const memberships = promotions.filter((promotion) => !!promotion.membership);

  const curItemPackages = packages.filter((pkg) => pkg.items.some((pkgItem) => pkgItem.orderItemId === item.id));
  const curItemMemberships = memberships.filter((membership) =>
    membership.items.some((membershipItem) => membershipItem.orderItemId === item.id),
  );
  const haveDiscountAmount = !item.discountAmount.isZero();

  const havePromotion = curItemPackages.length > 0 || curItemMemberships.length > 0;

  const name = item.name;
  const promotedAmountTxT = business.formatMoney(item.totalAmount);
  const originalAmountTxT = business.formatMoney(item.subTotalAmount);
  const unitPriceTxT = business.formatMoney(item.unitPrice);
  const quantity = item.quantity;
  const priceItems = item.subtotalDetail.priceItems;

  const renderMemberships = () => {
    if (curItemMemberships.length === 0) return null;
    return curItemMemberships.map((membership) => {
      const isRedeemQuantity = membership.discountType === DiscountType.ITEM_DEDUCTION;
      return (
        <div className="moe-flex moe-items-center moe-gap-x-xxs" key={membership.id}>
          <MinorMembership />
          <Text variant="small" className="moe-text-secondary">
            {membership.membership?.name}
            {isRedeemQuantity ? `(${item.purchasedQuantity})` : ''}
          </Text>
        </div>
      );
    });
  };

  const renderPackages = () => {
    if (curItemPackages.length === 0) return null;
    return curItemPackages.map((pkg) => {
      const isRedeemQuantity = pkg.discountType === DiscountType.ITEM_DEDUCTION;
      return (
        <div className="moe-flex moe-items-center moe-gap-x-xxs" key={pkg.id}>
          <MinorPackageOutlined className="moe-text-icon-secondary" />
          <Text variant="small" className="moe-text-secondary">
            {pkg.package?.packageName}
            {isRedeemQuantity ? `(${item.purchasedQuantity})` : ''}
          </Text>
        </div>
      );
    });
  };

  const renderPromotionsInfo = () => {
    if (!havePromotion) return null;
    return (
      <div className="moe-flex moe-flex-col moe-gap-y-xxs moe-mt-xxs">
        {renderMemberships()}
        {renderPackages()}
      </div>
    );
  };

  const renderPriceDetails = () => {
    const showExpandIcon = priceItems.length > 0;
    return (
      <div className="moe-flex moe-flex-col moe-mt-xxs">
        <div
          className={cn('moe-flex moe-items-center', showExpandIcon && 'moe-cursor-pointer')}
          onClick={showExpandIcon ? isExpanded.toggle : undefined}
        >
          <Text variant="small" className="moe-text-tertiary">
            {unitPriceTxT}
            {quantity > 0 ? ` x${quantity}` : ''}
          </Text>
          {showExpandIcon ? (
            isExpanded.value ? (
              <MinorChevronUpOutlined className="moe-text-icon-tertiary" />
            ) : (
              <MinorChevronDownOutlined className="moe-text-icon-tertiary" />
            )
          ) : null}
        </div>
        <Collapse.Transition isExpanded={isExpanded.value} className="moe-mt-xxs">
          <div className="moe-flex moe-flex-col moe-gap-y-xxs moe-bg-neutral-sunken-0 moe-rounded-s moe-mx-[-4px] moe-p-[4px]">
            {priceItems.map((priceItem) => {
              return (
                <div className="moe-flex moe-items-center moe-justify-between moe-text-secondary" key={priceItem.name}>
                  <Text variant="small" className="moe-flex-1 moe-mr-m">
                    {priceItem.name}
                  </Text>
                  <div className="moe-flex moe-items-center">
                    <Text variant="small">
                      {priceItem.operator === PriceDetailModelPriceItemOperator.SUBTRACT ? '-' : ''}
                      {business.formatMoney(priceItem.unitPrice)}
                      <span className="moe-ml-xl">x{priceItem.quantity}</span>
                    </Text>
                    <Text variant="small" className="moe-ml-xl">
                      {business.formatMoney(priceItem.subTotal)}
                    </Text>
                  </div>
                </div>
              );
            })}
          </div>
        </Collapse.Transition>
      </div>
    );
  };

  return (
    <div className={cn('moe-flex moe-flex-col', className)}>
      <div className="moe-flex moe-items-center moe-justify-between">
        <Text variant="regular-short">{name}</Text>
        <div className="moe-flex moe-items-center moe-relative">
          <Text variant="regular-short" className={cn('moe-text-primary')}>
            {promotedAmountTxT}
          </Text>
          <Condition if={haveDiscountAmount}>
            <Text
              variant="small"
              className="moe-line-through moe-absolute moe-translate-y-[100%] moe-text-tertiary moe-whitespace-nowrap moe-right-0 moe-top-[4px]"
            >
              {originalAmountTxT}
            </Text>
          </Condition>
        </div>
      </div>
      {renderPromotionsInfo()}
      {renderPriceDetails()}
    </div>
  );
};

const RefundOrderItem = (props: {
  item: IRefundOrderDetail<typeof FinanceKit.model>['items'][number];
  orderDetail: IRefundOrderDetail<typeof FinanceKit.model>;
  className?: string;
}) => {
  const { item, className } = props;
  const [business] = useSelector(selectCurrentBusiness);

  const name = item.itemName;
  const unitPriceTxT = business.formatAmount(-item.itemUnitPrice.valueOf());
  const quantity = +item.refundQuantity;
  const amountTxT = business.formatAmount(-item.refundTotalAmount.valueOf());

  const renderPriceDetails = () => {
    return (
      <div className="moe-flex moe-flex-col moe-mt-xxs">
        <div className={cn('moe-flex moe-items-center')}>
          <Text variant="small" className="moe-text-tertiary">
            {unitPriceTxT}
            {quantity > 0 ? ` x${quantity}` : ''}
          </Text>
        </div>
      </div>
    );
  };

  return (
    <div className={cn('moe-flex moe-flex-col', className)}>
      <div className="moe-flex moe-items-center moe-justify-between">
        <Text variant="regular-short">{name}</Text>
        <div className="moe-flex moe-items-center moe-relative">
          <Text variant="regular-short" className={cn('moe-text-primary')}>
            {amountTxT}
          </Text>
        </div>
      </div>
      {renderPriceDetails()}
    </div>
  );
};

const SimpleOrderItem = (props: {
  item: IGeneralOrderDetailV2<typeof FinanceKit.model>['items'][number];
  orderDetail: IGeneralOrderDetailV2<typeof FinanceKit.model>;
  className?: string;
}) => {
  const { item, className } = props;
  const [business] = useSelector(selectCurrentBusiness);
  const name = item.name;
  const description = item.description;
  const amountTxT = business.formatAmount(item.subTotalAmount.valueOf());
  return (
    <div className={cn('moe-flex moe-flex-col', className)}>
      <div className="moe-flex moe-items-center moe-justify-between">
        <Text variant="regular-short">{name}</Text>
        <div className="moe-flex moe-items-center moe-relative">
          <Text variant="regular-short" className={cn('moe-text-primary')}>
            {amountTxT}
          </Text>
        </div>
      </div>
      <Condition if={!!description}>
        <Text variant="small" className="moe-text-tertiary moe-mt-xxs">
          {description}
        </Text>
      </Condition>
    </div>
  );
};
