/**
 * @since 2023-07-18
 * <AUTHOR>
 * @description payment history table, stripe, square both use this component
 */

import { WaitListStatus } from '@moego/api-web/moego/models/appointment/v1/wait_list_enums';
import { RefundMode } from '@moego/api-web/moego/models/order/v1/refund_order_enums';
import { <PERSON><PERSON><PERSON> } from '@moego/finance-utils';
import { KitServiceType, useKitService } from '@moego/finance-web-kit';
import { useDispatch, useSelector } from 'amos';
import { Pagination } from 'antd';
import { type SorterResult } from 'antd/lib/table/interface';
import React, { memo, useCallback, useMemo, useState } from 'react';
import { Condition } from '../../../../components/Condition';
import { transSortOrder, usePaginationOptions } from '../../../../components/Table/Table.utils';
import { toastApi } from '../../../../components/Toast/Toast';
import { useCustomerId } from '../../../../router/paths';
import { FinanceKit } from '../../../../service/finance-kit';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { getCustomerPaymentList } from '../../../../store/payment/actions/private/payment.actions';
import {
  type PaymentListFilterRecord,
  type PaymentRecord,
  type PaymentVendor,
  invoiceEditTipBox,
  paymentMapBox,
} from '../../../../store/payment/payment.boxes';
import { type PagedList } from '../../../../store/utils/PagedList';
import { isNormal } from '../../../../store/utils/identifier';
import { useApptDetailDrawerCloseCallback } from '../../../../utils/hooks/useApptDetailDrawerCloseCallback';
import { useBool } from '../../../../utils/hooks/useBool';
import { useLatestCallback } from '../../../../utils/hooks/useLatestCallback';
import { useOpenApptDetailDrawer } from '../../../../utils/hooks/useOpenApptDetailDrawer';
import { useSerialCallback } from '../../../../utils/hooks/useSerialCallback';
import { PaymentActionName } from '../../../../utils/reportData/payment';
import { getAppointment } from '../../../Appt/store/appt.api';
import { TakePaymentModal } from '../../../Calendar/Grooming/TakePaymentModal/TakePaymentModal';
import { useRefundByPaymentModal } from '../../../PaymentFlow/ViewOrderDrawer/RefundByPayment/useRefundByPaymentModal';
import { useInvoiceReinvent } from '../../../PaymentFlow/hooks/useInvoiceReinvent';
import { useInvoiceReinventReport } from '../../../PaymentFlow/hooks/useInvoiceReinvent.report';
import { AppointmentStatus } from '../../../TicketDetail/AppointmentStatus';
import { useWaitDetail } from '../../../WaitDetail/hooks/useWaitDetail';
import { StyledTable } from '../../../settings/components/StyledTable';
import { PaymentIssueRefund } from '../../stripe/PaymentIssueRefund';
import { PaymentReceiptModal } from '../../stripe/PaymentReceipt';
import { RefundMethodModal } from '../RefundMethodModal';
import { StyledTablePaginationView } from './PaginationView.style';
import { PaymentHistoryTableView } from './PaymentHistoryTable.style';
import { PaymentRefundTable } from './PaymentRefundTable';
import { type GetPaymentsParams } from './useBusinessPaymentHistory';
import { usePaymentHistoryColumns } from './usePaymentHistoryColumns';

export interface PaymentTableProps {
  payments: PagedList<PaymentListFilterRecord, any, any>;
  className?: string;
  onChange: (params: GetPaymentsParams) => void;
  vendor?: PaymentVendor;
  source?: 'client_payment_history' | 'card_processing_payment_activity';
}

export const PaymentHistoryTable = memo<PaymentTableProps>((props) => {
  const { vendor, payments, onChange, className, source = 'card_processing_payment_activity' } = props;
  const [business, paymentMap] = useSelector(selectCurrentBusiness(), paymentMapBox);
  const { openApptDetailDrawer } = useOpenApptDetailDrawer();
  const openWaitDetail = useWaitDetail();
  const [receiptItem, setReceiptItem] = useState<PaymentRecord>();
  const [refundItem, setRefundItem] = useState<PaymentRecord>();
  const refundMethodModalVisible = useBool();
  const invoiceVisible = useBool();
  const [paymentRecord, setPaymentRecord] = useState<PaymentRecord>();
  const dispatch = useDispatch();
  const customerId = useCustomerId();
  const { isEnableToNewFlow } = useInvoiceReinvent();
  const reportPaymentData = useInvoiceReinventReport();
  const dataSource = useMemo(() => {
    return payments.getList().map((id: number) => {
      const record = paymentMap.mustGetItem(id);
      return record;
    });
  }, [payments, refundItem]);

  const issueRefundByPayment = useRefundByPaymentModal();
  const { getService: getRefundService } = useKitService(FinanceKit, KitServiceType.Refund);

  const handleRefund = useSerialCallback(async (record: PaymentRecord) => {
    reportPaymentData(PaymentActionName.Refund, {
      orderId: record.invoiceId,
      ctaId: source,
      isNewInvoiceFlow: isEnableToNewFlow,
    });

    if (record.module === 'membership') {
      toastApi.error('Please refer to support chat for membership refund.');
      return;
    }

    if (!record.isRefundable()) {
      toastApi.error('Cannot refund');
      return;
    }

    if (isEnableToNewFlow && isNormal(record.orderPaymentId)) {
      const refundService = await getRefundService();
      try {
        await refundService.previewRefund(
          {
            orderId: String(record.invoiceId),
            sourceOrderPayments: [{ id: String(record.orderPaymentId) }],
            refundMode: RefundMode.BY_PAYMENT,
            refundByPayment: {
              orderPaymentIds: [String(record.orderPaymentId)],
              refundAmountFlags: {
                isConvenienceFeeIncluded: true,
              },
              refundAmount: MoeMoney.fromAmount(record.refundableAmount()),
            },
          },
          {
            autoToast: false,
          },
        );
      } catch (e) {
        return toastApi.error('Item has been refunded: Further payment refunds are not allowed.', 5000);
      }
      return await issueRefundByPayment({
        orderId: String(record.invoiceId),
        orderPaymentId: String(record.orderPaymentId),
        refundableAmount: record.refundableAmount(),
        customerId: record.customerId,
      });
    }

    // product, package 没有groomingId，不做edit invoice支持
    if (!record.groomingId) {
      setRefundItem(record);
      return;
    }

    const res = await dispatch(getAppointment({ appointmentId: record.groomingId.toString() }));

    // cancel不做edit invoice支持
    if (!res || res.appointment.status === AppointmentStatus.CANCELED) {
      setRefundItem(record);
    } else {
      refundMethodModalVisible.open();
    }
    setPaymentRecord(record);
  });

  const handleReceipt = (record: PaymentRecord) => {
    setReceiptItem(record);
    reportPaymentData(PaymentActionName.InvoiceSendReceipt, {
      orderId: record.invoiceId,
      ctaId: source,
      isNewInvoiceFlow: isEnableToNewFlow,
    });
  };

  const columns = usePaymentHistoryColumns({
    business,
    vendor,
    filter: payments.filter,
    handleBookingId: async (record) => {
      const r = await dispatch(getAppointment({ appointmentId: record.groomingId.toString() }));
      // 仅 waitlistOnly 的 appt 打开 waitlist 详情，否则打开 appointment 详情
      // 请求接口虽有一定耗时，但是是可接受的，后续可以优化
      if (r.appointment.waitListStatus === WaitListStatus.WAITLISTONLY) {
        openWaitDetail({
          id: r.waitList.id,
          mask: true,
        });
      } else {
        openApptDetailDrawer({
          ticketId: record.groomingId,
        });
      }
    },
    handleReceipt,
    handleRefund,
  });

  const paginationOptions = usePaginationOptions(payments.total, payments.pageNum, payments.pageSize, {
    shouldShowTotal: true,
  });

  const handleTableChange = (_p: any, _f: any, sorter: SorterResult<PaymentRecord>[] | SorterResult<PaymentRecord>) => {
    const sorterDetail = Array.isArray(sorter) ? sorter[0] : sorter;
    onChange({
      order: transSortOrder(sorterDetail.order),
      pageNum: 1,
    });
  };

  const handleServiceItemClick = () => {
    paymentRecord?.invoiceId &&
      dispatch(
        invoiceEditTipBox.mergeItem(paymentRecord.invoiceId, {
          content: 'Edit invoice to issue service item refunds by adding a discount.',
        }),
      );
    invoiceVisible.open();
  };

  const handlePaymentOnlyClick = () => {
    paymentRecord && setRefundItem(paymentRecord);
  };

  const handlePaymentModalClose = () => {
    invoiceVisible.close();
    refundMethodModalVisible.close();

    paymentRecord?.invoiceId && invoiceEditTipBox.deleteItem(paymentRecord.invoiceId);
    customerId && dispatch(getCustomerPaymentList({ customerId, pageNum: payments.pageNum }));
    vendor === 'stripe' && onChange?.({ pageNum: payments.pageNum });
  };

  const handleBack = () => {
    setRefundItem(void 0);
  };

  const handleRefundMethodClose = () => {
    refundMethodModalVisible.close();
  };

  const handleIssueRefundClose = () => {
    setRefundItem(void 0);
    refundMethodModalVisible.close();
  };

  useApptDetailDrawerCloseCallback(() => {
    onChange({});
  });

  return (
    <>
      <PaymentReceiptModal item={receiptItem} onClose={() => setReceiptItem(void 0)} />
      <Condition if={!!refundItem}>
        <PaymentIssueRefund visible item={refundItem} onClose={handleIssueRefundClose} onBack={handleBack} />
      </Condition>
      <RefundMethodModal
        onClose={handleRefundMethodClose}
        visible={refundMethodModalVisible.value}
        onServiceItemClick={handleServiceItemClick}
        onPaymentOnlyClick={handlePaymentOnlyClick}
      />
      {invoiceVisible.value && !!paymentRecord?.invoiceId && (
        <TakePaymentModal invoiceId={paymentRecord.invoiceId} module="grooming" onClose={handlePaymentModalClose} />
      )}
      <PaymentHistoryTableView className={className}>
        <StyledTable<PaymentRecord>
          tableLayout="auto"
          scroll={{ x: 'max-content', y: '100%' }}
          columns={columns}
          pagination={false}
          loading={payments.isLoading() || handleRefund.isBusy()}
          rowKey={useCallback((record) => record.id, [])}
          dataSource={dataSource}
          onChange={handleTableChange}
          expandable={{
            expandIcon: () => null,
            expandedRowKeys: dataSource.map((s) => s.id),
            rowExpandable: (record) => Array.isArray(record.refunds) && record.refunds.length > 0,
            expandedRowRender: (record) => <PaymentRefundTable record={record} business={business} />,
          }}
        />

        <StyledTablePaginationView className="!moe-justify-end">
          <Pagination
            {...paginationOptions}
            className={'!moe-flex-shrink-0'}
            size="small"
            onChange={useLatestCallback((pageNum: number, pageSize?: number) => {
              onChange({ pageNum, pageSize });
            })}
          />
        </StyledTablePaginationView>
      </PaymentHistoryTableView>
    </>
  );
});
