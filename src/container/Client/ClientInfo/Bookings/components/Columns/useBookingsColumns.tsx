import { ID_ANONYMOUS, firstNormal } from '@moego/finance-utils';
import { type TypeofOrderPaymentStatus } from '@moego/finance-web-kit';
import { T_MINUTE } from '@moego/reporting';
import { Button, Condition, Link, Popover, Tag, createColumnHelper } from '@moego/ui';
import { type AccessorFnColumnDef, type DisplayColumnDef } from '@moego/ui/dist/esm/components/Table/Table.types';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import { isString } from 'lodash';
import React from 'react';
import { useUpdate } from 'react-use';
import { BusinessName } from '../../../../../../components/BusinessName';
import { usePermissionCheck } from '../../../../../../components/GuardRoute/WithPermission';
import { WithMultiLocation } from '../../../../../../components/WithFeature/WithMultiLocation';
import { useNewAccountStructure } from '../../../../../../components/WithFeature/useNewAccountStructure';
import { switchBusiness } from '../../../../../../store/business/business.actions';
import { type BusinessRecord } from '../../../../../../store/business/business.boxes';
import { customerMapBox } from '../../../../../../store/customer/customer.boxes';
import { groomingGetCustomerTickets } from '../../../../../../store/grooming/grooming.actions';
import {
  GroomingTicketListType,
  type GroomingTicketRecord,
  GroomingTicketStatus,
} from '../../../../../../store/grooming/grooming.boxes';
import { notifyMoveToWaitingList } from '../../../../../../store/onlineBooking/actions/public/onlineBooking.actions';
import { petMapBox } from '../../../../../../store/pet/pet.boxes';
import { serviceMapBox } from '../../../../../../store/service/service.boxes';
import { staffMapBox } from '../../../../../../store/staff/staff.boxes';
import { isNormal } from '../../../../../../store/utils/identifier';
import { truly } from '../../../../../../store/utils/utils';
import { deleteWaitList } from '../../../../../../store/waitList/actions/public/waitList.actions';
import { DATE_FORMAT_DAY_SHORT, DATE_FORMAT_EXCHANGE } from '../../../../../../utils/DateTimeUtil';
import { useLatestCallback } from '../../../../../../utils/hooks/useLatestCallback';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { computedDiffTimeString } from '../../../../../../utils/utils';
import { waitlistPreferenceToFormValue } from '../../../../../CreateWaitList/utils/utils';
import { useShowOBRequestActionModal } from '../../../../../OnlineBooking/components/OnlineBookingRequestActionModal/OnlineBookingRequestActionModal.hooks';
import { type OnlineBookingRequestActionModalProps } from '../../../../../OnlineBooking/components/OnlineBookingRequestActionModal/OnlineBookingRequestActionModal.options';
import { useViewOrder } from '../../../../../PaymentFlow/ViewOrderDrawer/useViewOrder';
import { ticketStatusOptions } from '../../../../../TicketDetail/interfaces';
import { useExpectedRender } from '../../../../../WaitList/hooks/useExpectedRender';
import { AppointmentActions } from '../AppointmentActions';
import { type BookingTableAction } from '../BookingTable';
import { OderStatus, RefundOrderStatusTag } from '../OderStatus';
import { PetStaffServiceCell } from '../PetStaffServiceCell';
import { BookingListOrderType, type GroomingTicketRecordData } from '../types';

import { TicketPaidStatusCellV2 } from '../TicketPaidStatusCellV2';
import { showNoShowStatusForAppt } from '../utils';

import { useInvoiceReinvent } from '../../../../../PaymentFlow/hooks/useInvoiceReinvent';
import { OrderPaymentStatus } from '../../../../../PaymentFlow/shared/OrderPaymentStatus';
import { useWaitDetail } from '../../../../../WaitDetail/hooks/useWaitDetail';
import { HistoryActions } from './HistoryActions';
import { UpcomingActions } from './UpcomingActions';

const helper = createColumnHelper<GroomingTicketRecordData>();
const hideNotOriginal = (
  def: DisplayColumnDef<GroomingTicketRecordData, GroomingTicketRecordData>,
): DisplayColumnDef<GroomingTicketRecordData, GroomingTicketRecordData> => {
  return {
    ...def,
    cell: (props) => {
      const record = props.getValue();
      if (record.orderType !== BookingListOrderType.ORIGIN) return null;
      if (isString(def.cell)) return def.cell;
      return def.cell?.(props);
    },
  };
};

const NORMAL_DATE_WIDTH = 240;
const NORMAL_SERVICE_INFO_WIDTH = 230;

function checkShowAction(type: number) {
  return [GroomingTicketListType.Cancelled, GroomingTicketListType.NoShow].includes(type);
}

export const useBookingsColumns = (options: {
  type: number;
  business: BusinessRecord;
  handleAction: (
    record: GroomingTicketRecord & {
      orderRefId?: number;
    },
    action: BookingTableAction,
  ) => void;
  customerId: number;
  refreshTableList: () => void;
}) => {
  const { type, business, handleAction, customerId, refreshTableList } = options;
  const update = useUpdate();
  const { isNewOrderV4Flow } = useInvoiceReinvent();
  const [staffMap, serviceMap, petMap] = useSelector(staffMapBox, serviceMapBox, petMapBox);
  const showAction = checkShowAction(type);
  const isNoShowList = type === GroomingTicketListType.NoShow;
  const isCancelledList = type === GroomingTicketListType.Cancelled;
  const dispatch = useDispatch();
  const { expectedFields } = useExpectedRender();
  const { openViewOrderDrawer } = useViewOrder();
  const showOBRequestActionModal = useShowOBRequestActionModal();
  const [customer] = useSelector(customerMapBox.mustGetItem(customerId));
  const openWaitDetail = useWaitDetail();
  const isWaitList = type === GroomingTicketListType.Waiting;
  const { isNewAndHasMultipleLocation } = useNewAccountStructure('working');
  const hasAccessClientTotalPaidInfo = usePermissionCheck({ permissions: ['accessClientTotalPaidInfo'] });
  const shouldSkipCancel = type === GroomingTicketListType.History || type === GroomingTicketListType.Upcoming;

  const getOrderLabel = (orderType: BookingListOrderType) => {
    if (orderType === BookingListOrderType.EXTRA) return 'Extra item';
    if (orderType === BookingListOrderType.TIP) return 'Extra tip';
    if (orderType === BookingListOrderType.REFUND) return 'Refund';
    if (orderType === BookingListOrderType.NO_SHOW) {
      return 'No show';
    }
    return null;
  };

  const handleDelete = useLatestCallback(async (record: GroomingTicketRecord) => {
    if (record.waitListDTO?.realWaitListId) {
      if (record.bookOnlineStatus) {
        const type = 'delete';
        const notifyActionParams: OnlineBookingRequestActionModalProps = {
          groomingId: record.id,
          type,
          shouldShowRefund: record.prepaidAmount > record.refundAmount,
          hasPetParentAppAccount: !!customer?.hasPetParentAppAccount,
        };
        const { notify } = await showOBRequestActionModal<{ notify: number[] }>({
          ...notifyActionParams,
          skipSubmit: true,
        });
        // 先通知，再delete
        await dispatch(
          notifyMoveToWaitingList({
            groomingId: record.id,
            notifyType: notify,
            type,
          }),
        );
      }
      await dispatch(deleteWaitList({ waitListId: record.waitListDTO.realWaitListId }));
      dispatch(groomingGetCustomerTickets({ customerId, type, skipCancel: shouldSkipCancel })).then(() => {
        update();
      });
      return;
    }
    handleAction(record, 'delete');
  });

  const handleOpenWaitList = useSerialCallback(async (record: GroomingTicketRecord) => {
    if (record.waitListDTO?.realWaitListId) {
      if (isNormal(record.businessId) && business.id !== Number(record.businessId)) {
        await dispatch(switchBusiness(Number(record.businessId)));
      }

      openWaitDetail({
        id: String(record.waitListDTO.realWaitListId),
        mask: true,
        onRefresh() {
          dispatch(groomingGetCustomerTickets({ customerId, type, skipCancel: shouldSkipCancel })).then(() => update());
        },
        onDelete: async () => await handleDelete(record),
      });
      return;
    }
    handleAction(record, 'book');
  });

  const dateCol = helper.accessor(
    (row) => row,
    hideNotOriginal({
      id: 'date',
      header: 'Appointment Date & time',
      maxSize: NORMAL_DATE_WIDTH,
      size: NORMAL_DATE_WIDTH,
      enableSorting: true,
      cell(props) {
        const record = props.getValue();
        if (!record.appointmentDate) {
          return '';
        }
        const startDate = business.formatFixedDate(record.appointmentDate);
        const startTime = business.formatFixedTime(record.appointmentStartTime * T_MINUTE);
        const endDate = business.formatFixedDate(record.appointmentEndDate);
        const endTime = business.formatFixedTime(record.appointmentEndTime * T_MINUTE);
        const startDay = dayjs(record.appointmentDate, DATE_FORMAT_EXCHANGE).format(DATE_FORMAT_DAY_SHORT);
        const endDay = dayjs(record.appointmentEndDate, DATE_FORMAT_EXCHANGE).format(DATE_FORMAT_DAY_SHORT);

        const isSameDate = startDate === endDate;

        return (
          <div>{`${startDate} ${startDay} ${startTime} - ${isSameDate ? '' : `${endDate} ${endDay} `}${endTime}`}</div>
        );
      },
    }),
  );

  const invoiceIdCol = helper.accessor((row) => row, {
    header: 'Invoice',
    // size: 250,
    cell: (props) => {
      const record = props.getValue();
      if (record.orderType === BookingListOrderType.ORIGIN) {
        const invoiceId = showNoShowStatusForAppt(record, isNoShowList)
          ? record.noShowOrderId
          : record.appointmentOrderId;
        return (
          <Link
            variant="regular"
            onClick={(e) => {
              e.stopPropagation();
              openViewOrderDrawer({
                orderId: firstNormal(record.orderRefId ?? ID_ANONYMOUS, invoiceId).toString(),
                module: 'grooming',
              });
            }}
          >
            #{invoiceId}
          </Link>
        );
      }
      const label = getOrderLabel(record.orderType);
      return (
        <div className="moe-flex moe-items-center">
          <Link
            variant="regular"
            onClick={(e) => {
              e.stopPropagation();
              openViewOrderDrawer({
                orderId: firstNormal(record.orderRefId ?? ID_ANONYMOUS, record.invoiceId).toString(),
                module: 'grooming',
              });
            }}
          >
            #{record.invoiceId}
          </Link>
          <Condition if={!!label}>
            <Tag variant="outlined" label={label} className="moe-ml-xxs group-hover/body-row:moe-border-white" />
          </Condition>
        </div>
      );
    },
  });

  const apptIdCol = helper.accessor(
    (row) => row,
    hideNotOriginal({
      header: 'Appointment ID',
      size: 180,
      cell: (props) => {
        const record = props.getValue();
        // const businessId = record.businessId;
        return <Link variant="regular">#{record.id}</Link>;
      },
    }),
  );

  const serviceInfoCol = helper.accessor(
    (row) => row,
    hideNotOriginal({
      header: isWaitList ? 'Pet / Service' : 'Pet / Staff / Service',

      size: NORMAL_SERVICE_INFO_WIDTH,
      cell: (props) => {
        const record = props.getValue();
        const { evaluationServiceList, petServiceList } = record;
        let EvaluationBlock = null;
        let ServiceBlock = null;

        if (evaluationServiceList?.length) {
          EvaluationBlock = evaluationServiceList.map((item) => {
            return (
              <PetStaffServiceCell
                key={item.evaluationServiceDetailId}
                petName={petMap.mustGetItem(Number(item.petId)).petName}
                serviceName={serviceMap.mustGetItem(Number(item.serviceId)).name}
              />
            );
          });
        }

        ServiceBlock = petServiceList.map((item) => {
          const staffName =
            !isWaitList && item.staffIds?.length
              ? item.staffIds
                  .filter(isNormal)
                  .map((staffId) => staffMap.mustGetItem(+staffId).firstName)
                  .join(', ')
              : undefined;
          return (
            <PetStaffServiceCell
              key={item.petDetailId}
              petName={petMap.mustGetItem(Number(item.petId)).petName}
              serviceName={serviceMap.mustGetItem(Number(item.serviceId)).name}
              staffName={staffName}
            />
          );
        });
        return (
          <>
            {EvaluationBlock}
            {ServiceBlock}
          </>
        );
      },
    }),
  );
  const invoiceTotalCol = helper.accessor((row) => row, {
    header: 'Invoice total',
    size: 120,
    cell: (props) => {
      const record = props.getValue();
      if (showNoShowStatusForAppt(record, isNoShowList)) {
        return business.formatAmount(record.noShowFee);
      }
      return business.formatAmount(record.subTotalAmount);
    },
  });
  const serviceSubtotalCol = helper.accessor((row) => row, {
    header: 'Service subtotal',
    size: 145,
    cell: (props) => {
      const record = props.getValue();
      return business.formatAmount(record.subTotalAmount);
    },
  });
  const NoShowFeeCol = helper.accessor((row) => row, {
    header: 'No-show fee',
    size: 120,
    cell: (props) => {
      const record = props.getValue();
      return business.formatAmount(record.noShowFee);
    },
  });
  const paidAmounCol =
    hasAccessClientTotalPaidInfo &&
    helper.accessor((row) => row, {
      header: 'Total paid',
      size: 120,
      cell: (props) => business.formatAmount(props.getValue().paidAmount),
    });
  const noShowPaymentStatusCol = helper.accessor((row) => row, {
    header: 'No-show payment status',
    size: 205,
    cell: (props) => {
      const record = props.getValue();
      return record.noShowPaymentStatus ? (
        <OrderPaymentStatus status={record.noShowPaymentStatus as TypeofOrderPaymentStatus} isOverPaid={false} />
      ) : (
        '--'
      );
    },
  });
  const paymentStatusCol = helper.accessor((row) => row, {
    header: 'Payment status',
    size: 180,
    cell: (props) => {
      const record = props.getValue();
      if (isNewOrderV4Flow) {
        return <TicketPaidStatusCellV2 paidStatus={record.isPaid} />;
      }

      if (record.orderType === BookingListOrderType.ORIGIN) {
        return showNoShowStatusForAppt(record, isNoShowList) ? (
          <OrderPaymentStatus status={record.noShowPaymentStatus as TypeofOrderPaymentStatus} isOverPaid={false} />
        ) : (
          <TicketPaidStatusCellV2 paidStatus={record.paidStatusV2()} />
        );
      }
      if (
        [BookingListOrderType.EXTRA, BookingListOrderType.TIP, BookingListOrderType.NO_SHOW].includes(record.orderType)
      ) {
        return <OderStatus status={record.orderPaymentStatus} />;
      }
      if (record.orderType === BookingListOrderType.REFUND) {
        return <RefundOrderStatusTag status={record.orderRefundStatus} />;
      }
      return null;
    },
  });
  const apptStatusCol = helper.accessor((row) => row, {
    header: 'Appointment status',
    size: 220,
    cell(props) {
      const record = props.getValue();

      const color = ticketStatusOptions.find((o) => o.id === +record.appointmentStatus)?.color;
      return (
        <div className={'status ' + [GroomingTicketStatus.mapLabels[record.appointmentStatus]]}>
          {color ? (
            <span
              className="moe-inline-block moe-w-8px-150 moe-h-8px-150 moe-rounded-full moe-mr-xxs"
              style={{ backgroundColor: color }}
            />
          ) : null}

          {record.bookOnlineStatus ? 'Pending' : GroomingTicketStatus.mapLabels[record.appointmentStatus]}
        </div>
      );
    },
  });

  const actionCol = helper.accessor((row) => row, {
    id: 'action',
    header: '',
    cell: (props) => {
      const record = props.getValue();
      return (
        <AppointmentActions
          record={record}
          handleAction={(action) => handleAction(record, action)}
          refreshTableList={refreshTableList}
        />
      );
    },
  });

  const upcomingActionCol = helper.accessor((row) => row, {
    header: 'Action',
    size: 120,
    cell: (props) => {
      const record = props.getValue();
      return <UpcomingActions record={record} refreshTableList={refreshTableList} />;
    },
  });

  const historyActionCol = helper.accessor((row) => row, {
    header: 'Action',
    size: 120,
    cell: (props) => {
      const record = props.getValue();
      return <HistoryActions record={record} refreshTableList={refreshTableList} />;
    },
  });

  const waitListActionCol = helper.accessor(
    (row) => row,
    hideNotOriginal({
      header: 'Action',
      size: 260,
      cell: (props) => {
        const record = props.getValue();
        return (
          <div className="moe-flex moe-gap-xs">
            <Button
              onPress={() => handleOpenWaitList(record)}
              isLoading={handleOpenWaitList.isBusy()}
              variant="primary"
              size="s"
            >
              Book now
            </Button>
            <Popover side="top">
              <Popover.Button color="danger" size="s">
                Delete
              </Popover.Button>
              <Popover.Content
                showCloseIcon={false}
                onConfirm={() => {
                  handleDelete(record);
                }}
                confirmText="Yes"
                cancelButtonProps={{
                  size: 's',
                }}
                confirmButtonProps={{
                  color: 'danger',
                  size: 's',
                }}
              >
                Are you sure to delete this booking?
              </Popover.Content>
            </Popover>
          </div>
        );
      },
    }),
  );
  const busineessNameCol = helper.accessor(
    (row) => row,
    hideNotOriginal({
      header: 'Business',
      minSize: 120,
      cell: (props) => {
        const record = props.getValue();
        const businessId = record.businessId;
        if (businessId) {
          return (
            <WithMultiLocation scene="all">
              <BusinessName businessId={record.businessId} className="moe-text-base moe-text-[#666]" />
            </WithMultiLocation>
          );
        }
        return '/';
      },
    }),
  );

  const waitListCols = [
    helper.accessor((row) => row, {
      id: 'date',
      header: 'Added time',
      maxSize: 240,
      size: 240,
      enableSorting: true,
      cell(props) {
        const record = props.getValue();

        const { createAt = '' } = record.waitListDTO || {};
        return (
          <div className="moe-flex moe-flex-col">
            <span className="moe-text-[14px] moe-text-[#333]">{business.formatFixedDate(createAt)}</span>
            <span className="moe-text-[14px] moe-text-[#999]">
              ( {computedDiffTimeString(dayjs(createAt).valueOf() / 1000, DATE_FORMAT_EXCHANGE)})
            </span>
          </div>
        );
      },
    }),
    serviceInfoCol,
    helper.accessor((row) => row, {
      id: 'excepted',
      header: 'Expected',
      cell(props) {
        const record = props.getValue();
        if (!record.waitListDTO) {
          return '';
        }
        const { staffPreference = {}, timePreference = {}, datePreference = {} } = record.waitListDTO;
        const afterValue = waitlistPreferenceToFormValue({ staffPreference, timePreference, datePreference });
        return (
          <div className="">
            {expectedFields.date.render(afterValue)}
            <span className="moe-mx-[2px]">/</span>
            {expectedFields.time.render(afterValue)}
            <span className="moe-mx-[2px]">/</span>
            {expectedFields.staff.render(afterValue)}
          </div>
        );
      },
    }),
    isNewOrderV4Flow ? serviceSubtotalCol : invoiceTotalCol,
    paymentStatusCol,
    apptStatusCol,
    waitListActionCol,
    isNewAndHasMultipleLocation ? busineessNameCol : null,
  ].filter(Boolean) as AccessorFnColumnDef<GroomingTicketRecordData, GroomingTicketRecordData>[];

  const normalCols = (
    isNewOrderV4Flow
      ? [
          dateCol,
          serviceInfoCol,
          serviceSubtotalCol,
          isNoShowList ? NoShowFeeCol : undefined,
          isCancelledList ? undefined : isNoShowList ? noShowPaymentStatusCol : paymentStatusCol,
          !isNoShowList && !isCancelledList ? paidAmounCol : undefined,
          !isNoShowList ? apptStatusCol : undefined,
          apptIdCol,
          busineessNameCol,
          type === GroomingTicketListType.Upcoming ? upcomingActionCol : undefined,
          type === GroomingTicketListType.History ? historyActionCol : undefined,
          showAction ? actionCol : undefined,
        ]
      : [
          dateCol,
          serviceInfoCol,
          invoiceIdCol,
          invoiceTotalCol,
          paymentStatusCol,
          !isNoShowList ? paidAmounCol : undefined,
          !isNoShowList ? apptStatusCol : undefined,
          apptIdCol,
          busineessNameCol,
          showAction ? actionCol : undefined,
        ]
  ).filter(truly);

  return isWaitList ? waitListCols : normalCols;
};
