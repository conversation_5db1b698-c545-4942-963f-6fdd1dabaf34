import type { DraggableSyntheticListeners, UniqueIdentifier } from '@dnd-kit/core';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { MajorDragOutlined } from '@moego/icons-react';
import type { CSSProperties, PropsWithChildren } from 'react';
import React, { createContext, memo, useContext, useMemo } from 'react';

export interface Props {
  id: UniqueIdentifier;
  className?: string;
}

interface Context {
  attributes: Record<string, any>;
  listeners: DraggableSyntheticListeners;
  ref(node: HTMLElement | null): void;
}

const SortableItemContext = createContext<Context>({
  attributes: {},
  listeners: undefined,
  ref() {},
});

export const SortableItem = memo(({ children, id, className }: PropsWithChildren<Props>) => {
  const { attributes, isDragging, listeners, setNodeRef, setActivatorNodeRef, transform, transition } = useSortable({
    id,
  });
  const context = useMemo(
    () => ({
      attributes,
      listeners,
      ref: setActivatorNodeRef,
    }),
    [attributes, listeners, setActivatorNodeRef],
  );
  const style: CSSProperties = {
    opacity: isDragging ? 0.4 : undefined,
    transform: CSS.Translate.toString(transform),
    transition,
  };

  return (
    <SortableItemContext.Provider value={context}>
      <div ref={setNodeRef} style={style} className={className}>
        {children}
      </div>
    </SortableItemContext.Provider>
  );
});

export interface DragHandleProps {
  className?: string;
}

export const DragHandle = memo<DragHandleProps>(({ className = '' }) => {
  const { attributes, listeners, ref } = useContext(SortableItemContext);
  return <MajorDragOutlined {...attributes} {...listeners} ref={ref} className={className} />;
});
