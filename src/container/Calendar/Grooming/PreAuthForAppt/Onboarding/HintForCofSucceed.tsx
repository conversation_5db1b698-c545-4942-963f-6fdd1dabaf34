/**
 * @since 2023-06-21
 * <AUTHOR>
 * @description pre auth onboarding 在 cof paid success 时的引导内容
 */
import { useSelector } from 'amos';
import classNames from 'classnames';
import React, { memo } from 'react';
import { selectPaymentSettingInfo } from '../../../../../store/payment/payment.selectors';
import { PRE_AUTH_FEATURE_NAME } from '../../../../../store/stripe/preAuth.boxes';
import { usePreAuthOnboarding } from '../usePreAuthOnboarding';
import { HintAnimate } from './HintAnimate';
import { HintDescription } from './HintDescription';
import { HintLearnMoreButton } from './HintLearnMoreButton';
import { HintTitle } from './HintTitle';
import { type HintForPreAuthBase } from './types';

export type HintForChargeSucceedProps = HintForPreAuthBase<'MoeGoPay', 'CofSucceed'>;

export const HintForCofSucceed = memo<HintForChargeSucceedProps>(({ name, className }) => {
  const [{ preAuthBspd }] = useSelector(selectPaymentSettingInfo());
  const { isCompatibleEnable } = usePreAuthOnboarding({
    name,
    autoIncrement: true,
  });

  return isCompatibleEnable ? (
    <div
      className={classNames([
        '!moe-flex !moe-justify-center !moe-bg-[#fef0e8] !moe-rounded-b-[8px] !moe-px-[28px] !moe-py-[24px]',
        className,
      ])}
    >
      <div className="moe-flex moe-w-[738px]">
        <HintAnimate className="moe-self-center moe-rounded-[4px]" width={216} />
        <div className="!moe-ml-[24px]">
          <HintTitle size="small" textClassName="!moe-pl-[8px]">
            Unlock effortless payment processing and secure revenue in advance with {PRE_AUTH_FEATURE_NAME}.
          </HintTitle>

          <HintDescription size="small" className="!moe-mt-[12px]">
            Enjoy streamlined and secured payment processing by pre-authorizing the ticket amount {preAuthBspd} hours
            prior to appointments and automatically capture funds by the end of service day.
          </HintDescription>

          <div className="!moe-flex !moe-justify-end !moe-mt-[12px]">
            <HintLearnMoreButton size="small" theme="light" />
          </div>
        </div>
      </div>
    </div>
  ) : null;
});
