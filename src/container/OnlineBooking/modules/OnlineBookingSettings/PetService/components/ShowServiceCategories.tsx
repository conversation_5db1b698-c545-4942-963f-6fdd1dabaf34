import { Switch } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { memo } from 'react';
import { toastApi } from '../../../../../../components/Toast/Toast';
import { updateOnlineBookingPreference } from '../../../../../../store/onlineBooking/actions/private/onlineBookingPreference.actions';
import { selectOnlineBookingPreference } from '../../../../../../store/onlineBooking/onlineBookingPreference.selectors';
import { useSerialUpdate } from '../../../../../../utils/hooks/hooks';

export const ShowServiceCategories = memo(() => {
  const [preference] = useSelector(selectOnlineBookingPreference);
  const dispatch = useDispatch();
  const update = useSerialUpdate(dispatch, updateOnlineBookingPreference);

  return (
    <Switch
      isSelected={preference.isShowCategories}
      onChange={async (checked) => {
        await update('isShowCategories', { isShowCategories: checked });
        toastApi.success('Service categories updated successfully');
      }}
      className="moe-mb-m"
    >
      Show service categories
    </Switch>
  );
});
