import { Drawer } from '@moego/ui';
import React, { useRef } from 'react';
import { LimitationForm, type LimitationFormRef } from '../../Limitation/LimitationForm';
import type { LimitationFormValue } from '../../Limitation/LimitationFormContext';
import cn from 'classnames';

export interface LimitationEditDrawerProps {
  className?: string;
  visible: boolean;
  value?: LimitationFormValue;
  onConfirm?: (v: LimitationFormValue) => void;
  onClose?: () => void;
  onCancel?: () => void;
}

const defaultLimitationValue: LimitationFormValue = {
  petSizeLimits: [],
  petBreedLimits: [],
  serviceLimits: [],
};

export const LimitationEditDrawer = (props: LimitationEditDrawerProps) => {
  const { className, value = defaultLimitationValue, visible, onConfirm, onClose, onCancel } = props;
  const limitationFormRef = useRef<LimitationFormRef>(null);
  const handleConfirm = async () => {
    await limitationFormRef.current?.submit();
  };

  return (
    <Drawer
      title="Book limit"
      className={cn('moe-w-[600px]', className)}
      isDismissable={false}
      isOpen={visible}
      autoCloseOnConfirm={false}
      confirmText="Save"
      onConfirm={handleConfirm}
      onClose={onClose}
      onCancel={onCancel}
    >
      <LimitationForm
        ref={limitationFormRef}
        value={value}
        onChange={async (v) => {
          await onConfirm?.(v);
          await onClose?.();
        }}
      />
    </Drawer>
  );
};

LimitationEditDrawer.displayName = 'LimitationEditDrawer';
