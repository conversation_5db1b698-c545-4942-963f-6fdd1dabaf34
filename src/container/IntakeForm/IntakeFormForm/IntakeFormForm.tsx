import { MinorWarningFilled } from '@moego/icons-react';
import { cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { Form, Input, Upload } from 'antd';
import FormItem from 'antd/es/form/FormItem';
import TextArea from 'antd/es/input/TextArea';
import React, { memo, useEffect, useMemo } from 'react';
import { useHistory } from 'react-router';
import { Link } from 'react-router-dom';
import { Breadcrumb } from '../../../components/Breadcrumb/Breadcrumb';
import { BreadcrumbItem } from '../../../components/Breadcrumb/Breadcrumb.style';
import { Button } from '../../../components/Button/Button';
import { Card } from '../../../components/Card/Card';
import { ColorPicker } from '../../../components/ColorPicker/ColorPicker';
import { FormFooter } from '../../../components/Style/Style';
import { validateThemeColor } from '../../../components/ThemeColorInput/LightThemeColorTips';
import { toastApi } from '../../../components/Toast/Toast';
import { PATH_INTAKE_FORM_EDIT, PATH_INTAKE_FORM_LIST } from '../../../router/paths';
import { getAgreementList } from '../../../store/agreement/agreement.actions';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import {
  addIntakeFormForm,
  addIntakeFormQuestion,
  getIntakeFormForm,
  removeIntakeFormQuestion,
  sortIntakeFormQuestion,
  updateIntakeFormAgreement,
  updateIntakeFormForm,
  updateIntakeFormQuestion,
} from '../../../store/intakeForm/intakeForm.actions';
import {
  type IntakeFormFormModel,
  IntakeFormQuestionCategory,
  intakeFormQuestionMapBox,
} from '../../../store/intakeForm/intakeForm.boxes';
import {
  selectIntakeFormAgreement,
  selectIntakeFormForm,
  selectIntakeFormFormQuestionList,
  selectIntakeFormQuestion,
} from '../../../store/intakeForm/intakeForm.selectors';
import { getPetVaccineList } from '../../../store/pet/petVaccine.actions';
import { getStripeAccount } from '../../../store/stripe/actions/public/stripe.actions';
import { ID_ANONYMOUS, isNormal } from '../../../store/utils/identifier';
import { useRouteParams } from '../../../utils/RoutePath';
import { formInput } from '../../../utils/form';
import { useFormRef } from '../../../utils/hooks/hooks';
import { useSerialCallback } from '../../../utils/hooks/useSerialCallback';
import { BookingQuestionList } from '../components/BookingQuestionList';
import { IntakeFormFormView } from './IntakeFormForm.style';
import { isNil } from 'lodash';
import { PlusOutlined } from '@ant-design/icons';
import { generateUID } from '../../../components/Upload/MoeGoUIUpload';

export interface IntakeFormFormProps {
  className?: string;
}

const metaInput = formInput<IntakeFormFormModel>().copy(
  'title',
  'message',
  'themeColor',
  'coverImage',
  'marketingPolicy',
);

interface LightThemeColorTipsForIntakeFormProps {
  themeColor: string;
  className?: string;
}

export const LightThemeColorTipsForIntakeForm = memo<LightThemeColorTipsForIntakeFormProps>(
  ({ themeColor, className }) => {
    const warn = useMemo(() => validateThemeColor(themeColor), [themeColor]);

    if (!warn) {
      return null;
    }
    return (
      <div
        className={cn(
          'moe-flex moe-gap-xxs moe-text-warning moe-text-[12px] moe-font-manrope moe-tracking-[0.24px] moe-items-center',
          className,
        )}
      >
        <MinorWarningFilled className="moe-w-[20px] moe-h-[20px]" />
        <div>This color is too light for clear readability. Try a darker shade.</div>
      </div>
    );
  },
);

export const IntakeFormForm = memo<IntakeFormFormProps>(({ className }) => {
  const formId = useRouteParams(PATH_INTAKE_FORM_EDIT).formId;
  const isAdd = !isNormal(+formId);
  const [form, business] = useSelector(selectIntakeFormForm(formId ? +formId : ID_ANONYMOUS), selectCurrentBusiness());
  const metaForm = useFormRef();
  const dispatch = useDispatch();
  const history = useHistory();
  const [localThemeColor, setLocalThemeColor] = React.useState(form.themeColor);
  const [coverImage, setCoverImage] = React.useState(form.coverImage);

  useEffect(() => {
    if (isNormal(business.id) && isNormal(+formId)) {
      dispatch(getIntakeFormForm(+formId));
    }
  }, [business.id, formId]);
  useEffect(() => {
    if (isNormal(business.id)) {
      dispatch([getAgreementList(), getStripeAccount(), getPetVaccineList()]);
    }
  }, [business.id]);
  useEffect(() => {
    metaInput.attach(metaForm, form);
  }, [form]);
  useEffect(() => {
    if (form.coverImage && !coverImage) {
      setCoverImage(form.coverImage);
    }
  }, [form.coverImage]);
  const handleSubmit = useSerialCallback(async () => {
    const input = await metaInput.validate(metaForm);
    if (isAdd) {
      const id = await dispatch(addIntakeFormForm(input));
      toastApi.success('The intake form is added successfully!');
      history.replace(PATH_INTAKE_FORM_EDIT.build({ formId: id }));
    } else {
      await dispatch(updateIntakeFormForm({ ...input, id: +formId }));
      toastApi.success('The intake form is updated successfully!');
    }
  });

  const handleValuesChange = (changedValues: Partial<IntakeFormFormModel>) => {
    const { themeColor } = changedValues;
    if (themeColor) {
      setLocalThemeColor(themeColor);
    }
  };

  return (
    <IntakeFormFormView className={className}>
      <Breadcrumb className="mt-8 mb-8">
        <BreadcrumbItem as={Link} to={PATH_INTAKE_FORM_LIST.build()}>
          Intake form list
        </BreadcrumbItem>
        <BreadcrumbItem>{formId ? 'Edit intake form' : 'Add new intake form'}</BreadcrumbItem>
      </Breadcrumb>
      <Card>
        <Form ref={metaForm} labelAlign="left" size="large" onValuesChange={handleValuesChange}>
          <FormItem
            label="Form name"
            name="title"
            rules={[
              { max: 200 },
              {
                validator: (_, value) => {
                  if (isNil(value) || value?.trim() === '') {
                    return Promise.reject('Please input form name');
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input />
          </FormItem>
          <FormItem
            label={
              <>
                Welcome
                <br />
                message
              </>
            }
            name="message"
            rules={[{ max: 500 }]}
          >
            <TextArea autoSize={{ minRows: 4, maxRows: 10 }} />
          </FormItem>
          <FormItem label="Theme color" name="themeColor" className="!moe-mb-[8px]">
            <ColorPicker />
          </FormItem>
          <FormItem label="Form cover" name="coverImage" className="!moe-mb-[8px]">
            <Upload
              key={coverImage ? coverImage : 'empty'}
              name="file"
              listType="picture-card"
              className="moe-mt-3"
              action="/api/business/upload/bookOnlinePetPhotosUpload"
              accept=".jpg,.jpeg,.png,.pdf"
              onPreview={(file) => {
                if (file.response?.data?.url) {
                  window.open(file.response.data.url, '_blank');
                } else if (file.url) {
                  window.open(file.url, '_blank');
                }
              }}
              onChange={(file) => {
                if (file.file.status === 'done') {
                  setCoverImage(file.file.response.data.url);
                  metaForm.current?.setFieldsValue({ coverImage: file.file.response.data.url });
                } else if (file.file.status === 'removed') {
                  setCoverImage('');
                  metaForm.current?.setFieldsValue({ coverImage: '' });
                }
              }}
              defaultFileList={
                coverImage
                  ? [{ url: coverImage, uid: generateUID(0), size: 1, name: 'coverImage', type: '' }]
                  : undefined
              }
            >
              {coverImage ? null : (
                <div>
                  <PlusOutlined />
                  <div style={{ marginTop: 8 }}>Upload</div>
                </div>
              )}
            </Upload>
          </FormItem>
          <FormItem
            label={
              <>
                Marketing
                <br />
                policy
              </>
            }
            name="marketingPolicy"
            className="!moe-mb-[8px]"
          >
            <TextArea autoSize={{ minRows: 4, maxRows: 10 }} placeholder="Please input marketing policy" />
          </FormItem>
          <LightThemeColorTipsForIntakeForm themeColor={localThemeColor} className="moe-ml-[120px]" />

          <FormFooter>
            <Button btnType="primary" buttonRadius="circle" onClick={handleSubmit} loading={handleSubmit.isBusy()}>
              {isAdd ? 'Add' : 'Save'}
            </Button>
          </FormFooter>
        </Form>
      </Card>
      {!isAdd && (
        <>
          <BookingQuestionList
            type={IntakeFormQuestionCategory.ForPet}
            selectAllQuestion={intakeFormQuestionMapBox}
            selectBusinessQuestions={(type) => selectIntakeFormFormQuestionList(type, +formId)}
            selectQuestion={selectIntakeFormQuestion}
            sortQuestions={(idList, type) => sortIntakeFormQuestion(idList, type, +formId)}
            addQuestion={(input) => addIntakeFormQuestion({ ...input, formId: +formId })}
            updateQuestion={(input) => updateIntakeFormQuestion({ ...input, formDetailId: input.id })}
            removeQuestion={(id, type) => removeIntakeFormQuestion(id, type, +formId)}
          />
          <BookingQuestionList
            type={IntakeFormQuestionCategory.ForOwner}
            selectAllQuestion={intakeFormQuestionMapBox}
            selectBusinessQuestions={(type) => selectIntakeFormFormQuestionList(type, +formId)}
            selectQuestion={selectIntakeFormQuestion}
            sortQuestions={(idList, type) => sortIntakeFormQuestion(idList, type, +formId)}
            addQuestion={(input) => addIntakeFormQuestion({ ...input, formId: +formId })}
            updateQuestion={(input) => updateIntakeFormQuestion({ ...input, formDetailId: input.id })}
            removeQuestion={(id, type) => removeIntakeFormQuestion(id, type, +formId)}
            selectIntakeFormAgreement={(id: number) => selectIntakeFormAgreement(+formId, id)}
            updateIntakeFormAgreement={(id, isShow, isRequired) =>
              updateIntakeFormAgreement(+formId, id, +isShow, +isRequired)
            }
            creditCard={{
              isCardRequired: form.isCardRequired,
              isCardShow: !business.primaryPayType ? 0 : form.isCardShow,
            }}
            updateCreditCard={(isShow: boolean, isRequired: boolean) => {
              const { isCardShow } = form;
              const showBeTrue = !isCardShow && isShow;
              if (showBeTrue && !business.primaryPayType) {
                toastApi.error('Card processing is not available!');
                return;
              }
              formId &&
                dispatch(
                  updateIntakeFormForm(
                    Object.assign(
                      { ...form, id: +formId },
                      {
                        isCardRequired: +isRequired,
                        isCardShow: +isShow,
                      },
                    ),
                  ),
                );
            }}
          />
        </>
      )}
    </IntakeFormFormView>
  );
});
