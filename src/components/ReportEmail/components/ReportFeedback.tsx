import { Text } from '@react-email/text';
import React, { memo } from 'react';
import { AnswerStyle, QuestionStyle } from './ReportEmail.style';
import { ReportSection } from './ReportSection';
import { Section } from '@react-email/section';
import { Hr } from '@react-email/hr';

export interface FeedbackItem {
  question: string;
  answer: string;
}

export interface ReportCustomizedFeedbackProps {
  feedbacks?: FeedbackItem[];
}

export const ReportFeedback = memo(({ feedbacks = [] }: ReportCustomizedFeedbackProps) => {
  if (!feedbacks || feedbacks.length === 0) {
    return null;
  }

  return (
    <ReportSection>
      {feedbacks.map((feedback, index) => (
        <Section key={index}>
          <Text style={QuestionStyle}>{feedback.question}</Text>
          <Text style={AnswerStyle}>{feedback.answer}</Text>
          {index < feedbacks.length - 1 && <Hr style={{ marginTop: 16, marginBottom: 16 }} />}
        </Section>
      ))}
    </ReportSection>
  );
});
