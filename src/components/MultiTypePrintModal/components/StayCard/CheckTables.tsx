import { cn } from '@moego/ui';
import { isFunction } from 'lodash/fp';
import React, { memo, type ReactNode } from 'react';

export type RecordData = Record<string, any>;

export interface CheckTableColumn {
  title: ReactNode;
  dataIndex: string;
  date?: string;
  className?: string;
  width?: string | number;
  render?: (value: string) => React.ReactNode;
}

export interface CheckTableProps<D extends RecordData = RecordData> {
  columns: CheckTableColumn[];
  data: D[];
  className?: string;
}

const childrenTdClass = '[&_td]:moe-border-collapse [&_td]:moe-border [&_td]:moe-border-[#cbcbcb]';
const cellClassName = 'moe-text-[10px] moe-py-[2px] moe-px-[10px] moe-text-nowrap';
const headerClassName = 'moe-font-bold moe-leading-[12px]';
const rowClassName = 'moe-h-8px-300 moe-font-medium';

export const CheckTable = memo((props: CheckTableProps) => {
  const { columns, data, className } = props;

  return (
    <table
      className={cn(
        'moe-border-collapse moe-border moe-border-divider moe-w-0 moe-table-auto',
        childrenTdClass,
        className,
      )}
    >
      <colgroup>
        {columns.map((col) => (
          <col key={col.dataIndex} style={{ width: col.width }} />
        ))}
      </colgroup>
      <thead>
        <tr>
          {columns.map((col) => {
            return (
              <td key={col.dataIndex} className={cn(headerClassName, cellClassName, col.className)}>
                {col.title}
              </td>
            );
          })}
        </tr>
      </thead>
      <tbody>
        {data.map((item, index) => {
          return (
            <tr key={index}>
              {columns.map((col) => (
                <td key={col.dataIndex} className={cn(rowClassName, cellClassName, col.className)}>
                  {isFunction(col.render) ? col.render(item[col.dataIndex]) : item[col.dataIndex]}
                </td>
              ))}
            </tr>
          );
        })}
      </tbody>
    </table>
  );
});

export const CheckTableGroup = (props: {
  columnsGroup: CheckTableColumn[][];
  dataGroup: RecordData[][];
  className?: string;
  tableClassName?: string;
}) => {
  const { dataGroup, className, columnsGroup, tableClassName } = props;
  return (
    <div className={cn('moe-flex moe-flex-col moe-gap-y-8px-150', className)}>
      {columnsGroup.map((columns, index) => {
        return <CheckTable key={index} columns={columns} data={dataGroup[index]} className={tableClassName} />;
      })}
    </div>
  );
};
