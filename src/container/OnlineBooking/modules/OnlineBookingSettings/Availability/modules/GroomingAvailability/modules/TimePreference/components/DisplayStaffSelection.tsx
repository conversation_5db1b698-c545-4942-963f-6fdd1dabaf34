import { Form, Switch } from '@moego/ui';
import { useDispatch } from 'amos';
import React, { memo, useCallback } from 'react';
import { OBTestIds } from '../../../../../../../../../../config/testIds/onlineBooking';
import { setAvailabilityDraftSettings } from '../../../../../../../../../../store/onlineBooking/actions/private/availabilityGrooming.actions';
import { useAsyncAction } from '../../../../../../../../../../utils/hooks/useAsyncAction';
import { useBool } from '../../../../../../../../../../utils/hooks/useBool';
import { UpdateInterceptorAssignStaffModal } from './UpdateInterceptorModal';
import { DraftAvailableSlotRecord } from '../../../../../../../../../../store/onlineBooking/settings/availabilityGrooming.boxes';

export const DisplayStaffSelection = memo<{ draftSettings: DraftAvailableSlotRecord }>((props) => {
  const { draftSettings } = props;
  const dispatch = useDispatch();

  const updateInterceptorModalVisible = useBool(false);
  const { action, interceptor } = useAsyncAction({
    rejectContent: 'user cancel update display staff selection to false',
  });

  const onCancelAssignStaff = useCallback(() => {
    updateInterceptorModalVisible.close();
    action?.reject?.();
  }, [action]);

  const onContinueAssignStaff = useCallback(() => {
    updateInterceptorModalVisible.close();
    action?.resolve?.();
  }, [action]);

  return (
    <>
      <div className="moe-flex moe-flex-col moe-items-start">
        <Form.Label
          label="Enable staff selection"
          tooltip={
            'Once toggled off, clients can’t choose staff when booking online. The system will auto-assign available staff to pending requests, but clients will not be notified.\n\nYou can re-assign them when accepting requests and notify clients as needed.'
          }
          className="moe-mb-s"
        ></Form.Label>
        <Switch
          isSelected={draftSettings.displayStaffSelectionPage}
          onChange={async (checked) => {
            // 切换到 disable 时需要打开弹窗等待交互
            if (!checked) {
              updateInterceptorModalVisible.open();
              await interceptor();
            }
            dispatch(setAvailabilityDraftSettings({ displayStaffSelectionPage: checked }));
          }}
          data-testid={OBTestIds.SettingStaffSelectionSwitch}
        >
          {draftSettings.displayStaffSelectionPage ? 'Enabled' : 'Disabled'}
        </Switch>
      </div>
      <UpdateInterceptorAssignStaffModal
        visible={updateInterceptorModalVisible.value}
        onClose={onCancelAssignStaff}
        onCancel={onCancelAssignStaff}
        onContinue={onContinueAssignStaff}
      />
    </>
  );
});
