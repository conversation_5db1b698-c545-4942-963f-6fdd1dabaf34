import { useSelector } from 'amos';
import { noop } from 'lodash';
import React, { createContext, type PropsWithChildren, useContext, useEffect, useMemo } from 'react';
import { type OpenApiModels } from '../../../../../../openApi/schema';
import {
  DepositType,
  NoShowProtectType,
  type OnlineBookingPreferenceRecord,
  PrepaymentType,
} from '../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { selectOnlineBookingPreference } from '../../../../../../store/onlineBooking/onlineBookingPreference.selectors';
import { isNormal } from '../../../../../../store/utils/identifier';
import {
  type EditingPaymentType,
  PAYMENT_TYPE_UNSELECTED,
  useEditingPaymentType,
} from '../hooks/useEditingPaymentType';

type PrepaymentOptions = Pick<
  OpenApiModels['GET/grooming/bookOnline/setting/info']['Res']['data']['bookOnlineInfo'],
  'prepayType' | 'prepayTipEnable' | 'depositType' | 'depositAmount' | 'depositPercentage' | 'prepayPolicy'
>;

type PreAuthOptions = Pick<
  OpenApiModels['GET/grooming/bookOnline/setting/info']['Res']['data']['bookOnlineInfo'],
  'preAuthTipEnable' | 'preAuthPolicy'
>;

type CardOnFileOptions = Pick<
  OpenApiModels['GET/grooming/bookOnline/setting/info']['Res']['data']['bookOnlineInfo'],
  'cancellationPolicy'
>;

export type PaymentOptions = PrepaymentOptions & PreAuthOptions & CardOnFileOptions;

const defaultPaymentOptions: PaymentOptions = {
  prepayType: PrepaymentType.FullPay,
  prepayTipEnable: 0,
  depositType: DepositType.ByAmount,
  depositAmount: 0,
  depositPercentage: 0,
  prepayPolicy: '',
  preAuthTipEnable: 0,
  preAuthPolicy: '',
  cancellationPolicy: '',
};

interface PaymentTypeAndOptions {
  paymentTypeStates: EditingPaymentType;
  paymentOptions: PaymentOptions | null;
}

const PaymentTypeAndOptionsContext = createContext<PaymentTypeAndOptions>({
  paymentTypeStates: {
    paymentType: NoShowProtectType.Closed,
    setPaymentType: noop,
    paymentTypeForDisableView: NoShowProtectType.Closed,
    setPaymentTypeForDisableView: noop,
    resetPaymentType: noop,
  },
  paymentOptions: null,
});

const pickPaymentOptions = (
  preference:
    | OnlineBookingPreferenceRecord['paymentSetting']
    | OnlineBookingPreferenceRecord['paymentSetting']['certainGroupSetting'],
) => {
  if (!preference) {
    return null;
  }
  // Prepayment
  const { prepayType, prepayTipEnable, depositType, depositAmount, depositPercentage, prepayPolicy } = preference;
  // Pre-auth
  const { preAuthTipEnable, preAuthPolicy } = preference;
  // Card on file
  const { cancellationPolicy } = preference;
  return {
    prepayType,
    prepayTipEnable,
    depositType,
    depositAmount,
    depositPercentage,
    prepayPolicy,
    preAuthTipEnable,
    preAuthPolicy,
    cancellationPolicy,
  };
};

export const PaymentTypeAndOptionsDefaultProvider = ({ children }: PropsWithChildren<{}>) => {
  const [preference] = useSelector(selectOnlineBookingPreference());
  const paymentOptions = useMemo(() => pickPaymentOptions(preference.paymentSetting), [preference]);
  const paymentTypeStates = useEditingPaymentType({
    loaded: isNormal(preference.id),
    paymentType: preference.paymentSetting.paymentType,
  });
  const value = useMemo(() => ({ paymentOptions, paymentTypeStates }), [paymentOptions, paymentTypeStates]);
  return <PaymentTypeAndOptionsContext.Provider value={value}>{children}</PaymentTypeAndOptionsContext.Provider>;
};

export const PaymentTypeAndOptionsCertainClientsProvider = ({ children }: PropsWithChildren<{}>) => {
  const [preference] = useSelector(selectOnlineBookingPreference());
  const paymentOptions = useMemo(() => pickPaymentOptions(preference.paymentSetting.certainGroupSetting), [preference]);
  const loaded = isNormal(preference.id);
  const paymentTypeStates = useEditingPaymentType({
    loaded,
    paymentType: preference.paymentSetting.certainGroupSetting?.paymentType ?? PAYMENT_TYPE_UNSELECTED,
  });

  // 用户删除了 certain clients 配置后，手动触发一次 reset，这样用户再次编辑的时候 payment type 选项就会被清空，以符合需求
  const hasCertainGroupSetting = !!preference.paymentSetting.certainGroupSetting;
  useEffect(() => {
    if (loaded && !hasCertainGroupSetting) {
      paymentTypeStates.resetPaymentType();
    }
  }, [loaded, hasCertainGroupSetting]);

  const value = useMemo(() => ({ paymentOptions, paymentTypeStates }), [paymentOptions, paymentTypeStates]);
  return <PaymentTypeAndOptionsContext.Provider value={value}>{children}</PaymentTypeAndOptionsContext.Provider>;
};

export function usePaymentOptions(withDefault: true): PaymentOptions;
export function usePaymentOptions(withDefault?: false): PaymentOptions | null;
export function usePaymentOptions(withDefault?: boolean) {
  const { paymentOptions } = useContext(PaymentTypeAndOptionsContext);
  return paymentOptions || (withDefault ? defaultPaymentOptions : null);
}

export const usePaymentType = () => useContext(PaymentTypeAndOptionsContext).paymentTypeStates;
