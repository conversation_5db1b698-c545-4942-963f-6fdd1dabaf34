import { useDispatch } from 'amos';
import { isString } from 'lodash';
import { toastApi } from '../../../../../components/Toast/Toast';
import {
  cancelGroomingTicket,
  changeGroomingTicketStatus,
  type TicketCancelParams,
  type TicketCancelParamsOrigin,
} from '../../../../../store/grooming/grooming.actions';
import { GroomingCancelByType } from '../../../../../store/grooming/grooming.boxes';
import { useLatestCallback } from '../../../../../utils/hooks/useLatestCallback';
import { AppointmentStatus } from '../../../../TicketDetail/AppointmentStatus';
import { useConfirmCheckIn } from '../../../../Appt/components/ApptCheckInOutAlert/hooks/useConfirmCheckIn';
import { abortFlow } from '../../../../../utils/abortFlow';
import { useEarlyCheckInValidate } from './useEarlyCheckInValidate';

export interface TicketChangeExtraParams extends TicketCancelParams {
  /**
   * ready for pickup notification
   */
  messageMethodForPickupNotification?: number;
  checkoutDate?: string;
}

export const useChangeTicketStatus = <Result = void>(refreshTicket: (status: AppointmentStatus) => Result) => {
  const dispatch = useDispatch();
  const handleConfirmCheckIn = useConfirmCheckIn();
  const validateEarlyCheckIn = useEarlyCheckInValidate();

  return useLatestCallback(
    async (
      ticketId: number,
      status: AppointmentStatus,
      extra: TicketChangeExtraParams = {},
      needRefreshTicket = true,
    ): Promise<Result | void> => {
      try {
        if (status === AppointmentStatus.CANCELED) {
          const params: TicketCancelParamsOrigin = {
            id: ticketId,
          };
          // 下面是旧逻辑平移过来
          params.noShow = extra?.noShow ? 1 : 2;
          params.cancelReason = extra?.cancelReason || '';
          if (extra?.repeatType) {
            params.repeatType = extra.repeatType;
          }
          params.releasePreAuth = extra?.releasePreAuth;
          // 未传则默认是 business cancel 与后端逻辑相同，避免未传导致后端不展示 cancel 的 history log
          params.cancelByType = extra?.cancelByType ?? GroomingCancelByType.Business;
          if (extra?.autoRefundOrder) {
            params.autoRefundOrder = extra.autoRefundOrder;
          }
          if ('chargeNoShowFee' in extra) {
            params.chargeNoShowFee = extra.chargeNoShowFee;
          }
          await dispatch(cancelGroomingTicket(params));
        } else {
          if (status === AppointmentStatus.CHECKED_IN) {
            await validateEarlyCheckIn(ticketId);

            const { next } = await handleConfirmCheckIn({ appointmentId: `${ticketId}` });
            if (!next) {
              abortFlow('Check in process canceled by alert!');
            }
          }

          const res = await dispatch(
            changeGroomingTicketStatus({
              groomingId: ticketId,
              status,
              messageMethodForPickupNotification: extra?.messageMethodForPickupNotification,
              checkOut: extra?.checkoutDate ? { endDate: extra?.checkoutDate } : undefined,
            }),
          );
          // ready for pickup sent failed, need to show error message
          if (
            status === AppointmentStatus.READY &&
            res &&
            res.notificationSent === false &&
            isString(res.reasonForNotificationFailed) &&
            res.reasonForNotificationFailed.length > 0
          ) {
            toastApi.error(res.reasonForNotificationFailed);
          }
        }
        if (needRefreshTicket) {
          return refreshTicket(status);
        }
      } catch (e) {
        console.error(e);
        if (needRefreshTicket) {
          await refreshTicket(status);
        }
        // 如果接口报错了，阻止后续流程继续进行
        abortFlow(e?.message);
      }
    },
  );
};
