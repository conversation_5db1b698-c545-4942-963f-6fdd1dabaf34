import { useSelector } from 'amos';
import { useEffect } from 'react';
import { PATH_CUSTOMER_LIST } from '../../../../../router/paths';
import { selectCreationIsAvailable } from '../../../../../store/business/customerSetting.selectors';
import { useRouteState } from '../../../../../utils/RoutePath';
import { useBool } from '../../../../../utils/hooks/useBool';

export const useLeadsDrawerVisible = () => {
  const [canViewLeads] = useSelector(selectCreationIsAvailable);
  const leadsDrawerVisible = useBool();
  const locationState = useRouteState(PATH_CUSTOMER_LIST) ?? {};
  const { showViewLeadsDrawer = false } = locationState;

  useEffect(() => {
    if (showViewLeadsDrawer) {
      leadsDrawerVisible.open();
    }
  }, [showViewLeadsDrawer]);

  return {
    canViewLeads,
    visible: leadsDrawerVisible.value,
    setVisible: leadsDrawerVisible.as,
  };
};
