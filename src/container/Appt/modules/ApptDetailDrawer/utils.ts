import { OrderStatus } from '@moego/api-web/moego/models/order/v1/order_enums';
import { OrderModelOrderType, type OrderModelAppointmentView } from '@moego/api-web/moego/models/order/v1/order_models';

export function hasOriginalOrder(orders: OrderModelAppointmentView[]) {
  return orders.some((order) => order.orderType === OrderModelOrderType.ORIGIN);
}

export function getUnsettledOrders(orders: OrderModelAppointmentView[]) {
  return orders.filter((order) => [OrderStatus.CREATED, OrderStatus.PROCESSING].includes(order.status));
}

export function getUnsettledOriginalOrders(orders: OrderModelAppointmentView[]) {
  return orders.filter(
    (order) =>
      [OrderStatus.CREATED, OrderStatus.PROCESSING].includes(order.status) &&
      order.orderType === OrderModelOrderType.ORIGIN,
  );
}
