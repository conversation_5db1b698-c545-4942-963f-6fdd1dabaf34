import { cn, DatePicker, Form, numberToString, Select, TimePicker, type useForm, useWatch } from '@moego/ui';
import { useSelector } from 'amos';
import { type Dayjs } from 'dayjs';
import React, { memo } from 'react';
import { selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import { SelectRequireStaffDateTypeOptions } from '../../../../../../components/DateType/DateType.utils';
import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';

interface SelectDynamicDateTimeProps {
  dateSelect: {
    label: string;
    key: string;
  };
  date: {
    label: string;
    key: string;
    disabledDate?: (d: Dayjs) => boolean;
  };
  time: {
    label: string;
    key: string;
    disabledTimes?: (d: Dayjs) => boolean;
  };
  isDateClearable?: boolean;
  isTimeClearable?: boolean;
  form?: ReturnType<typeof useForm>;
}
export const SelectDynamicDateTime = memo((props: SelectDynamicDateTimeProps) => {
  const { dateSelect, date, time, isDateClearable = true, isTimeClearable = true, form } = props;
  const [business] = useSelector(selectCurrentBusiness);

  const dateType = useWatch({
    control: form?.control,
    name: dateSelect.key,
  }) as PetDetailDateType | undefined;
  const isShowDatePicker = dateType === PetDetailDateType.PET_DETAIL_DATE_DATE_POINT;

  return (
    <div className="moe-grid moe-grid-cols-2 moe-gap-s">
      <Form.Item name={dateSelect.key} label={dateSelect.label} rules={{ required: true }} transformer={numberToString}>
        <Select
          isRequired
          classNames={{
            // 文案太长，指定宽度
            overlay: '!moe-w-[290px]',
          }}
        >
          {SelectRequireStaffDateTypeOptions.map((option) => (
            <Select.Item key={String(option.key)} title={option.label} />
          ))}
        </Select>
      </Form.Item>
      {isShowDatePicker && (
        <Form.Item name={date.key} rules={{ required: true }} label={date.label}>
          <DatePicker
            isRequired
            placeholder="Select date"
            disabledDate={date.disabledDate}
            format={business.dateFormat}
            isClearable={isDateClearable}
          />
        </Form.Item>
      )}
      <div className={cn(isShowDatePicker && 'moe-col-span-2')}>
        <Form.Item name={time.key} rules={{ required: true }} label={time.label}>
          <TimePicker
            isRequired
            placeholder="Choose time"
            format={business.timeFormat()}
            disabledTimes={time.disabledTimes}
            isClearable={isTimeClearable}
          />
        </Form.Item>
      </div>
    </div>
  );
});
