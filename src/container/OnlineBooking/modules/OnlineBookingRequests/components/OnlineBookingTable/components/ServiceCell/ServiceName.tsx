import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Text, type TextProps } from '@moego/ui';
import { useSelector } from 'amos';
import React, { type FC } from 'react';
import { companyServiceMapBox } from '../../../../../../../../store/service/service.boxes';

export interface ServiceNameProps {
  serviceCellInfo: {
    serviceItemType: ServiceItemType;
    serviceName?: string;
    evaluationName?: string;
    serviceId?: string;
  };
  as?: TextProps['as'];
}

export const ServiceName: FC<ServiceNameProps> = (props) => {
  const {
    serviceCellInfo: { serviceItemType, serviceName, evaluationName, serviceId },
    as,
  } = props;
  const [companyServiceMap] = useSelector(companyServiceMapBox);

  const isEvaluation = serviceItemType === ServiceItemType.EVALUATION;
  const { name: evaluationRelatedServiceName } = companyServiceMap.mustGetItem(Number(serviceId));

  // evaluation display name like: Evaluation (Boarding deluxe)
  const nameForEvaluation = evaluationRelatedServiceName
    ? `${evaluationName} (${evaluationRelatedServiceName})`
    : evaluationName;
  const name = isEvaluation ? nameForEvaluation : serviceName;

  return (
    <Text variant="regular-short" className="moe-text-primary" as={as}>
      {name}
    </Text>
  );
};
