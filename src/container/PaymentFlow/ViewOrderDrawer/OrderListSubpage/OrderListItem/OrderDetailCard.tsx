import { type IGeneralOrderDetailV2 } from '@moego/finance-web-kit';
import { MinorChevronDownOutlined, MinorChevronUpOutlined, MinorLocationOutlined } from '@moego/icons-react';
import { Collapse, Heading, Text, cn } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import React, { memo, useContext } from 'react';
import { type FinanceKit } from '../../../../../service/finance-kit';
import { type WeekDay, WeekDayMap } from '../../../../../store/business/business.boxes';
import { selectBusiness, selectCurrentBusiness } from '../../../../../store/business/business.selectors';
import { useBool } from '../../../../../utils/hooks/useBool';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { LayoutCard } from '../../../CartMixinOrderDrawer/components/Layout/LayoutCard';
import { ViewOrderDrawerContext } from '../../ViewOrderDrawer.context';
import { OrderTypeHeader } from '../../components/OrderTypeHeader';
import { OrderActions } from '../OrderActions';
import { CollectInfo } from './CollectInfo';
import { OrderItemsInfo } from './OrderItemsInfo';

interface OrderDetailCardProps {
  className?: string;
  orderDetail: IGeneralOrderDetailV2<typeof FinanceKit.model>;
  isCancelledAppt?: boolean;
}

export const OrderDetailCard = memo<OrderDetailCardProps>((props) => {
  const { orderDetail, isCancelledAppt } = props;
  const order = orderDetail.order;
  const context = useContext(ViewOrderDrawerContext);

  const [business, orderBusiness] = useSelector(selectCurrentBusiness, selectBusiness(Number(order.businessId)));

  const totalAmount = business.formatAmount(order.totalAmount.valueOf());
  const defaultExpanded = order.isNoShow;
  const isExpanded = useBool(defaultExpanded);

  const handleRefreshList = useSerialCallback(async () => {
    context?.refreshOrderList();
  });

  return (
    <LayoutCard border className={cn('moe-flex-1 moe-p-0', props.className)}>
      <OrderTypeHeader order={orderDetail.order} />
      <section className="moe-p-[16px]">
        <div data-slot="status-and-actions" className={'moe-flex moe-justify-between moe-items-center moe-max-w-full'}>
          <Heading size={'2'}>{totalAmount}</Heading>
          <OrderActions
            onMarkCompleted={handleRefreshList}
            onRefundItems={handleRefreshList}
            order={order}
            isCancelledAppt={isCancelledAppt}
          />
        </div>

        {/* 时间、地址信息 */}
        <div data-slot="invoice-header" className={'moe-mt-xxs'}>
          <div className="moe-flex moe-gap-xxs moe-mt-xs moe-items-center">
            <Text variant={'small'} className="moe-text-secondary">
              {WeekDayMap[dayjs(+order.createTime * T_SECOND).get('day') as WeekDay]},{' '}
              {business.formatDate(+order.createTime * T_SECOND)}
            </Text>
            <Text variant={'small'} className="moe-text-tertiary">
              ·
            </Text>
            <Text variant={'small'} className="moe-flex moe-items-center moe-text-secondary">
              <MinorLocationOutlined className="moe-mr-xxs" />
              {orderBusiness.businessName}
            </Text>
          </div>
        </div>

        {/* 折叠内容 */}
        <div>
          <Collapse.Transition isExpanded={isExpanded.value}>
            <div>
              <OrderItemsInfo orderDetail={orderDetail} className="moe-mt-m" />
              <CollectInfo orderDetail={orderDetail} className="moe-mt-m" />
            </div>
          </Collapse.Transition>
          <Text
            variant="small"
            className="moe-mt-m moe-text-tertiary moe-cursor-pointer moe-flex moe-items-center"
            onClick={isExpanded.toggle}
          >
            {isExpanded.value ? 'Collapse details' : 'View details'}
            {isExpanded.value ? <MinorChevronUpOutlined /> : <MinorChevronDownOutlined />}
          </Text>
        </div>
      </section>
    </LayoutCard>
  );
});
