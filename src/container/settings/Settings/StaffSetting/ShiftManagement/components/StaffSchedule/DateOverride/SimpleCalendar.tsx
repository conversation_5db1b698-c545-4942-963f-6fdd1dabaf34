import { Calendar, Heading, Text } from '@moego/ui';
import dayjs, { type Dayjs } from 'dayjs';
import React, { memo, type PropsWithChildren, useEffect, useMemo, useState } from 'react';
import IconCalendarCheck from '../../../../../../../../assets/svg/calendar-schedule-check.svg';
import { SvgIcon } from '../../../../../../../../components/Icon/Icon';
import { useBizCustomClose, useBizHolidayClose } from '../../../hooks/useCheckBizOpen';
import { CalendarCollapse } from './CalendarCollapse';
import { SMTestIds } from '../../../../../../../../config/testIds/shiftManagement';

interface SimpleCalendarProps {
  isEdit: boolean;
  staffId: number;
  defaultDate?: Dayjs;
  value?: Dayjs[];
  onChange: (dates: Dayjs[]) => void;
  businessId: number;
  dateFormatMD: (date: Dayjs) => void;
}
export const SimpleCalendar = memo((props: PropsWithChildren<SimpleCalendarProps>) => {
  const { isEdit, value = [], onChange, businessId, defaultDate, dateFormatMD } = props;

  const today = useMemo(() => dayjs(), []);
  const checkDateInBizCustomClosed = useBizCustomClose(businessId);
  const checkDateInBizHolidayClosed = useBizHolidayClose(businessId);
  const [expandCalendar, setExpandCalendar] = useState(!isEdit);

  const innerValue = isEdit && value.length === 0 && defaultDate ? [defaultDate] : value;

  useEffect(() => {
    setExpandCalendar(isEdit ? false : true);
  }, [isEdit]);

  const renderSelectedDate = () => {
    // 判断 value 是否是连续的，如果连续，则显示 mm/dd - mm/dd， 如果不连续，则显示 mm/dd, mm/dd, mm/dd, ...
    if (innerValue.length === 0) {
      return '-';
    }

    const sortedValue = innerValue.sort((a, b) => a.diff(b, 'day'));

    const result: Dayjs[][] = [[sortedValue[0]]];
    let curIndex = 0;
    for (let i = 1; i < sortedValue.length; i++) {
      const prev = sortedValue[i - 1];
      const cur = sortedValue[i];
      if (cur.diff(prev, 'day') === 1) {
        result[curIndex].push(cur);
      } else {
        curIndex++;
        result[curIndex] = [cur];
      }
    }

    return result
      .map((dates) => {
        if (dates.length === 1) {
          return dateFormatMD(dates[0]);
        }

        return `${dateFormatMD(dates[0])} - ${dateFormatMD(dates[dates.length - 1])}`;
      })
      .join(', ');
  };

  const moeCalendar = (
    <Calendar
      classNames={{
        wrapper: 'moe-gap-[12px]',
      }}
      value={innerValue}
      headerExtra={null}
      selectionMode="multiple"
      disabledDate={(date) => {
        const isBizClosedDate = !!(checkDateInBizCustomClosed(date) || checkDateInBizHolidayClosed(date)?.description);
        return isBizClosedDate || date.isBefore(today.startOf('date'));
      }}
      renderCellTooltip={(date) => {
        const isBizClosedDate = !!(checkDateInBizCustomClosed(date) || checkDateInBizHolidayClosed(date)?.description);
        if (isBizClosedDate) return 'closed';

        return null;
      }}
      onChange={onChange}
      hidePrevButton={innerValue.length > 0 && innerValue[0].startOf('month') <= today.startOf('month')}
      data-testid={SMTestIds.SettingDateOverrideDrawerCalendar}
    />
  );

  return (
    <>
      <Heading size="5" className="moe-mt-m moe-mb-[12px]">
        Select the override date(s)
      </Heading>
      <div className="moe-flex moe-items-center moe-gap-xs">
        <Text variant="small">Selected:</Text>
        <Text variant="small">{renderSelectedDate()}</Text>
      </div>
      <CalendarCollapse isExpand={expandCalendar} onExpandChange={setExpandCalendar}>
        {moeCalendar}
      </CalendarCollapse>
    </>
  );
});

export function SimpleTitleWithIcon(props: PropsWithChildren<{}>) {
  const { children } = props;
  return (
    <div className="!moe-my-[20px] moe-flex moe-items-center">
      <SvgIcon src={IconCalendarCheck} color="#3D414B" size={20} className="moe-mr-xs" />
      {children}
    </div>
  );
}
