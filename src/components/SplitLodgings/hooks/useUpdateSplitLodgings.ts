import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import { isNil, isUndefined, update } from 'lodash';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { useLatestCallback } from '../../../utils/hooks/useLatestCallback';
import {
  type ISplitLodgingItemChangePayload,
  SplitLodgingChangeType,
  type SplitLodgingItemValue,
  type SplitLodgingServiceRange,
  type UpdateSplitLodgingsParams,
} from '../utils/SplitLodgings.types';
import { getMaxServicePriceByLodgingType } from '../../../store/lodging/actions/private/lodgingType.actions';
import { MoeMoney } from '@moego/finance-utils';
import { selectService } from '../../../store/service/service.selectors';

export const useUpdateSplitLodgings = (params: UpdateSplitLodgingsParams) => {
  const dispatch = useDispatch();
  const { serviceRange, serviceId } = params;
  const [service, business] = useSelector(selectService(serviceId), selectCurrentBusiness);

  // 每次修改的时候都要同步一下
  // 以当前 lodging 的 endDate 和 endTime 为准同步至将下一个 lodging 的 startDate 和 startTime
  const syncLodgingDatesAndTimes = (splitLodgings: SplitLodgingItemValue[]): SplitLodgingItemValue[] => {
    return splitLodgings.map((currentLodging, index) => {
      if (index === 0) return currentLodging;
      const prevLodging = splitLodgings[index - 1];
      return {
        ...currentLodging,
        startDate: !isNil(prevLodging.endDate) ? prevLodging.endDate : currentLodging.startDate,
        startTime: !isUndefined(prevLodging.endTime) ? prevLodging.endTime : currentLodging.startTime,
      };
    });
  };

  const createNewLodging = useLatestCallback((splitLodgings: SplitLodgingItemValue[]): SplitLodgingItemValue => {
    // 新增 lodging 时价格优先用现有的 applicable 的 lodging 的价格
    const firstApplicableLodging = splitLodgings.find((item) => item.isApplicable);
    return {
      lodgingId: undefined,
      startDate: undefined,
      startTime: undefined,
      endDate: undefined,
      endTime: serviceRange.endTime,
      isApplicable: true,
      price: firstApplicableLodging?.price ?? service.price,
    };
  });

  const addSplitLodging = useLatestCallback((splitLodgings: SplitLodgingItemValue[]) => {
    const result = [...splitLodgings];
    if (splitLodgings.length === 1) {
      update(result, '[0]', (item) => ({
        ...item,
        endDate: null,
        endTime: serviceRange.endTime,
        startDate: serviceRange.startDate,
        startTime: serviceRange.startTime,
      }));
    }
    result.push(createNewLodging(result));
    return syncLodgingDatesAndTimes(result);
  });

  const deleteSplitLodging = useLatestCallback((splitLodgings: SplitLodgingItemValue[], deleteIndex: number) => {
    const result = [...splitLodgings];
    result.splice(deleteIndex, 1);

    if (result.length === 1) {
      update(result, '[0]', (item) => ({
        ...item,
        startDate: null,
        startTime: undefined,
        endDate: null,
        endTime: undefined,
      }));
    }

    return syncLodgingDatesAndTimes(result);
  });

  const tryGetPriceAfterChangeLodging = useLatestCallback(
    async (payload: ISplitLodgingItemChangePayload, isSingleLodging: boolean) => {
      if (payload.type === SplitLodgingChangeType.LodgingId) {
        const lodgingId = payload.value.lodgingId;
        // 只在非单个 lodging 时，才需要获取价格
        // 如果 lodgingId 存在，并且 lodging 非可用，则获取 lodging 可选 service 的最大价格
        if (!isSingleLodging && !isUndefined(lodgingId) && !payload.value.isApplicable) {
          const { maxPrice } = await dispatch(getMaxServicePriceByLodgingType(lodgingId));
          return MoeMoney.fromMoney(maxPrice).valueOf();
        }
      }
    },
  );

  const changeSplitLodging = useLatestCallback(
    async (
      splitLodgings: SplitLodgingItemValue[],
      payload: ISplitLodgingItemChangePayload,
      serviceRange: SplitLodgingServiceRange,
    ) => {
      const result = [...splitLodgings];

      if (payload.index === 0) {
        update(result, `[${payload.index}]`, (item) => ({
          ...item,
          startDate: serviceRange.startDate,
          startTime: serviceRange.startTime,
        }));
      }

      switch (payload.type) {
        case SplitLodgingChangeType.LodgingId: {
          const isSingleLodging = result.length === 1;
          const maxPrice = await tryGetPriceAfterChangeLodging(payload, isSingleLodging);

          update(result, `[${payload.index}]`, (item) => ({
            ...item,
            ...(!isUndefined(maxPrice) ? { price: maxPrice } : {}),
            isApplicable: payload.value.isApplicable,
            lodgingId: payload.value.lodgingId,
            lodgingName: payload.value.lodgingName,
          }));
          break;
        }

        case SplitLodgingChangeType.Price:
          update(result, `[${payload.index}]`, (item) => ({ ...item, price: payload.value.price }));
          break;

        case SplitLodgingChangeType.Date: {
          // 修改当前 lodging 的 endDate
          update(result, `[${payload.index}]`, (item) => ({
            ...item,
            endDate: isNil(payload.value.endDate) ? undefined : business.formatDate(payload.value.endDate),
          }));

          // 如果选中的日期已经是 serviceRange 的最后一天则移除后续所有 items
          if (payload.value.endDate?.isSame(dayjs(serviceRange.endDate), 'day')) {
            result.splice(payload.index + 1, result.length - payload.index - 1);
          }

          // 修改 date 之后后续所有的 lodging 都 reset date
          let index = payload.index + 1;
          while (index < result.length) {
            update(result, `[${index}]`, (item) => ({ ...item, endDate: null, startDate: null }));
            index++;
          }
          break;
        }

        case SplitLodgingChangeType.Time:
          update(result, `[${payload.index}]`, (item) => ({ ...item, endTime: payload.value.endTime?.getMinutes() }));
          break;
      }

      return syncLodgingDatesAndTimes(result);
    },
  );

  return { addSplitLodging, deleteSplitLodging, changeSplitLodging };
};
