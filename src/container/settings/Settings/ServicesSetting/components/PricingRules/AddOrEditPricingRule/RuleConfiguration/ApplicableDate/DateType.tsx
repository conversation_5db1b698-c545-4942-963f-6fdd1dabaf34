import { Radio, RadioGroup } from '@moego/ui';
import React, { memo } from 'react';
import { ApplicableDateType } from '../../utils/AddOrEditPricingRule.config';

interface DateTypeProps {
  value: number;
  onChange?: (value: number) => void;
}
export const DateType = memo<DateTypeProps>((props) => {
  const { value, onChange } = props;
  return (
    <RadioGroup isRequired orientation="horizontal" value={value} onChange={onChange}>
      {ApplicableDateType.values.map((item) => (
        <Radio value={item} key={item}>
          {ApplicableDateType.mapLabels[item]}
        </Radio>
      ))}
    </RadioGroup>
  );
});
