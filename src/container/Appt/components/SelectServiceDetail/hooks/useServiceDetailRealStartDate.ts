import { useSelector } from 'amos';
import { useMemo } from 'react';
import { selectMainServiceInAppt } from '../../../store/appt.selectors';
import { PetDetailDateType } from '@moego/api-web/moego/models/appointment/v1/pet_detail_enums';

/**
 * 结合 dateType 和 startDate 信息，获得具体的开始日期，并包装为 useCallback 函数
 */
export interface ServiceDetailRealStartDateProps {
  startDate?: string;
  dateType?: PetDetailDateType;
  appointmentId: string;
}

export const useServiceDetailRealStartDate = ({
  startDate,
  dateType,
  appointmentId,
}: ServiceDetailRealStartDateProps) => {
  const [mainService] = useSelector(selectMainServiceInAppt(appointmentId));

  return useMemo(() => {
    switch (dateType) {
      case PetDetailDateType.PET_DETAIL_DATE_FIRST_DAY:
        return mainService.startDate;
      case PetDetailDateType.PET_DETAIL_DATE_LAST_DAY:
        return mainService.endDate;
      default:
        return startDate;
    }
  }, [dateType, mainService.endDate, mainService.startDate, startDate]);
};
