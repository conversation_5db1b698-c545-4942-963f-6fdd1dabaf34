import { ComponentType, useKitComponents } from '@moego/finance-ui';
import { type TypeofPaymentStatus } from '@moego/finance-web-kit';
import { MajorMoreOutlined } from '@moego/icons-react';
import { Dropdown, IconButton, Button as Moe<PERSON><PERSON><PERSON>, Tooltip } from '@moego/ui';
import { useSelector } from 'amos';
import { type ColumnType } from 'antd/lib/table';
import { T_SECOND } from 'monofile-utilities/lib/consts';
import React, { useMemo } from 'react';
import { BusinessName } from '../../../../components/BusinessName';
import { Button, ButtonList } from '../../../../components/Button/Button';
import { Font } from '../../../../components/Style/Style';
import { WithMultiLocation } from '../../../../components/WithFeature/WithMultiLocation';
import { FinanceKit } from '../../../../service/finance-kit';
import { type BusinessRecord } from '../../../../store/business/business.boxes';
import { selectCurrentPermissions } from '../../../../store/business/role.selectors';
import {
  type PaymentListFilterRecord,
  type PaymentRecord,
  PaymentStatus,
  type PaymentVendor,
} from '../../../../store/payment/payment.boxes';
import { truly } from '../../../../store/utils/utils';
import { useInvoiceReinvent } from '../../../PaymentFlow/hooks/useInvoiceReinvent';

interface TUsePaymentHistoryColumns {
  business: BusinessRecord;
  filter: PaymentListFilterRecord;
  handleReceipt: (record: PaymentRecord) => void;
  handleRefund: (record: PaymentRecord) => void;
  handleBookingId: (record: PaymentRecord) => void;
  vendor?: PaymentVendor;
}

export const usePaymentHistoryColumns = ({
  business,
  filter,
  handleReceipt,
  handleRefund,
  handleBookingId,
  vendor,
}: TUsePaymentHistoryColumns) => {
  const [permissions] = useSelector(selectCurrentPermissions);
  const { isEnableToNewFlow } = useInvoiceReinvent();
  const canProcessRefund = permissions.has('canProcessRefund');
  const canProcessPayment = permissions.has('canProcessPayment');
  return useMemo(
    () =>
      Array<ColumnType<PaymentRecord> | undefined>(
        {
          title: 'Date & time',
          key: 'order',
          width: 180,
          sorter: true,
          render: (record) => business.formatDateTime(record.createTime * T_SECOND),
        },
        {
          title: 'Client name',
          render: (record) => record.customerName,
          width: 140,
        },
        {
          title: 'Amount',
          align: 'right',
          render: (record) => business.formatAmount(record.amount),
          width: 120,
        },
        vendor
          ? {
              title: 'Processing fee',
              align: 'right',
              width: 120,
              render: (record) => {
                // 由于 processingFee 为 -1 状态的可能性比较多，无法确认是否处于 In processing 状态，暂时都不显示
                if (record.processingFee === -1) {
                  return '';
                }
                return business.formatAmount(record.processingFee);
              },
            }
          : void 0,
        {
          title: 'Paid by',
          render: (record) => record.customerName,
          width: 120,
        },
        {
          title: 'Payment method',
          render: (record) => record.paymentMethod || '',
          width: 220,
        },
        isEnableToNewFlow
          ? {
              title: 'Payment status',
              width: 130,
              render: (record: PaymentRecord) => {
                const { Components } = useKitComponents(FinanceKit, ComponentType.PaymentStatus);
                if (!Components) return null;
                return <Components.PaymentStatusComponent status={record.status as TypeofPaymentStatus} />;
              },
            }
          : {
              title: 'Charge Status',
              width: 130,
              render: (record) => {
                return (
                  <Font color={PaymentStatus.mapLabels[record.status]?.theme} weight="600">
                    {record.printStatus()}
                  </Font>
                );
              },
            },
        {
          title: 'Booking ID',
          width: 120,
          render: (record) => {
            const isPrepaidRequireCapture = record.isPrepay && record.status === PaymentStatus.Processing;
            return (
              <div className="moe-flex moe-flex-col">
                <Font
                  color={isPrepaidRequireCapture ? '#999' : '#0091ff'}
                  onClick={isPrepaidRequireCapture ? undefined : () => handleBookingId(record)}
                >
                  {record.groomingId ? '#' + record.groomingId : void 0}
                </Font>
                <WithMultiLocation scene="all">
                  <BusinessName businessId={record.businessId} className="moe-text-s moe-text-[#999]" />
                </WithMultiLocation>
              </div>
            );
          },
        },
        {
          title: 'Action',
          width: 180,
          render: (record) => {
            const isPrepaidRequireCapture = record.isPrepay && record.status === PaymentStatus.Processing;
            if (isEnableToNewFlow) {
              return (
                <div className="moe-flex moe-gap-s moe-items-center">
                  <Tooltip
                    isDisabled={canProcessRefund}
                    side="top"
                    content="Please request “Can process refund” permission from the owner"
                    delay={0}
                    closeDelay={0}
                  >
                    <MoeButton
                      isDisabled={!canProcessRefund}
                      onPress={() => handleRefund(record)}
                      size="s"
                      color="danger"
                    >
                      Refund payment
                    </MoeButton>
                  </Tooltip>
                  <Dropdown align="end">
                    <Dropdown.Trigger>
                      <IconButton icon={<MajorMoreOutlined />} size="m" />
                    </Dropdown.Trigger>
                    <Dropdown.Menu disabledKeys={isPrepaidRequireCapture ? ['viewReceipt'] : []}>
                      <Dropdown.MenuItem key={'viewReceipt'} onAction={() => handleReceipt(record)}>
                        View receipt(s)
                      </Dropdown.MenuItem>
                    </Dropdown.Menu>
                  </Dropdown>
                </div>
              );
            }
            return (
              <ButtonList>
                <Button
                  size="sm"
                  buttonRadius="circle"
                  btnType="primary"
                  onClick={() => handleReceipt(record)}
                  disabled={isPrepaidRequireCapture}
                >
                  Receipt
                </Button>
                <Tooltip
                  isDisabled={canProcessPayment}
                  side="top"
                  content="Please request “Can process payment” permission from the owner"
                  delay={0}
                  closeDelay={0}
                >
                  <Button
                    disabled={!canProcessPayment}
                    size="sm"
                    buttonRadius="circle"
                    btnType="secondary"
                    onClick={() => handleRefund(record)}
                  >
                    Refund
                  </Button>
                </Tooltip>
              </ButtonList>
            );
          },
        },
      ).filter(truly),
    [business, filter, handleReceipt, handleRefund, handleBookingId],
  );
};
