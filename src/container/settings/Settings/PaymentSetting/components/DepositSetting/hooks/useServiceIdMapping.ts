import { useSelector } from 'amos';
import { selectAllCategoriesOfAllServices } from '../../../../../../../store/service/category.selectors';
import React from 'react';
import { type ServiceItemModel } from '../../../../../../../store/service/service.boxes';

export const useServiceIdMapping = () => {
  const [allServices] = useSelector(selectAllCategoriesOfAllServices());
  return React.useMemo(() => {
    const serviceIdMapping = new Map<number, ServiceItemModel>();
    for (const serviceType in allServices) {
      for (const servicesByType of allServices[serviceType]) {
        for (const item of servicesByType.options) {
          serviceIdMapping.set(item.value, item);
        }
      }
    }
    return serviceIdMapping;
  }, [allServices]);
};
