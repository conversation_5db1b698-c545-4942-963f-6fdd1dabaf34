/* eslint-disable sonarjs/no-nested-functions */
import { useMemo } from 'react';
import { type PetServiceListWithMainService, type ApptPetIdsServiceList } from '../../../../../store/appt.types';
import { isAdditionalServiceRuleHit } from '../AdditionalService.utils';
import { PriceUnitEnum } from '../../../../../../../components/ServiceApplicablePicker/utils/priceUnit';

export interface MergedPetServiceListParams {
  stayNights: number;
  originPetIdsServiceList?: ApptPetIdsServiceList;
  additionalPetServiceList: ApptPetIdsServiceList;
  petServiceListWithMain: PetServiceListWithMainService[];
}

/**
 * 合并 origin service list 和 additional service list
 * 如果一个 serviceId 已经在 additional service 存在了，就干掉 origin service list 的 serviceId 对应的 service
 */
export const useMergedPetServiceList = (params: MergedPetServiceListParams) => {
  const { stayNights, originPetIdsServiceList, additionalPetServiceList, petServiceListWithMain } = params;
  const finalPetIdsServiceList = useMemo(() => {
    return (
      originPetIdsServiceList?.map((petItem) => {
        const { petId, serviceList: origServiceList } = petItem;
        const petAdditionalServiceList = additionalPetServiceList
          .filter((p) => p.petId === petId)
          .map((p) => p.serviceList)
          .flat();
        const petMainServiceInfo = petServiceListWithMain.find((p) => p.petId === petId)?.mainServiceInfo;
        const stayLength = petMainServiceInfo?.priceUnit === PriceUnitEnum.PER_DAY ? stayNights + 1 : stayNights;
        if (!isAdditionalServiceRuleHit(petMainServiceInfo?.additionalServiceRule, stayLength)) {
          return petItem;
        }
        const mergedServiceList = origServiceList
          .filter((service) => {
            return petAdditionalServiceList.findIndex((s) => s.serviceId === service.serviceId) === -1;
          })
          .concat(petAdditionalServiceList);
        return {
          ...petItem,
          serviceList: mergedServiceList,
        };
      }) || []
    );
  }, [additionalPetServiceList, originPetIdsServiceList, petServiceListWithMain, stayNights]);

  return {
    finalPetIdsServiceList,
  };
};
