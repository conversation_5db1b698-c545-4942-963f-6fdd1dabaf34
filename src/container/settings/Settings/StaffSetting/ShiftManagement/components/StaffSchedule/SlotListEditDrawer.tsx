import { Drawer, Input, Markup, TimePicker, Tooltip, type TimePickerProps } from '@moego/ui';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState, type ReactNode } from 'react';
import SvgFormRequiredSvg from '../../../../../../../assets/svg/form-required.svg';
import { SvgIcon } from '../../../../../../../components/Icon/Icon';
import { toastApi } from '../../../../../../../components/Toast/Toast';
import type { WorkingSlotValue } from '../../../../../../../store/staff/staff.boxes';
import { useSelector } from 'amos';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';
import { TimeFormat } from '@moego/api-web/moego/models/organization/v1/company_enums';
import { MinorInfoOutlined } from '@moego/icons-react';
import { LimitationForm, type LimitationFormRef } from '../Limitation/LimitationForm';
import type { LimitationFormValue } from '../Limitation/LimitationFormContext';
import { useSerialCallback } from '@moego/tools';

export interface SlotListEditDrawerProps {
  title: ReactNode;
  visible: boolean;
  value: WorkingSlotValue['slotHourSettingList'][number];
  onChange?: (v: WorkingSlotValue['slotHourSettingList'][number]) => void;
  onConfirm?: () => void;
  onClose?: () => void;
  onCancel?: () => void;
}

export const SlotListEditDrawer = (props: SlotListEditDrawerProps) => {
  const { value, onChange, title, visible, onConfirm, onClose, onCancel } = props;
  const [innerValue, setInnerValue] = useState(value);
  const { startTime, capacity, limit } = innerValue;
  const [business] = useSelector(selectCurrentBusiness);

  const limitationFormRef = useRef<LimitationFormRef>(null);

  useEffect(() => {
    if (value) {
      setInnerValue(value);
    }
  }, [value]);

  const handleDrawerConfirm = useSerialCallback(async () => {
    await limitationFormRef.current?.submit();
  });

  const onSlotTimeChange: TimePickerProps['onChange'] = (v) => {
    setInnerValue({ ...innerValue, startTime: v?.getMinutes() ?? 0 });
  };

  const onCapacityChange = (v: number | null) => {
    setInnerValue({ ...innerValue, capacity: v ?? 0 });
  };

  const onLimitChange = async (v: LimitationFormValue) => {
    const nextInnerValue = { ...innerValue, limit: v };
    await onConfirm?.();
    await onChange?.(nextInnerValue);
    await onClose?.();
    toastApi.success('Applied successfully');
  };

  if (!innerValue) return null;

  return (
    <Drawer
      title={title || 'Edit slot'}
      isDismissable={false}
      autoCloseOnConfirm={false}
      isOpen={visible}
      className="moe-w-[600px]"
      confirmButtonProps={{
        isLoading: handleDrawerConfirm.isBusy(),
      }}
      onConfirm={handleDrawerConfirm}
      onCancel={onCancel}
      onClose={async () => {
        if (handleDrawerConfirm.isBusy()) return;

        await onClose?.();
      }}
    >
      <div>
        <div className="moe-grid moe-grid-cols-2 moe-gap-x-m moe-gap-y-xxs moe-border-divider moe-border-b moe-pb-m">
          <div>
            <span>Slot</span>
            <SvgIcon src={SvgFormRequiredSvg} color="#F3413B" className="!moe-ml-0 moe-mr-xxs" />
          </div>

          <div>Pet maximum</div>

          <TimePicker
            value={dayjs().setMinutes(startTime)}
            minuteStep={5}
            onChange={onSlotTimeChange}
            use12Hours={business.timeFormatType === TimeFormat.HOUR_12}
          />

          <Input.Number
            className="moe-w-[220px]"
            isStepper
            step={1}
            minValue={0}
            value={capacity}
            onChange={onCapacityChange}
          />
        </div>

        <div className="moe-pt-m">
          <Markup className="moe-mb-[20px] moe-flex moe-items-center moe-text-secondary moe-gap-xxs" variant="small">
            Select the limitations and set rules
            <Tooltip
              side="top"
              content="Set rules for each selected limitation. Matching any rule will restrict the pet."
            >
              <MinorInfoOutlined className="moe-cursor-pointer" />
            </Tooltip>
          </Markup>

          <LimitationForm ref={limitationFormRef} value={limit} onChange={onLimitChange} />
        </div>
      </div>
    </Drawer>
  );
};
