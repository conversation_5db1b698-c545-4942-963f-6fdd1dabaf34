import { useDispatch } from 'amos';
import dayjs from 'dayjs';
import { useCallback, useMemo } from 'react';
import {
  addLodgingPlaceholderCard,
  removeLodgingPlaceholderCard,
} from '../../../../store/calendarLatest/actions/private/lodgingCalendar.actions';
import { CalendarCardTypeMixed } from '../../../../store/calendarLatest/card.types';
import { getLodgingCardUUID } from '../../../../store/calendarLatest/card.utils';
import {
  type LodgingCalendarPlaceholderCardType,
  LodgingCalendarPlaceholderCardTypeMap,
} from '../../../../store/calendarLatest/lodgingCalendar.boxes';
import { type LodgingCalendarEvent } from '../LodgingCalendar.types';
import { store } from '../../../../provider';
import { selectBusinessLodgingPlaceholderCardList } from '../../../../store/calendarLatest/lodgingCalendar.selectors';

const removePlaceholderCard = () => {
  const cards = store.select(selectBusinessLodgingPlaceholderCardList);
  if (cards.size === 0) {
    return;
  }
  store.dispatch(removeLodgingPlaceholderCard());
};

export const useLodgingPlaceHolderCard = () => {
  const dispatch = useDispatch();

  const lodgingPlaceholderAddCardId = useMemo(
    () => getLodgingCardUUID('0', CalendarCardTypeMixed.Placeholder.toString()),
    [],
  );
  const lodgingPlaceholderNewAppointmentCardId = useMemo(
    () => getLodgingCardUUID('1', CalendarCardTypeMixed.Placeholder.toString()),
    [],
  );

  const addPlaceholderCard = useCallback(
    (
      eventInfo: Pick<LodgingCalendarEvent, 'resourceId' | 'start' | 'end'>,
      type: LodgingCalendarPlaceholderCardType,
    ) => {
      // remove previous placeholder card
      removePlaceholderCard();

      const { resourceId, start, end } = eventInfo;

      dispatch(
        addLodgingPlaceholderCard({
          cardId:
            type === LodgingCalendarPlaceholderCardTypeMap.Add
              ? lodgingPlaceholderAddCardId
              : lodgingPlaceholderNewAppointmentCardId,
          type,
          startDate: dayjs(start),
          endDate: dayjs(end),
          lodgingUnitId: resourceId!,
        }),
      );
    },
    [lodgingPlaceholderAddCardId, lodgingPlaceholderNewAppointmentCardId],
  );

  return {
    addPlaceholderCard,
    removePlaceholderCard,
  };
};
