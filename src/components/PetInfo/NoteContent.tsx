import { MinorPinFilled } from '@moego/icons-react';
import { Heading, Text, cn } from '@moego/ui';
import { upperFirst } from 'lodash';
import React, { Fragment, useMemo } from 'react';
import { Condition } from '../Condition';

export interface NotesContent {
  type?: 'client' | 'pet';
  notes: { note: string; isPinned?: boolean }[];
  /** 最多展示多少条记录 */
  max?: number;
  onClick?: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
  /** 在新版moego ui上,popover自带title */
  showTitle?: boolean;
  className?: string;
}

/**
 * client 和 pet的 note都用到了
 */
export function NotesContent(props: React.PropsWithChildren<NotesContent>) {
  const { type, notes, children, onClick, max = 2, showTitle = true, className } = props;
  const list = useMemo(() => notes.slice(0, max), [max, notes]);

  return (
    <div
      className={cn('moe-px-[4px] moe-flex moe-flex-col moe-justify-stretch moe-gap-y-[8px]', className)}
      onClick={onClick}
    >
      <Condition if={showTitle}>
        <Heading size="5" className="moe-text-primary moe-mb-[8px]">
          {upperFirst(type)} notes
        </Heading>
      </Condition>
      {list.map((note, index) => {
        const isLast = index === notes.length - 1;
        return (
          <Fragment key={index}>
            <div className="moe-flex moe-items-start moe-gap-x-[8px]">
              <Condition if={type === 'pet' && note.isPinned}>
                <MinorPinFilled className="moe-text-gray-400" />
              </Condition>
              <Text variant="small" className="moe-text-secondary moe-line-clamp-4">
                {note.note}
              </Text>
            </div>
            {!isLast && <div style={{ borderTop: '1px dashed #E6E6E6' }} />}
          </Fragment>
        );
      })}
      {children}
    </div>
  );
}
